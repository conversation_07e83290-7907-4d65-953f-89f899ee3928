group 'com.xyy.flutter.container.container'
version '1.0-SNAPSHOT'

buildscript {
    ext.kotlin_version = '1.3.50'
    repositories {
        google()
        jcenter()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:3.5.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

rootProject.allprojects {
    repositories {
        google()
        jcenter()
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'

android {
    compileSdkVersion 29

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }
    defaultConfig {
        minSdkVersion 16
    }
    lintOptions {
        disable 'InvalidPackage'

    }
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    api 'com.squareup.retrofit2:retrofit:2.4.0'
    api 'com.squareup.retrofit2:converter-gson:2.4.0'
    api 'com.squareup.retrofit2:adapter-rxjava2:2.4.0'
    api 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    api 'androidx.lifecycle:lifecycle-runtime:2.2.0'
    //rxpermission
    api 'com.tbruyelle.rxpermissions2:rxpermissions:0.9.5'
    implementation 'androidx.recyclerview:recyclerview:1.1.0'
    //官方support包
    api('androidx.appcompat:appcompat:1.1.0')
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
}
