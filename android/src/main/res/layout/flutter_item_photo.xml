<?xml version="1.0" encoding="utf-8"?>
<com.xyy.flutter.container.container.bridge.photo.SquareRelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFF4F5F9">

    <ImageView
        android:id="@+id/iv_photo"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop" />

    <View
        android:id="@+id/view_marker"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#44000000"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/iv_select"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_margin="8dp"
        android:scaleType="center"
        android:src="@drawable/checkbox_normal" />

</com.xyy.flutter.container.container.bridge.photo.SquareRelativeLayout>