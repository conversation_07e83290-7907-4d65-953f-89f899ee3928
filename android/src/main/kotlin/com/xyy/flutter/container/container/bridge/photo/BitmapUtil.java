package com.xyy.flutter.container.container.bridge.photo;

import android.content.res.ColorStateList;
import android.graphics.Bitmap;
import android.graphics.Bitmap.CompressFormat;
import android.graphics.Bitmap.Config;
import android.graphics.BitmapFactory;
import android.graphics.BitmapShader;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Point;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.media.ExifInterface;
import android.text.TextUtils;

import androidx.annotation.ColorInt;
import androidx.core.graphics.drawable.DrawableCompat;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

public class BitmapUtil {

    public static final int BIT_MAX_WIDTH = 1080;
    public static final int BIT_MAX_HEIGHT = 1920;
    public static final int BIT_NOT_QUALITY = 100;

    /**
     * 计算图片的压缩比
     *
     * @param options
     * @param reqWidth
     * @param reqHeight
     * @return
     */
    public static int calculateInSampleSize(BitmapFactory.Options options,
                                            int reqWidth, int reqHeight) {
        // Raw height and width of image
        final int height = options.outHeight;
        final int width = options.outWidth;
        int inSampleSize = 1;

        if (height > reqHeight || width > reqWidth) {
            if (width > height) {
                inSampleSize = Math.round((float) height / (float) reqHeight);
            } else {
                inSampleSize = Math.round((float) width / (float) reqWidth);
            }
        }

        return inSampleSize;
    }

    /**
     * 压缩文件
     *
     * @param filePath
     * @return
     */

    public static final Point getBitmapSize(final String filePath) {
        if (TextUtils.isEmpty(filePath)) {
            return new Point(-1, -1);
        }
        BitmapFactory.Options opts = new BitmapFactory.Options();
        opts.inJustDecodeBounds = true;
        Bitmap bitmap = BitmapFactory.decodeFile(filePath, opts);
        Point point = new Point();
        point.set(opts.outWidth, opts.outHeight);
        return point;
    }

    /**
     * 压缩文件
     *
     * @param filePath
     * @return
     */

    public static final Bitmap compressFile(final String filePath) {
        if (TextUtils.isEmpty(filePath)) {
            return null;
        }
        /**
         * 解决照片旋转90度的bug
         */
        int degree = readPictureDegree(filePath);
        BitmapFactory.Options opts = new BitmapFactory.Options();
        opts.inJustDecodeBounds = true;
        Bitmap bitmap = BitmapFactory.decodeFile(filePath, opts);
        opts.inJustDecodeBounds = false;
        opts.inSampleSize = calculateInSampleSize(opts, BIT_MAX_WIDTH, BIT_MAX_HEIGHT);
        opts.inPreferredConfig = Config.ARGB_8888;
        opts.inPurgeable = true;// 同时设置才会有效
        opts.inInputShareable = true;//当系统内存不够时候图片自动被回收
        try {
            bitmap = BitmapFactory.decodeFile(filePath, opts);
        } catch (Throwable e) {
            opts.inSampleSize = opts.inSampleSize * 2 / 3;

            try {
                bitmap = BitmapFactory.decodeFile(filePath, opts);
            } catch (Throwable e2) {
                opts.inSampleSize = opts.inSampleSize / 2;

                try {
                    bitmap = BitmapFactory.decodeFile(filePath, opts);
                } catch (Throwable e3) {
                }
            }
        }
        if (degree > 0) {
            bitmap = rotaingImageView(degree, bitmap);
        }
        System.gc();
        return bitmap;
    }

    /**
     * @param srcFile 目标文件
     * @param desFile 压缩后的文件
     */
    public static boolean compressFile(String srcFile, String desFile) {
        if (TextUtils.isEmpty(srcFile)) {
            return false;
        }
        Bitmap bitmap = compressFile(srcFile);
        if (bitmap == null) {
            return false;
        }
        File file = bitmapToFile(bitmap, desFile);
        bitmap = null;
        System.gc();
        ExifUtil.copyExif(srcFile, desFile);
        bitmap = null;
        System.gc();
        return !(file == null);
    }


    public static Bitmap toCircleBitmap(Bitmap bitmap) {
        if (null == bitmap) {
            return null;
        }
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();
        if (!(width > 0 && height > 0)) {
            return null;
        }
        int ovalLen = Math.min(width, height);
        Rect src = new Rect((width - ovalLen) / 2, (height - ovalLen) / 2,
                (width - ovalLen) / 2, (height - ovalLen) / 2);
        Bitmap output = Bitmap.createBitmap(ovalLen, ovalLen, Config.ARGB_8888);
        Canvas canvas = new Canvas(output);
        Paint paint = new Paint();
        paint.setAntiAlias(true);
        bitmap = Bitmap.createBitmap(bitmap, src.left, src.top, ovalLen,
                ovalLen, null, true);
        Path path = new Path();
        path.addOval(new RectF(0, 0, ovalLen, ovalLen), Path.Direction.CW);
        BitmapShader tempShader = new BitmapShader(bitmap,
                BitmapShader.TileMode.CLAMP, BitmapShader.TileMode.CLAMP);
        paint.setShader(tempShader);
        canvas.drawPath(path, paint);
        return output;
    }

    /**
     * 把传入的bitmap转换成为一个file文件
     *
     * @param bitmap
     */
    public static File bitmapToFile(Bitmap bitmap, String filePath) {
        File file = new File(filePath);
        if (file.exists()) {
            file.delete();
            try {
                file.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        try {
            FileOutputStream fos = new FileOutputStream(file);
            boolean compress = bitmap.compress(CompressFormat.JPEG, 80, fos);
            fos.flush();
            fos.close();
            if (compress) {
                return file;
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }


    public static File bitmapToFile(Bitmap bitmap, String filePath, int quality) {
        File file = new File(filePath);
        if (file.exists()) {
            file.delete();
            try {
                file.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        try {
            FileOutputStream fos = new FileOutputStream(file);
            boolean compress = bitmap.compress(CompressFormat.JPEG, quality, fos);
            fos.flush();
            fos.close();
            if (compress) {
                return file;
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }

    /**
     * 把一个图片缩放到指定大小
     *
     * @param requireWidth
     * @param requireHeight
     * @param file
     * @return
     */
    public static File scaleFile(int requireWidth, int requireHeight, File file) {

        Bitmap bm = BitmapFactory.decodeFile(file.getAbsolutePath());
        int width = bm.getWidth();
        int height = bm.getHeight();

        float scaleHeight = (float) requireHeight / (float) height;
        float scaleWidht = (float) requireWidth / (float) width;
        Matrix matrix = new Matrix();

        matrix.postScale(scaleWidht, scaleHeight);
        Bitmap newbm = Bitmap.createBitmap(bm, 0, 0, width, height, matrix,
                true);
        return bitmapToFile(newbm, file.getAbsolutePath());

    }

    /*
     * 旋转图片
     *
     * @param angle 角度
     *
     * @param bitmap
     *
     * @return Bitmap
     */
    public static Bitmap rotaingImageView(int angle, Bitmap bitmap) {
        // 旋转图片 动作
        Matrix matrix = new Matrix();
        matrix.postRotate(angle);
        if (bitmap == null) {
            return bitmap;
        }
        // 创建新的图片
        Bitmap resizedBitmap = Bitmap.createBitmap(bitmap, 0, 0,
                bitmap.getWidth(), bitmap.getHeight(), matrix, true);
        return resizedBitmap;
    }

    /**
     * 读取图片属性：旋转的角度
     *
     * @param path 图片绝对路径
     * @return degree旋转的角度
     */
    public static int readPictureDegree(String path) {
        int degree = 0;
        try {
            ExifInterface exifInterface = new ExifInterface(path);
            int orientation = exifInterface.getAttributeInt(
                    ExifInterface.TAG_ORIENTATION,
                    ExifInterface.ORIENTATION_NORMAL);
            switch (orientation) {
                case ExifInterface.ORIENTATION_ROTATE_90:
                    degree = 90;
                    break;
                case ExifInterface.ORIENTATION_ROTATE_180:
                    degree = 180;
                    break;
                case ExifInterface.ORIENTATION_ROTATE_270:
                    degree = 270;
                    break;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return degree;
    }


    //对Drawable 进行着色,只对当前Drawable 有效果
    public static Drawable tintDrawable(Drawable drawable, ColorStateList colors) {
        final Drawable wrappedDrawable = DrawableCompat.wrap(drawable.mutate());
        DrawableCompat.setTintList(wrappedDrawable, colors);
        return wrappedDrawable;
    }


    //对Drawable 进行着色,只对当前Drawable 有效果
    public static Drawable tintDrawable(Drawable drawable, @ColorInt int colors) {
        final Drawable wrappedDrawable = DrawableCompat.wrap(drawable.mutate());
        DrawableCompat.setTintList(wrappedDrawable, ColorStateList.valueOf(colors));
        return wrappedDrawable;
    }


    //对Drawable 进行着色 对全部调用这个Drawable 的有效果
    public static Drawable tintAllDrawable(Drawable drawable, ColorStateList colors) {
        final Drawable wrappedDrawable = DrawableCompat.wrap(drawable);
        DrawableCompat.setTintList(wrappedDrawable, colors);
        return wrappedDrawable;
    }
}
