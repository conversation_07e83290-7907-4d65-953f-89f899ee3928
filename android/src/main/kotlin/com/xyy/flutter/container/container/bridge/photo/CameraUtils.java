package com.xyy.flutter.container.container.bridge.photo;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.DocumentsContract;
import android.provider.MediaStore;
import android.widget.Toast;

import androidx.core.content.FileProvider;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;


import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/4/15
 */
public class CameraUtils {

    public static final String SUFFIX_PHOTO = "xyy.png";
    public static final int REQUEST_CAMERA = 101;
    public static final int REQUEST_GALLERY = 102;
    public static final int REQUEST_GALLERY_MULTIPLE_CHOOSE = 103;
    public static final int REQUEST_CAMERA_MULTIPLE_CHOOSE = 104;

    /**
     * 打开相机
     */
    public static void openCamera(Fragment fragment) {
        FragmentActivity context = fragment.getActivity();
        String suffix = System.currentTimeMillis() + SUFFIX_PHOTO;
        File mCameraFile = getFile(suffix, context);
        if (mCameraFile != null) {
            SharedPreferences sp = context.getSharedPreferences("flutter_sp", Context.MODE_PRIVATE);
            sp.edit().putString("imgPath", mCameraFile.getAbsolutePath()).apply();
        }
        Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        try {// 尽可能调用系统相机
            String cameraPackageName = getCameraPhoneAppInfos(context);
            if (cameraPackageName == null) {
                cameraPackageName = "com.android.camera";
            }
            Intent intent_camera = context.getPackageManager()
                    .getLaunchIntentForPackage(cameraPackageName);
            if (intent_camera != null) {
                intent.setPackage(cameraPackageName);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        Uri imgUri;
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                String authority = context.getPackageName() + ".fileprovider";
                imgUri = FileProvider.getUriForFile(context, authority, mCameraFile);
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            } else {
                imgUri = Uri.fromFile(mCameraFile);
            }
            if (mCameraFile == null) {
                Toast.makeText(context, "可能该功能无法使用", Toast.LENGTH_SHORT).show();
            }
            if (mCameraFile != null && !mCameraFile.exists()) {
                Toast.makeText(context, "文件无法创建", Toast.LENGTH_SHORT).show();
            }
            if (imgUri == null) {
                Toast.makeText(context, "应用有异常发生", Toast.LENGTH_SHORT).show();
            }
            intent.putExtra(MediaStore.EXTRA_OUTPUT, imgUri);
        } catch (Exception e) {
            e.printStackTrace();
        }
        fragment.startActivityForResult(intent, REQUEST_CAMERA);
    }


    /**
     * 获取照片存储目录
     */
    private static File getFile(String photoName, Activity activity) {
        String directory;
        if (Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState())) {
            directory = Environment.getExternalStorageDirectory().getAbsolutePath() + File.separator
                    + "DCIM" + File.separator + "camera" + File.separator;
        } else {
            directory = activity.getCacheDir().getAbsolutePath() + File.separator + "pics" + File.separator;
        }
        File dir = new File(directory);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        File file = new File(directory + photoName);
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
                Toast.makeText(activity, "文件没有创建成功", Toast.LENGTH_SHORT).show();
                return null;
            }
        }
        return file;
    }

    /**
     * 获取手机系统相机
     *
     * @param context
     * @return
     */
    private static String getCameraPhoneAppInfos(Activity context) {
        try {
            String strCamera = "";
            List<PackageInfo> packages = context.getPackageManager()
                    .getInstalledPackages(0);
            for (int i = 0; i < packages.size(); i++) {
                try {
                    PackageInfo packageInfo = packages.get(i);
                    String strLabel = packageInfo.applicationInfo.loadLabel(
                            context.getPackageManager()).toString();
                    // 一般手机系统中拍照软件的名字
                    if ("相机,照相机,照相,拍照,摄像,Camera,camera".contains(strLabel)) {
                        strCamera = packageInfo.packageName;
                        if ((packageInfo.applicationInfo.flags & ApplicationInfo.FLAG_SYSTEM) != 0) {
                            break;
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (strCamera != null) {
                return strCamera;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static File handleImageOn19(Uri uri, Activity activity) {
        String imagePath = null;
        if (DocumentsContract.isDocumentUri(activity, uri)) {
            //如果是document类型的uri，通过document id处理
            String docId = DocumentsContract.getDocumentId(uri);
            if (uri.getAuthority().equals("com.android.providers.media.documents")) {
                String id = docId.split(":")[1];//解析出数字格式的id
                String selection = MediaStore.Images.Media._ID + "=" + id;
                imagePath = getImagePath(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, selection, activity);
            }
        } else if ("content".equalsIgnoreCase(uri.getScheme())) {
            //如果是content类型的uri，则使用普通方式处理
            imagePath = getImagePath(uri, null, activity);
        } else if ("file".equalsIgnoreCase(uri.getScheme())) {
            //如果是file类型的uri，直接获取图片路径
            imagePath = uri.getPath();
        }
        return new File(imagePath);
    }

    private static String getImagePath(Uri uri, String selection, Activity activity) {
        String path = null;
        //通过Uri和selection来获取真实的图片路径
        Cursor cursor = activity.getContentResolver().query(uri, null, selection, null, null);
        if (cursor != null) {
            if (cursor.moveToFirst()) {
                path = cursor.getString(cursor.getColumnIndex(MediaStore.Images.Media.DATA));
            }
            cursor.close();
        }
        return path;
    }
}
