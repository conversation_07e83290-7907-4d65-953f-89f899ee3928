package com.xyy.flutter.container.container.bridge

import android.content.Context
import androidx.fragment.app.FragmentActivity
import com.xyy.flutter.container.container.bridge.callback.LocationCallback
import com.xyy.flutter.container.container.bridge.callback.RequestCallback
import com.xyy.flutter.container.container.bridge.callback.UploadCallback
import com.xyy.flutter.container.container.ui.FlutterRunnerActivity

interface IBridgeImpl {
    fun locate(context: Context, callback: LocationCallback)

    fun toast(context: Context, msg: String?, type: String?)

    fun uploadImage(activity: FragmentActivity, uploadUrl: String, localPaths: List<String>,
                    limitWidth: Int, limitHeight: Int, param: UploadCallback, extraParams: Map<String, Any?>, isUploadOrigin: Boolean = false)

    fun request(activity: Context, method: String, path: String, contentType: String,
                requestParams: Any?, headerMap: Map<String, String>, param: RequestCallback)

    fun handleError(activity: Context, errorDetail: String?)
}
