apply plugin: 'com.android.library'

android {
    compileSdkVersion  rootProject.ext.android.compileSdkVersion
    buildToolsVersion rootProject.ext.android.buildToolsVersion
    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        resConfigs rootProject.ext.android.resConfigs
        versionCode 1
        versionName "1.0.0"
    }
    signingConfigs {
        ybm {
            keyAlias '小药药'
            keyPassword 'ybmmarket20'
            storeFile file('ybm.jks')
            storePassword 'ybmmarket20'
        }
    }
    buildTypes {
        release {//线上版本
            minifyEnabled false
            proguardFiles 'proguard-rules.pro'
            signingConfig signingConfigs.ybm
            debuggable false
            jniDebuggable false
        }
        debug {//开发版本
            minifyEnabled false
            proguardFiles 'proguard-rules.pro'
            signingConfig signingConfigs.ybm
            debuggable true
            jniDebuggable true
        }
    }
    lintOptions {
        abortOnError Boolean.valueOf(modulesLintAbortOnError)
        ignoreWarnings Boolean.valueOf(modulesLintIgnoreWarnings)
        if (Boolean.valueOf(modulesLintBaseLineEnable)) {
            baseline file("lint-baseline.xml")
        }
        lintConfig file("$rootDir/lint.xml")
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    //极光
//    implementation 'cn.jiguang.sdk:jpush:4.3.0'
//    implementation 'cn.jiguang.sdk:jcore:2.9.0'
    implementation 'cn.jiguang.sdk:jpush:5.4.0'
    implementation project(':YBMBaseLib')
}
