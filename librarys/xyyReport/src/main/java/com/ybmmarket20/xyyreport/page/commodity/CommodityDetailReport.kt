package com.ybmmarket20.xyyreport.page.commodity

import android.content.Context
import com.ybmmarket20.report.ReportActionSearchProductButtonClickBean
import com.ybmmarket20.report.ReportActionSubModuleClickBean
import com.ybmmarket20.report.ReportActionSubModuleGoodsClickBean
import com.ybmmarket20.report.ReportPageExposureBean
import com.ybmmarket20.report.ReportPageSubModuleGoodsExposureBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.page.common.addCart.AddCartPopupWindowReport
import com.ybmmarket20.xyyreport.session.SessionManager
import com.ybmmarket20.xyyreport.spm.ScmBean
import com.ybmmarket20.xyyreport.spm.SpmUtil
import com.ybmmarket20.xyyreport.spm.XyyReportActivity

/**
 * 商详埋点
 */
object CommodityDetailReport {

    @JvmStatic
    fun trackCommodityPv(context: Context, skuId: String?) {
        SpmLogUtil.print("商详-PV")
        val spm = SpmUtil.getSpmPv("productDetail_${skuId}-0_0")
        val pe = ReportPageExposureBean()
        ReportUtil.pvTrack(context, pe, spm)
    }

    @JvmStatic
    private fun bottomBtnClick(context: Context, position: Int, content: String?) {
        if (context !is XyyReportActivity) return
        val spm = context.getSpmCtn()?.newInstance()?.apply {
            spmC = "ftProdDetail@Z"
            spmD = "btn@$position"
        }
        val scm = ScmBean().apply {
            scmA = "appFE"
            scmB = "0"
            scmC = "all_0"
            scmD = "text-$content"
        }
        val click = ReportActionSubModuleClickBean()
        AddCartPopupWindowReport.addExtensionForAddCartPopupWindow(context, spm, scm, isGoods = false)
        ReportUtil.clickTrack(context, click, spm, scm)
    }

    @JvmStatic
    fun bottomBtnClickShop(context: Context, shopCode: String?) {
        SpmLogUtil.print("商详-底部按钮点击-店铺")
        bottomBtnClick(context, 1, "店铺_shop-$shopCode")
    }

    @JvmStatic
    fun bottomBtnClickCart(context: Context) {
        SpmLogUtil.print("商详-底部按钮点击-购物车")
        bottomBtnClick(context, 2, "购物车")
    }

    @JvmStatic
    fun bottomBtnClickRight(context: Context, position: Int, content: String?) {
        SpmLogUtil.print("商详-底部按钮点击-$content")
        bottomBtnClick(context, position + 2, content)
    }

    @JvmStatic
    fun trackSameGoodsSpecGoodsExposure(context: Context, position: Int, productId: Long, productName: String?) {
        if (context !is XyyReportActivity) return
        if (context.getScmCnt() == null) return
        SpmLogUtil.print("商详-同品其他规格-商品曝光")
        val spm = context.getSpmCtn()?.newInstance()?.apply {
            spmC = "spuList@2"
            spmD = "prod@${position+1}"
        }
        val scm = ScmBean().apply {
            scmA = "productBusiness"
            scmB = "0"
            scmC = "all_0"
            scmD = "prod-$productId"
            scmE = getSpecificationsScmId(context)
        }
        // 移除极光埋点 - page_list_product_exposure (保留搜索页面)
        /*
        val goodsExposure = ReportPageSubModuleGoodsExposureBean().apply {
            this.productId = productId
            this.productName = productName
        }
        ReportUtil.goodsExposureTrack(context, goodsExposure, spm, scm)
        */
    }

    @JvmStatic
    fun trackSameGoodsSpecGoodsClick(context: Context, position: Int, productId: Long, productName: String?): String? {
        if (context !is XyyReportActivity) return null
        if (context.getScmCnt() == null) return null
        SpmLogUtil.print("商详-同品其他规格-商品点击")
        val spm = context.getSpmCtn()?.newInstance()?.apply {
            spmC = "spuList@2"
            spmD = "prod@${position+1}"
        }
        val scmE = "${getSpecificationsScmId(context)}${SessionManager.get().newGoodsScmRandom()}"
        val scm = ScmBean().apply {
            scmA = "productBusiness"
            scmB = "0"
            scmC = "all_0"
            scmD = "prod-$productId"
            this.scmE = scmE
        }
        return scmE
    }

    @JvmStatic
    fun trackSameGoodsSpecGoodsBtnClick(context: Context, position: Int, productId: Long, productName: String?, content: String?) {
        if (context !is XyyReportActivity) return
        if (context.getScmCnt() == null) return
        val scmE = trackSameGoodsSpecGoodsClick(context, position, productId, productName)
        SpmLogUtil.print("商详-同品其他规格-商品按钮点击")
        val spm = context.getSpmCtn()?.newInstance()?.apply {
            spmC = "spuList@2"
            spmD = "prod@${position+1}_btn@1"
        }
        val scm = ScmBean().apply {
            scmA = "productBusiness"
            scmB = "0"
            scmC = "all_0"
            scmD = "prod-${productId}_text-$content"
            this.scmE = scmE
        }
        val goodsBtnClick = ReportActionSearchProductButtonClickBean().apply {
            this.productId = productId
            this.productName = productName
        }
        AddCartPopupWindowReport.addExtensionForAddCartPopupWindow(context, spm, scm, isGoods = true)
        ReportUtil.track(context, goodsBtnClick, spm, scm)
    }

    @JvmStatic
    fun setSameSpecificationsScmId(context: Context, scmId: String?) {
        if (context is XyyReportActivity) {
            context.putExtension(CommoditySpmConstant.EXTENSION_GOODS_LIST_SAME_SPEC_SCM_ID, scmId)
        }
    }

    @JvmStatic
    fun getSpecificationsScmId(context: Context): String? {
        return if (context is XyyReportActivity) {
            context.getExtensionValue(CommoditySpmConstant.EXTENSION_GOODS_LIST_SAME_SPEC_SCM_ID)?.toString()
        } else null
    }
}