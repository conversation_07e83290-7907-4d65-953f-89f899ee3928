<?xml version="1.0" encoding="utf-8"?>
<HorizontalScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="38dp"
    android:background="@color/white"
    android:fillViewport="true"
    android:scrollbars="none">

    <RadioGroup
        android:id="@+id/ll_timeFilter"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:visibility="visible"
        tools:visibility="visible">

        <RadioButton
            android:id="@+id/time_0"
            android:layout_width="57dp"
            android:layout_height="27dp"
            android:background="@drawable/abc_hometime_bg_new"
            android:button="@null"
            android:gravity="center"
            android:text="昨天"
            android:textColor="@drawable/home_tag_textcolor_new"
            android:textSize="13sp" />


        <RadioButton
            android:id="@+id/time_1"
            android:layout_width="57dp"
            android:layout_height="27dp"
            android:layout_marginLeft="15dp"
            android:background="@drawable/abc_hometime_bg_new"
            android:button="@null"
            android:gravity="center"
            android:checked="true"
            android:text="今天"
            android:textColor="@drawable/home_tag_textcolor_new"
            android:textSize="13sp" />


        <RadioButton
            android:id="@+id/time_2"
            android:layout_width="57dp"
            android:layout_height="27dp"
            android:layout_marginLeft="15dp"
            android:background="@drawable/abc_hometime_bg_new"
            android:button="@null"
            android:gravity="center"
            android:text="本周"
            android:textColor="@drawable/home_tag_textcolor_new"
            android:textSize="13sp" />


        <RadioButton
            android:id="@+id/time_3"
            android:layout_width="57dp"
            android:layout_height="27dp"
            android:layout_marginLeft="15dp"
            android:background="@drawable/abc_hometime_bg_new"
            android:button="@null"
            android:gravity="center"
            android:text="本月"
            android:textColor="@drawable/home_tag_textcolor_new"
            android:textSize="13sp" />

        <RadioButton
            android:id="@+id/time_5"
            android:layout_width="57dp"
            android:layout_height="27dp"
            android:layout_marginLeft="15dp"
            android:background="@drawable/abc_hometime_bg_new"
            android:button="@null"
            android:gravity="center"
            android:text="上周"
            android:textColor="@drawable/home_tag_textcolor_new"
            android:textSize="13sp" />

        <RadioButton
            android:id="@+id/time_6"
            android:layout_width="57dp"
            android:layout_height="27dp"
            android:layout_marginLeft="15dp"
            android:background="@drawable/abc_hometime_bg_new"
            android:button="@null"
            android:gravity="center"
            android:text="上月"
            android:textColor="@drawable/home_tag_textcolor_new"
            android:textSize="13sp" />


        <RadioButton
            android:id="@+id/time_4"
            android:layout_width="57dp"
            android:layout_height="27dp"
            android:layout_marginLeft="15dp"
            android:background="@drawable/abc_hometime_bg_new"
            android:button="@null"
            android:gravity="center"
            android:text="全年"
            android:textColor="@drawable/home_tag_textcolor_new"
            android:textSize="13sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="27dp"
            android:visibility="gone"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:layout_marginLeft="15dp"
            android:background="@drawable/abc_hometime_bg_new"
            android:button="@null"
            android:gravity="center"
            android:text="自定义查询"
            android:textColor="@drawable/home_tag_textcolor_new"
            android:textSize="13sp"/>
    </RadioGroup>
</HorizontalScrollView>