<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:id="@+id/root_constraintLayout"
    android:layout_height="match_parent"
    android:background="#B3000000">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.6326836582" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.7326836582" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_pop_bottom"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="@+id/guideline">

        <com.xyy.common.widget.RoundLinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:gravity="bottom"
            android:orientation="horizontal"
            android:paddingBottom="35dp"
            app:layout_constraintBottom_toTopOf="@+id/btn_cancel"
            app:layout_constraintTop_toTopOf="parent"
            app:rv_backgroundColor="#F4F4F4">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/btn_share_local"
                    android:layout_width="@dimen/share_icon_size"
                    android:layout_height="@dimen/share_icon_size"
                    android:layout_gravity="center_horizontal"
                    android:src="@drawable/ic_share_down" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:text="保存到相册"
                    android:textColor="@color/text_color_666666"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="25dp"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/btn_share_wechat"
                    android:layout_width="@dimen/share_icon_size"
                    android:layout_height="@dimen/share_icon_size"
                    android:layout_gravity="center_horizontal"
                    android:src="@drawable/ic_share_wechat" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:text="微信"
                    android:textColor="@color/text_color_666666"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="25dp"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/btn_share_wechat_moment"
                    android:layout_width="@dimen/share_icon_size"
                    android:layout_height="@dimen/share_icon_size"
                    android:src="@drawable/ic_share_moment" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:text="朋友圈"
                    android:textColor="@color/text_color_666666"
                    android:textSize="14sp" />
            </LinearLayout>

        </com.xyy.common.widget.RoundLinearLayout>

        <TextView
            android:id="@+id/btn_cancel"
            android:layout_width="0dp"
            android:layout_height="49dp"
            android:background="@color/white"
            android:elevation="1dp"
            android:gravity="center"
            android:text="取消"
            android:textColor="@color/text_color_333333"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.xyy.common.widget.RoundedImageView
        android:id="@+id/iv_share_img"
        android:layout_width="250dp"
        android:layout_height="0dp"
        android:layout_gravity="center"
        android:scaleType="fitXY"
        app:layout_constraintBottom_toTopOf="@+id/guideline1"
        app:layout_constraintDimensionRatio="125:183"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:riv_corner_radius="8dp" />
</androidx.constraintlayout.widget.ConstraintLayout>