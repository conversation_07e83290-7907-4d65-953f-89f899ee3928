<?xml version="1.0" encoding="utf-8"?>
<com.xyy.common.widget.statusview.StatusViewLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/status_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_gray_EFEFF4">

    <com.xyy.common.widget.RoundConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="10dp"
        android:paddingTop="15dp"
        android:paddingBottom="15dp"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="7dp">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_begin="10dp" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.4" />

        <TextView
            android:id="@+id/tv_merchantName_key"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="客户名称"
            android:textColor="@color/text_color_FF8E8E93"
            android:textSize="15sp"
            app:layout_constraintLeft_toLeftOf="@id/guideline1"
            app:layout_constraintRight_toLeftOf="@id/guideline2"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_merchantName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/text_color_333333"
            android:textSize="15sp"
            app:layout_constraintLeft_toRightOf="@id/guideline2"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_merchantName_key"
            tools:text="光谷大药房光谷大药房光谷大药房光谷大药房光谷大药房光谷大药房光谷大药房" />

        <TextView
            android:id="@+id/tv_invoiceOriginalType_key"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:text="原发票类型"
            android:textColor="@color/text_color_FF8E8E93"
            android:textSize="15sp"
            app:layout_constraintLeft_toLeftOf="@id/guideline1"
            app:layout_constraintRight_toLeftOf="@id/guideline2"
            app:layout_constraintTop_toBottomOf="@id/tv_merchantName" />

        <TextView
            android:id="@+id/tv_invoiceOriginalType"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/text_color_333333"
            android:textSize="15sp"
            app:layout_constraintLeft_toRightOf="@id/guideline2"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_invoiceOriginalType_key"
            tools:text="电子普通发票" />

        <TextView
            android:id="@+id/tv_invoiceApplyType_key"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:text="申请发票类型"
            android:textColor="@color/text_color_FF8E8E93"
            android:textSize="15sp"
            app:layout_constraintLeft_toLeftOf="@id/guideline1"
            app:layout_constraintRight_toLeftOf="@id/guideline2"
            app:layout_constraintTop_toBottomOf="@id/tv_invoiceOriginalType" />

        <TextView
            android:id="@+id/tv_invoiceApplyType"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/text_color_333333"
            android:textSize="15sp"
            app:layout_constraintLeft_toRightOf="@id/guideline2"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_invoiceApplyType_key"
            tools:text="纸质普通发票" />

        <TextView
            android:id="@+id/tv_submitTime_key"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:text="提交时间"
            android:textColor="@color/text_color_FF8E8E93"
            android:textSize="15sp"
            app:layout_constraintLeft_toLeftOf="@id/guideline1"
            app:layout_constraintRight_toLeftOf="@id/guideline2"
            app:layout_constraintTop_toBottomOf="@id/tv_invoiceApplyType" />

        <TextView
            android:id="@+id/tv_submitTime"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/text_color_333333"
            android:textSize="15sp"
            app:layout_constraintLeft_toRightOf="@id/guideline2"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_submitTime_key"
            tools:text="2019-07-08 13:10:10" />

        <TextView
            android:id="@+id/tv_auditTime_key"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:text="审核时间"
            android:textColor="@color/text_color_FF8E8E93"
            android:textSize="15sp"
            app:layout_constraintLeft_toLeftOf="@id/guideline1"
            app:layout_constraintRight_toLeftOf="@id/guideline2"
            app:layout_constraintTop_toBottomOf="@id/tv_submitTime" />

        <TextView
            android:id="@+id/tv_auditTime"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/text_color_333333"
            android:textSize="15sp"
            app:layout_constraintLeft_toRightOf="@id/guideline2"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_auditTime_key"
            tools:text="2019-07-08 13:10:10" />

        <TextView
            android:id="@+id/tv_auditStatus_key"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:text="审核状态"
            android:textColor="@color/text_color_FF8E8E93"
            android:textSize="15sp"
            app:layout_constraintLeft_toLeftOf="@id/guideline1"
            app:layout_constraintRight_toLeftOf="@id/guideline2"
            app:layout_constraintTop_toBottomOf="@id/tv_auditTime" />

        <TextView
            android:id="@+id/tv_auditStatus"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/text_color_333333"
            android:textSize="15sp"
            app:layout_constraintLeft_toRightOf="@id/guideline2"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_auditStatus_key"
            tools:text="通过" />

        <TextView
            android:id="@+id/tv_remark_key"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:text="备注"
            android:textColor="@color/text_color_FF8E8E93"
            android:textSize="15sp"
            app:layout_constraintLeft_toLeftOf="@id/guideline1"
            app:layout_constraintRight_toLeftOf="@id/guideline2"
            app:layout_constraintTop_toBottomOf="@id/tv_auditStatus" />

        <TextView
            android:id="@+id/tv_remark"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/text_color_333333"
            android:textSize="15sp"
            app:layout_constraintLeft_toRightOf="@id/guideline2"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_remark_key"
            tools:text="备注备注" />

    </com.xyy.common.widget.RoundConstraintLayout>

</com.xyy.common.widget.statusview.StatusViewLayout>