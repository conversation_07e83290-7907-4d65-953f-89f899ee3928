<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:layout_width="90dp"
        android:layout_height="90dp"
        android:layout_centerInParent="true">

        <ImageView
            android:id="@+id/fiv"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginTop="10dp"
            android:contentDescription="@null"
            android:scaleType="centerCrop"
            android:src="@color/color_f6f6f6"/>

        <LinearLayout
            android:id="@+id/ll_del"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:gravity="end"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_del"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:contentDescription="@null"
                android:scaleType="fitXY"
                android:src="@drawable/icon_correction_delete" />

        </LinearLayout>

    </RelativeLayout>

</RelativeLayout>