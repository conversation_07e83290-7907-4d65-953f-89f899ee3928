<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/match_wrap"
    android:background="@color/background_gray">

    <TextView
        android:id="@+id/tv_msg_time"
        style="@style/msg_style_header_time"
        tools:text="ddd" />

    <RelativeLayout
        style="@style/msg_style_main"
        android:layout_below="@id/tv_msg_time">

        <RelativeLayout
            android:id="@+id/rl_msg_type"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:background="@drawable/bg_rect_round_top_gray">

            <TextView
                android:id="@+id/tv_msg_type"
                style="@style/msg_style_title"
                android:layout_centerVertical="true"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                tools:text="ddd" />

            <ImageView
                android:id="@+id/iv_auditing_read"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="11dp"
                android:src="@drawable/shape_dot_red" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/rl_msg_type"
            android:minHeight="60dp"
            android:paddingLeft="@dimen/dp10"
            android:paddingTop="9dp"
            android:paddingBottom="9dp">

            <TextView
                android:id="@+id/tv_msg_share_content"
                android:layout_width="match_parent"
                android:layout_height="42dp"
                android:gravity="center_vertical"
                android:layout_alignParentTop="true"
                android:layout_marginRight="@dimen/dp15"
                android:layout_toLeftOf="@id/iv_auditing_arrow"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="@color/text_color_666666"
                android:textSize="@dimen/dp15"
                tools:text="XXX给您分配了客户，快去跟进吧！字符长啊啊啊" />

            <ImageView
                android:id="@+id/iv_auditing_arrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="11dp"
                android:src="@drawable/zzlb_jiantou" />
        </RelativeLayout>
    </RelativeLayout>


</RelativeLayout>