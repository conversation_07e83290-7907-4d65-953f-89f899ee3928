<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/main_white"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/im_debug"
        android:layout_width="185dp"
        android:layout_height="142dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="95dp"
        android:src="@drawable/icon_about_bg" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical">


            <com.xyy.common.widget.RoundTextView
                android:id="@+id/tv_clear_cache"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="8dp"
                android:paddingLeft="@dimen/dp20"
                android:paddingTop="3dp"
                android:paddingRight="@dimen/dp20"
                android:paddingBottom="3dp"
                android:text="清除缓存"
                android:textColor="@color/text_color_666666"
                android:textSize="14sp"
                android:visibility="visible"
                app:rv_cornerRadius="@dimen/dp5"
                app:rv_strokeColor="@color/text_color_666666"
                app:rv_strokeWidth="@dimen/dp1" />

            <com.xyy.common.widget.RoundTextView
                android:id="@+id/tv_status_padding"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="15dp"
                android:paddingLeft="@dimen/dp20"
                android:paddingTop="3dp"
                android:paddingRight="@dimen/dp20"
                android:paddingBottom="3dp"
                android:text="打开状态栏适配（慎点）"
                android:textColor="@color/text_color_666666"
                android:textSize="14sp"
                android:visibility="visible"
                app:rv_cornerRadius="@dimen/dp5"
                app:rv_strokeColor="@color/text_color_666666"
                app:rv_strokeWidth="@dimen/dp1"
                tools:visibility="visible" />

            <com.xyy.common.widget.RoundTextView
                android:id="@+id/tv_ip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="15dp"
                android:paddingLeft="@dimen/dp20"
                android:paddingTop="3dp"
                android:paddingRight="@dimen/dp20"
                android:paddingBottom="3dp"
                android:text="配置IP"
                android:textColor="@color/text_color_666666"
                android:textSize="14sp"
                android:visibility="gone"
                app:rv_cornerRadius="@dimen/dp5"
                app:rv_strokeColor="@color/text_color_666666"
                app:rv_strokeWidth="@dimen/dp1"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_http"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/tv_ip"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="15dp"
                android:textColor="@color/text_color_666666"
                android:visibility="gone"
                tools:text="https://crm.test.ybm100.com" />

        </LinearLayout>

        <TextView
            android:id="@+id/about_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@id/about_phone"
            android:layout_centerHorizontal="true"
            android:gravity="center_horizontal"
            android:paddingTop="20dp"
            android:paddingBottom="8dp"
            android:text="版本号:V1.1.0"
            android:textColor="@color/main_black"
            android:textSize="@dimen/sp14" />

        <ImageView
            android:id="@+id/iv_about_update"
            android:layout_width="5dp"
            android:layout_height="5dp"
            android:layout_alignTop="@id/about_version"
            android:layout_alignEnd="@id/about_version"
            android:layout_marginTop="22dp"
            android:layout_marginEnd="-5dp"
            android:src="@drawable/shape_dot_red"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/about_phone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@id/copyright"
            android:layout_marginBottom="26dp"
            android:gravity="center_horizontal"
            android:text="服务热线:027-83229881"
            android:textColor="@color/main_black"
            android:textSize="@dimen/sp14" />

        <TextView
            android:id="@+id/about_icp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@id/copyright"
            android:layout_marginBottom="0dp"
            android:gravity="center_horizontal"
            android:text="备案号:京ICP备2022016495号-7A"
            android:textColor="@color/main_black"
            android:textSize="@dimen/sp14" />

        <ImageView
            android:id="@+id/copyright"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="18dp"
            android:src="@drawable/icon_copyright" />
    </RelativeLayout>
</LinearLayout>