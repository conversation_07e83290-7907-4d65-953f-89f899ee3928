<?xml version="1.0" encoding="utf-8"?>
<com.xyy.common.widget.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_notice"
    android:layout_width="match_parent"
    android:layout_height="34dp"
    android:layout_marginStart="15dp"
    android:layout_marginEnd="15dp"
    android:layout_marginBottom="10dp"
    android:paddingStart="10dp"
    android:paddingEnd="10dp"

    app:rv_backgroundColor="@android:color/white"
    app:rv_cornerRadius="2dp"
    app:rv_strokeColor="@android:color/white"
    app:rv_strokeWidth="0.5dp"

    android:visibility="invisible">

    <ImageView
        android:id="@+id/iv_notice"
        android:layout_width="30dp"
        android:layout_height="match_parent"
        android:src="@drawable/iv_notice"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.ybm100.app.crm.widget.marquee.MarqueeView
        android:id="@+id/marqueeView"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintEnd_toStartOf="@+id/tv_dismiss"
        app:layout_constraintStart_toEndOf="@+id/iv_notice"
        app:mvGravity="left"
        app:mvInterval="3000"
        app:mvSingleLine="true"
        app:mvTextColor="@color/color_F96A0E"
        app:mvTextSize="14sp" />

    <TextView
        android:id="@+id/tv_dismiss"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="忽略"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</com.xyy.common.widget.RoundConstraintLayout>