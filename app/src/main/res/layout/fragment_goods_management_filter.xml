<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:clickable="true">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nsv_scrollview"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/rtv_reset"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">


            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="15dp"
                android:paddingTop="33dp"
                android:paddingBottom="15dp"
                android:text="商品范围"
                android:textColor="@color/color_292933"
                android:textSize="16sp" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="5dp"
                app:layout_constraintBottom_toTopOf="@+id/rtv_reset"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_title" />

            <CheckBox
                android:id="@+id/cb_folding"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="5dp"
                android:button="@null"
                android:checked="false"
                android:drawableRight="@drawable/selector_goods_filter_folding"
                android:drawablePadding="4dp"
                android:text="更多地区"
                android:textColor="#FF9494A6"
                android:textSize="12sp" />


            <TextView
                android:id="@+id/tv_zone_list_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="15dp"
                android:paddingTop="30dp"
                android:paddingBottom="15dp"
                android:text="所在专区"
                android:textColor="@color/color_292933"
                android:textSize="16sp"
                android:visibility="gone"
                tools:visibility="visible" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_zone_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="28dp"
                android:orientation="horizontal"
                android:paddingLeft="5dp"
                android:paddingRight="15dp"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_zone_list_label"
                tools:visibility="visible" />


        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <com.xyy.common.widget.RoundTextView
        android:id="@+id/rtv_reset"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:gravity="center"
        android:text="重置"
        android:textColor="@color/color_292933"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/rtv_confirm"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent"
        app:rv_strokeColor="#f7f7f8"
        app:rv_strokeWidth="1px" />

    <com.xyy.common.widget.RoundTextView
        android:id="@+id/rtv_confirm"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:gravity="center"
        android:text="确定"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/rtv_reset"
        app:rv_backgroundColor="@color/color_00B377" />


</androidx.constraintlayout.widget.ConstraintLayout>