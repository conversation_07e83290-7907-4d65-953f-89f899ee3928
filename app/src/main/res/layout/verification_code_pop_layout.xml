<?xml version="1.0" encoding="utf-8"?>
<com.ybmmarket20.common.widget.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="270dp"
    android:layout_height="wrap_content"
    app:rv_backgroundColor="@color/white"
    app:rv_cornerRadius="2dp">

    <ImageView
        android:id="@+id/btn_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:layout_marginRight="5dp"
        android:padding="5dp"
        android:src="@drawable/close_icon1"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="27dp"
        android:text="企业被委托人身份信息认证"
        android:textColor="#292933"
        android:textSize="16sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_describe"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:paddingLeft="23dp"
        android:paddingRight="23dp"
        android:textColor="#9494A6"
        android:textSize="14sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        tools:text="为了确保配送药品的安全，请输入手机号132****1234收到的短信验证码" />

    <com.ybmmarket20.view.VerificationView
        android:id="@+id/verificationView"
        android:layout_width="0dp"
        android:layout_height="42dp"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="26dp"
        android:layout_marginRight="30dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_describe"
        app:vAutoShowInputBoard="false"
        app:vBackgroundResource="@drawable/bac_square_selector"
        app:vCursorDrawable="@color/color_00B377"
        app:vTextColor="@color/color_292933"
        app:vTextCount="4"
        app:vTextSize="18sp" />

    <TextView
        android:id="@+id/tv_countdown"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="7dp"
        android:padding="8dp"
        android:text="重新发送(112S)"
        android:textColor="@color/color_676773"
        android:textSize="12sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/verificationView" />

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/btn_cancel"
        android:layout_width="0dp"
        android:layout_height="36dp"
        android:layout_marginLeft="23dp"
        android:layout_marginTop="12dp"
        android:layout_marginRight="15dp"
        android:layout_marginBottom="20dp"
        android:gravity="center"
        android:text="更换手机号"
        android:textColor="@color/color_676773"
        android:textSize="14sp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/btn_confirm"
        app:layout_constraintTop_toBottomOf="@+id/tv_countdown"
        app:rv_cornerRadius="2dp"
        app:rv_strokeColor="@color/color_9494A6"
        app:rv_strokeWidth="1dp" />

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/btn_confirm"
        android:layout_width="0dp"
        android:layout_height="36dp"
        android:layout_marginRight="23dp"
        android:enabled="false"
        android:gravity="center"
        android:text="确认"
        android:textColor="@color/white"
        android:textSize="14sp"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintLeft_toRightOf="@+id/btn_cancel"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/btn_cancel"
        app:rv_backgroundColor="@color/color_A9AEB7"
        app:rv_cornerRadius="2dp" />

</com.ybmmarket20.common.widget.RoundConstraintLayout>
