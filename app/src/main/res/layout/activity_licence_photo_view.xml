<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <ImageButton
        android:id="@+id/iv_close"
        android:layout_width="46dp"
        android:layout_height="44dp"
        android:background="@color/transparent"
        android:padding="@dimen/dp12"
        android:src="@drawable/ic_black_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/layout_title"
        android:layout_width="0dp"
        android:layout_height="44dp"
        android:layout_marginEnd="@dimen/dp46"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_close"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="44dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxEms="7"
            android:singleLine="true"
            android:textColor="@color/color_292933"
            android:textSize="17sp"
            tools:text="" />

        <TextView
            android:id="@+id/tv_title_num"
            android:layout_width="wrap_content"
            android:layout_height="44dp"
            android:gravity="center"
            android:textColor="@color/color_292933"
            android:textSize="17sp"
            tools:text="（1/3）" />
    </LinearLayout>

    <com.ybm100.app.crm.ui.activity.CustomerViewpager
        android:id="@+id/viewPage"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_title" />

    <LinearLayout
        android:id="@+id/layout_save"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:alpha="0.4"
        android:background="@color/color_000000"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableLeft="@drawable/ic_download"
            android:text="保存到相册"
            android:textColor="@color/white"
            android:textSize="14sp" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>