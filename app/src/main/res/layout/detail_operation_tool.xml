<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/detail_operation_tool_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="47dp"
        android:background="@drawable/product_text_bg_1dp"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/ll_bottom_btn"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/ll_share"
                android:layout_width="60dp"
                android:layout_height="match_parent"
                android:gravity="center"
                android:visibility="gone"
                tools:visibility="visible"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/icon_goods_detail_share" />

                <TextView
                    android:id="@+id/tv_share"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="1dp"
                    android:text="分享"
                    android:textColor="@color/color_292933"
                    android:textSize="@dimen/detail_tv_dimen_10sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_on_line_service"
                android:layout_width="60dp"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/icon_service_detail" />

                <TextView
                    android:id="@+id/tv_customer_service"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="1dp"
                    android:text="客服"
                    android:textColor="@color/color_292933"
                    android:textSize="@dimen/detail_tv_dimen_10sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/detail_ll_inventory"
                android:layout_width="60dp"
                android:layout_height="match_parent"
                android:gravity="center"
                android:visibility="gone"
                android:orientation="vertical">

                <CheckBox
                    android:id="@+id/inventory_cb"
                    style="@style/inventoryCheckboxTheme"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="false" />

                <TextView
                    android:id="@+id/tv_inventory"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="1dp"
                    android:text="加常购"
                    android:textColor="@color/selector_text_color_292933"
                    android:textSize="@dimen/detail_tv_dimen_10sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/detail_ll_self_shop"
                android:layout_width="60dp"
                android:layout_height="match_parent"
                android:gravity="center"
                tools:visibility="visible"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:id="@+id/tv_self_shop_bottom_open"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableTop="@drawable/icon_self_shop"
                    android:drawablePadding="2dp"
                    android:gravity="center"
                    android:text="@string/str_commodity_fragment_bottom_shop_open"
                    android:textColor="@color/selector_text_color_292933"
                    android:textSize="@dimen/detail_tv_dimen_10sp" />
            </LinearLayout>

            <FrameLayout
                android:id="@+id/ll_detail_fl"
                android:layout_width="60dp"
                android:layout_height="match_parent">

                <LinearLayout
                    android:id="@+id/detail_ll_cart"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/icon_product_detail_cart" />

                    <TextView
                        style="@style/HomeTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="1dp"
                        android:text="购物车"
                        android:textColor="@color/color_292933"
                        android:textSize="@dimen/detail_tv_dimen_10sp" />
                </LinearLayout>

                <com.ybmmarket20.common.widget.RoundRelativeLayout
                    android:id="@+id/ll_detail_rl"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="right"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="4dp"
                    android:layout_marginRight="10dp"
                    android:gravity="center"
                    android:minWidth="18dp"
                    android:minHeight="18dp"
                    android:visibility="invisible"
                    app:rv_backgroundColor="@color/record_red"
                    app:rv_cornerRadius="100dp"
                    tools:visibility="visible">
                    <!--main_cart_bg-->
                    <TextView
                        android:id="@+id/ll_detail_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=""
                        android:textColor="@color/white"
                        android:textSize="10dp"
                        tools:text="" />

                </com.ybmmarket20.common.widget.RoundRelativeLayout>
            </FrameLayout>

        </LinearLayout>

        <RelativeLayout
            android:id="@+id/rl_detail_parent"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <RelativeLayout
                android:id="@+id/rl_detail"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_alignParentRight="true"
                tools:visibility="visible">

                <!--加购按钮-->
                <com.ybmmarket20.view.ProductEditLayout3
                    android:id="@+id/el_edit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="10dp"
                    android:visibility="visible"
                    tools:visibility="gone" />

                <!--到货提醒-->
                <LinearLayout
                    android:id="@+id/ll_remind"
                    android:layout_width="120dp"
                    android:layout_height="match_parent"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:background="@color/detail_tv_00B377"
                    android:gravity="center"
                    tools:visibility="gone"
                    android:visibility="gone">

                    <TextView
                        android:id="@+id/add_remind"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_centerVertical="true"
                        android:gravity="center"
                        android:textColor="@color/white"
                        android:textSize="@dimen/detail_btn"
                        android:visibility="gone"
                        tools:text="到货提醒"
                        tools:visibility="gone" />
                </LinearLayout>

                <!--无法加购-->
                <TextView
                    android:id="@+id/add_cart"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:background="@color/color_A9AEB7"
                    android:clickable="true"
                    android:gravity="center"
                    android:text="无法加购"
                    android:textColor="@color/white"
                    android:textSize="@dimen/detail_btn"
                    android:visibility="gone" />

                <!--去拼团-->
                <TextView
                    android:id="@+id/add_spell_group"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:background="@color/base_colors"
                    android:gravity="center"
                    android:textColor="@color/white"
                    android:textSize="@dimen/detail_btn"
                    android:visibility="gone"
                    tools:visibility="gone"
                    tools:text="¥23.50去拼团" />

                <!--秒杀即将开抢-->
                <TextView
                    android:id="@+id/tv_seckill_pre"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:background="@color/base_colors"
                    android:gravity="center"
                    android:textColor="@color/white"
                    android:textSize="@dimen/detail_btn"
                    android:visibility="gone"
                    tools:visibility="gone"
                    tools:text="即将开抢" />

            </RelativeLayout>
        </RelativeLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_audit_no_passed"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:visibility="gone"
            tools:visibility="gone">

            <TextView
                android:id="@+id/tv_audit_no_passed"
                android:layout_width="@dimen/dimen_dp_120"
                android:layout_height="match_parent"
                android:background="@color/color_00b377"
                android:gravity="center"
                android:textColor="@color/white"
                android:textSize="@dimen/dimen_dp_16"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="资质认证" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>

</LinearLayout>