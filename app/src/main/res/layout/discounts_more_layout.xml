<?xml version="1.0" encoding="utf-8"?>
<com.xyy.common.widget.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:rv_backgroundColor="@color/white"
    app:rv_cornerRadius_TL="2dp"
    app:rv_cornerRadius_TR="2dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="14dp"
        android:text="更多信息"
        android:textColor="@color/color_292933"
        android:textSize="16sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:layout_marginRight="6dp"
        android:gravity="center_vertical"
        android:src="@drawable/ic_close"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_title" />

    <View
        android:id="@+id/line"
        style="@style/divider_line_style"
        android:layout_marginTop="14dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />

    <FrameLayout
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="240dp"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/line" />

</com.xyy.common.widget.RoundConstraintLayout>
