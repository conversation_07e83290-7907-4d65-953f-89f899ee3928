<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:paddingLeft="20dp">

        <TextView
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:paddingTop="@dimen/dp15"
            android:paddingBottom="@dimen/dp15"
            android:text="请选择所在地区"
            android:textColor="@color/color_292933"
            android:textSize="17sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/dp20"
            android:paddingTop="@dimen/dp15"
            android:paddingEnd="@dimen/dp20"
            android:paddingBottom="@dimen/dp15"
            android:src="@drawable/ddsx_close" />
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_group"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp35"
        android:layout_marginTop="20dp"
        android:background="@color/white"
        android:paddingLeft="20dp"
        android:paddingRight="20dp" />

    <FrameLayout
        android:id="@+id/fl_content"
        android:layout_width="match_parent"
        android:layout_height="337dp"
        android:maxHeight="337dp"
        android:layout_marginTop="@dimen/dp20"
        android:layout_weight="1"
        android:background="@color/white" />
</LinearLayout>