<?xml version="1.0" encoding="utf-8"?>
<com.xyy.common.widget.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="30dp"
    android:layout_marginStart="15dp"
    android:layout_marginTop="15dp"
    android:paddingStart="8dp"
    app:rv_backgroundColor="#f7f7f8"
    app:rv_cornerRadius="1dp">

    <TextView
        android:id="@+id/tv_tagName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:textColor="@color/text_color_333333"
        android:textSize="13sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="dadasa" />

    <ImageButton
        android:id="@+id/iv_delete"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:background="@null"
        android:paddingStart="5dp"
        android:paddingEnd="8dp"
        android:src="@drawable/tag_close"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@id/tv_tagName"
        app:layout_constraintTop_toTopOf="parent" />
</com.xyy.common.widget.RoundConstraintLayout>