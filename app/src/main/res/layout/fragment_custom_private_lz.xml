<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tools:visibility="visible">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:background="@color/white">

            <View
                style="@style/cut_off_line_style"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_sort"
                android:layout_width="wrap_content"
                android:layout_height="42dp"
                android:drawableEnd="@drawable/ic_green_down"
                android:drawablePadding="@dimen/dp4"
                android:gravity="center_vertical"
                android:paddingLeft="@dimen/dp28"
                android:paddingRight="@dimen/dp10"
                android:text="综合排序"
                android:textColor="@color/text_color_35C561"
                android:textSize="14sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_customer_type"
                android:layout_width="wrap_content"
                android:layout_height="42dp"
                android:drawableRight="@drawable/ic_gray_down"
                android:drawablePadding="@dimen/dp4"
                android:gravity="center"
                android:paddingStart="@dimen/dp10"
                android:paddingEnd="@dimen/dp10"
                android:text="客户类型"
                android:textColor="@color/text_color_666666"
                android:textSize="14sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_customer_status"
                android:layout_width="wrap_content"
                android:layout_height="42dp"
                android:drawableRight="@drawable/ic_gray_down"
                android:drawablePadding="@dimen/dp4"
                android:gravity="center"
                android:paddingStart="@dimen/dp10"
                android:paddingEnd="@dimen/dp20"
                android:text="签约状态"
                android:textColor="@color/text_color_666666"
                android:textSize="14sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                style="@style/cut_off_line_style"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:id="@+id/refresh_layout_customer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <com.xyy.common.widget.statusview.StatusViewLayout
            android:id="@+id/svl_contact_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_customer_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/color_gray_EFEFF4" />
        </com.xyy.common.widget.statusview.StatusViewLayout>

    </com.scwang.smartrefresh.layout.SmartRefreshLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
