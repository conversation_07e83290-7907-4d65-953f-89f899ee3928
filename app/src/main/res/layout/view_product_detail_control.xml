<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_product_aptitude_control"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_60"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@drawable/bg_detail_time_layout2" >

        <ImageView
            android:layout_width="42dp"
            android:layout_height="@dimen/dimen_dp_54"
            android:src="@drawable/icon_sepell_group_fire"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginEnd="@dimen/dimen_dp_19" />


        <TextView
            android:id="@+id/tv_control_aptitude"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:text="价格认证资质可见"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_15" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_product_control"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        android:paddingTop="@dimen/dimen_dp_7"
        android:paddingBottom="@dimen/dimen_dp_7"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:text="价格认证资质可见"
        android:textColor="#EF7C07"
        android:textSize="@dimen/dimen_dp_15" />

</androidx.constraintlayout.widget.ConstraintLayout>