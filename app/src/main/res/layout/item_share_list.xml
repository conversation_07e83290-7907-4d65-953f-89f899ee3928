<?xml version="1.0" encoding="utf-8"?>
<com.xyy.common.widget.RoundLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="@dimen/dp10"
    android:layout_marginRight="@dimen/dp10"
    android:orientation="vertical"
    android:padding="@dimen/dp10"
    android:paddingTop="12dp"
    app:rv_backgroundColor="@android:color/white"
    app:rv_cornerRadius="7.5dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.xyy.common.widget.RoundTextView
            android:id="@+id/tv_item_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:paddingLeft="7dp"
            android:paddingTop="2dp"
            android:paddingRight="7dp"
            android:paddingBottom="2dp"
            android:textColor="@color/text_color_35C561"
            android:textSize="11sp"
            app:rv_backgroundColor="@color/white"
            app:rv_cornerRadius="10dp"
            app:rv_strokeColor="@color/text_color_35C561"
            app:rv_strokeWidth="1dp"
            tools:text="商品活动" />

        <TextView
            android:id="@+id/tv_item_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="7dp"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/text_color_333333"
            android:textSize="@dimen/title_text_size"
            tools:text="经典优选冬季感冒家庭套装冒庭套装冒庭套装冒庭套装" />
    </LinearLayout>


    <ImageView
        android:id="@+id/iv_item_icon"
        android:layout_width="match_parent"
        android:layout_height="149dp"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/dp11"
        android:scaleType="centerCrop"
        tools:src="@tools:sample/backgrounds/scenic" />

    <TextView
        android:id="@+id/tv_item_share_context"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:textColor="@color/text_color_333333"
        android:textSize="@dimen/sample_text_size"
        tools:text="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp6">

        <TextView
            android:id="@+id/tv_item_share_branch_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:drawableLeft="@drawable/ic_share_address"
            android:drawablePadding="4dp"
            android:includeFontPadding="false"
            android:textColor="@color/text_color_999999"
            android:textSize="12sp"
            tools:text="山东" />

        <TextView
            android:id="@+id/tv_item_share_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/text_color_999999"
            android:textSize="@dimen/sp12"
            tools:text="分享次数：10000次" />
    </LinearLayout>

    <com.xyy.common.widget.RoundTextView
        android:id="@+id/tv_share"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp44"
        android:layout_marginTop="@dimen/dp10"
        android:background="@color/text_color_35C561"
        android:gravity="center"
        android:text="分享"
        android:textColor="@color/white"
        android:textSize="@dimen/sp17"
        app:layout_constraintTop_toBottomOf="@id/tv_item_share_context"
        app:rv_backgroundColor="@color/text_color_35C561"
        app:rv_cornerRadius="@dimen/dp2" />
</com.xyy.common.widget.RoundLinearLayout>