<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingLeft="5dp"
    android:paddingRight="5dp">

    <View
        android:id="@+id/v_arrow"
        android:layout_width="10dp"
        android:background="@drawable/customer_lifecycle_more_arrow"
        android:layout_height="5dp" />

    <TextView
        android:id="@+id/tv_info"
        android:layout_width="match_parent"
        android:layout_marginTop="4dp"
        android:layout_height="wrap_content"
        android:padding="10dp"
        android:textSize="14sp"
        android:textColor="@color/color_FFFFFF"
        android:background="@drawable/shape_sku_collect_info_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />


</FrameLayout>