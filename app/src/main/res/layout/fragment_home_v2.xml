<?xml version="1.0" encoding="utf-8"?>
<com.scwang.smartrefresh.layout.SmartRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/refresh_layout_home"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:id="@+id/cl"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#F1F1F1"
            android:focusable="true"
            android:focusableInTouchMode="true">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/appbar"
                android:layout_width="match_parent"
                android:layout_height="155dp"
                android:background="@drawable/bg_home_header">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_range_parent"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:paddingStart="15dp"
                    android:paddingEnd="15dp"
                    android:paddingBottom="15dp"
                    app:layout_scrollFlags="scroll">

                    <TextView
                        android:id="@+id/tv_range"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableRight="@drawable/down_arrow"
                        android:drawablePadding="6dp"
                        android:maxWidth="133dp"
                        android:singleLine="true"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:visibility="visible"
                        app:layout_constraintBottom_toBottomOf="@+id/rtv_search_customer"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="@+id/rtv_search_customer"
                        tools:text="武汉武汉武汉武汉武汉武汉" />

                    <com.xyy.common.widget.RoundTextView
                        android:id="@+id/rtv_search_customer"
                        android:layout_width="0dp"
                        android:layout_height="34dp"
                        android:layout_marginStart="15dp"
                        android:drawableStart="@drawable/ic_search_home"
                        android:drawablePadding="5dp"
                        android:gravity="center_vertical"
                        android:hint="搜索客户"
                        android:paddingStart="10dp"
                        android:textColorHint="@color/color_9494A6"
                        android:textSize="12sp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/tv_range"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_goneMarginStart="0dp"
                        app:rv_backgroundColor="@color/color_F6F6F6"
                        app:rv_cornerRadius="17dp"
                        app:rv_strokeColor="@color/color_F6F6F6"
                        app:rv_strokeWidth="0.5dp" />

                    <TextView
                        android:id="@+id/tv_goods_management"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_marginTop="18dp"
                        android:drawableTop="@drawable/ic_goods_management"
                        android:drawablePadding="7dp"
                        android:text="商品管理"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        app:layout_constraintEnd_toStartOf="@+id/tv_order_management"
                        app:layout_constraintHorizontal_chainStyle="spread_inside"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/rtv_search_customer" />

                    <TextView
                        android:id="@+id/tv_order_management"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableTop="@drawable/ic_order_management"
                        android:drawablePadding="7dp"
                        android:text="订单管理"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        app:layout_constraintEnd_toStartOf="@+id/tv_visit_management"
                        app:layout_constraintStart_toEndOf="@+id/tv_goods_management"
                        app:layout_constraintTop_toTopOf="@+id/tv_goods_management" />

                    <TextView
                        android:id="@+id/tv_visit_management"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableTop="@drawable/ic_visit_management"
                        android:drawablePadding="7dp"
                        android:text="拜访管理"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        app:layout_constraintEnd_toStartOf="@+id/tv_task_goods"
                        app:layout_constraintStart_toEndOf="@+id/tv_order_management"
                        app:layout_constraintTop_toTopOf="@+id/tv_goods_management" />

                    <TextView
                        android:id="@+id/tv_task_goods"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:drawableTop="@drawable/ic_task_goods"
                        android:drawablePadding="7dp"
                        android:text="任务管理"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/tv_visit_management"
                        app:layout_constraintTop_toTopOf="@+id/tv_goods_management" />

                </androidx.constraintlayout.widget.ConstraintLayout>


            </com.google.android.material.appbar.AppBarLayout>

            <androidx.core.widget.NestedScrollView
                android:id="@+id/nsv_scrollview"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="-17dp"
                android:fillViewport="true"
                app:layout_behavior="@string/appbar_scrolling_view_behavior" />

        </androidx.coordinatorlayout.widget.CoordinatorLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_head"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:alpha="0"
            android:background="@color/white"
            android:paddingStart="15dp"
            android:paddingTop="25dp"
            android:paddingEnd="15dp"
            android:paddingBottom="10dp">

            <TextView
                android:id="@+id/tv_head_range"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableRight="@drawable/down_arrow"
                android:drawablePadding="6dp"
                android:maxWidth="133dp"
                android:singleLine="true"
                android:textColor="@color/color_292933"
                android:textSize="16sp"
                app:layout_constraintBottom_toBottomOf="@+id/rtv_head_search_customer"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/rtv_head_search_customer"
                tools:text="武汉武汉武汉武汉武汉武汉" />

            <com.xyy.common.widget.RoundTextView
                android:id="@+id/rtv_head_search_customer"
                android:layout_width="0dp"
                android:layout_height="34dp"
                android:layout_marginStart="15dp"
                android:drawableStart="@drawable/ic_search_home"
                android:drawablePadding="5dp"
                android:gravity="center_vertical"
                android:hint="搜索客户"
                android:paddingStart="10dp"
                android:textColorHint="@color/color_9494A6"
                android:textSize="12sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/tv_head_range"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_goneMarginStart="0dp"
                app:rv_backgroundColor="@color/color_F6F6F6"
                app:rv_cornerRadius="17dp"
                app:rv_strokeColor="@color/color_F6F6F6"
                app:rv_strokeWidth="0.5dp" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </FrameLayout>

</com.scwang.smartrefresh.layout.SmartRefreshLayout>
