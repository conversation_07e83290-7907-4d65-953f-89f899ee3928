<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/ic_shadow_low">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp10"
        android:text="单据编号："
        android:textColor="@color/text_color_666666"
        android:textSize="12sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_audit_id" />

    <TextView
        android:id="@+id/tv_audit_id"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp13"
        android:layout_marginRight="10dp"
        android:ellipsize="end"
        android:lines="1"
        android:textColor="@color/text_color_666666"
        android:textSize="12sp"
        app:layout_constraintLeft_toRightOf="@id/tv_title"
        app:layout_constraintRight_toLeftOf="@id/tv_audit_status"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="YBN288265457WHHJBJ" />

    <TextView
        android:id="@+id/tv_audit_status"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginEnd="@dimen/dp10"
        android:textColor="@color/text_color_35C561"
        android:textSize="12sp"
        app:layout_constraintLeft_toRightOf="@id/tv_audit_id"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_audit_id"
        tools:text="已完成" />

    <View
        android:id="@+id/view_line"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="@dimen/dp13"
        android:background="@color/color_F6F6F6"
        android:paddingStart="@dimen/dp15"
        android:paddingEnd="@dimen/dp15"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_audit_id" />


    <!--药店名称-->
    <TextView
        android:id="@+id/tv_shopName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginTop="@dimen/dp10"
        android:layout_marginRight="10dp"
        android:ellipsize="end"
        android:lines="1"
        android:textColor="@color/text_color_333333"
        android:textSize="15sp"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_line"
        tools:text="江夏区光谷大道金象药店发大V而反而无法" />

    <TextView
        android:id="@+id/tv_operator"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="4dp"
        android:layout_marginRight="10dp"
        android:textColor="@color/text_color_8E8E93"
        android:textSize="12sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_shopName"
        tools:text="提交人：彭松" />

    <TextView
        android:id="@+id/tv_form"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="3dp"
        android:layout_marginRight="10dp"
        android:textColor="@color/text_color_8E8E93"
        android:textSize="12sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_operator"
        tools:text="单据来源：药帮忙app" />

    <TextView
        android:id="@+id/tv_up_time"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="3dp"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="14dp"
        android:textColor="@color/text_color_8E8E93"
        android:textSize="12sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_form"
        tools:text="提交时间：2018.11.23 12:34:08" />

    <TextView
        android:id="@+id/tv_ec_org"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="3dp"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="14dp"
        android:textColor="@color/text_color_8E8E93"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_up_time"
        tools:text="" />

</androidx.constraintlayout.widget.ConstraintLayout>
