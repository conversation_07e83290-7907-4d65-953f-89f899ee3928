<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    android:background="@drawable/second_level_selector_header_bg"
    android:paddingRight="2dp"
    android:paddingLeft="2dp"
    android:paddingTop="6dp"
    android:paddingBottom="6dp"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <TextView
        android:maxLines="1"
        android:singleLine="true"
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:drawablePadding="5dp"
        android:gravity="center_vertical"
        android:textColor="@color/selector_text_color_tab_292933"
        android:textSize="12sp" />

    <TextView
        android:maxLines="1"
        android:singleLine="true"
        android:layout_marginLeft="10dp"
        android:id="@+id/tv_count"
        android:layout_weight="1"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:drawablePadding="12dp"
        android:drawableRight="@drawable/second_level_selector_header_text"
        android:gravity="center_vertical"
        android:textColor="@color/selector_text_color_3f3f3f"
        android:textSize="12sp" />

</LinearLayout>