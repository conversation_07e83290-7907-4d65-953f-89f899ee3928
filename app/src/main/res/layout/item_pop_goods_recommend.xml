<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout"
    android:layout_width="match_parent"
    android:layout_height="44dp">

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="wrap_content"
        android:layout_height="44dp"
        android:gravity="center_vertical"
        android:paddingLeft="@dimen/dp10"
        android:paddingEnd="@dimen/dp10"
        android:textColor="@drawable/selector_switch_textcolor"
        android:textSize="@dimen/sp14"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="市场热销" />

    <ImageView
        android:id="@+id/iv_select"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp10"
        android:src="@drawable/ic_green_right"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tv_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_content"
        tools:visibility="visible" />

    <View
        style="@style/cut_off_line_style"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>