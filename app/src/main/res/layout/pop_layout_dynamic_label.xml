<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/color_f7f7f8"
    android:orientation="vertical">


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvDynamicLabelItem"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:overScrollMode="never"
        android:paddingHorizontal="@dimen/dimen_dp_10" />


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="bottom"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_reset"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:background="@drawable/bg_filtrate_classify_btn2_reset"
            android:text="重置"
            android:textColor="@color/text_292933"
            android:textSize="16sp" />

        <Button
            android:id="@+id/btn_affirm"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:background="@color/base_colors_new"
            android:elevation="0dp"
            android:text="确定"
            android:textColor="@color/white"
            android:textSize="16sp" />

    </LinearLayout>

</LinearLayout>