<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:app="http://schemas.android.com/apk/res-auto"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              xmlns:tools="http://schemas.android.com/tools"
              android:orientation="vertical"
              android:background="#F7F7F8">

    <RelativeLayout
        android:id="@+id/ll_title"
        style="@style/header_white_layout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_back"
            style="@style/header_layout_left"
            android:src="@drawable/nav_return"/>

        <TextView
            android:id="@+id/tv_title"
            style="@style/header_layout_mid"
            android:text="采购对账单"
            android:textColor="#FF292933"/>

        <TextView
            android:id="@+id/tv_right"
            style="@style/header_layout_right_text"
            android:visibility="visible"/>

        <ImageView
            android:id="@+id/iv_right"
            style="@style/header_layout_right_img"
            android:src="@drawable/nav_scarch"
            android:visibility="gone"/>
    </RelativeLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_10"
        android:background="@color/white"
        android:paddingBottom="@dimen/dimen_dp_15"
        app:layout_constraintTop_toBottomOf="@+id/ll_title">

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dimen_dp_40"
            android:layout_marginStart="@dimen/dimen_dp_12"
            android:drawableEnd="@drawable/icon_purchase_arrow_down"
            android:drawablePadding="@dimen/dimen_dp_3"
            android:gravity="center_vertical"
            tools:text="2022.06"
            android:textColor="#111334"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_0_5"
            android:background="#eeeeee"
            app:layout_constraintTop_toBottomOf="@+id/tv_time"/>

        <LinearLayout
            android:id="@+id/ll_total_amount"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_38"
            android:layout_marginTop="@dimen/dimen_dp_16"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_time">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:text="采购总金额："
                    android:textColor="#676773"
                    android:textSize="@dimen/dimen_dp_12"
                    app:layout_constraintTop_toTopOf="parent"/>

                <TextView
                    android:id="@+id/tv_purchase_total_amount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    tools:text="¥20300.00"
                    android:textColor="#676773"
                    android:textSize="@dimen/dimen_dp_14"
                    app:layout_constraintBottom_toBottomOf="parent"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:layout_width="@dimen/dimen_dp_0_5"
                android:layout_height="match_parent"
                android:layout_marginBottom="@dimen/dimen_dp_3"
                android:background="#DDDDDD"/>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:text="实际采购总金额："
                    android:textColor="#676773"
                    android:textSize="@dimen/dimen_dp_12"
                    app:layout_constraintTop_toTopOf="parent"/>

                <TextView
                    android:id="@+id/tv_actual_purchase_total_amount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    tools:text="¥20300.00"
                    android:textColor="#333333"
                    android:textSize="@dimen/dimen_dp_14"
                    app:layout_constraintBottom_toBottomOf="parent"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:layout_width="@dimen/dimen_dp_0_5"
                android:layout_height="match_parent"
                android:layout_marginBottom="@dimen/dimen_dp_3"
                android:background="#DDDDDD"/>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:text="实际退款总额："
                    android:textColor="#676773"
                    android:textSize="@dimen/dimen_dp_12"
                    app:layout_constraintTop_toTopOf="parent"/>

                <TextView
                    android:id="@+id/tv_actual_refund_total_amount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    tools:text="¥20300.00"
                    android:textColor="#676773"
                    android:textSize="@dimen/dimen_dp_14"
                    app:layout_constraintBottom_toBottomOf="parent"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/tv_saved_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_dp_12"
            android:layout_marginTop="@dimen/dimen_dp_20"
            tools:text="已节省：¥200.00"
            android:textColor="#292933"
            android:textSize="@dimen/dimen_dp_13"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ll_total_amount"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_tip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="* 实际采购总额为已支付的订单总额，减去已退款的退款总额"
        android:textColor="#9494A5"
        android:textSize="@dimen/dimen_dp_13"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cl_header"/>
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_weight="1"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginEnd="@dimen/dimen_dp_10"
            app:layout_constraintVertical_bias="1.0"
            app:layout_constraintVertical_weight="1"/>

        <LinearLayout
            android:id="@+id/ll_empty_data"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:gravity="center"
            android:visibility="gone"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv"
                android:layout_width="154dp"
                android:layout_height="154dp"
                android:layout_gravity="center_horizontal"
                android:scaleType="fitCenter"
                android:src="@drawable/icon_empty"/>

            <TextView
                android:id="@+id/tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:gravity="center"
                android:text="没有相关数据"
                android:textColor="@color/text_9494A6"
                android:textSize="@dimen/common_empty_tv"/>
        </LinearLayout>

    </FrameLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_54"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/tv_download_to_email"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_44"
            android:layout_marginStart="@dimen/dimen_dp_15"
            android:layout_marginEnd="@dimen/dimen_dp_15"
            android:layout_marginBottom="@dimen/dimen_dp_8"
            android:background="@color/color_00b377"
            android:gravity="center"
            android:text="下载对账单到邮箱"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_15"
            app:layout_constraintBottom_toBottomOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>