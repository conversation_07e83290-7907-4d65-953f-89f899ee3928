<?xml version="1.0" encoding="utf-8"?>

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="60dp"
    android:layout_gravity="bottom"
    android:clipChildren="false"
    android:orientation="horizontal">


    <FrameLayout
        android:id="@+id/fl_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="10dp"
        android:background="@color/white" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="10dp"
        android:background="@color/divider_line_base_1px" />

    <LinearLayout
        android:id="@+id/ll_bottom_tab"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_gravity="bottom"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/ll_home"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginTop="10dp"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_ll_home"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:src="@drawable/bid02" />

            <TextView
                android:id="@+id/tv_ll_home"
                style="@style/HomeTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="首页" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_to_top"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="match_parent"
            android:layout_marginTop="10dp"
            android:layout_weight="1"
            android:gravity="center"
            android:visibility="gone"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_to_top"
                android:layout_width="@dimen/dimen_dp_36"
                android:layout_height="@dimen/dimen_dp_36"
                android:src="@drawable/icon_to_top" />

            <ImageView
                android:id="@+id/iv_single_top"
                android:layout_width="@dimen/dimen_dp_36"
                android:layout_height="@dimen/dimen_dp_36"
                android:src="@drawable/bid01_checked_single_img" />
        </LinearLayout>

<!--        <LinearLayout-->
<!--            android:id="@+id/ll_sort"-->
<!--            android:layout_width="0dp"-->
<!--            android:layout_height="match_parent"-->
<!--            android:layout_marginTop="10dp"-->
<!--            android:layout_weight="1"-->
<!--            android:gravity="center"-->
<!--            android:orientation="vertical">-->

<!--            <ImageView-->
<!--                android:id="@+id/iv_sort"-->
<!--                android:layout_width="22dp"-->
<!--                android:layout_height="22dp"-->
<!--                android:src="@drawable/bid04" />-->
<!--            &lt;!&ndash;android:clickable="true"&ndash;&gt;-->


<!--            <TextView-->
<!--                android:id="@+id/tv_sort"-->
<!--                style="@style/HomeTextView"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:text="全部药品" />-->

<!--        </LinearLayout>-->

        <LinearLayout
            android:id="@+id/ll_discover"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginTop="10dp"
            android:layout_weight="1"
            android:gravity="center"
            android:visibility="gone"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_discover"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:src="@drawable/bid10" />

            <TextView
                android:id="@+id/tv_discover"
                style="@style/HomeTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="发现" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_activity"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center_horizontal|bottom"
            android:visibility="gone">


            <ImageView
                android:id="@+id/iv_activity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        </LinearLayout>

        <FrameLayout
            android:id="@+id/ll_shopping_fl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginTop="10dp"
            android:layout_weight="1">

            <LinearLayout
                android:id="@+id/ll_shopping"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_shopping"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:src="@drawable/bid06" />

                <TextView
                    android:id="@+id/tv_shopping"
                    style="@style/HomeTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="购物车" />

            </LinearLayout>

            <TextView
                android:id="@+id/ll_shopping_tv"
                style="@style/more_msg_tip_style"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="8dp"
                android:layout_marginTop="4dp"
                android:gravity="center"
                android:text=""
                android:visibility="gone" />
        </FrameLayout>

        <FrameLayout
            android:id="@+id/ll_order_fl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginTop="10dp"
            android:layout_weight="1"
            android:gravity="center">

            <LinearLayout
                android:id="@+id/ll_order"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_order"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:src="@drawable/icon_main_tab_order_false" />

                <TextView
                    android:id="@+id/tv_order"
                    style="@style/HomeTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="订单" />

            </LinearLayout>

            <TextView
                android:id="@+id/ll_order_tv"
                style="@style/more_msg_tip_style"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="8dp"
                android:layout_marginTop="4dp"
                android:gravity="center"
                android:text=""
                android:visibility="invisible" />

        </FrameLayout>


        <LinearLayout
            android:id="@+id/ll_me"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginTop="10dp"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_me"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:src="@drawable/bid08" />

            <TextView
                android:id="@+id/tv_me"
                style="@style/HomeTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="我的" />

        </LinearLayout>

    </LinearLayout>

    <View
        android:id="@+id/main_bottom_view_masking"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="10dp"
        android:background="#99222222"
        android:visibility="gone" />
</FrameLayout>