<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:minHeight="@dimen/sign_height_12"
    android:orientation="vertical">

    <TextView
        android:textColor="@color/text_day"
        android:id="@+id/txt_day"
        android:layout_width="@dimen/sign_height_13"
        android:layout_height="@dimen/sign_height_13"
        android:gravity="center"
        android:text="7"
        android:textSize="@dimen/sign_tv_05" />

    <TextView
        android:visibility="invisible"
        android:id="@+id/tv_integral"
        android:layout_width="@dimen/sign_height_13"
        android:layout_height="@dimen/sign_height_14"
        android:gravity="center_horizontal|top"
        android:text="1"
        android:textColor="@color/text_integral"
        android:textSize="@dimen/sign_tv_06" />

</LinearLayout>