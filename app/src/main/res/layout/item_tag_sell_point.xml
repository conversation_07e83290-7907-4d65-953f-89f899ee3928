<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        android:background="@drawable/abc_sell_point_bg">

        <TextView
            android:id="@+id/tv_tagName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center"
            android:lines="1"
            android:paddingStart="8dp"
            android:paddingTop="8dp"
            app:layout_constraintHorizontal_bias="0.0"
            android:paddingBottom="8dp"
            android:text="我介务经费"
            android:textColor="@drawable/abc_sell_point_text_color"
            android:textSize="13sp"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginRight="8dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_tag_delete"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginStart="1dp"
            android:paddingRight="8dp"
            android:src="@drawable/ic_tag_close"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_tagName"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
