<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="15dp"
    android:paddingBottom="15dp">

    <ImageView
        android:id="@+id/iv_radio"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:src="@drawable/icon_radio_btn_normal"
        app:layout_constraintBottom_toBottomOf="@+id/iv_cover"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv_cover" />

    <ImageView
        android:id="@+id/iv_cover"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_marginStart="15dp"
        app:layout_constraintStart_toEndOf="@+id/iv_radio"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_retail_price"
        android:layout_width="0dp"
        android:layout_height="20dp"
        android:background="@color/color_F6F6F6"
        android:gravity="center"
        android:textColor="@color/color_676773"
        android:textSize="12sp"
        android:maxLines="1"
        android:ellipsize="end"
        app:layout_constraintBottom_toBottomOf="@+id/iv_cover"
        app:layout_constraintEnd_toEndOf="@+id/iv_cover"
        app:layout_constraintStart_toStartOf="@+id/iv_cover"
        tools:text="零售价 ¥19.00" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/color_292933"
        android:textSize="15sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/iv_cover"
        app:layout_constraintTop_toTopOf="@+id/iv_cover"
        tools:text="999必无忧 盐酸特比萘 芬凝胶超过折行最多 芬凝胶超过折行最多" />

    <TextView
        android:id="@+id/tv_specification"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginTop="3dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/color_676773"
        android:textSize="11sp"
        app:layout_constraintStart_toEndOf="@+id/iv_cover"
        app:layout_constraintTop_toBottomOf="@+id/tv_name"
        tools:text="12颗*1盒" />

    <TextView
        android:id="@+id/tv_final_price_and_gross_rate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="3dp"
        android:textColor="@color/color_9494A6"
        android:textSize="10sp"
        app:layout_constraintBottom_toTopOf="@+id/fake_guide_line"
        app:layout_constraintStart_toStartOf="@+id/recycler_view_label"
        app:layout_goneMarginBottom="0dp"
        tools:text="¥15:00（终端毛利率25%）" />

    <View
        android:id="@+id/fake_guide_line"
        android:layout_width="1px"
        android:layout_height="1px"
        android:background="#00000000"
        android:layout_marginBottom="15dp"
        app:layout_constraintBottom_toBottomOf="@id/iv_cover"
        app:layout_constraintStart_toEndOf="@+id/iv_cover" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view_label"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/fake_guide_line"
        app:layout_constraintEnd_toEndOf="@+id/tv_name"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="@+id/tv_name" />

    <TextView
        android:id="@+id/tv_sales_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="7dp"
        android:textColor="@color/color_292933"
        android:textSize="14sp"
        app:layout_constraintStart_toStartOf="@+id/iv_cover"
        app:layout_constraintTop_toBottomOf="@+id/recycler_view_label"
        tools:text="销售额进展：21元" />

</androidx.constraintlayout.widget.ConstraintLayout>