<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp100"
        android:src="@drawable/icon_login_title" />

    <RelativeLayout
        style="@style/user_info_layout_style"
        android:layout_marginTop="35dp">

        <TextView
            android:id="@+id/tv_login_phone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="账号："
            android:textColor="@color/text_color_333333"
            android:textSize="@dimen/sp15" />

        <com.ybm100.app.crm.widget.EditTextWithDel
            android:id="@+id/et_login_phone"
            style="@style/login_layout"
            android:layout_marginStart="@dimen/dp15"
            android:layout_toEndOf="@id/tv_login_phone"
            android:digits="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ._"
            android:hint="请输入OA账号（公司邮箱前缀）"
            android:textSize="@dimen/dp15" />
    </RelativeLayout>

    <RelativeLayout style="@style/user_info_layout_style">

        <TextView
            android:id="@+id/tv_login_pwd"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="密码："
            android:textColor="@color/text_color_333333"
            android:textSize="@dimen/sp15" />

        <com.ybm100.app.crm.widget.EditTextWithDel
            android:id="@+id/et_login_pwd"
            style="@style/login_layout"
            android:layout_marginStart="@dimen/dp15"
            android:layout_marginEnd="@dimen/dp10"
            android:layout_toStartOf="@+id/im_show_pwd"
            android:layout_toEndOf="@id/tv_login_pwd"
            android:hint="请输入OA密码（公司邮箱密码）"
            android:imeOptions="actionDone"
            android:inputType="textPassword"
            android:textSize="@dimen/dp15"
            android:typeface="sans" />

        <ImageView
            android:id="@+id/im_show_pwd"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/dp5"
            android:background="@drawable/selector_show_pwd"
            android:visibility="invisible" />
    </RelativeLayout>

    <RelativeLayout style="@style/user_info_layout_style">

        <TextView
            android:id="@+id/tv_verify_code"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="验证码："
            android:textColor="@color/text_color_333333"
            android:textSize="@dimen/sp15" />

        <com.ybm100.app.crm.widget.EditTextWithDel
            android:id="@+id/et_verify_code"
            style="@style/login_layout"
            android:layout_marginEnd="@dimen/dp10"
            android:layout_toStartOf="@+id/im_verify_code"
            android:layout_toEndOf="@id/tv_verify_code"
            android:hint="请输入验证码"
            android:imeOptions="actionDone"
            android:textSize="@dimen/dp15"
            android:typeface="sans" />

        <ImageView
            android:id="@+id/im_verify_code"
            android:layout_width="100dp"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="6dp"
            android:layout_marginBottom="6dp"
            android:background="@drawable/shape_verifycode_bg_gray"
            android:padding="@dimen/dp1"
            android:visibility="visible" />

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="100dp"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="20dp"
            android:padding="10dp"
            android:visibility="visible" />
    </RelativeLayout>

    <TextView
        android:id="@+id/tv_login_button"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp45"
        android:layout_marginStart="@dimen/dp20"
        android:layout_marginTop="@dimen/dp35"
        android:layout_marginEnd="@dimen/dp20"
        android:background="@drawable/btn_un_click"
        android:gravity="center"
        android:text="登录"
        android:textColor="@color/text_color_999999"
        android:textSize="@dimen/sp16" />

    <TextView
        android:id="@+id/tv_forget_pwd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center|right"
        android:layout_marginTop="@dimen/dp10"
        android:layout_marginRight="20dp"
        android:clickable="false"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:text="忘记密码"
        android:textColor="@color/text_color_35C561"
        android:textSize="@dimen/sp14" />

    <TextView
        android:id="@+id/tv_status"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="bottom|right"
        android:padding="20dp"
        android:text="test"
        android:visibility="gone"
        tools:visibility="visible" />
</LinearLayout>
