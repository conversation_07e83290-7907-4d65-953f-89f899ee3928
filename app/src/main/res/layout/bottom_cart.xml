<?xml version="1.0" encoding="utf-8"?>
<com.xyy.common.widget.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="45dp"
    app:rv_backgroundColor="@color/color_4F4E4E"
    app:rv_cornerRadius="2dp"
    app:rv_isRippleEnable="false">

    <com.xyy.common.widget.RoundTextView
        android:id="@+id/rtv_confirm_recommendation"
        android:layout_width="100dp"
        android:layout_height="0dp"
        android:gravity="center"
        android:text="确认推荐"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:rv_backgroundColor="@color/color_00B377"
        app:rv_cornerRadius="2dp"
        app:rv_isRippleEnable="false" />

    <ImageView
        android:id="@+id/iv_customer_cart"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:src="@drawable/ic_customer_cart"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.ybm100.app.crm.widget.BadgeView
        android:id="@+id/bv_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:padding="2dp"
        android:textColor="@color/white"
        android:textSize="10sp"
        app:layout_constraintBottom_toTopOf="@+id/iv_customer_cart"
        app:layout_constraintEnd_toEndOf="@+id/iv_customer_cart"
        app:layout_constraintStart_toEndOf="@+id/iv_customer_cart"
        app:layout_constraintTop_toTopOf="@id/iv_customer_cart" />

    <TextView
        android:id="@+id/tv_description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:layout_marginEnd="15dp"
        android:text="已选0个客户"
        android:textColor="@color/white"
        android:textSize="13sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/rtv_confirm_recommendation"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@+id/iv_customer_cart"
        app:layout_constraintTop_toTopOf="parent" />

</com.xyy.common.widget.RoundConstraintLayout>