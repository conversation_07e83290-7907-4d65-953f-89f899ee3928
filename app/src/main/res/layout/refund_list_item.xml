<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_root"
    android:layout_width="match_parent"
    android:layout_height="203dp"
    android:layout_alignParentStart="true"
    android:layout_alignParentLeft="true"
    android:layout_alignParentTop="true"
    android:layout_marginLeft="10dp"
    android:layout_marginTop="10dp"
    android:layout_marginRight="10dp"
    android:background="@drawable/bg_refund_list"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:background="@color/white"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/tv_order_no"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_weight="1"
            android:text="20160321100912312"
            android:textColor="@color/text_292933"
            android:textSize="15sp" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:src="@drawable/check_order03" />
    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="#fbfbfb"
        android:orientation="horizontal"
        android:paddingLeft="10dp"
        android:paddingTop="10dp"
        android:paddingBottom="10dp">

        <com.ybmmarket20.common.widget.RoundedImageView
            android:id="@+id/iv_order"
            android:layout_width="99dp"
            android:layout_height="100dp"
            android:layout_centerVertical="true"
            android:padding="5dp"
            app:rv_backgroundColor="@color/white"
            app:rv_cornerRadius="3dp"
            app:rv_strokeColor="@color/divider_line_base_1px"
            app:rv_strokeWidth="0.5dp" />

        <TextView
            android:id="@+id/tv_order_number"
            android:layout_width="99dp"
            android:layout_height="22dp"
            android:layout_alignParentBottom="true"
            android:background="@drawable/gray_bottom_radius_5dp_bg"
            android:gravity="center"
            android:text="4件药品"
            android:textColor="@color/white"
            android:textSize="@dimen/check_order_tv2" />

        <LinearLayout
            android:id="@+id/ll_order_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="2dp"
            android:layout_toRightOf="@+id/iv_order"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_order_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="2016-3-21 10:09"
                android:textColor="@color/text_292933"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_order_total"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:text=""
                android:textColor="@color/text_292933"
                android:textSize="14sp" />

            <LinearLayout
                android:id="@+id/tv_refund_list_time_layout"
                android:layout_width="wrap_content"
                android:layout_height="17dp"
                android:background="@drawable/bg_brand_f95e35"
                android:visibility="gone"
                android:orientation="horizontal"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tv_refund_list_time_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/icon_order_unpay"
                    android:gravity="center"
                    android:textColor="@color/white"
                    android:paddingStart="@dimen/dimen_dp_3"
                    android:paddingEnd="@dimen/dimen_dp_10"
                    android:textSize="10dp"
                    tools:text="待填写物流信息" />

                <TextView
                    android:id="@+id/tv_refund_list_time_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:paddingRight="5dp"
                    android:textColor="@color/color_ff5729"
                    android:textSize="11dp"
                    tools:text="1天22小时56分" />

            </LinearLayout>
        </LinearLayout>

    </RelativeLayout>

    <!--操作按键-->
    <LinearLayout
        android:id="@+id/ral_btn"
        android:layout_width="match_parent"
        android:layout_height="53dp"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_refund"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:drawableLeft="@drawable/refund_drawable_selector"
            android:drawablePadding="7dp"
            android:gravity="center"
            android:minWidth="74dp"
            android:text=""
            android:textColor="@color/text_292933"
            android:textSize="14sp" />

        <View
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <Button
            android:id="@+id/btn_cancel_the_refund"
            android:layout_width="87dp"
            android:layout_height="33dp"
            android:layout_marginLeft="20dp"
            android:background="@drawable/order_green_border"
            android:gravity="center"
            android:text="取消退款"
            android:textColor="@color/base_colors"
            android:textSize="@dimen/check_order_tv3" />

        <Button
            android:id="@+id/btn_detail"
            android:layout_width="87dp"
            android:layout_height="33dp"
            android:layout_marginLeft="20dp"
            android:background="@drawable/order_gray_border"
            android:gravity="center"
            android:text="查看详情"
            android:textColor="@color/text_292933"
            android:textSize="@dimen/check_order_tv3" />
    </LinearLayout>
</LinearLayout>
