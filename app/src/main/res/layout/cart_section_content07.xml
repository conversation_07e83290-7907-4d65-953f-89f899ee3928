<?xml version="1.0" encoding="utf-8"?>
<com.ybmmarket20.view.SwipeMenuLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/card_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="10dp"
    android:layout_marginRight="10dp"
    android:orientation="horizontal"
    card_view:ios="false"
    card_view:leftSwipe="true"
    card_view:swipeEnable="true">

    <!--正常的条目布局-->
    <RelativeLayout
        android:id="@+id/fg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/ll_item_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_cart_section_content_01"
            android:orientation="horizontal"
            android:showDividers="middle">

            <RelativeLayout
                android:layout_width="133dp"
                android:layout_height="match_parent">

                <LinearLayout
                    android:id="@+id/cart_item_ll"
                    android:layout_width="40dp"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:background="@drawable/line_vertical"
                        android:layerType="software" />

                    <CheckBox
                        android:id="@+id/shop_check"
                        style="@style/CustomCheckboxTheme"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:visibility="visible" />

                    <TextView
                        android:id="@+id/shop_lose"
                        android:layout_width="30dp"
                        android:layout_height="16dp"
                        android:layout_margin="6dp"
                        android:background="@drawable/bg_cart_lose_icon"
                        android:gravity="center"
                        android:text="失效"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        android:visibility="gone" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:background="@drawable/line_vertical"
                        android:layerType="software" />

                </LinearLayout>

                <FrameLayout
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_toRightOf="@+id/cart_item_ll">

                    <RelativeLayout
                        android:layout_width="93dp"
                        android:layout_height="93dp"
                        android:layout_gravity="center_vertical"
                        android:background="@color/white">

                        <ImageView
                            android:id="@+id/shop_photo"
                            android:layout_width="80dp"
                            android:layout_height="80dp"
                            android:layout_centerInParent="true" />

                        <TextView
                            android:id="@+id/shop_no_limit_tv01"
                            android:layout_width="45dp"
                            android:layout_height="45dp"
                            android:layout_centerInParent="true"
                            android:layout_gravity="center"
                            android:background="@drawable/shop_limit01"
                            android:gravity="center"
                            android:text=""
                            android:textColor="#ffffff"
                            android:textSize="12dp"
                            android:visibility="visible" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_alignParentBottom="true"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/shop_no_limit_tv02"
                                android:layout_width="match_parent"
                                android:layout_height="17dp"
                                android:layout_centerHorizontal="true"
                                android:layout_marginTop="0.5dp"
                                android:background="@drawable/gray_bottom_black_product_text_bg"
                                android:gravity="center"
                                android:singleLine="true"
                                android:text=""
                                android:textColor="#ffffff"
                                android:textSize="11dp"
                                android:visibility="gone" />

                        </LinearLayout>

                        <ImageView
                            android:id="@+id/iv_shop_mark"
                            android:layout_width="110dp"
                            android:layout_height="110dp"
                            android:layout_gravity="center_vertical"
                            android:scaleType="fitXY"
                            android:src="@drawable/transparent" />
                    </RelativeLayout>
                </FrameLayout>
            </RelativeLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/shop_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:gravity="center_vertical"
                    android:maxLines="2"
                    android:text=""
                    android:textColor="@color/cart_tv_shop_name"
                    android:textSize="@dimen/cart_content_tv01"
                    tools:text="宝宝一贴灵" />

                <LinearLayout
                    android:id="@+id/ll_price_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="10dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/shop_price"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:maxLines="1"
                        android:textColor="@color/cart_tv_shop_price"
                        android:textSize="@dimen/cart_content_tv02"
                        tools:text="￥14.50/盒" />

                    <TextView
                        android:id="@+id/shop_description"
                        style="@style/cart_item_tv_discount"
                        tools:text="折后约11.56" />
                </LinearLayout>

                <com.ybmmarket20.view.TagView
                    android:id="@+id/rl_icon_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:layout_marginRight="10dp" />

                <TextView
                    android:id="@+id/tv_freight_tips"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:text=""
                    android:textColor="@color/text_676773"
                    android:textSize="11dp"
                    tools:text="满80盒包邮，还差30盒" />
                <!--标签 比上次购买时降  -->
                <com.ybmmarket20.view.TagView
                    android:id="@+id/data_tag_list_view"
                    android:layout_width="match_parent"
                    android:layout_height="15dp"
                    android:layout_marginTop="3dp"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/shop_tv_limit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="right"
                    android:layout_marginTop="3dp"
                    android:layout_marginEnd="10dp"
                    android:paddingLeft="8dp"
                    android:paddingRight="8dp"
                    android:text=""
                    android:textColor="@color/cart_tv_shop_price"
                    android:textSize="@dimen/cart_content_tv02"
                    android:visibility="visible" />

                <RelativeLayout
                    android:id="@+id/cart_rl_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:layout_marginTop="3dp"
                    android:layout_marginBottom="5dp">

                    <TextView
                        android:id="@+id/shop_price1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:text=""
                        android:textColor="@color/cart_tv_only_price"
                        android:textSize="@dimen/cart_content_tv04"
                        android:textStyle="bold" />

                    <RelativeLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="10dp"
                        android:layout_marginBottom="5dp">

                        <include layout="@layout/layout_cart_sku_edit" />

                        <TextView
                            android:id="@+id/shop_no_tv01"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:text=""
                            android:textColor="@color/cart_description"
                            android:textSize="@dimen/cart_content_tv02"
                            android:visibility="visible" />
                    </RelativeLayout>
                </RelativeLayout>
            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/preferential_combo_tip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/ll_item_content"
            android:background="@color/white"
            android:paddingStart="@dimen/dimen_dp_38"
            android:paddingTop="@dimen/dimen_dp_5"
            android:paddingBottom="@dimen/dimen_dp_15"
            android:singleLine="true"
            android:text="限购 此商品为限购商品，超出50盒的部分将按原价购买"
            android:textSize="@dimen/dimen_dp_11"
            android:visibility="gone"
            tools:visibility="visible" />
    </RelativeLayout>

    <Button
        android:id="@+id/btnTop"
        android:layout_width="60dp"
        android:layout_height="match_parent"
        android:background="#d9dee4"
        android:text="置顶"
        android:textColor="@android:color/white"
        android:visibility="gone" />

    <Button
        android:id="@+id/btnCollect"
        android:layout_width="90dp"
        android:layout_height="match_parent"
        android:background="@drawable/bg_cart_section_content_05"
        android:clickable="true"
        android:text="收藏"
        android:textColor="@android:color/white" />

    <Button
        android:id="@+id/btnDelete"
        android:layout_width="90dp"
        android:layout_height="match_parent"
        android:background="@drawable/bg_cart_section_content_06"
        android:text="删除"
        android:textColor="@android:color/white" />

</com.ybmmarket20.view.SwipeMenuLayout>