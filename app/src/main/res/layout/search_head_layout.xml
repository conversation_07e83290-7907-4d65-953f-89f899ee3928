<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="?attr/actionBarSize"
    android:background="@android:color/white"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/search_cancel"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:paddingStart="16.5dp"
        android:paddingTop="13dp"
        android:paddingEnd="15.5dp"
        android:paddingBottom="13dp"
        android:src="@drawable/platform_nav_return_for_channel"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.ybm100.app.crm.goodsmanagement.widget.searchview.EditText_Clear
        android:id="@+id/et_search"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:layout_weight="1"
        android:background="@drawable/bg_search_view"
        android:drawableLeft="@drawable/platform_ic_search_icon"
        android:drawablePadding="6dp"
        android:gravity="start|center_vertical"
        android:hint="默认搜索关键词"
        android:imeOptions="actionSearch"
        android:maxLength="40"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:singleLine="true"
        android:textColor="@color/text_color_333333"
        android:textColorHint="@color/text_color_999999"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/search_cancel_right"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:gravity="center_vertical"
        android:paddingStart="15dp"
        android:paddingEnd="15dp"
        android:text="取消"
        android:textColor="@color/color_292933"
        android:textSize="15sp" />


</LinearLayout>