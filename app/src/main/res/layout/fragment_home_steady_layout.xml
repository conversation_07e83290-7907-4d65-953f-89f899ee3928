<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_home_steady"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_f7f7f8"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <!-- 头部搜索   -->

    <com.ybmmarket20.view.homesteady.HomeSteadySearchView
        android:id="@+id/hssv_search"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:paddingTop="@dimen/dimen_dp_7"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/v_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_7"
        android:background="@color/white"
        app:layout_constraintTop_toBottomOf="@+id/hssv_search" />

    <com.ybm.app.view.CommonRecyclerView
        android:id="@+id/crv_refresh_common"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/v_divider"
        app:layout_constraintVertical_weight="1" />

    <com.ybmmarket20.view.DialImageView
        android:id="@+id/iv_dial_suspension"
        android:layout_width="114dp"
        android:layout_height="76dp"
        android:layout_marginBottom="90dp"
        android:src="@drawable/transparent"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <ImageView
        android:id="@+id/iv_ad_suspension"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginBottom="110dp"
        android:src="@drawable/icon_ad_suspension"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:visibility="gone" />

    <LinearLayout
        android:id="@+id/ll_mask"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>