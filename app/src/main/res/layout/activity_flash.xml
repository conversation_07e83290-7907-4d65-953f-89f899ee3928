<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/constraint_layout"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/flash_bg_new"
    android:fitsSystemWindows="true">

    <Button
        android:id="@+id/btn_skip"
        android:layout_width="80dp"
        android:layout_height="30dp"
        android:layout_marginTop="35dp"
        android:layout_marginRight="25dp"
        android:background="@drawable/shape_count_down_bg"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:visibility="gone"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tool:text="跳过 3" />

</androidx.constraintlayout.widget.ConstraintLayout>
