<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/dimen_dp_5">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_goods_coupon_bg_left"
        android:layout_width="@dimen/dimen_dp_73"
        android:layout_height="87dp"
        android:background="@drawable/icon_fragment_coustom_coupon_dialog_left_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/ll_coupon_amount"
            android:layout_width="wrap_content"
            android:layout_height="31dp"
            android:layout_marginTop="@dimen/dimen_dp_18"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.4"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_PriceUnit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_FF2121"
                android:textSize="@dimen/dimen_dp_10"
                tools:text="¥" />

            <TextView
                android:id="@+id/tv_coupon_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_FF2121"
                android:textSize="@dimen/dimen_dp_23"
                tools:text="2000" />

            <TextView
                android:id="@+id/tv_discount_unit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_ff4244"
                android:textSize="@dimen/dimen_dp_15"
                android:visibility="gone"
                tools:text="折" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_coupon_full_reduce"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:textColor="@color/color_FF2121"
            android:textSize="@dimen/dimen_dp_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ll_coupon_amount"
            tools:text="满200减20" />

        <TextView
            android:id="@+id/tv_coupon_full_reduce_max"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:textColor="@color/color_FF2121"
            android:textSize="@dimen/dimen_dp_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_coupon_full_reduce"
            tools:text="最高可减80" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_goods_coupon_bg_right"
        android:layout_width="0dp"
        android:layout_height="87dp"
        android:background="@drawable/icon_fragment_coustom_coupon_dialog_right_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/cl_goods_coupon_bg_left"
        app:layout_constraintTop_toTopOf="@+id/cl_goods_coupon_bg_left">

        <TextView
            android:id="@+id/tv_coupon_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginTop="@dimen/dimen_dp_6"
            android:layout_marginEnd="@dimen/dimen_dp_10"
            android:textColor="@color/text_292933"
            android:textSize="@dimen/dimen_dp_10"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="券名称券名称券名称券名称1234567券名称券名称券名称券名称1干什么呢券名称券名称券名称券名称1234567" />

        <TextView
            android:id="@+id/tv_coupon_limit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="使用时间:"
            android:textColor="@color/text_676773"
            android:textSize="10dp"
            app:layout_constraintBottom_toTopOf="@+id/tv_coupon_date"
            app:layout_constraintStart_toStartOf="@+id/tv_coupon_title" />

        <TextView
            android:id="@+id/tv_coupon_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginBottom="7dp"
            android:textColor="@color/text_676773"
            android:textSize="@dimen/dimen_dp_10"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="2019/11/11-2019/12/11" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_coupon_immediate_use"
            android:layout_width="57dp"
            android:layout_height="20dp"
            android:layout_marginTop="3dp"
            android:layout_marginEnd="@dimen/dimen_dp_4"
            android:background="@drawable/shape_goods_coupon_immediate_use"
            android:gravity="center"
            android:text="@string/immediate_use"
            android:textColor="@color/color_FF2121"
            android:textSize="@dimen/dimen_dp_10"
            android:layout_marginBottom="26dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_coupon_title"
            app:rv_cornerRadius="14dp"
            app:rv_strokeColor="@color/color_FF2121"
            app:rv_strokeWidth="0.5dp"/>

        <TextView
            android:id="@+id/tv_coupon_get_it_now"
            android:layout_width="57dp"
            android:layout_height="20dp"
            android:layout_marginTop="3dp"
            android:layout_marginBottom="26dp"
            android:layout_marginEnd="@dimen/dimen_dp_4"
            android:background="@drawable/shape_goods_coupon_immediate_use"
            android:gravity="center"
            android:text="立即领取"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_10"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_coupon_title" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>