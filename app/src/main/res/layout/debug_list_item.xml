<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/selector_btn_text"
    android:orientation="vertical"
    android:paddingLeft="4dp"
    android:paddingTop="1dp"
    android:paddingBottom="1dp">

    <TextView
        android:id="@+id/time"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxLines="2" />

    <TextView
        android:id="@+id/url"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxLines="2"
        android:text="" />

    <TextView
        android:id="@+id/param"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:singleLine="true"
        android:text="" />

    <TextView
        android:id="@+id/response"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:singleLine="true" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/gray_gray"
        android:paddingLeft="4dp" />
</LinearLayout>
