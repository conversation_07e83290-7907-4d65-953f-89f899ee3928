<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_height="match_parent"
    android:layout_width="wrap_content"
    >
    <com.xyy.common.widget.RoundLinearLayout
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        android:layout_centerInParent="true"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="10dp"
        android:paddingRight="5dp"
        app:rv_cornerRadius="2dp"
        app:rv_strokeColor="@color/color_d3d3d3"
        app:rv_strokeWidth="@dimen/dp1">

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="5dp"
            android:textColor="@color/text_color_333333"
            android:textSize="14sp"
            tools:text="张三" />

        <ImageView
            android:id="@+id/iv_delete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_personal_delete" />
    </com.xyy.common.widget.RoundLinearLayout>
</RelativeLayout>
