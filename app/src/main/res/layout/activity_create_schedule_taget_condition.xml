<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:orientation="vertical">

        <TextView
            android:id="@+id/textView1"
            android:layout_width="match_parent"
            android:layout_height="34dp"
            android:background="@color/color_gray_EFEFF4"
            android:gravity="center_vertical"
            android:paddingLeft="15dp"
            android:text="必填项" />

        <View
            android:id="@+id/view_top_line"
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:background="@color/color_gray_EFEFF4" />

        <com.ybm100.app.crm.widget.SimpleItemView
            android:id="@+id/siv_is_medical_safeguard"
            android:layout_width="match_parent"
            android:layout_height="@dimen/schedule_item_height"
            app:left_text_content="是否可用医保"
            app:right_edit_editable="false"
            app:right_edit_hint="请选择" />

        <View style="@style/schedule_line" />


        <com.ybm100.app.crm.widget.SimpleItemView
            android:id="@+id/siv_drugstore_type"
            android:layout_width="match_parent"
            android:layout_height="@dimen/schedule_item_height"
            app:left_text_content="客户类型"
            app:right_edit_editable="false"
            android:visibility="gone"
            app:right_edit_hint="请选择" />

        <View style="@style/schedule_line" />

        <com.ybm100.app.crm.widget.SimpleItemView
            android:id="@+id/siv_drugstore_area"
            android:layout_width="match_parent"
            android:layout_height="@dimen/schedule_item_height"
            app:left_text_content="客户面积(平方米)"
            app:right_edit_hint="请输入"
            app:right_edit_inputType="numberDecimal"
            app:right_edit_max_lengh="12" />

        <View style="@style/schedule_line" />


        <com.ybm100.app.crm.widget.SimpleItemView
            android:id="@+id/siv_num_of_person"
            android:layout_width="match_parent"
            android:layout_height="@dimen/schedule_item_height"
            app:left_text_content="客户成员数(人)"
            app:right_edit_hint="请输入"
            app:right_edit_inputType="number"
            app:right_edit_max_lengh="3" />

        <View style="@style/schedule_line" />

        <com.ybm100.app.crm.widget.SimpleItemView
            android:id="@+id/siv_surrounding"
            android:layout_width="match_parent"
            android:layout_height="@dimen/schedule_item_height"
            app:left_text_content="周边环境"
            app:right_edit_editable="false"
            app:right_edit_hint="请选择" />

        <View style="@style/schedule_line" />

        <com.ybm100.app.crm.widget.SimpleItemView
            android:id="@+id/siv_consumer_group"
            android:layout_width="match_parent"
            android:layout_height="@dimen/schedule_item_height"
            app:left_text_content="主要消费人群"
            app:right_edit_editable="false"
            app:right_edit_hint="请选择" />

        <View style="@style/schedule_line" />

        <com.ybm100.app.crm.widget.SimpleItemView
            android:id="@+id/siv_consumer_num"
            android:layout_width="match_parent"
            android:layout_height="@dimen/schedule_item_height"
            app:left_text_content="客户流量(人/天)"
            app:right_edit_editable="false"
            app:right_edit_hint="请选择" />

        <View style="@style/schedule_line" />

        <com.ybm100.app.crm.widget.SimpleItemView
            android:id="@+id/siv_sku_num"
            android:layout_width="match_parent"
            android:layout_height="@dimen/schedule_item_height"
            app:left_text_content="客户需求SKU数"
            app:right_edit_editable="false"
            app:right_edit_hint="请选择" />

        <View style="@style/schedule_line" />

        <com.ybm100.app.crm.widget.SimpleItemView
            android:id="@+id/siv_main_structure"
            android:layout_width="match_parent"
            android:layout_height="@dimen/schedule_item_height"
            app:left_text_content="主要消费结构"
            app:right_edit_editable="false"
            app:right_edit_hint="请选择" />

        <View style="@style/schedule_line" />

        <com.ybm100.app.crm.widget.SimpleItemView
            android:id="@+id/siv_need_move"
            android:layout_width="match_parent"
            android:layout_height="@dimen/schedule_item_height"
            app:left_text_content="是否需要动销"
            app:right_edit_editable="false"
            app:right_edit_hint="请选择" />

        <View style="@style/schedule_line" />

        <com.ybm100.app.crm.widget.SimpleItemView
            android:id="@+id/siv_need_department"
            android:layout_width="match_parent"
            android:layout_height="@dimen/schedule_item_height"
            app:left_text_content="是否需要门店诊断"
            app:right_edit_editable="false"
            app:right_edit_hint="请选择" />

        <View style="@style/schedule_line" />

        <com.ybm100.app.crm.widget.SimpleItemView
            android:id="@+id/siv_need_training"
            android:layout_width="match_parent"
            android:layout_height="@dimen/schedule_item_height"
            app:left_text_content="是否需要店员培训"
            app:right_edit_editable="false"
            app:right_edit_hint="请选择" />

        <View style="@style/schedule_line" />

        <com.ybm100.app.crm.widget.SimpleItemView
            android:id="@+id/siv_month_sales_volume"
            android:layout_width="match_parent"
            android:layout_height="@dimen/schedule_item_height"
            app:left_text_content="月销售额(元)"
            app:right_edit_hint="请输入"
            app:right_edit_inputType="numberDecimal"
            app:right_edit_max_lengh="12" />

        <TextView
            android:id="@+id/textView2"
            android:layout_width="match_parent"
            android:layout_height="39dp"
            android:background="@color/bg_gray"
            android:paddingLeft="15dp"
            android:paddingTop="15dp"
            android:text="非必填项"
            android:textColor="#ff8e8e93"
            android:textSize="13sp" />

        <View style="@style/schedule_line" />

        <com.ybm100.app.crm.widget.SimpleItemView
            android:id="@+id/siv_missing"
            android:layout_width="match_parent"
            android:layout_height="@dimen/schedule_item_height"
            app:left_text_content="缺失品种"
            app:right_edit_editable="false"
            app:right_edit_hint="请输入" />

        <View style="@style/schedule_line" />

        <com.ybm100.app.crm.widget.SimpleItemView
            android:id="@+id/siv_core_suppliers"
            android:layout_width="match_parent"
            android:layout_height="@dimen/schedule_item_height"
            app:left_text_content="核心供应商"
            app:right_edit_editable="false"
            app:right_edit_hint="请输入" />

        <View style="@style/schedule_line" />

        <com.ybm100.app.crm.widget.SimpleItemView
            android:id="@+id/siv_customer_demand"
            android:layout_width="match_parent"
            android:layout_height="@dimen/schedule_item_height"
            app:left_text_content="客户需求"
            app:right_edit_editable="false"
            app:right_edit_hint="请输入" />

        <View
            style="@style/schedule_divide"
            android:layout_height="32dp" />

    </LinearLayout>
</ScrollView>