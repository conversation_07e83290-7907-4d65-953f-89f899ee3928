<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rl_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/ly_search"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <!--android:id="@+id/ll_title" id不要随便换BaseActivity会用到-->
        <LinearLayout
            android:id="@+id/ll_title"
            android:layout_width="match_parent"
            android:layout_height="@dimen/header_height"
            android:background="@drawable/base_header_default_bg"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingTop="@dimen/header_height_padding_top"
            android:paddingBottom="3dp">

            <ImageView
                android:id="@+id/title_left_search"
                android:layout_width="54dp"
                android:layout_height="25dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_back" />

            <RelativeLayout
                android:id="@+id/rel_search"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="4dp"
                android:layout_weight="1"
                android:focusableInTouchMode="true"
                android:focusable="true"
                android:background="@drawable/search_round_corner_gray_bg">

                <EditText
                    android:id="@+id/title_et"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="10dp"
                    android:layout_toLeftOf="@+id/iv_clear"
                    android:background="@null"
                    android:hint="@string/search_hint"
                    android:imeOptions="actionSearch"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="#292933"
                    android:textCursorDrawable="@drawable/color_cursor"
                    android:textSize="14sp" />

                <ImageView
                    android:id="@+id/iv_clear"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="10dp"
                    android:src="@drawable/clear_sousou"
                    android:visibility="invisible" />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="54dp"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical">

                <TextView
                    android:id="@+id/brand_browse"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:button="@null"
                    android:drawableTop="@drawable/browse_tab_selector"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:text="大图"
                    android:textColor="@color/brand_name_tv1"
                    android:textSize="@dimen/sign_tv_06"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/title_right_btn"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    android:text="搜索"
                    android:textColor="@color/text_292933" />

            </RelativeLayout>
        </LinearLayout>

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:id="@+id/brand_ctl"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/appbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:elevation="0dp">

                <!--筛选-->
                <LinearLayout
                    android:id="@+id/ll_filter"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_scrollFlags="scroll|enterAlways">

                    <RadioGroup
                        android:id="@+id/brand_rg_01"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:background="@drawable/base_header_default_bg"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_default"
                            style="@style/drug_list_tv_05"
                            android:layout_width="wrap_content"
                            android:layout_marginLeft="15dp"
                            android:layout_marginRight="0dp"
                            android:layout_weight="0"
                            android:drawableRight="@null"
                            android:drawablePadding="2dp"
                            android:paddingLeft="6dp"
                            android:paddingRight="6dp"
                            android:text="默认" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1" />

                        <TextView
                            android:id="@+id/rb_all_category"
                            style="@style/drug_list_tv_05"
                            android:layout_width="wrap_content"
                            android:layout_marginLeft="0dp"
                            android:layout_marginRight="0dp"
                            android:layout_weight="0"
                            android:drawablePadding="2dp"
                            android:maxLength="6"
                            android:paddingLeft="6dp"
                            android:paddingRight="6dp"
                            android:text="分类" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1" />

                        <TextView
                            android:id="@+id/tv_price"
                            style="@style/drug_list_tv_05"
                            android:layout_width="wrap_content"
                            android:layout_marginLeft="0dp"
                            android:layout_marginRight="0dp"
                            android:layout_weight="0"
                            android:drawableRight="@drawable/price_def"
                            android:drawablePadding="2dp"
                            android:paddingLeft="6dp"
                            android:paddingRight="6dp"
                            android:text="价格" />

                        <TextView
                            android:id="@+id/rb_brand_rg_01_new"
                            style="@style/simple_filter_btn_item2"
                            android:layout_width="wrap_content"
                            android:layout_weight="0"
                            android:gravity="center"
                            android:paddingLeft="6dp"
                            android:paddingRight="6dp"
                            android:layout_margin="@dimen/dimen_dp_0"
                            android:layout_gravity="center_vertical"
                            android:textSize="@dimen/brand_rb"
                            android:text="最新" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1" />

                        <TextView
                            android:id="@+id/tv_manufacturer"
                            style="@style/drug_list_tv_05"
                            android:layout_width="wrap_content"
                            android:layout_marginLeft="0dp"
                            android:layout_marginRight="0dp"
                            android:layout_weight="0"
                            android:drawablePadding="2dp"
                            android:paddingLeft="6dp"
                            android:paddingRight="6dp"
                            android:text="厂家" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1" />

                        <TextView
                            android:id="@+id/tv_shop"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginLeft="0dp"
                            android:layout_marginTop="4dp"
                            android:layout_marginRight="15dp"
                            android:layout_marginBottom="4dp"
                            android:layout_weight="0"
                            android:drawableRight="@drawable/selector_filter_icon"
                            android:drawablePadding="2dp"
                            android:gravity="center"
                            android:paddingLeft="6dp"
                            android:paddingRight="6dp"
                            android:text="@string/brand_manufacturers_filtrate"
                            android:textColor="@color/selector_text_color_category_default"
                            android:textSize="@dimen/brand_rb_01" />

                    </RadioGroup>

                    <RadioGroup
                        android:id="@+id/brand_rg_02"
                        android:layout_width="match_parent"
                        android:layout_height="36dp"
                        android:background="@drawable/bg_brand_rb_01"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:visibility="visible">

                        <TextView
                            android:id="@+id/rb_new"
                            style="@style/simple_filter_btn_item2"
                            android:layout_width="0dp"
                            android:layout_marginLeft="15dp"
                            android:layout_weight="3"
                            android:gravity="center"
                            android:text="最新" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1" />

                        <TextView
                            android:id="@+id/rb_sales"
                            style="@style/simple_filter_btn_item2"
                            android:layout_width="0dp"
                            android:layout_weight="3"
                            android:text="销量" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1" />

                        <TextView
                            android:id="@+id/rb_available"
                            style="@style/simple_filter_btn_item2"
                            android:layout_width="0dp"
                            android:layout_weight="3"
                            android:text="有货" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1" />

                        <TextView
                            android:id="@+id/rb_promotion"
                            style="@style/simple_filter_btn_item2"
                            android:layout_width="0dp"
                            android:layout_weight="3"
                            android:text="促销" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1" />

                        <TextView
                            android:id="@+id/rb_self_support"
                            style="@style/simple_filter_btn_item2"
                            android:layout_width="0dp"
                            android:layout_marginRight="10dp"
                            android:layout_weight="3"
                            android:text="自营" />

                        <View
                            android:layout_width="0.5dp"
                            android:layout_height="match_parent"
                            android:background="#eeeeee" />

                    </RadioGroup>

                </LinearLayout>
            </com.google.android.material.appbar.AppBarLayout>

            <!--历史搜索记录-->
            <com.ybmmarket20.view.MyScrollView
                android:id="@+id/msv_tag"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone">

                <LinearLayout
                    android:id="@+id/ll_tag"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/ll_history"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="35dp"
                                android:layout_marginTop="6dp"
                                android:layout_weight="1"
                                android:gravity="center_vertical"
                                android:paddingLeft="16dp"
                                android:text="历史搜索"
                                android:textColor="@color/color_history_search"
                                android:textSize="15sp"
                                android:textStyle="bold" />

                            <ImageView
                                android:layout_width="14dp"
                                android:layout_height="14dp"
                                android:layout_marginTop="6dp"
                                android:layout_gravity="center_vertical"
                                android:src="@drawable/icon_clean" />

                            <TextView
                                android:id="@+id/tv_history"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginTop="6dp"
                                android:layout_marginRight="14dp"
                                android:gravity="center"
                                android:paddingLeft="6dp"
                                android:paddingRight="6dp"
                                android:text="@string/del_empty"
                                android:textColor="#9494A6"
                                android:textSize="@dimen/dimen_dp_12" />
                        </LinearLayout>

                        <com.ybmmarket20.view.TagGroup
                            android:id="@+id/tag_history"
                            style="@style/TagGroup_History"
                            android:layout_height="wrap_content"
                            android:background="@android:color/white"
                            android:paddingLeft="16dp"
                            android:paddingRight="3dp"
                            app:atg_maxHeight="100dp" />

                        <com.ybmmarket20.view.taggroupview.TagContainerLayout
                            android:id="@+id/tagcontainerLayout1"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_margin="0dp"
                            android:background="@android:color/white"
                            android:paddingLeft="20dp"
                            android:paddingRight="3dp"
                            app:horizontal_interval="8dp"
                            app:tag_background_color="#F7F7F8"
                            app:tag_clickable="true"
                            app:tag_corner_radius="2dp"
                            app:tag_enable_cross="true"
                            app:tag_text_color="#292933"
                            app:vertical_interval="8dp" />

                    </LinearLayout>

                    <RelativeLayout
                        android:id="@+id/rl_see_more_history"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="15dp"
                        android:layout_marginBottom="15dp"
                        android:gravity="center_horizontal">

                        <TextView
                            android:id="@+id/tv_see_more_history"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:text="@string/see_more_history_record"
                            android:textColor="#9494A6"
                            android:textSize="@dimen/dimen_dp_12" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_toRightOf="@+id/tv_see_more_history"
                            android:src="@drawable/icon_next" />

                    </RelativeLayout>

                    <View
                        android:id="@+id/view_line"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginLeft="16dp"
                        android:layout_marginRight="16dp"
                        android:background="#F5F5F5" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="35dp"
                        android:layout_marginTop="6dp"
                        android:gravity="center_vertical"
                        android:paddingLeft="16dp"
                        android:text="推荐搜索"
                        android:textColor="@color/color_history_search"
                        android:textSize="15sp" />

                    <com.ybmmarket20.view.TagGroup
                        android:id="@+id/tag_recommend"
                        style="@style/TagGroup_History"
                        android:background="@android:color/white"
                        android:paddingLeft="16dp"
                        android:paddingRight="3dp"
                        app:atg_backgroundColor="#F7F7F8"
                        app:atg_maxHeight="74dp"
                        app:atg_radius="2dp"
                        app:atg_textColor="#292933" />

                </LinearLayout>
            </com.ybmmarket20.view.MyScrollView>

            <!-- <com.ybmmarket20.common.statusview.StatusViewLayout
                 android:id="@+id/status_view_layout"
                 android:layout_width="match_parent"
                 android:layout_height="match_parent"
                 >-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                app:layout_behavior="@string/appbar_scrolling_view_behavior">

                <com.ybmmarket20.view.SearchGuidKeyLayout
                    android:id="@+id/search_guid_layout_by_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"/>

                <com.ybm.app.view.CommonRecyclerView
                    android:id="@+id/search_product_list_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/activity_bg"
                    android:descendantFocusability="blocksDescendants" />
            </LinearLayout>

            <!--      </com.ybmmarket20.common.statusview.StatusViewLayout>-->
        </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </LinearLayout>

    <!--悬浮购物车-->
    <RelativeLayout
        android:id="@+id/rl_cart"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/iv_fast_scroll_search"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_marginBottom="10dp"
        android:visibility="invisible"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/iv_cart"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:src="@drawable/icon_float_car" />

        <TextView
            android:id="@+id/tv_num"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="8dp"
            android:layout_marginRight="8dp"
            android:layout_marginLeft="37dp"
            android:layout_marginStart="37dp"
            android:background="@drawable/bg_message"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="10sp"
            android:visibility="gone"
            tools:visibility="visible"/>

    </RelativeLayout>

    <!--置顶按钮-->
    <com.ybmmarket20.view.MyFastScrollView
        android:id="@+id/iv_fast_scroll_search"
        android:layout_width="53dp"
        android:layout_height="53dp"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_marginRight="9dp"
        android:layout_marginBottom="60dp" />

</RelativeLayout>