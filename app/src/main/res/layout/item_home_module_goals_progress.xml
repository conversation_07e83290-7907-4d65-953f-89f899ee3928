<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="10dp"
    android:background="@color/color_EEFDF2"
    android:padding="10dp">

    <TextView
        android:id="@+id/tv_goal_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:text="实收GMV实收GMV实收GMV实收GMV实收GMV实收GMV实收GMV实收GMV实收GMV"
        android:textColor="@color/color_292933"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_goal_progress_value"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:textColor="@color/color_292933"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_goal_name"
        tools:text="9898/56569898/56569898/56569898/56569898/56569898/56569898/56569898/56569898/5656" />

    <TextView
        android:id="@+id/tv_progress_bar_value"
        android:layout_width="50dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp"
        android:ellipsize="end"
        android:gravity="start"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:textColor="@color/color_676773"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_goal_progress_value"
        tools:text="dasdsasasa%" />

    <ProgressBar
        android:id="@+id/progress_bar"
        style="@style/Widget.AppCompat.ProgressBar.Horizontal"
        android:layout_width="0dp"
        android:layout_height="8dp"
        android:layout_marginEnd="6dp"
        android:max="100"
        app:layout_constraintBottom_toBottomOf="@+id/tv_progress_bar_value"
        app:layout_constraintEnd_toStartOf="@+id/tv_progress_bar_value"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_progress_bar_value" />

    <TextView
        android:id="@+id/tv_goal_progress_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/color_676773"
        android:textSize="12sp"
        android:includeFontPadding="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_progress_bar_value"
        tools:text="本月实收GMV/本月实收GMV本月实收GMV/本月实收GMV本月实收GMV/本月实收GMV本月实收GMV/本月实收GMV" />

</androidx.constraintlayout.widget.ConstraintLayout>