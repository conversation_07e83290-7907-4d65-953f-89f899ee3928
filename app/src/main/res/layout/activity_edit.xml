<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_gray_EFEFF4">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:background="@color/white"
        android:paddingBottom="10dp">

        <EditText
            android:id="@+id/et_explain"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:gravity="start|top"
            android:hint="请输入备注"
            android:maxLength="500"
            android:minHeight="202dp"
            android:padding="10dp"
            android:textColor="@color/text_color_333333"
            android:textColorHint="@color/text_color_999999"
            android:textSize="@dimen/sample_text_size"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_record_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginEnd="10dp"
            android:layout_marginBottom="10dp"
            android:text="0/200"
            android:textColor="@color/text_color_8E8E93"
            android:textSize="15sp"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/et_explain" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.ybm100.app.crm.widget.SwitchEditInputLayout
        android:id="@+id/switchEditInputLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true" />

</RelativeLayout>