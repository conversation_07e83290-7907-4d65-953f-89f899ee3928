<?xml version="1.0" encoding="utf-8"?>
<com.xyy.common.widget.statusview.StatusViewLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/status_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_gray_EFEFF4"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <!--日程标题-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white">

                <ImageView
                    android:id="@+id/iv_schedule_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp15"
                    android:src="@drawable/icon_schedule_type1"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_schedule_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_marginTop="@dimen/dp15"
                    android:layout_marginEnd="@dimen/dp10"
                    android:textColor="@color/text_color_333333"
                    android:textSize="17sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/iv_schedule_type"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="上门拜访健康爱大药房健康仁爱大药房健康爱大药房健康仁爱大药房健康仁爱大药房健康仁爱大药房" />

                <TextView
                    android:id="@+id/tv_schedule_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_marginTop="@dimen/dp5"
                    android:textColor="@color/color_gray_8E8E93"
                    android:textSize="12sp"
                    app:layout_constraintStart_toEndOf="@+id/iv_schedule_type"
                    app:layout_constraintTop_toBottomOf="@+id/tv_schedule_name"
                    tools:text="电话拜访" />

                <TextView
                    android:id="@+id/tv_schedule_accompany_people"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_marginTop="@dimen/dp5"
                    android:textColor="@color/color_gray_8E8E93"
                    android:textSize="12sp"
                    app:layout_constraintStart_toEndOf="@+id/iv_schedule_type"
                    app:layout_constraintTop_toBottomOf="@+id/tv_schedule_type"
                    tools:text="陪访BD：张磊" />

                <TextView
                    android:id="@+id/tv_schedule_people"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_marginTop="@dimen/dp5"
                    android:textColor="@color/color_gray_8E8E93"
                    android:textSize="12sp"
                    app:layout_constraintStart_toEndOf="@+id/iv_schedule_type"
                    app:layout_constraintTop_toBottomOf="@+id/tv_schedule_accompany_people"
                    tools:text="BD:张三" />

                <TextView
                    android:id="@+id/tv_schedule_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_marginTop="@dimen/dp5"
                    android:layout_marginBottom="@dimen/dp15"
                    android:textColor="@color/color_gray_8E8E93"
                    android:textSize="12sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/iv_schedule_type"
                    app:layout_constraintTop_toBottomOf="@+id/tv_schedule_people"
                    tools:text="2018.11.11 13:30" />

                <ImageView
                    android:id="@+id/iv_schedule_effective"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp8"
                    android:layout_marginBottom="@dimen/dp10"
                    android:src="@drawable/ic_effective_schedule"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    tools:visibility="visible" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!--日程信息-->
            <com.xyy.common.widget.RoundLinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp10"
                android:orientation="vertical"
                app:rv_backgroundColor="@color/white"
                app:rv_cornerRadius="5dp">

                <TextView
                    style="@style/simple_text_view_black"
                    android:layout_marginStart="@dimen/normal_margin"
                    android:layout_marginTop="@dimen/normal_margin"
                    android:layout_marginBottom="@dimen/dp10"
                    android:text="拜访信息"
                    android:textColor="@color/text_color_333333"
                    android:textSize="17sp" />

                <LinearLayout
                    android:id="@+id/layout_schedule_remind"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/normal_margin"
                    android:paddingRight="@dimen/normal_margin"
                    android:visibility="gone">

                    <TextView
                        style="@style/detail_left"
                        android:layout_width="145dp"
                        android:text="提醒时间" />

                    <TextView
                        android:id="@+id/tv_schedule_remind"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingTop="5dp"
                        android:paddingBottom="5dp"
                        android:textColor="@color/text_color_333333"
                        android:textSize="15sp"
                        tools:text="2018.11.11 15:12" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/normal_margin"
                    android:paddingRight="@dimen/normal_margin">

                    <TextView
                        style="@style/detail_left"
                        android:layout_width="145dp"
                        android:text="关联任务" />

                    <TextView
                        android:id="@+id/tv_schedule_return"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingTop="5dp"
                        android:paddingBottom="5dp"
                        android:textColor="@color/text_color_333333"
                        android:textSize="15sp"
                        tools:text="老顾客回访" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_schedule_contact_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/normal_margin"
                    android:paddingRight="@dimen/normal_margin">

                    <TextView
                        style="@style/detail_left"
                        android:layout_width="145dp"
                        android:text="联系人" />

                    <TextView
                        android:id="@+id/tv_schedule_contact_name"
                        style="@style/detail_right"
                        android:layout_width="wrap_content"
                        tools:text="望京爱民诊所店名可" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layout_schedule_contact_way"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/normal_margin"
                    android:paddingRight="@dimen/normal_margin">

                    <TextView
                        style="@style/detail_left"
                        android:layout_width="145dp"
                        android:text="联系电话" />

                    <TextView
                        android:id="@+id/tv_schedule_contact_way"
                        style="@style/detail_right"
                        android:layout_width="wrap_content"
                        tools:text="19911223344" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layout_schedule_contact_KP"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/normal_margin"
                    android:paddingRight="@dimen/normal_margin">

                    <TextView
                        style="@style/detail_left"
                        android:layout_width="145dp"
                        android:text="是否KP" />

                    <TextView
                        android:id="@+id/tv_schedule_contact_KP"
                        style="@style/detail_right"
                        android:layout_width="wrap_content"
                        tools:text="是" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layout_schedule_callLog_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/normal_margin"
                    android:paddingRight="@dimen/normal_margin"
                    android:visibility="gone">

                    <TextView
                        style="@style/detail_left"
                        android:layout_width="145dp"
                        android:text="系统拨打电话时长" />

                    <TextView
                        android:id="@+id/tv_schedule_callLog_time"
                        style="@style/detail_right"
                        android:layout_width="wrap_content"
                        tools:text="5分10秒" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layout_schedule_contact_visit_reason"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/normal_margin"
                    android:paddingRight="@dimen/normal_margin">

                    <TextView
                        style="@style/detail_left"
                        android:layout_width="145dp"
                        android:text="拜访目的" />

                    <TextView
                        android:id="@+id/tv_schedule_contact_visit_reason"
                        style="@style/detail_right"
                        android:layout_width="wrap_content"
                        tools:text="望京爱民诊所店名可" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/normal_margin"
                    android:paddingRight="@dimen/normal_margin">

                    <TextView
                        style="@style/detail_left"
                        android:layout_width="145dp"
                        android:text="拜访总结" />

                    <TextView
                        android:id="@+id/tv_schedule_remark"
                        style="@style/detail_right"
                        android:layout_width="wrap_content"
                        tools:text="资质已回收，活动已介绍" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="vertical"
                    android:paddingLeft="@dimen/normal_margin"
                    android:paddingRight="@dimen/normal_margin">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:layout_marginBottom="10dp" />

                    <TextView
                        android:id="@+id/tv_schedule_address"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="10dp"
                        android:drawableLeft="@drawable/ic_address_icon_gray"
                        android:drawablePadding="3dp"
                        android:textColor="@color/text_color_8E8E93"
                        android:textSize="14sp"
                        tools:text="湖北省武汉市江夏区金融港三路A2座" />

                </LinearLayout>

            </com.xyy.common.widget.RoundLinearLayout>

            <!--对象经营状况-->
            <com.xyy.common.widget.RoundLinearLayout
                android:id="@+id/layout_merchantBasicInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:layout_marginBottom="10dp"
                android:orientation="vertical"
                app:rv_backgroundColor="@color/white"
                app:rv_cornerRadius="5dp">

                <include layout="@layout/layout_detail_list" />

            </com.xyy.common.widget.RoundLinearLayout>
        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</com.xyy.common.widget.statusview.StatusViewLayout>