<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:paddingLeft="@dimen/dp10"
        android:paddingTop="@dimen/dp7"
        android:paddingRight="@dimen/dp10"
        android:paddingBottom="@dimen/dp7">

        <com.ybm100.app.crm.widget.searchview.SearchView
            android:id="@+id/sv_contact_search"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:transitionName="@string/share_search_bar"
            app:textHintSearch="搜索"
            app:textSizeSearch="@dimen/dp14" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/fl_contact_search"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</LinearLayout>