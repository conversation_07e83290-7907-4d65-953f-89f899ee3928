<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/ll_title"
        style="@style/header_layout">

        <ImageView
            android:id="@+id/iv_back"
            style="@style/header_layout_left"
            android:src="@drawable/ic_back" />

        <Space
            android:id="@+id/headerSpace"
            android:layout_width="@dimen/dimen_dp_10"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_toRightOf="@+id/iv_back"/>

        <ImageView
            android:id="@+id/iv_close"
            style="@style/header_layout_left"
            android:layout_toRightOf="@+id/headerSpace"
            android:minWidth="0dp"
            android:src="@drawable/icon_seckill_grey_close"
            tools:visibility="gone" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@id/iv_share"
            android:layout_toRightOf="@id/iv_close"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_title"
                style="@style/header_layout_mid"
                android:layout_centerInParent="true"
                android:layout_marginLeft="0dp"
                android:layout_marginRight="0dp"
                android:ellipsize="end"
                android:maxLines="1"
                tools:text="这是h5标题这是h5标题这是h5标题这是h5标题这是h5标题" />

            <LinearLayout
                android:id="@+id/ll_control_pin_mall"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="gone">

                <ImageView
                    android:id="@+id/iv_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/huomaimaimall" />

                <TextView
                    android:id="@+id/tv_share"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="7.5dp"
                    android:background="@drawable/bg_common_h5_tv_share"
                    android:drawableLeft="@drawable/icon_control_pin_mall_share"
                    android:drawablePadding="1.5dp"
                    android:paddingLeft="8dp"
                    android:paddingTop="2.5dp"
                    android:paddingRight="8dp"
                    android:paddingBottom="2.5dp"
                    android:text="分享"
                    android:textColor="@color/white"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_search_two"
                android:layout_width="match_parent"
                android:layout_height="34dp"
                android:layout_centerVertical="true"
                android:background="@drawable/search_round_corner_gray_bg_03"
                android:orientation="vertical"
                android:paddingLeft="6dp"
                android:paddingRight="6dp"
                android:visibility="gone"
                tools:visibility="gone">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical">

                    <ImageView
                        android:id="@+id/iv_a_magnifying_glass"
                        android:layout_width="22dp"
                        android:layout_height="22dp"
                        android:layout_centerVertical="true"
                        android:src="@drawable/icon_a_magnifying_glass" />

                    <TextView
                        android:id="@+id/title_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="3dp"
                        android:layout_toRightOf="@+id/iv_a_magnifying_glass"
                        android:background="@null"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:text="@string/search_hint"
                        android:textColor="@color/color_9494A6"
                        android:textSize="13dp" />

                    <ImageView
                        android:id="@+id/iv_voice_two"
                        android:layout_width="22dp"
                        android:layout_height="22dp"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:src="@drawable/nav_voice_01" />

                </RelativeLayout>
            </LinearLayout>
        </RelativeLayout>

        <ImageView
            android:id="@+id/iv_share"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@+id/iv_search"
            android:src="@drawable/icon_share_menu"
            android:visibility="gone"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/iv_search"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="54dp"
            android:layout_toLeftOf="@+id/rl_cart"
            android:src="@drawable/icon_a_magnifying_glass"
            tools:visibility="gone" />

        <RelativeLayout
            android:id="@+id/rl_cart"
            style="@style/header_layout_right_img"
            android:layout_width="50dp"
            android:layout_marginStart="5dp"
            android:visibility="visible">

            <ImageView
                android:id="@+id/iv_cart"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/cart_icon"
                android:layout_centerInParent="true" />

            <TextView
                android:id="@+id/tv_num"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_alignParentRight="true"
                android:layout_marginRight="2dp"
                android:background="@drawable/bg_message"
                android:gravity="center"
                android:text=""
                android:textColor="@color/white"
                android:textSize="10sp"
                android:visibility="gone" />
        </RelativeLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_pingan_gold"
            android:layout_width="wrap_content"
            android:paddingRight="16dp"
            android:layout_alignParentRight="true"
            android:visibility="gone"
            android:layout_centerVertical="true"
            android:layout_height="wrap_content">
            <ImageView
                android:id="@+id/iv_pingan_gold"
                android:layout_width="17dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tv_pingan_gold"
                android:layout_marginEnd="3dp"
                app:layout_constraintTop_toTopOf="@id/tv_pingan_gold"
                app:layout_constraintBottom_toBottomOf="@id/tv_pingan_gold"
                android:src="@drawable/icon_pingan_gold"
                android:layout_height="17dp"/>

            <TextView
                android:id="@+id/tv_pingan_gold"
                android:layout_width="wrap_content"
                android:text="平安钱包"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:textColor="@color/text_color_333333"
                android:textSize="15dp"
                android:layout_height="wrap_content"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tv_right"
            style="@style/header_layout_right_text" />
    </RelativeLayout>

    <include layout="@layout/common_header_search_layout" />

</LinearLayout>