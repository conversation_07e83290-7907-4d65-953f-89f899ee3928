<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape android:shape="rectangle">
            <stroke
                android:width="0px"
                android:color="#F0F0F0" />
            <corners android:radius="14dp" />
            <solid android:color="#F0F0F0" />
        </shape>
    </item>
    <item android:gravity="center">
        <selector>
            <item android:state_focused="true">
                <layer-list>
                    <item>
                        <shape android:shape="rectangle">
                            <stroke
                                android:width="1dp"
                                android:color="#35C561" />
                            <corners android:radius="20dp" />
                            <solid android:color="@android:color/white" />
                        </shape>
                    </item>
                </layer-list>
            </item>
            <item android:state_selected="true">
                <layer-list>
                    <item>
                        <shape android:shape="rectangle">
                            <stroke
                                android:width="1dp"
                                android:color="#35C561" />
                            <corners android:radius="20dp" />
                            <solid android:color="@android:color/white" />
                        </shape>
                    </item>
                </layer-list>
            </item>
            <item android:state_checked="true">
                <layer-list>
                    <item>
                        <shape android:shape="rectangle">
                            <stroke
                                android:width="1dp"
                                android:color="#35C561" />
                            <corners android:radius="20dp" />
                            <solid android:color="@android:color/white" />
                        </shape>
                    </item>
                </layer-list>

            </item>
            <item android:state_pressed="true">
                <layer-list>
                    <item>
                        <shape android:shape="rectangle">
                            <stroke
                                android:width="1dp"
                                android:color="#35C561" />
                            <corners android:radius="20dp" />
                            <solid android:color="@android:color/white" />
                        </shape>
                    </item>
                </layer-list>
            </item>
            <item android:state_activated="true">
                <layer-list>
                    <item>
                        <shape android:shape="rectangle">
                            <stroke
                                android:width="1dp"
                                android:color="#35C561" />
                            <corners android:radius="20dp" />
                            <solid android:color="@android:color/white" />
                        </shape>
                    </item>
                </layer-list>
            </item>
        </selector>
    </item>

</layer-list>
