package com.ybmmarket20.more_account.data

import android.annotation.SuppressLint
import androidx.arch.core.executor.ArchTaskExecutor
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.common.util.ToastUtils
import retrofit2.HttpException
import java.lang.Exception
import java.util.concurrent.TimeoutException

/*
    TODO: NetError 统一, 增加一个错误 properity extenstion，不再复用网络返回的两个值
    9xxxx 网络错误
    8xxxx 数据库错误
    7xxxx 其他IO来源错误
 */
fun <T> BaseBean<T>.initWithException(e: Exception) = when (e) {
    is TimeoutException -> this.apply {
        this.code = 90001
        this.msg = "请求超时"
        ToastUtils.showLong(this.msg)
        showToast(this.msg)
    }
    is HttpException -> this.apply {
        this.code = 90002
        this.msg = e.cause.toString()
        showToast(this.msg)
    }
    is IllegalArgumentException -> this.apply {
        this.code = 90003
        this.msg = "请求参数错误 ${e.toString()}"
        showToast(this.msg)
    }
    else -> this.apply {
        this.code = 99999
        this.msg = "未知网络错误"
        e.printStackTrace()
        showToast(this.msg)
    }
}

/**
 * 保证线程已调用Looper.prepare()
 */
@SuppressLint("RestrictedApi")
fun <T> BaseBean<T>.showToast(msg: String) {
    ArchTaskExecutor.getMainThreadExecutor().execute {
        ToastUtils.showLong(msg)
    }
}
