package com.ybmmarket20.adapter

import android.widget.ImageView
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybmmarket20.R
import com.ybmmarket20.bean.CartCouponGoods
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.ifNotNull

/**
 * <AUTHOR>
 * 优惠券底部优惠券弹框adapter
 */
class CartBottomCouponGoodsAdapter(layoutId: Int, data: List<CartCouponGoods>?, private val selectProductListener: CartBottomCouponGoodsListener?) :
    YBMBaseAdapter<CartCouponGoods>(layoutId, data) {

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: CartCouponGoods?) {
        ifNotNull(baseViewHolder, t) { viewHolder, bean ->
            val iv = viewHolder.getView<ImageView>(R.id.iv_cart_coupon_goods)
            bean?.imageUrl?.let {
                ImageUtil.load(mContext, AppNetConfig.LORD_IMAGE + bean.imageUrl, iv)
            }
            viewHolder.setText(R.id.tv_cart_coupon_goods_price, "¥${bean.price}")
            viewHolder.setText(R.id.tv_cart_coupon_goods_count, "X${bean.amount}")
            val checkImg = viewHolder.getView<ImageView>(R.id.cb_cart_coupon_goods)
            if (bean.status == 1) {
                R.drawable.icon_cart_bottom_coupon_checked
            } else {
                R.drawable.icon_cart_bottom_coupon_uncheck
            }.also(checkImg::setImageResource)
            checkImg.setOnClickListener {
                selectProductListener?.callback(t)
            }
        }
    }

}

interface CartBottomCouponGoodsListener {
    fun callback(t: CartCouponGoods?)
}