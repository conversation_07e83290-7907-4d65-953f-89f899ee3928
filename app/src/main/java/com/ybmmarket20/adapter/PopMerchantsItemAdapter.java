package com.ybmmarket20.adapter;

import android.content.Intent;
import android.view.View;
import android.widget.ImageView;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.ProductDetailActivity;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;

import java.util.ArrayList;
import java.util.List;

public class PopMerchantsItemAdapter extends YBMBaseAdapter<RowsBean> {

    private boolean isEmpty = true;

    public void setNewData(List data, boolean isEmpty) {
        if (data == null) {
            data = new ArrayList();
        }
        this.isEmpty = isEmpty;

        try {
            super.setNewData((List) data);
        } catch (Throwable var3) {
            BugUtil.sendBug(var3);
        }

    }

    public PopMerchantsItemAdapter(List<RowsBean> list) {
        super(R.layout.item_son_pop_merchants, list);
    }

    @Override
    protected void bindItemView(YBMBaseHolder ybmBaseHolder, RowsBean rowsBean) {

        ImageHelper.with(mContext).load(AppNetConfig.LORD_IMAGE + rowsBean.getImageUrl())
                .placeholder(R.drawable.jiazaitu_min).diskCacheStrategy(DiskCacheStrategy.SOURCE).
                into(((ImageView) ybmBaseHolder.getView(R.id.iv_tag)));

        ybmBaseHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (rowsBean != null) {
                    Intent intent = new Intent(mContext, ProductDetailActivity.class);
                    intent.putExtra(IntentCanst.PRODUCTID, rowsBean.getId() + "");
                    mContext.startActivity(intent);
                }
            }
        });

    }

}
