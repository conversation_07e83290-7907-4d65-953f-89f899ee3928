package com.ybmmarket20.adapter;

import android.content.Intent;
import android.graphics.Paint;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.ProductDetailActivity;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.utils.AuditStatusSyncUtil;
import com.ybmmarket20.utils.ImageUtil;
import com.ybmmarket20.utils.StringUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.TagView;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;


/**
 * 首页秒杀商品
 */
public class SeckillProductAdapter extends YBMBaseAdapter<RowsBean> {

    private boolean isEmpty = true;
    private boolean whiteBg = false;

    public SeckillProductAdapter() {
        super(R.layout.home_seckill_product_item, new ArrayList<RowsBean>());
    }

    public SeckillProductAdapter(boolean whiteBg) {
        super(whiteBg ? R.layout.home_seckill_product_item2 : R.layout.home_seckill_product_item, new ArrayList<RowsBean>());
        this.whiteBg = whiteBg;
    }

    public void setNewData(List data, boolean isEmpty) {
        if (data == null) {
            data = new ArrayList();
        }
        this.isEmpty = isEmpty;

        try {
            super.setNewData((List) data);
        } catch (Throwable var3) {
            BugUtil.sendBug(var3);
        }

    }

    @Override
    protected void bindItemView(final YBMBaseHolder baseViewHolder, final RowsBean rowsBean) {
        baseViewHolder.getConvertView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                // 秒杀商品点击埋点
                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put("id", rowsBean.getId());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                XyyIoUtil.track(XyyIoUtil.ACTION_HOME_SECKILL_PRODUCT, jsonObject, rowsBean);

                if (!rowsBean.isStart()) {
                    ToastUtils.showShort("活动尚未开始");
                    return;
                }
                Intent intent = new Intent(mContext, ProductDetailActivity.class);
                intent.putExtra(IntentCanst.PRODUCTID, rowsBean.getProductId());
                mContext.startActivity(intent);
            }
        });
        ImageView iv_product = baseViewHolder.getView(R.id.iv_product);
        ImageView iv_tag_left = baseViewHolder.getView(R.id.iv_tag_left);
        TextView home_time_bg = baseViewHolder.getView(R.id.tv_state);

        //活动价
        boolean isShowUrl = rowsBean.isReducePrice() && rowsBean.isMarkerUrl();
        baseViewHolder.setText(R.id.tv_activity_price, String.valueOf("药采节价:" + UiUtils.transform(rowsBean.getReducePrice())));
        baseViewHolder.setGone(R.id.tv_activity_price, isShowUrl);

        ImageUtil.load(mContext, ImageUtil.getImageUrl(rowsBean.getMarkerUrl()), iv_tag_left);

        ImageHelper.with(mContext).load(AppNetConfig.LORD_IMAGE + rowsBean.getProductImg())
                .placeholder(R.drawable.jiazaitu_min).diskCacheStrategy(DiskCacheStrategy.SOURCE)
                .dontAnimate().dontTransform().into(iv_product);
        //商品区间价格-秒杀
        //baseViewHolder.setText(R.id.tv_name, rowsBean.getProductName()).setText(R.id.tv_spec, rowsBean.getProductSpec()).setText(R.id.tv_price, UiUtils.showProductPrice(rowsBean));
        baseViewHolder.setText(R.id.tv_name, rowsBean.getProductName()).setText(R.id.tv_spec, rowsBean.getProductSpec());
        //是否控销  isControl=1表示控销，=2表示不是控销
        TextView tv = baseViewHolder.getView(R.id.tv_old);
        if (rowsBean.getIsControl() == 1 && !rowsBean.isPurchase()) {
            //控销不可购买
            tv.setVisibility(View.GONE);
            baseViewHolder.setText(R.id.tv_price, "暂无购买权限");
        } else {
            //控销可购买
            tv.setVisibility(View.VISIBLE);
            tv.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG);
            tv.setText(String.valueOf(StringUtil.getUniformPrice2Double(rowsBean.getOldPrice())));
            baseViewHolder.setText(R.id.tv_price, "¥" + UiUtils.transform(rowsBean.getFob()));

            //是否是OEM协议商品
            setIsOEM(baseViewHolder, rowsBean);
        }

        if (rowsBean.isSoldOut() || rowsBean.getAvailableQty() <= 0) {////2是已售罄//4是已下架
            String sellOutStr = mContext.getResources().getString(R.string.text_sell_out);
            String soldOutStr = mContext.getResources().getString(R.string.text_sold_out);
            home_time_bg.setVisibility(View.VISIBLE);
            home_time_bg.setText(rowsBean.getStatus() == 2 ? sellOutStr : soldOutStr);
        } else {
            home_time_bg.setVisibility(View.INVISIBLE);
        }

        TagView rlIconType = baseViewHolder.getView(R.id.rl_icon_type);
        rlIconType.bindData(rowsBean.getTagList(), 2, isEmpty);
        handleAuditPassedVisible(baseViewHolder);
    }

    private void setIsOEM(YBMBaseHolder baseViewHolder, RowsBean rowsBean) {
        if (rowsBean.getIsOEM()) {
            //是否签署协议
            if (rowsBean.getSignStatus() == 1) {

            } else {
                baseViewHolder.getView(R.id.tv_old).setVisibility(View.GONE);
                ((TextView) baseViewHolder.getView(R.id.tv_price))
                        .setTextSize(12);
                baseViewHolder.setText(R.id.tv_price, "价格签署协议可见");
            }
        }

        //是否符合协议标准展示价格,1:符合0:不符合
        if (rowsBean.showAgree == 0) {
            baseViewHolder.getView(R.id.tv_old).setVisibility(View.GONE);
            ((TextView) baseViewHolder.getView(R.id.tv_price))
                    .setTextSize(12);
            baseViewHolder.setText(R.id.tv_price, "价格签署协议可见");
        }
    }

    /**
     * 处理价格认证资质可见
     */
    private void handleAuditPassedVisible(YBMBaseHolder baseViewHolder) {
        if (AuditStatusSyncUtil.getInstance().isAuditFirstPassed()) {
            //通过一审
            baseViewHolder.setGone(R.id.tv_audit_passed_visible, false);
            baseViewHolder.setGone(R.id.ll_price_parent, true);
        } else {
            //未通过一审
            baseViewHolder.setGone(R.id.tv_audit_passed_visible, true);
            baseViewHolder.setGone(R.id.ll_price_parent, false);
        }
    }

}

