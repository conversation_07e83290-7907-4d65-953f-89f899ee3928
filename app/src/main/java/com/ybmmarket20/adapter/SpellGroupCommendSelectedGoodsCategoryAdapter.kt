package com.ybmmarket20.adapter

import android.widget.TextView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.ShopHomeIndexBean
import com.ybmmarket20.utils.UiUtils

open class SpellGroupCommendSelectedGoodsCategoryAdapter(
    layoutResId: Int,
    data: MutableList<ShopHomeIndexBean.Floor>?
) : YBMBaseAdapter<ShopHomeIndexBean.Floor>(layoutResId, data) {
    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: ShopHomeIndexBean.Floor) {
        val tvFloorName = baseViewHolder?.getView<TextView>(R.id.tv_name)
        tvFloorName?.let {
            it.text = t.floorName
            if (t.isSelect) {
                it.setTextColor(UiUtils.getColor(R.color.color_00b377))
                it.isSelected = true
            } else {
                it.setTextColor(UiUtils.getColor(R.color.color_676773))
                it.isSelected = false
            }
        }
    }

}