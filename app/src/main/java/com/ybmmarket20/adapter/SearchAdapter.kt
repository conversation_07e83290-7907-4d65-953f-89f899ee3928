package com.ybmmarket20.adapter

import android.content.Context
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.*
import com.ybmmarket20.bean.product_detail.ReportPDButtonClick
import com.ybmmarket20.bean.product_detail.ReportPDExtendOuterBean
import com.ybmmarket20.common.*
import com.ybmmarket20.common.JGTrackManager.Common
import com.ybmmarket20.common.JGTrackManager.GlobalVariable
import com.ybmmarket20.report.coupon.ICouponEntryType
import com.ybmmarket20.reportBean.JGPageListCommonBean
import com.ybmmarket20.reportBean.PageListProductClick
import com.ybmmarket20.reportBean.PageListProductExposure
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.BaseFlowData
import com.ybmmarket20.utils.analysis.flowDataPageOPListExposureWithCode
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.view.operationposition.OPCardAdapter
import com.ybmmarketkotlin.adapter.GoodListAdapterNew
import com.ybmmarketkotlin.adapter.GoodsListAnalysisAdapter
import com.ybmmarketkotlin.adapter.SpellGroupLimitTimePremiumAdapter
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter
import com.ydmmarket.report.ReportManager

/**
 * 大搜Adapter(商品+运营位)
 */
class SearchAdapter(
        val context: Context,
        data: MutableList<SearchRowsBean>,
) : GoodsListAnalysisAdapter<SearchRowsBean>(data) {

    constructor(iCouponEntryType: ICouponEntryType, context: Context, data: MutableList<SearchRowsBean>): this(context, data) {
        goodsAdapter.mCouponEntryType = iCouponEntryType
        mLimitTimePremiumAdapter.mCouponEntryType = iCouponEntryType
    }

    var jgTrackBean:JgTrackBean? = null
        set(value) {
            field = value
            goodsAdapter.jgTrackBean = value?.copy()?.apply {
                //这里路径细化到bindItemView去处理
//                entrance = splicingModule2Entrance(entrance?:"",Common.MODULE_SEARCH_FEED)
                module = Common.MODULE_SEARCH_FEED
            }
            mLimitTimePremiumAdapter.jgTrackBean = value?.copy()?.apply {
                //这里路径细化到bindItemView去处理
//                entrance = splicingModule2Entrance(entrance?:"",Common.MODULE_SEARCH_FEED)
                module = Common.MODULE_SEARCH_FEED
            }

            opAdapter.jgTrackBean = value?.copy()?.apply {
                entrance = splicingModule2Entrance(entrance ?: "", Common.MODULE_OPERATIONS)
                module = Common.MODULE_OPERATIONS
            }
        }

    var jGPageListCommonBean: JGPageListCommonBean? = null
        set(value) {
            field = value
            goodsAdapter.jGPageListCommonBean = value
            mLimitTimePremiumAdapter.jGPageListCommonBean = value
            opAdapter.jGPageListCommonBean = value
        }

    private val goodsAdapter: GoodListAdapterNew = GoodListAdapterNew(R.layout.item_goods_new, mutableListOf(), true).apply {
        isFromSearch = true
    }
    private val mLimitTimePremiumAdapter: SpellGroupLimitTimePremiumAdapter = SpellGroupLimitTimePremiumAdapter(mutableListOf()).apply {
        isFromSearch = true
    }

    fun getListGoodsAdapter(): GoodListAdapterNew = goodsAdapter

    private val opAdapter = OPCardAdapter(mutableListOf()).apply {
        mTrackViewListener = { searchBean, productPosition ->
            //每个只报一次
            productViewTrackMap[searchBean.productInfo?.id.toString()] ?: kotlin.run {
                productViewTrackMap[searchBean.productInfo?.id.toString()] = System.currentTimeMillis()

                searchPageListProductExposure(PageListProductExposure(
                        jGPageListCommonBean = jGPageListCommonBean,
                        search_sort_strategy_id = searchBean.productInfo?.searchSortStrategyCode?:"",
                        operation_id = searchBean.productInfo?.operationId ?: "",
                        operation_rank = productPosition+1,
                        list_position_type = (searchBean.productInfo?.positionType ?: 0).toString(),
                        list_position_typename = searchBean.productInfo?.positionTypeName ?: "",
                        product_id = searchBean.productInfo?.id,
                        product_name = searchBean.productInfo?.productName?:"",
                        rank = searchBean.outPosition+1,
                        product_first = searchBean.productInfo?.categoryFirstId,
                        product_price = searchBean.productInfo?.jgProductPrice,
                        product_type = searchBean.productInfo?.productType?.toString()?:"",
                        product_activity_type = searchBean.productInfo?.productActivityType,
                        product_shop_code = searchBean.productInfo?.shopCode,
                        product_shop_name = searchBean.productInfo?.shopName
                ))
            }
        }

        mTrackClickListener = { searchBean, productPosition,isBtnClick,mContent,number ->
            mContext.searchResourceClickJGTrack(
                    searchBean,
                    productPosition,
                    Common.MODULE_OPERATIONS,
                    JGTrackManager.Common.MODULE_OPERATIONS,
                    jgTrackBean?.entrance ?: "",
                    operationId = searchBean.productInfo?.operationId ?: "",
                    operationRank = searchBean.outPosition+1,
                    listPositionType = searchBean.productInfo?.positionType?:0,
                    listPositionTypeName = searchBean.productInfo?.positionTypeName?:"")

            searchPageListProductClick(PageListProductClick(
                    jGPageListCommonBean = jGPageListCommonBean,
                    search_sort_strategy_id = searchBean.productInfo?.searchSortStrategyCode?:"",
                    operation_id = searchBean.productInfo?.operationId ?: "",
                    operation_rank = productPosition+1,
                    rank = searchBean.outPosition+1,
                    list_position_type = (searchBean.productInfo?.positionType ?: 0).toString(),
                    list_position_typename = searchBean.productInfo?.positionTypeName ?: "",
                    product_id = searchBean.productInfo?.id,
                    product_name = searchBean.productInfo?.productName?:"",
                    product_first = searchBean.productInfo?.categoryFirstId,
                    product_price = searchBean.productInfo?.jgProductPrice,
                    product_type = searchBean.productInfo?.productType?.toString()?:"",
                    product_activity_type = searchBean.productInfo?.productActivityType,
                    product_shop_code = searchBean.productInfo?.shopCode,
                    product_shop_name = searchBean.productInfo?.shopName
            ))

            if (isBtnClick){
                try {
                    context.jgReport(
                            ReportPDButtonClick().apply {
                                url = jgTrackBean?.url ?: ""
                                title = jgTrackBean?.title ?: ""
                                referrer = jgTrackBean?.jgReferrer?:""
                                accountId = SpUtil.getAccountId()
                                merchantId = SpUtil.getMerchantid()
                                productId = searchBean.productInfo?.id?.toInt()
                                productName = searchBean.productInfo?.productName?:""
                                productFirst = searchBean.productInfo?.categoryFirstId
                                productPrice = searchBean.productInfo?.jgProductPrice
                                productType = searchBean.productInfo?.productType.toString()
                                productActivityType = searchBean.productInfo?.productActivityType
                                productNumber = number
                                productShopCode = searchBean.productInfo?.shopCode
                                productShopName = searchBean.productInfo?.shopName
                                btnName = mContent
                                btnDesc = "列表页"
                                direct = "1"
                                outerBean = ReportPDExtendOuterBean().apply {
                                    sptype = jGPageListCommonBean?.sptype
                                    jgspid = jGPageListCommonBean?.jgspid
                                    sid = jGPageListCommonBean?.sid
                                    resultCnt = jGPageListCommonBean?.result_cnt
                                    pageNo =jGPageListCommonBean?.page_no
                                    pageSize = jGPageListCommonBean?.page_size
                                    totalPage = jGPageListCommonBean?.total_page
                                    rank = searchBean.outPosition+1
                                    keyWord = searchBean.productInfo?.searchKeyword?:""
                                    listPositionType = searchBean.productInfo?.positionType.toString()
                                    listPositionTypename = searchBean.productInfo?.positionTypeName ?: ""
                                    searchSortStrategyId = searchBean.productInfo?.searchSortStrategyCode?:""
                                    operationId = searchBean.productInfo?.operationId ?: ""
                                    operationRank = productPosition+1
                                }
                            }

                    )
                }catch (e:Exception){
                    e.printStackTrace()
                }
            }

            if (searchBean.isOperation()) {
                GlobalVariable.mJgOperationInfo = JgOperationPositionInfo(
                        searchBean.productInfo?.productId,
                        searchBean.productInfo?.operationId,
                        searchBean.outPosition + 1,
                        productPosition + 1, )
            }

            GlobalVariable.apply{
                mJgSearchRowsBean = searchBean.productInfo
                mJgSearchSomeField = JgSearchSomeField(mJgPageListCommonBean = jGPageListCommonBean,searchBean.outPosition+1)
            }
        }
    }

    // key: 商品Id     value:当时埋点的时间戳
    private val productViewTrackMap = hashMapOf<String, Long>()

    companion object {
//        private const val TRACK_DURATION = 2 * 60 * 1000 //2分钟内不上报
//        private const val TRACK_DURATION = 0 //无时间限时
    }

    override var flowData: BaseFlowData? = null
        set(value) {
            field = value
            goodsAdapter.flowData = value
            mLimitTimePremiumAdapter.flowData = value
            opAdapter.flowData = value
        }

    init {
        //商品
        addItemType(SEARCH_LIST_CARD_TYPE_GOODS, goodsAdapter.layoutResId)
        //运营位
        addItemType(SEARCH_LIST_CARD_TYPE_OPERATION_POSITION, opAdapter.layoutResId)
        //单品使用商品样式
        addItemType(SEARCH_LIST_CARD_TYPE_OPERATION_POSITION_SINGLE_GOODS, goodsAdapter.layoutResId)
        //限时加补拼团品样式
        addItemType(SEARCH_SPELL_GROUP_LIMIT_TIME_PREMIUM_GOODS,R.layout.item_goods_new_limit_time_premium)

        goodsAdapter.setContext(context)
        mLimitTimePremiumAdapter.setContext(context)
        opAdapter.setContext(context)
    }

    override fun setBaseFlowData(flowData: BaseFlowData?) {

    }


    override fun bindItemView(baseViewHolder: YBMBaseHolder, t: SearchRowsBean) {
        super.bindItemView(baseViewHolder, t)
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            when(bean.itemType) {
                SEARCH_LIST_CARD_TYPE_GOODS -> bindGoodsItemView(holder, bean)
                SEARCH_LIST_CARD_TYPE_OPERATION_POSITION_SINGLE_GOODS -> bindOPItemGoodsView(holder, bean)
                SEARCH_LIST_CARD_TYPE_OPERATION_POSITION -> bindOPItemView(holder, bean)
                SEARCH_SPELL_GROUP_LIMIT_TIME_PREMIUM_GOODS -> bindLimitTimePremiumGoodsItemView(holder, bean)
            }
        }
    }

    override fun setNewData(data: MutableList<Any?>?) {
        productViewTrackMap.clear()
        super.setNewData(data)
    }

    /**
     * 限时补价拼团品
     * @param holder YBMBaseHolder
     * @param bean SearchRowsBean
     */
    private fun bindLimitTimePremiumGoodsItemView(holder: YBMBaseHolder, bean: SearchRowsBean) {
        bean.productInfo?.let {

            //这个回调要在后面的bind传递 所以要先赋值带过去
            mLimitTimePremiumAdapter.productClickTrackListener = { rowsBean,position,isBtnClick,mContent,number ->
                val module = if (rowsBean.isOPSingleGoods) JGTrackManager.Common.MODULE_OPERATIONS else Common.MODULE_SEARCH_FEED
                val resourceName = if (rowsBean.isOPSingleGoods) Common.MODULE_OPERATIONS else Common.MODULE_SEARCH_FEED
                mContext.searchResourceClickJGTrack(
                        SearchRowsBean(
                                if (rowsBean.isOPSingleGoods) 3 else 1,
                                rowsBean,
                                null,
                                keyword = rowsBean.searchKeyword?:""),
                        position,
                        resourceName,
                        module,
                        splicingModule2Entrance(
                                jgTrackBean?.entrance ?: "",
                                module),
                        operationId = rowsBean.operationId ?: "",
                        operationRank = bean.outPosition+1,
                        listPositionType = rowsBean.positionType,
                        listPositionTypeName = rowsBean.positionTypeName?:"")

                searchPageListProductClick(PageListProductClick(
                        jGPageListCommonBean = jGPageListCommonBean,
                        search_sort_strategy_id = rowsBean.searchSortStrategyCode?:"",
                        operation_id = rowsBean.operationId ?: "",
                        operation_rank = if (rowsBean.isOPSingleGoods) 1 else null,
                        rank = position+1,
                        list_position_type = rowsBean.positionType.toString(),
                        list_position_typename = rowsBean.positionTypeName ?: "",
                        product_id = rowsBean.id,
                        product_name = rowsBean.productName?:"",
                        product_first = rowsBean.categoryFirstId,
                        product_price = rowsBean.jgProductPrice,
                        product_type = rowsBean.productType.toString(),
                        product_activity_type = rowsBean.productActivityType,
                        product_shop_code = rowsBean.shopCode,
                        product_shop_name = rowsBean.shopName
                ))

                if (isBtnClick){
                    try {
                        context.jgReport(
                                ReportPDButtonClick().apply {
                                    url = jgTrackBean?.url ?: ""
                                    title = jgTrackBean?.title ?: ""
                                    referrer = jgTrackBean?.jgReferrer?:""
                                    accountId = SpUtil.getAccountId()
                                    merchantId = SpUtil.getMerchantid()
                                    productId = rowsBean.id.toInt()
                                    productName = rowsBean.productName?:""
                                    productFirst = rowsBean.categoryFirstId
                                    productPrice = rowsBean.jgProductPrice
                                    productType = rowsBean.productType.toString()
                                    productActivityType = rowsBean.productActivityType
                                    productNumber = number
                                    productShopCode = rowsBean.shopCode
                                    productShopName = rowsBean.shopName
                                    btnName = mContent
                                    btnDesc = "列表页"
                                    direct = "1"
                                    outerBean = ReportPDExtendOuterBean().apply {
                                        sptype = jGPageListCommonBean?.sptype
                                        jgspid = jGPageListCommonBean?.jgspid
                                        sid = jGPageListCommonBean?.sid
                                        resultCnt = jGPageListCommonBean?.result_cnt
                                        pageNo =jGPageListCommonBean?.page_no
                                        pageSize = jGPageListCommonBean?.page_size
                                        totalPage = jGPageListCommonBean?.total_page
                                        rank = position+1
                                        keyWord = rowsBean.searchKeyword?:""
                                        listPositionType = rowsBean.positionType.toString()
                                        listPositionTypename = rowsBean.positionTypeName ?: ""
                                        searchSortStrategyId = rowsBean.searchSortStrategyCode?:""
                                        operationId = rowsBean.operationId ?: ""
                                        operationRank = if (rowsBean.isOPSingleGoods) 1 else null
                                    }
                                }

                        )
                    }catch (e:Exception){
                        e.printStackTrace()
                    }
                }

                if (rowsBean.isOPSingleGoods) {
                    GlobalVariable.mJgOperationInfo = JgOperationPositionInfo(
                            rowsBean.productId,
                            rowsBean.operationId,
                            position + 1, 1)
                }
                GlobalVariable.apply {
                    mJgSearchRowsBean = rowsBean
                    mJgSearchSomeField = JgSearchSomeField(mJgPageListCommonBean = jGPageListCommonBean,position + 1)
                }
            }
            mLimitTimePremiumAdapter.bindItemView(holder,it)
            productViewTrackMap[it.id.toString()]?: kotlin.run {
                val module = if (it.isOPSingleGoods) JGTrackManager.Common.MODULE_OPERATIONS else Common.MODULE_SEARCH_FEED
                val resourceName = if (it.isOPSingleGoods) Common.MODULE_OPERATIONS else Common.MODULE_SEARCH_FEED
                productViewTrackMap[it.id.toString()] = System.currentTimeMillis()

                searchPageListProductExposure(PageListProductExposure(
                        jGPageListCommonBean = jGPageListCommonBean,
                        search_sort_strategy_id = bean.productInfo?.searchSortStrategyCode?:"",
                        operation_id = bean.productInfo?.operationId ?: "",
                        operation_rank = if (it.isOPSingleGoods) 1 else null,
                        rank = holder.adapterPosition +1,
                        list_position_type = (bean.productInfo?.positionType ?: 0).toString(),
                        list_position_typename = bean.productInfo?.positionTypeName ?: "",
                        product_id = bean.productInfo?.id,
                        product_name = bean.productInfo?.productName?:"",
                        product_first = bean.productInfo?.categoryFirstId,
                        product_price = bean.productInfo?.jgProductPrice,
                        product_type = bean.productInfo?.productType?.toString()?:"",
                        product_activity_type = bean.productInfo?.productActivityType,
                        product_shop_code = bean.productInfo?.shopCode,
                        product_shop_name = bean.productInfo?.shopName
                ))
            }
        }
    }

    /**
     * 商品
     */
    private fun bindGoodsItemView(holder: YBMBaseHolder, bean: SearchRowsBean) {
        bean.productInfo?.let {

            //这个回调要在后面的bind传递 所以要先赋值带过去
            goodsAdapter.productClickTrackListener = { rowsBean,position,isBtnClick,mContent,number ->
                val module = if (rowsBean.isOPSingleGoods) JGTrackManager.Common.MODULE_OPERATIONS else Common.MODULE_SEARCH_FEED
                val resourceName = if (rowsBean.isOPSingleGoods) Common.MODULE_OPERATIONS else Common.MODULE_SEARCH_FEED
                mContext.searchResourceClickJGTrack(
                        SearchRowsBean(
                                if (rowsBean.isOPSingleGoods) 3 else 1,
                                rowsBean,
                                null,
                                keyword = rowsBean.searchKeyword?:""),
                        position,
                        resourceName,
                        module,
                        splicingModule2Entrance(
                                jgTrackBean?.entrance ?: "",
                                module),
                        operationId = rowsBean.operationId ?: "",
                        operationRank = bean.outPosition+1,
                        listPositionType = rowsBean.positionType,
                        listPositionTypeName = rowsBean.positionTypeName?:"")

                searchPageListProductClick(PageListProductClick(
                        jGPageListCommonBean = jGPageListCommonBean,
                        search_sort_strategy_id = rowsBean.searchSortStrategyCode?:"",
                        operation_id = rowsBean.operationId ?: "",
                        operation_rank = if (rowsBean.isOPSingleGoods) 1 else null,
                        rank = position+1,
                        list_position_type = rowsBean.positionType.toString(),
                        list_position_typename = rowsBean.positionTypeName ?: "",
                        product_id = rowsBean.id,
                        product_name = rowsBean.productName?:"",
                        product_first = rowsBean.categoryFirstId,
                        product_price = rowsBean.jgProductPrice,
                        product_type = rowsBean.productType.toString(),
                        product_activity_type = rowsBean.productActivityType,
                        product_shop_code = rowsBean.shopCode,
                        product_shop_name = rowsBean.shopName
                ))

                if (isBtnClick){
                    try {
                        context.jgReport(
                                ReportPDButtonClick().apply {
                                    url = jgTrackBean?.url ?: ""
                                    title = jgTrackBean?.title ?: ""
                                    referrer = jgTrackBean?.jgReferrer?:""
                                    accountId = SpUtil.getAccountId()
                                    merchantId = SpUtil.getMerchantid()
                                    productId = rowsBean.id.toInt()
                                    productName = rowsBean.productName?:""
                                    productFirst = rowsBean.categoryFirstId
                                    productPrice = rowsBean.jgProductPrice
                                    productType = rowsBean.productType.toString()
                                    productActivityType = rowsBean.productActivityType
                                    productNumber = number
                                    productShopCode = rowsBean.shopCode
                                    productShopName = rowsBean.shopName
                                    btnName = mContent
                                    btnDesc = "列表页"
                                    direct = "1"
                                    outerBean = ReportPDExtendOuterBean().apply {
                                        sptype = jGPageListCommonBean?.sptype
                                        jgspid = jGPageListCommonBean?.jgspid
                                        sid = jGPageListCommonBean?.sid
                                        resultCnt = jGPageListCommonBean?.result_cnt
                                        pageNo =jGPageListCommonBean?.page_no
                                        pageSize = jGPageListCommonBean?.page_size
                                        totalPage = jGPageListCommonBean?.total_page
                                        rank = position+1
                                        keyWord = rowsBean.searchKeyword?:""
                                        listPositionType = rowsBean.positionType.toString()
                                        listPositionTypename = rowsBean.positionTypeName ?: ""
                                        searchSortStrategyId = rowsBean.searchSortStrategyCode?:""
                                        operationId = rowsBean.operationId ?: ""
                                        operationRank = if (rowsBean.isOPSingleGoods) 1 else null
                                    }
                                }

                        )
                    }catch (e:Exception){
                        e.printStackTrace()
                    }
                }

                if (rowsBean.isOPSingleGoods) {
                    GlobalVariable.mJgOperationInfo = JgOperationPositionInfo(
                            rowsBean.productId,
                            rowsBean.operationId,
                            position + 1, 1)
                }
                GlobalVariable.apply {
                    mJgSearchRowsBean = rowsBean
                    mJgSearchSomeField = JgSearchSomeField(mJgPageListCommonBean = jGPageListCommonBean,position + 1)
                }
            }
            goodsAdapter.bindItemViewWithBackground(holder, it, getGoodsViewItemBackgroundResId(holder))
            productViewTrackMap[it.id.toString()]?: kotlin.run {
                val module = if (it.isOPSingleGoods) JGTrackManager.Common.MODULE_OPERATIONS else Common.MODULE_SEARCH_FEED
                val resourceName = if (it.isOPSingleGoods) Common.MODULE_OPERATIONS else Common.MODULE_SEARCH_FEED
                productViewTrackMap[it.id.toString()] = System.currentTimeMillis()

                searchPageListProductExposure(PageListProductExposure(
                        jGPageListCommonBean = jGPageListCommonBean,
                        search_sort_strategy_id = bean.productInfo?.searchSortStrategyCode?:"",
                        operation_id = bean.productInfo?.operationId ?: "",
                        operation_rank = if (it.isOPSingleGoods) 1 else null,
                        rank = holder.adapterPosition +1,
                        list_position_type = (bean.productInfo?.positionType ?: 0).toString(),
                        list_position_typename = bean.productInfo?.positionTypeName ?: "",
                        product_id = bean.productInfo?.id,
                        product_name = bean.productInfo?.productName?:"",
                        product_first = bean.productInfo?.categoryFirstId,
                        product_price = bean.productInfo?.jgProductPrice,
                        product_type = bean.productInfo?.productType?.toString()?:"",
                        product_activity_type = bean.productInfo?.productActivityType,
                        product_shop_code = bean.productInfo?.shopCode,
                        product_shop_name = bean.productInfo?.shopName
                ))
            }
        }
    }

    /**
     * 运营位
     */
    private fun bindOPItemView(
            holder: YBMBaseHolder,
            bean: SearchRowsBean
    ) {
        opAdapter.bindItemViewWithBackground(
                holder,
                bean)
    }

    /**
     * 运营位展示商品样式
     */
    private fun bindOPItemGoodsView(holder: YBMBaseHolder, bean: SearchRowsBean) {
        try {
            bean.operationInfo?.let {
                val rowsBean = it.products[0]
                rowsBean.acptUrl = it.jumpUrl
                rowsBean.isOPSingleGoods = true
                rowsBean.directModule = "2"
                rowsBean.oPSingleGoodsActName = it.title
                rowsBean.opSingleGoodsActJumpUrl = it.jumpUrl
                rowsBean.shopName = it.products[0].shopName
                rowsBean.shopUrl = it.products[0].shopUrl
                bean.productInfo = rowsBean
                bindGoodsItemView(holder, bean)
                traceGoodsExposure(rowsBean,holder)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 商品曝光埋点
     */
    private fun traceGoodsExposure(rowsBean: RowsBean, holder: YBMBaseHolder) {
        if (flowData != null) {
            flowDataPageOPListExposureWithCode(
                    flowData,
                    rowsBean.productId,
                    rowsBean.showName,
                    "0",
                    rowsBean.searchSortStrategyCode,
                    "0",
                    rowsBean.operationExhibitionId,
                    rowsBean.operationId,
                    "${holder.bindingAdapterPosition}"
            )
        }
    }

    fun searchPageListProductExposure(pageListProductExposure: PageListProductExposure?) {
        pageListProductExposure ?: return
        ReportManager.getInstance().report(
                pageListProductExposure
        )
    }
    fun searchPageListProductClick(pageListProductExposure: PageListProductClick?) {
        pageListProductExposure ?: return
        ReportManager.getInstance().report(
                pageListProductExposure
        )
    }

    /**
     * 设置商品View背景圆角
     */
    private fun getGoodsViewItemBackgroundResId(holder: YBMBaseHolder): Int {
        val currPosition = holder.bindingAdapterPosition
        //是否是第一个
        val isFirst = currPosition == 0
        //是否是最后一个
        val isLast = mData.size == currPosition + 1
        //上一个item是否是运营位类型（非单品）
        val isTopCircle = if (!isFirst) {
            val preBean = mData[currPosition - 1] as SearchRowsBean
            preBean.cardType == SEARCH_LIST_CARD_TYPE_OPERATION_POSITION && preBean.operationInfo != null && (preBean.operationInfo.showType == OPERATION_POSITION_TYPE_SINGLE_SHOP || preBean.operationInfo.showType == OPERATION_POSITION_TYPE_MULTI_SHOP)
        } else false
        //下一个item是否是运营位类型（非单品）
        val isBottomCircle = if (!isLast) {
            val nextBean = mData[currPosition + 1] as SearchRowsBean
            nextBean.cardType == SEARCH_LIST_CARD_TYPE_OPERATION_POSITION && nextBean.operationInfo != null && (nextBean.operationInfo.showType == OPERATION_POSITION_TYPE_SINGLE_SHOP || nextBean.operationInfo.showType == OPERATION_POSITION_TYPE_MULTI_SHOP)
        } else false
        return if (isTopCircle && isBottomCircle) R.drawable.shape_op_bg_radius_all
        else if (isTopCircle) R.drawable.shape_op_bg_radius_tl_tr
        else if (isBottomCircle) R.drawable.shape_op_bg_radius_bl_br
        else R.drawable.shape_op_bg_radius_nothing
    }

}