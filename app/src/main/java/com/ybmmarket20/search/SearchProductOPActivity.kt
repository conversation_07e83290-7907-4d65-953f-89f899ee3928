package com.ybmmarket20.search

import android.os.Bundle
import android.os.CountDownTimer
import android.text.TextUtils
import android.util.SparseArray
import android.view.View
import com.analysys.ANSAutoPageTracker
import com.github.mzule.activityrouter.annotation.Router
import com.github.mzule.activityrouter.router.Routers
import com.google.gson.Gson
import com.ybm.app.bean.NetError
import com.ybmmarket20.adapter.SearchAdapter
import com.ybmmarket20.adapter.YBMBaseListAdapter
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.SearchDynamicLabelConfig
import com.ybmmarket20.bean.SearchFilterBean
import com.ybmmarket20.bean.SearchResultOPBean
import com.ybmmarket20.bean.SearchRowsBean
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.JGTrackManager.Companion.eventTrack
import com.ybmmarket20.common.JGTrackManager.Companion.setSuperProperty
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.common.splicingPageTitle2Entrance
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.report.coupon.CouponEntryType
import com.ybmmarket20.reportBean.AppActionTopHotWordClick
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.BaseFlowData
import com.ybmmarket20.utils.analysis.addAnalysisRequestParams
import com.ybmmarket20.utils.analysis.updateFlowData
import com.ybmmarket20.view.searchFilter.view.DYNAMIC_LABEL_STYLE_HORIZONTAL_LINEAR
import java.io.UnsupportedEncodingException
import java.net.URLEncoder

/**
 * 大搜（带有运营位）
 */
@Router("searchproductop")
open class SearchProductOPActivity: AnalysisSearchProductActivity(),ANSAutoPageTracker {

    private val countDownTimerMap = SparseArray<CountDownTimer>()

    val mData = mutableListOf<SearchRowsBean>()

    private val mAdapter: SearchAdapter by lazy { SearchAdapter(this, this, mData) }

    var mJgTrackBean = JgTrackBean()

    companion object{
        private const val TRACK_URL = "com.ybmmarket20.search.SearchProductOPActivity"
        val INTENT_ENTRANCE = IntentCanst.JG_ENTRANCE
    }

    override fun getRawAction(): String {
        return if (isShowCart) {
            "ybmpage://searchproductop?keyword=$keyword&isShowCart=1"
        } else {
            "ybmpage://searchproductop"
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        mJgTrackBean = JgTrackBean()
        intent.getStringExtra(INTENT_ENTRANCE)?.let {
            mEntrance = it
        }
        intent.getSerializableExtra((IntentCanst.JG_TRACK_BEAN))?.let {
            (it as? JgTrackBean?)?.let { bean->
                mJgTrackBean = bean
            }
        }
        intent.getSerializableExtra((IntentCanst.JG_APP_ACTION_TOP_HOT_WORD_CLICK))?.let {
            (it as? AppActionTopHotWordClick?)?.let { bean->
                mJgAppActionTopHotWordClick = bean
            }
        }

        mJgTrackBean.apply {
            pageId = JGTrackManager.TrackSearchResult.PAGE_ID
            title = JGTrackManager.TrackSearchResult.TITLE
            jgReferrer = <EMAIL>()
            jgReferrerTitle = JGTrackManager.TrackSearchResult.TITLE
            jgReferrerModule = JGTrackManager.TrackSearchResult.TITLE
            url = <EMAIL>()
            entrance = if (mEntrance.isNotEmpty()){
                splicingPageTitle2Entrance(mEntrance,JGTrackManager.TrackSearchResult.TITLE)
            }else{
                JGTrackManager.TrackSearchResult.TITLE
            }
        }

        mAdapter.jgTrackBean = mJgTrackBean
        super.onCreate(savedInstanceState)
    }

    override fun initData() {
        super.initData()
        mBrandRg02.visibility = View.GONE
        dynamicLabelView.setData(searchViewModel, DYNAMIC_LABEL_STYLE_HORIZONTAL_LINEAR)
        //点击回调
        dynamicLabelView.setItemSelectCallback { it, configList->
            mDynamicLabelSelectedMap = it
            mDynamicLabelSelectedConfigList = configList
            searchViewModel.updateSearchDynamicLabelSelected(it)
            mHandler.sendEmptyMessage(20)
            dynamicLabelClick(dynamicLabelView.mDataList, nearEffectiveStr, DYNAMIC_LABEL_STYLE_HORIZONTAL_LINEAR)
            showProgress()
            getSearchDataPost()
            getAggsDataPost()
        }
        //曝光回调
//        dynamicLabelView.setOnExposureCallback(::dynamicLabelExposure)
    }

    override fun getAdapter(): YBMBaseListAdapter<*> = mAdapter
    override fun getSearchDataPost() {
        jgAppHomeHotWordClick(keyword)
        val params = getParams(false)
        val url = searchUrl
        HttpManager.getInstance().post(url, params, object : BaseResponse<SearchResultOPBean>() {
            override fun onSuccess(
                content: String?,
                obj: BaseBean<SearchResultOPBean>?,
                searchResult: SearchResultOPBean
            ) {
                super.onSuccess(content, obj, searchResult)
                dismissProgress()
                hideSoftInput()
                searchType = searchResult.type
                if (obj != null) {
                    if (obj.isSuccess) {
                        if (adapter is SearchAdapter) {
                            //清空商品曝光记录
                            (adapter as SearchAdapter).clearCacheRecord()
                        }
                        addGoodsReportParams(searchResult)
                        val flowData = BaseFlowData(searchResult.sptype, searchResult.spid, searchResult.sid)
                        updateFlowData(mFlowData,searchResult.sptype, searchResult.spid, searchResult.sid,searchResult.nsid)
                        detailAdapter.flowData = flowData
                        //为运营位商品添加 搜索策略编码
                        searchResult.setSearchSortStrategyCodeForAllRowsBean()
                        //更新资质审核状态
                        updateLicenseStatus(searchResult.licenseStatus, currentLicenesStatusListener)
                        //根据状态获取埋点数据、触发列表打开事件
                        analysisAfterGetListData(flowData, params)
                        //处理无结果query截断召回和截断后仍无结果兜底召
                        handleNoQuery(searchResult)
                        handleAnalysisKeyword()
                        //搜索PV
                        searchPagePv(keyword)
                        //列表生成
                        trackListCreate(searchResult.scmId, searchResult.searchSortStrategyCode, searchResult.qtListData, params.paramsMap, mDynamicLabelConfig, selectedShopNames)
                        //处理 319类型的活动
                        handleActivityShowType(searchResult)
                        //购物车悬浮窗
                        showFloatCart(isFromOftenBuy)
                        if (isShowCart) {
                            showFloatCart(isShowCart)
                        }
                        //处理商品列表数据
                        mData.clear()
                        //处理更换关键词回到列表顶部
                        searchProductListView.scrollToPosition(0)

                        //更新数据
                        updateSearchData(true, searchResult,true,!isSortnetSection)
                        //隐藏搜索启动页
                        setSearchStartPageVisibility(false)
                        //显示第一行筛选项
                        mBrandRg01.visibility = View.VISIBLE
                        //显示第二行筛选项
                        mBrandRg02.visibility = View.GONE
                        //显示商品列表
                        searchProductListView.visibility = View.VISIBLE
                    }
                }
                popDismiss()
                //大搜搜索埋点
                trackSearch(searchResult.count)
                setSuperProperty(this@SearchProductOPActivity,JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID,searchResult.searchSortStrategyCode ?:"")

                searchJGTrack(searchResult.rows?.size?:0, selectedSearchPosition)

            }

            override fun onFailure(error: NetError?) {
                super.onFailure(error)
                dismissProgress()
                popDismiss()
                detailAdapter.loadMoreFail()
            }
        })
    }

    override fun onDestroy() {
        JGTrackManager.unRegisterSuperProperty(this@SearchProductOPActivity,JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID)
        super.onDestroy()
    }

    private fun searchJGTrack(
            resultCnt:Int,
            rank:Int
    ) {
        try {
            //搜索来源,默认是输入，  1:输入2:历史3:推荐4:联想 5语音  6关键词纠错无结果 7关键词纠错人工映射 8搜索发现
            val searchType = when (mJgSource){
                JG_SOURCE_HISTORY_SEARCH -> "历史搜索"
                JG_SOURCE_SUG_SEARCH-> "提示词"
                JG_SOURCE_VOICE_SEARCH-> "语音搜索"
                JG_SOURCE_FIND_SOURCE -> "搜索发现"
                JG_SOURCE_SCAN_SEARCH-> "扫一扫"
                else -> "关键词搜索"
            }

            val map = java.util.HashMap<String, Any>()
            map[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackSearchResult.TITLE
            map[JGTrackManager.FIELD.FIELD_REFERRER] =JGTrackManager.TrackSearchResult.TRACK_URL
            map[JGTrackManager.FIELD.FIELD_MODULE] ="搜索框"
            map[JGTrackManager.FIELD.FIELD_KEY_WORD] = keyword
            map[JGTrackManager.FIELD.FIELD_SEARCH_TYPE] =searchType
            map[JGTrackManager.FIELD.FIELD_SUG_RANK] = if (rank <= 1) 1 else rank
            map[JGTrackManager.FIELD.FIELD_RESULT_CNT] = resultCnt
//            map[JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID] = searchSortStrategyId

            eventTrack(this, JGTrackManager.TrackSearchResult.EVENT_SEARCH, map)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun getLoadMoreResponse() {
        val params = getParams(true)
        HttpManager.getInstance().post(searchUrl, params, object :
            BaseResponse<SearchResultOPBean>() {
            override fun onSuccess(
                content: String?,
                obj: BaseBean<SearchResultOPBean>?,
                rowsBeans: SearchResultOPBean
            ) {
                super.onSuccess(content, obj, rowsBeans)
                if (obj != null) {
                    if (obj.isSuccess) {
                        addGoodsReportParams(rowsBeans)
                        //列表生成
                        trackListCreate(rowsBeans.scmId, rowsBeans.searchSortStrategyCode, rowsBeans.qtListData, params.paramsMap, mDynamicLabelConfig, selectedShopNames)
                        updateLicenseStatus(rowsBeans.licenseStatus, null)
                        if (rowsBeans.rows != null) {
                            updateSearchData(false, rowsBeans,true,!isSortnetSection)
                        }
                        searchJGTrack(rowsBeans.rows?.size?:0,selectedSearchPosition)
                    }
                }
            }

            override fun onFailure(error: NetError?) {
                super.onFailure(error)
                detailAdapter.loadMoreFail()
            }
        })
    }

    override fun getAggsUrl(): String = AppNetConfig.SORTNET_AGGS_V2

    override fun getParams(isLoadMore: Boolean): RequestParams {
        if (isLoadMore) {
            addAnalysisRequestParams(searchMoreParams, mFlowData)
            searchMoreParams.put("isNextPage","1")
            return searchMoreParams
        }

        val merchantId = SpUtil.getMerchantid()
        val params = RequestParams()
        params.put("isNextPage","0")
        params.put("jgspid",mJgspid?:"")
        params.put("showSimilarGoodsJump", "1")
        params.put("merchantId", merchantId)
        val pageUrl = intent.getStringExtra(Routers.KEY_RAW_URL)
        if (pageUrl != null) {
            params.put("pageurl", pageUrl)
        }
        if (prePageSource != null) {
            try {
                params.put(
                    "pageSource",
                    prePageSource + "_e" + URLEncoder.encode(
                        URLEncoder.encode(keyword, "UTF-8"),
                        "UTF-8"
                    )
                )
            } catch (e: UnsupportedEncodingException) {
                e.printStackTrace()
            }
        }

        // 综合、销量、价格 排序
        if (searchFilterSynthesizeBean != null) {
            val saleProperty = when (searchFilterSynthesizeBean.selectedSearchOption) {
                SearchFilterBean.SALESVOLUME -> PROPERTY_FILTER_SALE_VOLUME_V2
                SearchFilterBean.PRICE -> PROPERTY_FILTER_PRICE_V2
                else -> PROPERTY_FILTER_DEFAULT_V2
            }
            params.put("sortStrategy", saleProperty)
        } else {
            params.put("sortStrategy", PROPERTY_FILTER_DEFAULT_V2)
        }

        // 规格
        if (!TextUtils.isEmpty(specStr)) {
            val specArr = specStr.split(",")
            params.put("specs", Gson().toJson(specArr).toString())
        }

        if (!TextUtils.isEmpty(isExcludePt)) {
            params.put("isExcludePt", isExcludePt)
        }

        // 外部进入增加shopcode过滤条件
        if (isOrderBundling){
            if (!TextUtils.isEmpty(selectedShopcodes)) {
                params.put("shopCodes", shopCodesFromOut)
            }
        }else{
            if (!TextUtils.isEmpty(shopCodesFromOut)) {
                params.put("shopCodes", shopCodesFromOut)
            }
        }


        // 商家
        if (!TextUtils.isEmpty(selectedShopcodes.toString())) {
            params.put("shopCodes", selectedShopcodes.toString())
        }

        //自营
//        if (isThirdCompany == 0) {
//            params.put("isThirdCompany", "0")
//        }
        //是否是优选，1：是，0：否
//        params.put("isHighGross", if (highGross == 1) "1" else "")
        //厂家
        if (!TextUtils.isEmpty(manufacturer)) {
            val manufacturerArr = manufacturer.split("*")
            params.put("manufacturers", Gson().toJson(manufacturerArr).toString())
        }
        //经营类型
        if (!TextUtils.isEmpty(drugClassification)) {
            params.put("drugClassificationsStr", drugClassification)
        }
        //仅看有货
//        if (isAvailable) {
//            params.put("hasStock", "1")
//        }
        //可用券
//        params.put("isAvailableCoupons", if(isCanUseCoupon) "1" else "")
        //有促销
//        if (isPromotion) {
//            params.put("isPromotion", "1")
//        }
        //价格区间-最低价
        if (!TextUtils.isEmpty(priceRangeFloor)) {
            params.put("minPrice", priceRangeFloor)
        }
        //价格区间-最高价
        if (!TextUtils.isEmpty(priceRangeTop)) {
            params.put("maxPrice", priceRangeTop)
        }
        // 分类
        if (TextUtils.isEmpty(keyword)) {

            //全部分类 categoryId
            if (!TextUtils.isEmpty(id)) {
                params.put("categoryIdsStr", id)
            }
            if (mClassifyPop2 != null && !TextUtils.isEmpty(id)) {
                mClassifyPop2.setDataType("categoryIdsStr", id)
            }
        } else if (!keyword.equals("all", ignoreCase = true)) {
            //定义"all"为搜索全部药品(优惠券跳转过来)，参数不传
            if (!isFromOftenBuy) {
                params.put("queryWord", keyword)
            }
            if (!TextUtils.isEmpty(id)) {
                params.put("categoryIdsStr", id)
            }
            if (mClassifyPop2 != null) {
                mClassifyPop2.setDataType("categoryIdsStr", id)
            }
            if (mClassifyPop2 != null && !isFromOftenBuy) {
                mClassifyPop2.setDataType("queryWord", keyword)
            }
        }

        if (preKeyWord != null && preKeyWord == keyword) {
            addAnalysisRequestParams(params, mFlowData)
        }

        params.put("type", SEARCH_TYPE_NORMAL.toString() + "")
        if (isFromOftenBuy) {
            params.put("spFrom", if (isFromHome == 1) "6" else "5")
            params.put("masterStandardProductId", masterStandardProductId)
        } else {
            params.put("spFrom", spFrom)
        }
        // 搜索全区或活动搜索，多种活动用 taglist 用","隔开
//        params.put("tagList", tagList)
        //快递
//        params.put("isSupportJdExpress", if (isSelectedJD) "1" else "")
//        params.put("isSupportSfExpress", if (isSelectedSF) "1" else "")

//        //批购包邮
//        if (isDpby) {
//            params.put("isWholesale", "1")
//            XyyIoUtil.track("action_Search_FreeShipping_outside")
//        } else {
//            params.put("isWholesale", "")
//        }
//
//        //是否是拼团，1：是，0：否
//        params.put("isGroupBuying", if(isSpellGroup) "1" else "")

        // 拼团包邮
//        if (isDpby && isSpellGroup) {
//            params.put("isGroupBuyingOrWholesale", "1")
//        }
        //只看中药
//        if (isTraditionalChineseMedicine){
//            params.put("isOnlyTraditionalChineseMedicine", "1")
//        }
        //同省
//        if (isSameProvince){
//            params.put("isSameProvince", "1")
//        }

        //搜索场景。1主搜，2店铺同款商品搜索。
        params.put("searchScene", searchScene)
        mDynamicLabelSelectedMap.forEach {
            params.put(it.key, it.value)
        }
        params.put("nearEffect", nearEffective)
        params.put("isFilter", isFilter)
        return params
    }

    override fun requestParamsEqualsPre(
        preParams: RequestParams?,
        curParams: RequestParams
    ): Boolean {
        return preParams?.equalsParamValues(
            curParams,
            "sortStrategy",
            "isThirdCompany",
            "manufacturers",
            "drugClassificationsStr",
            "hasStock",
            "isPromotion",
            "minPrice",
            "maxPrice",
            "categoryIdsStr",
            "queryWord"
        ) ?: false
    }

    override fun clearOptionAndSearchNewData(mJgSource: Int) {
        nearEffective = null
        mDynamicLabelSelectedMap.clear()
        dynamicLabelView.reset()
        mClassifyPop2?.reset(false)
        mClassifyPop2?.clearNearEffective()
        mHandler.sendEmptyMessage(20)
        super.clearOptionAndSearchNewData(mJgSource)
    }

    override fun getManufacturersUrl(): String = AppNetConfig.FIND_MANUFACTURER_V2
    override fun getCacheMap(): SparseArray<CountDownTimer> = countDownTimerMap

    override fun getManufacturersParams(): RequestParams? = null

    override fun getSearchScene(): String = "1"
    override fun getPageType(): Int = PAGE_TYPE_OP
    override fun getJgTrackBean(): JgTrackBean = mJgTrackBean
    override fun isShowNearEffective(): Boolean = true
    override fun getSearchUrl(): String = AppNetConfig.SORTNET_V2

    override fun getCouponEntryType(): String = CouponEntryType.COUPON_ENTRY_TYPE_SEARCH_MAIN

    override fun registerPageProperties(): MutableMap<String, Any> {
        val properties: MutableMap<String, Any> = HashMap()
        properties[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackSearchIntermediateState.PAGE_ID
        properties[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackSearchIntermediateState.TITLE
        return properties
    }

    override fun registerPageUrl(): String  = this.getFullClassName()

    private fun handleAnalysisKeyword() {
        mAnalysisKeyword = keyword
    }

}