package com.ybmmarket20.network.request

import com.ybmmarket20.bean.AuthenticationProcessingInfo
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.*

/**
 * 审核进行中（添加店铺审核和提交凭据审核）
 */

interface IAuthenticationProcessingRequest {
    @FormUrlEncoded
    @POST("getSimpleMerchantInfo")
    suspend fun queryAuthenticationProcessingInfo(@Field("accountId")accountId: String, @Field("merchantId")merchantId: String, @Header("merchantId")merchantIdH: String): BaseBean<AuthenticationProcessingInfo>
}

class AuthenticationProcessingRequest {

    /**
     * 查询审核进行中的信息
     */
    suspend fun queryAuthenticationProcessingInfo(accountId: String, merchantId: String): BaseBean<AuthenticationProcessingInfo> = try {
        NetworkService.instance.mRetrofit.create(IAuthenticationProcessingRequest::class.java).queryAuthenticationProcessingInfo(accountId, merchantId, merchantId)
    } catch (e: Exception) {
        BaseBean<AuthenticationProcessingInfo>().initWithException(e)
    }
}