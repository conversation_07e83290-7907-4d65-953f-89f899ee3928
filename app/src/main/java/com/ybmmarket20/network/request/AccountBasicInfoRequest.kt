package com.ybmmarket20.network.request

import com.ybmmarket20.bean.AptitudeBasicInfoBean
import com.ybmmarket20.bean.BaseBean
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

interface AccountBasicInfoRequest {
    @FormUrlEncoded
    @POST("licenseAudit/getMerchantBasicInfo")
    suspend fun requestNews(@Field("merchantId")merchantId : String): BaseBean<AptitudeBasicInfoBean>
}