package com.ybmmarket20.network

import com.tencent.bugly.network.BuglyListenerFactory
import com.ybm.app.common.ApiMonitorInterceptor
import com.ybm.app.common.HttpLoggingInterceptor
import com.ybm.app.common.OkHttpManager
import com.ybm.app.common.OkHttpManager.YBMDns
import com.ybm.app.common.RequestParamsInterceptor
import com.ybm.app.common.UserAgentInterceptor
import com.ybm.app.common.WafInterceptor
import com.ybmmarket20.BuildConfig
import com.ybmmarket20.bean.AptitudeBasicInfoBean
import com.ybmmarket20.bean.AptitudeXyyBean
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CartVoucher
import com.ybmmarket20.bean.CouponInfoBean
import com.ybmmarket20.bean.EmptyBean
import com.ybmmarket20.bean.FreightTipBean
import com.ybmmarket20.bean.ImPackUrlBean
import com.ybmmarket20.bean.InvoiceListBaseBean
import com.ybmmarket20.bean.Login
import com.ybmmarket20.bean.OpenAccount
import com.ybmmarket20.bean.RefundReason
import com.ybmmarket20.bean.RowsPriceDiscount
import com.ybmmarket20.bean.SearchAggsBean
import com.ybmmarket20.bean.ServiceType
import com.ybmmarket20.bean.ShopBasicInfoBean
import com.ybmmarket20.bean.ShopTab
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.interceptor.BaseParamInterceptor
import com.ybmmarket20.network.request.AccountBasicInfoRequest
import com.ybmmarket20.network.request.InvoiceRequest
import com.ybmmarket20.network.request.LoginRequest
import com.ybmmarket20.network.request.OpenAccountRequest
import com.ybmmarket20.network.request.PriceAfterDiscountRequest
import com.ybmmarket20.network.request.ProductAddOnItemRequest
import com.ybmmarket20.network.request.QualificationRequest
import com.ybmmarket20.network.request.RefundReasonRequest
import com.ybmmarket20.network.request.ServiceTypeRequest
import com.ybmmarket20.network.request.ShopAfterSaleDistributionRequest
import com.ybmmarket20.network.request.ShopHomeRequest
import com.ybmmarket20.network.request.SpecFilterRequest
import com.ybmmarket20.network.request.UnregisterRequest
import com.ybmmarketkotlin.bean.ShopBaseInfo
import com.ybmmarketkotlin.bean.ShopListBean
import com.ybmmarketkotlin.bean.VoucherShopListTabBean
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import java.util.concurrent.TimeUnit
import kotlin.reflect.KClass

class NetworkService private constructor() {
    private val MAXTIMEOUT = 30 //最大超时时间 单位秒

    companion object {
        @JvmStatic
        val instance = NetworkService()
    }

    val mRetrofit: Retrofit

    init {
        val httpClient = createHttpClient()
        mRetrofit = createRetrofitClient(httpClient)
    }

    private fun createHttpClient(): OkHttpClient {
        val builder = OkHttpClient.Builder()
            .eventListenerFactory(BuglyListenerFactory.getInstance())
                .connectTimeout(MAXTIMEOUT.toLong(), TimeUnit.SECONDS)
                .readTimeout(MAXTIMEOUT.toLong(), TimeUnit.SECONDS).writeTimeout(MAXTIMEOUT.toLong(), TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .addInterceptor(RequestParamsInterceptor())
                .addInterceptor(OkHttpManager.AppInterceptor(OkHttpManager.AppInterceptor.LIB_NEW))
                .addInterceptor(BaseParamInterceptor())
                .addInterceptor(UserAgentInterceptor())
                .addInterceptor(ApiMonitorInterceptor())
                .addInterceptor(WafInterceptor())
                .addNetworkInterceptor(OkHttpManager.NetInterceptor())
                .dns(YBMDns())

        if (BuildConfig.DEBUG) {
            HttpLoggingInterceptor("OkHttp", BuildConfig.DEBUG).let {
                it.level = HttpLoggingInterceptor.Level.BODY
                builder.addInterceptor(it)
            }
        }

        return builder.build()
    }

    private fun createRetrofitClient(client: OkHttpClient): Retrofit {
        val url = AppNetConfig.HOST
        return Retrofit.Builder()
            .baseUrl(url)
//            .addConverterFactory(GsonConverterFactory.create())
            .addConverterFactory(BaseConverterFactory.create())
            .client(client)
            .build()
    }

    private  inline fun <T> requestGenerator(request: ()->BaseBean<T>) : BaseBean<T>{
        return try{
            request()
        }catch (e :Exception){
            BaseBean<T>().initWithException(e)
        }
    }

    private inline fun <K : Any, T> requestGenerator(kClass: KClass<K>, request : (K)->BaseBean<T>) : BaseBean<T>{
        return try{
            request(mRetrofit.create(kClass.java))
        }
        catch (e: Exception){
            BaseBean<T>().initWithException(e)
        }
    }

    suspend fun requestBasicInfo(merchantId: String): BaseBean<AptitudeBasicInfoBean> =
         requestGenerator { mRetrofit.create(AccountBasicInfoRequest::class.java).requestNews(merchantId)}

    suspend fun login(name: String, password: String): BaseBean<Login> =
            requestGenerator(LoginRequest::class){request-> request.login(name, password)}

    /**
     * 获取发票信息
     */
    suspend fun getInvoiceInfo(merchantId: String, orderId: String): BaseBean<InvoiceListBaseBean> =
            requestGenerator(InvoiceRequest::class) {it.getInvoiceInfo(merchantId, orderId)}

    /**
     * 发送邮件
     */
    suspend fun sendMail(merchantId: String, orderId: String, email: String): BaseBean<Any> =
            requestGenerator(InvoiceRequest::class){it.sendMail(merchantId,orderId,email)}
    /**
     * 获取self店铺信息
     */
    suspend fun getSelfShopInfo(merchantId: String, shopCode: String): BaseBean<ShopBaseInfo> =
            requestGenerator { mRetrofit.create(ShopHomeRequest::class.java).getShopInfo(merchantId, shopCode) }

    /**
     * 获取pop店铺信息
     */
    suspend fun getPopCompanyDetail(merchantId: String, orgId: String, selectDeliveryAddress: Boolean): BaseBean<ShopBasicInfoBean> =
            requestGenerator(ShopHomeRequest::class){it.getPopCompanyDetail(merchantId, orgId, selectDeliveryAddress) }

    /**
     * 获取店铺tab
     */
    suspend fun getShopTabs(merchantId: String): BaseBean<List<ShopTab>> = requestGenerator(ShopHomeRequest::class) { it.getShopTabs(merchantId) }

    /**
     * 获取pop店铺信息中的客服链接
     */
    suspend fun sendOnLineService(isThirdCompany: String): BaseBean<ImPackUrlBean> =
            requestGenerator { mRetrofit.create(ShopHomeRequest::class.java).sendOnLineService(isThirdCompany) }

    /**
     * 获取自营店铺资质列表
     */
    suspend fun getSelfQualification(merchantId: String, shopCode: String): BaseBean<AptitudeXyyBean> =
            requestGenerator(QualificationRequest::class){it.getSelfQualification(merchantId, shopCode)}
    /**
     * 获取Pop店铺资质列表
     */
    suspend fun getPopQualification(originId: String): BaseBean<AptitudeXyyBean> =
            requestGenerator(QualificationRequest::class){it.getPopQualification(originId) }

    /**
     * 店铺-获取自营售后配送内容
     */
    suspend fun getSelfAfterSaleDistributionContent(merchantId: String, shopCode: String): BaseBean<FreightTipBean> =
            requestGenerator(ShopAfterSaleDistributionRequest::class){it.getSelfAfterSaleDistributionContent(merchantId, shopCode) }

    /**
     * 店铺-获取pop售后配送内容
     */
    suspend fun getPopAfterSaleDistributionContent(orgId: String): BaseBean<String> =
            requestGenerator (ShopAfterSaleDistributionRequest::class){it.getPopAfterSaleDistributionContent(orgId)}

    /**
     *
     */
    suspend fun getCouponTemplate(merchantId: String, templateIds: String): BaseBean<List<CouponInfoBean>> = try {
        mRetrofit.create(ProductAddOnItemRequest::class.java).getPopCompanyDetail(merchantId, templateIds)
    } catch (e: Exception) {
        BaseBean<List<CouponInfoBean>>().initWithException(e)
    }

    /**
     *
     */
    suspend fun getVoucherShopListTab(merchantId: String, templateIds: String): BaseBean<VoucherShopListTabBean> = try {
        mRetrofit.create(ProductAddOnItemRequest::class.java).getVoucherShopListTab(merchantId, templateIds)
    } catch (e: Exception) {
        BaseBean<VoucherShopListTabBean>().initWithException(e)
    }

    /**
     *
     */
    suspend fun getVoucherShopListBean(params: Map<String, String>): BaseBean<ShopListBean> = try {
        mRetrofit.create(ProductAddOnItemRequest::class.java).getVoucherShopListBean(params)
    } catch (e: Exception) {
        BaseBean<ShopListBean>().initWithException(e)
    }

    /**
     *
     */
    suspend fun getCartVoucherBean(merchantId: String, templateIds: String): BaseBean<CartVoucher> =
            requestGenerator(ProductAddOnItemRequest::class){ it.getCartVoucherBean(merchantId, templateIds)}

    /**
     * 店铺-获取开户信息
     */
    suspend fun getShopOpenAccountData(merchantId: String, orgId: String): BaseBean<OpenAccount> =
            requestGenerator(OpenAccountRequest::class){it.getShopOpenAccountData(merchantId,orgId) }

//    /**
//     * 获取规格过滤数据
//     */
//    suspend fun getSpecFilterData(merchantId: String, keyword: String, type: String, shopCode: String): BaseBean<SearchAggsBean> =
//        requestGenerator(SpecFilterRequest::class) { it.getShopOpenAccountData(merchantId, keyword, type, shopCode) }

    /**
     * 获取规格过滤数据
     */
    suspend fun getSpecFilterData(map: Map<String, String>): BaseBean<SearchAggsBean> =
        requestGenerator(SpecFilterRequest::class) { it.getShopOpenAccountData(map) }

    /**
     *
     */
    suspend fun findCancelAnAccountStatus(merchantId: String): BaseBean<Boolean> =
        requestGenerator(UnregisterRequest::class) { it.findCancelAnAccountStatus(merchantId)}


    /**
     *
     */
    suspend fun cancelAnAccount(merchantId: String): BaseBean<EmptyBean> =
        requestGenerator(UnregisterRequest::class){ it.cancelAnAccount(merchantId)}

    /**
     * 获取折后价
     */
    suspend fun getPriceAfterDiscount(ids: String): BaseBean<List<RowsPriceDiscount>> =
        requestGenerator(PriceAfterDiscountRequest::class){ it.getPriceAfterDiscount(ids)}

    /**
     * 退款服务类型
     */
    suspend fun getServiceType(orderNo: String): BaseBean<MutableList<ServiceType>> =
        requestGenerator(ServiceTypeRequest::class){ it.getServiceType(orderNo) }

    /**
     * 获取退款原因列表
     */
    suspend fun getRefundReasonList(orderNo: String, merchantId: String, refundOrderDetailList: String): BaseBean<MutableList<RefundReason>> =
        requestGenerator(RefundReasonRequest::class) { it.getRefundReasonList(orderNo, merchantId, refundOrderDetailList) }
}