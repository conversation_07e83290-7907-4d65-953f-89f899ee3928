package com.ybmmarket20.constant;

/**
 * intent-常量
 */
public class IntentCanst {

    public static String PAR_KEY = "rowsbean";
    public static String CART_LISTBEAN = "cartListBean";
    public static String LIST_ADDRESS = "list_address";
    public static String ORDER_ID = "order_id";
    public static String ORDER_NO = "order_no";
    public static String ORDER_STATE = "order_state";
    public static String ORDER_WAITER_NUMBER = "order_waiter_number";
    public static String ACTIVITY_MAIN = "main";
    public static String ACTIVITY_MESSAGE = "url";
    public static String ORDER_DETAIL_WAIT = "order_detail_wait";
    public static String ORDER_DETAIL_PROCESS = "order_detail_process";
    public static String ORDER_DETAIL_FINISH = "order_detail_finish";
    public static String ORDER_DETAIL_DEFAULT = "order_detail_default";
    public static String APTITUDE_UPDATE = "aptitude_update";
    public static String ORDER_DETAIL_ID = "order_detail_id";
    public static String APTITUDE_POSITION = "aptitude_position";
    public static String APTITUDE_IMG_URL = "aptitude_img_url";
    public static String PRODUCTNO = "product_no";
    public static String PRODUCTID = "product_id";
    public static String COMEFROM = "comefrom";
    public static String EMAIL_ADDRESS = "email_address";
    public static String BUY_SALES = "buy_sales";
    public static String VOUCHERINFO = "voucherInfo_state";
    public static String FIRST_RUN = "first_run";
    public static String CAPTURE_PRODUCT = "capture_product";
    public static String CAPTURE_PLAN = "capture_plan";
    public static String CAPTURE_REFUND = "capture_refund";
    public static String GUIDE_RUN = "guide_run";
    public static String MENTION = "mention";
    public static String PAGE_ID = "page_id";
    public static String MODULE = "module";
    public static String OFFSET = "offset";
    public static String SOURCETYPE = "sourceType";
    public static int GUIDE_VERSION = 1;//引导页版本号
    public static String JG_TRACK_BEAN="jgTrackBean";
    public static String JG_REFERRER="jgReferrer";
    public static String JG_REFERRER_TITLE = "jgReferrerTitle";
    public static String JG_REFERRER_MODULE = "jgReferrerModule";  //来源模块
    public static String JG_MODULE = "jgModule";
    public static String JG_ENTRANCE = "jgEntrance";
    public static String ACTIVITY_ENTRANCE = "activityEntrance";
    public static String JG_SEARCH_SORT_STRATEGY_ID = "jgSearchSortStrategyId";
    public static String JG_JGSPID = "jgspid";
    public static String JG_APP_ACTION_TOP_HOT_WORD_CLICK = "app_action_top_hot_word_click";
    public static String JG_JSON_REPORT_PD_EXTEND_OUTER_BEAN = "JG_JSON_REPORT_PD_EXTEND_OUTER_BEAN";

    public static String REPLENISHMENTPROGRAM_CODE = "replenishmentprogram_code";
    public static String REPLENISHMENTPROGRAM_NUMBER = "replenishmentprogram_number";
    public static String OVERLAY_TICKET_LIST = "overlay_ticket_list";
    public static String MSELECTDJVOUCHERLIST = "select_dj_voucher_list";
    public static String MSELECTNORMALVOUCHERLIST = "select_normal_voucher_list";
    public static String SHOP_BASIC_INFO = "shop_Basic_Info";
    public static String SKU_PRICE = "sku_price";
    public static String SKU_ID = "sku_id";
    public static String SNAPSHOT_ID = "snapshot_id";

    //通知我的财富刷新
    public static String ACTION_MY_WEALTH_REFRESH = "MY_WEALTH_REFRESH";

    //退出登陆的广播
    public static String ACTION_LOGOUT = "ACTION_LOGOUT";

    //登陆的广播
    public static String ACTION_LOGIN = "ACTION_LOGIN";

    // 更换首页布局配置
    public static String ACTION_CHANGE_HOME_LAYOUT_TYPE = "action_change_home_layout_type";
    // 更新购物车布局配置
    public static String ACTION_CART_STYLE = "action_cart_style";

    //更新地址的广播
    public static String ACTION_ADDRESS = "ACTION_ADDRESS";

    //购物车为你推荐添加商品通知购物车页刷新数据的广播
    public static String ACTION_CARTFRAGMENT_ADD_GOOD = "ACTION_CARTFRAGMENT_GOOD";

    //凑单页接受加购消息
    public static String ACTION_RELATED_GOODS_ADD_GOODS = "ACTION_RELATED_GOODS_ADD_GOODS";

    //更新main的购物车数量的广播
    public static String ACTION_SHOPNUMBER = "ACTION_SHOPNUMBER";

    //更新main的购物车数量的广播
    public static String ACTION_CART_NUM = "cart_num";

    //购物车数量修改了
    public static String CART_NUM_CHANGED = "CART_NUM_CHANGED";

    //全部商品修改商品数量通知修改购物车商品与详情商品数量
    public static String ACTION_CHANG_CAR_NUMBER = "ACTION_CHANG_PRODUCT_NUMBER";

    //增加商品到购物车
    public static String ACTION_ADD_PRODUCT = "ACTION_ADD_PRODUCT";


    //商品详情增加商品到购物车
    public static String ACTION_ADD_PRODUCT_FROM_DETAIL = "ACTION_ADD_PRODUCT_FROM_DETAIL";

    //商品详情增加商品到购物车通知优惠券页面
    public static String ACTION_ADD_PRODUCT_FROM_DETAIL_TO_COUPON = "ACTION_ADD_PRODUCT_FROM_DETAIL_TO_COUPON";

    //重新获取地址
    public static String ACTION_MERCHANTBASEINFO = "ACTION_MERCHANTBASEINFO";

    //完成订单
    public static String ACTION_BUY_PRODUCT = "ACTION_BUY_PRODUCT";
    // 删除收藏商品
    public static String ACTION_DEL_FAVORITE = "ACTION_DEL_FAVORITE";

    //添加邮箱
    public static String ACTION_NEW_EAMIL = "ACTION_NEW_EAMIL";

    //忘记密码的phone
    public static String ACTION_PHONE = "ACTION_PHONE";
    //验证码
    public static String ACTION_VERIFICATIONCODE = "ACTION_VERIFICATIONCODE";

    //修改密码的phone
    public static String ACTION_NEW_PHONE = "ACTION_NEW_PHONE";

    //修改消息数量
    public static String MSG_NUM_EDIT = "MSG_NUM_EDIT";

    //刷新页面
    public static String REFRESH_PAGE = "REFRESH_PAGE";
    public static String REFRESH_HOT_SEARCH = "REFRESH_HOT_SEARCH";

    //更新心愿单列表的广播
    public static String ACTION_WISHLIST = "ACTION_WISHLIST";

    //个人中心订单状态的广播
    public static String ACTION_ORDER_STATUS = "ACTION_ORDER_STATUS";

    //个人中心切换用户
    public static String ACTION_SWITCH_USER = "ACTION_SWITCH_USER";

    //个人中心确认退款状态
    public static String ACTION_CONFIRM_REFUND_STATUS = "ACTION_CONFIRM_REFUND_STATUS";

    //支付成功刷新首页
    public static String ACTION_SWITCH_HOME_INFO = "action_switch_home_info";

    // pop 店铺中领取到优惠券，刷新优惠券列表
    public static String ACTION_RECEIVE_POP_COUPON = "action_receive_pop_coupon";

    //通知订单列表刷新
    public static String ACTION_ORDER_LIST_REFRESH = "ACTION_ORDER_LIST_REFRESH";

    //通知订单列表指定id的Item刷新
    public static String ACTION_ORDER_LIST_ITEM_REFRESH = "ACTION_ORDER_LIST_REFRESH";

    //全部药品初始化
    public static String ACTION_BRAND_SET = "action_brand_set";

    //计划单商品数量修改
    public static String ACTION_PLAN_EDIT_PRODUCT_NUM = "action_plan_edit_product_num";

    //
    public static String ACTION_COLLECT = "action_collect";

    //首页优惠券弹窗
    public static String ACTION_AD_COLLECT_POP = "action_ad_collect_pop";

    public static String ACTION_AD_COLLECT_HINT_POP = "action_ad_collect_hint_pop";

    public static String ACTION_GIFT_PAYMENT = "action_gift_payment";

    public static String ACTION_GIFT_PAYMENT_2 = "action_gift_payment_2";

    public static String ACTION_GIFT_PRODUCT_LIST = "action_gift_product_list";

    // 购物车底部弹框优惠券去凑单进入凑单页，凑单页关闭时广播action
    public static String ACTION_CART_TO_COUPON_RESULT = "action_cart_to_coupon_result";

    //隐藏首页订单气泡
    public static String ACTION_HIDE_ORDER_BUBBLE = "action_hide_order_bubble";
    //显示首页订单气泡
    public static String ACTION_SHOW_ORDER_BUBBLE = "action_show_order_bubble";


    //切换账户刷新为你推荐
    public static final String ACTION_REFRESH_RECOMMEND = "action_refresh_recommend";

    public static String APPLY_FOR_CONVOY = "apply_for_convoy";

    public static String FINISH_THE_ESCORT = "finish_the_escort";

    public static String SET_NOTIFICATION = "set_notification";

    public static int RX_BUS_UPDATE_LICENCEDTAIL=1001;
    public static int RX_BUS_LICENSE_STATUS=1002;
    public static int RX_BUS_LICENSE_XYY_DOWN_STATUS=1003;
    public static int RX_BUS_NET_ERR=1004;
    public static int RX_BUS_REFRESH_LICENCE_AUDIT_LIST=1005;
    //代下单授权已读标识
    public static int RX_BUS_AGENT_ORDER_AUTHORIZATION_READ_FLAG = 1006;
    //代下单订单授权标识
    public static int RX_BUS_AGENT_ORDER_AUTHORIZATION_STATUS = 1007;
    //代下单驳回标识
    public static int RX_BUS_AGENT_ORDER_REJECT_ORDER = 1008;
    //代下单确认订单
    public static int RX_BUS_AGENT_ORDER_CONFIRM_ORDER = 1009;
    //倒计时取消
    public static int RX_BUS_AGENT_ORDER_CANCLE = 1010;

    //首页搜索pageSource
    public static String PAGE_SOURCE_HOME_SEARCH = "_ahome_bsearch";
    //分类页搜索pageSource
    public static String PAGE_SOURCE_CATEGORY_SEARCH = "_acategory_bsearch";
    //分类列表pageSource
    public static String PAGE_SOURCE_CATEGORY_LIST = "_anavigation_bcategory";
    //常购清单列表pageSource
    public static String PAGE_SOURCE_OFTEN_BUY_LIST = "_anavigation_bfind";

    // 登录爬虫验证 resultKey
    public static String LOGINVERTIFICATIONRESULT = "loginVertificationResult";

}
