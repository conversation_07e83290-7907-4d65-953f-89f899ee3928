package com.ybmmarket20.view.homesteady

import android.content.Context
import android.text.TextUtils
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter
import com.ybmmarket20.R
import com.ybmmarket20.bean.homesteady.HostSearchItem
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import org.json.JSONObject

/**
 * <AUTHOR>
 * @date 2020-05-09
 * @description 首页推荐热搜词
 */

class HomeSteadyRecommendHostKeyAdapter(
        var mContext: Context,
        keys: MutableList<HostSearchItem>
) : YBMBaseMultiItemAdapter<HostSearchItem>(keys) {

    init {
        addItemType(HOME_STEADY_LAYOUT_REAL, R.layout.item_home_steady_recommend_hot_key_real)
        addItemType(HOME_STEADY_LAYOUT_DEFAULT, R.layout.item_home_steady_recommend_hot_key_default)
    }

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: HostSearchItem?) {
        t?.also {
          if (t.itemType == HOME_STEADY_LAYOUT_REAL) {
              baseViewHolder?.setText(R.id.tv_hot_key, t.keyword)
              baseViewHolder?.getConvertView()?.setOnClickListener {

                  XyyIoUtil.track(XyyIoUtil.ACTION_HOME_HOTSEARCHWORD, JSONObject().apply { put("word", t.keyword) })

                  if (!TextUtils.isEmpty(t.androidUrl)) {
                      RoutersUtils.open(t.androidUrl)
                  } else {
                      RoutersUtils.open("ybmpage://searchproduct?keyword=" + t.keyword)
                  }
              }
          }
        }
    }


}