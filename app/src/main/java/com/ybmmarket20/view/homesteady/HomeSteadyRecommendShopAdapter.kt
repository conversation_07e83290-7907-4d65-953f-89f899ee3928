package com.ybmmarket20.view.homesteady

import android.text.TextUtils
import android.util.SparseArray
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybmmarket20.R
import com.ybmmarket20.bean.RecommendShopInfoContentItem
import com.ybmmarket20.utils.analysis.BaseFlowData
import com.ybmmarket20.utils.analysis.openUrl
import com.ybmmarket20.view.ShopNameWithTagView
import com.ybmmarket20.view.homesteady.callback.IRecommendShopAnalysisCallback
import kotlin.math.ceil

/**
 * 首页人气好店模块适配器
 */
class HomeSteadyRecommendShopAdapter(
    list: MutableList<RecommendShopInfoContentItem>,
    val analysisCallback: IRecommendShopAnalysisCallback?
): YBMBaseAdapter<RecommendShopInfoContentItem>(R.layout.item_home_steady_recommend_shop, list) {

    private val traceProductData = SparseArray<String>()

    override fun bindItemView(holder: YBMBaseHolder, recommendShopInfoItem: RecommendShopInfoContentItem?) {
        val shopIcon = holder.getView<ImageView>(R.id.iv_recommend_shop)
        val shopName = holder.getView<TextView>(R.id.tv_recommend_shop_name)
        val tagView = holder.getView<ShopNameWithTagView>(R.id.snwt_recommend_shop_tag)
        recommendShopInfoItem?.also {
            ImageHelper.with(mContext).load(it.logo).diskCacheStrategy(DiskCacheStrategy.SOURCE).dontAnimate().into(shopIcon)
            shopName.text = it.name
            tagView.bindData(it.shopPropTags, "")
            if (it.shopPropTags == null || it.shopPropTags!!.isEmpty()) {
                tagView.visibility = View.GONE
            } else {
                tagView.visibility = View.VISIBLE
            }
            setActivityTag(holder, it)
            holder.itemView.setOnClickListener{ _ ->
                openUrl(it.indexLink, BaseFlowData(it.sptype, it.spid, it.sid, "", ""))
                val rows = ceil(((holder.bindingAdapterPosition + 1) / 2f).toDouble()).toInt()
                val index = holder.bindingAdapterPosition - (rows - 1) * 2 + 1
                analysisCallback?.onHomeSteadyAnalysisRecommendShopClick("$rows-$index", it.shopCode?: "")
            }
            handleAnalysisExposure(holder, it)
        }
    }

    /**
     * 设置活动标签
     */
    private fun setActivityTag(holder: YBMBaseHolder, recommendShopInfoItem: RecommendShopInfoContentItem) {
        val clRecommendShopCoupon = holder.getView<ConstraintLayout>(R.id.cl_recommend_shop_coupon)
        val clRecommendShopPromotion = holder.getView<ConstraintLayout>(R.id.cl_recommend_shop_promotion)
        val tvRecommendShopDelivery = holder.getView<TextView>(R.id.tv_recommend_shop_delivery)
        val tvRecommendShopCouponContent = holder.getView<TextView>(R.id.tv_recommend_shop_coupon_content)
        val tvRecommendShopPromotionContent = holder.getView<TextView>(R.id.tv_recommend_shop_promotion_content)
        val activityInfo = recommendShopInfoItem.activityInfo
        val freightTips = recommendShopInfoItem.freightTips
        clRecommendShopCoupon.visibility = View.GONE
        clRecommendShopPromotion.visibility = View.GONE
        tvRecommendShopDelivery.visibility = View.GONE
        if (activityInfo != null) {
            //活动标签
            if (activityInfo.activityType == 1) {
                //券
                clRecommendShopCoupon.visibility = View.VISIBLE
                tvRecommendShopCouponContent.text = "券 | ${activityInfo.activityContent}"
            } else {
                //其他标签
                clRecommendShopPromotion.visibility = View.VISIBLE
                tvRecommendShopPromotionContent.text = activityInfo.activityContent
            }
        } else if(!TextUtils.isEmpty(freightTips)) {
            //起送包邮
            tvRecommendShopDelivery.visibility = View.VISIBLE
            tvRecommendShopDelivery.text = freightTips
        }
    }

    /**
     * 处理商品曝光
     */
    private fun handleAnalysisExposure(holder: YBMBaseHolder, t: RecommendShopInfoContentItem) {
        if (traceProductData[holder.adapterPosition] == null) {
            val rows = ceil(((holder.bindingAdapterPosition + 1) / 2f).toDouble()).toInt()
            val index = holder.bindingAdapterPosition - (rows - 1) * 2 + 1
            analysisCallback?.onHomeSteadyAnalysisRecommendShopExposure(t.shopCode, "$rows-$index")
            traceProductData.put(holder.adapterPosition, t.shopCode)
        }
    }
}