package com.ybmmarket20.view;

import static com.ybmmarket20.utils.analysis.AnalysisConst.Cart.ACTION_GROUPPURCHASE_GIFTCARD_CLICK;
import static com.ybmmarket20.utils.analysis.AnalysisConst.Cart.ACTION_GROUPPURCHASE_GIFTCARD_EXPOSURE;

import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.os.Message;
import android.text.InputType;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.activity.ComponentActivity;
import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.constraintlayout.widget.Group;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.SavedStateViewModelFactory;
import androidx.lifecycle.ViewModelProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.apkfuns.logutils.LogUtils;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.google.gson.Gson;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.BaseYBMApp;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.ActPtBean;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.CartDataBean;
import com.ybmmarket20.bean.CartGoodsInfo;
import com.ybmmarket20.bean.JgRequestParams;
import com.ybmmarket20.bean.ProductDetailBean;
import com.ybmmarket20.bean.ProductDetailBeanWrapper;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.bean.SettleBean;
import com.ybmmarket20.bean.SpellGroupGoodsItem;
import com.ybmmarket20.bean.SpellGroupPromoBean;
import com.ybmmarket20.bean.SpellGroupRecommendGoodsBean;
import com.ybmmarket20.bean.TagBean;
import com.ybmmarket20.bean.product_detail.ReportPDButtonClick;
import com.ybmmarket20.bean.product_detail.ReportPDExtendOuterBean;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.JGTrackManager;
import com.ybmmarket20.common.JGTrackTopLevelKt;
import com.ybmmarket20.common.JgOperationPositionInfo;
import com.ybmmarket20.common.JgTrackBean;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.db.info.HandlerGoodsDao;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.reportBean.AddToCart;
import com.ybmmarket20.reportBean.JGPageListCommonBean;
import com.ybmmarket20.utils.DialogUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.StringUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.utils.analysis.BaseFlowData;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.viewmodel.ListItemAddCartViewModel;
import com.ybmmarket20.viewmodel.SpellGroupRecommendGoodsViewModel;
import com.ybmmarket20.viewmodel.SpellGroupRecommendGoodsViewModelKt;
import com.ybmmarket20.viewmodel.viewstore.GlobalViewModelStore;
import com.ybmmarket20.xyyreport.page.common.addCart.AddCartPopupWindowReport;
import com.ybmmarket20.xyyreport.page.order.ChangeCartUtil;
import com.ybmmarketkotlin.utils.TextViewKt;
import com.ydmmarket.report.ReportManager;

import org.jetbrains.annotations.NotNull;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class ShowSpellGroupPopWindow extends BaseBottomPopWindow implements CSUListAdapter.CSUItemEventListener {

    private CartDataBean mCartDataBean;

    @Override
    public void onItemClick(String skuId) {
        HashMap<String, String> m = new HashMap<String, String>();
        m.put("productId", skuId);
        XyyIoUtil.track(ACTION_GROUPPURCHASE_GIFTCARD_CLICK, m);
        ShowSpellGroupPopWindow.this.dismiss();
    }

    @Override
    public void onItemExposure(String skuId) {
        HashMap<String, String> m = new HashMap<String, String>();
        m.put("productId", skuId);
        XyyIoUtil.track(ACTION_GROUPPURCHASE_GIFTCARD_EXPOSURE, m);
    }

    static class PromotionTagHolder extends BaseViewHolder {
        private TextView mTitleText;
        private TextView mDetailText;
        private CSUListView mCSUListview;

        public PromotionTagHolder(View view) {
            super(view);
            mTitleText = view.findViewById(R.id.title);
            mDetailText = view.findViewById(R.id.desc_title);
            mCSUListview = view.findViewById(R.id.csu_item);
        }
    }

    class PromotionTagAdapter extends BaseQuickAdapter<TagBean, PromotionTagHolder> {
        public PromotionTagAdapter(int layoutResId) {
            super(layoutResId);
        }

        @NonNull
        @NotNull
        @Override
        public PromotionTagHolder onCreateViewHolder(@NonNull @NotNull ViewGroup parent, int viewType) {
            PromotionTagHolder holder = super.onCreateViewHolder(parent, viewType);
            holder.mCSUListview.setItemEventListener(ShowSpellGroupPopWindow.this);
            return holder;
        }


        @Override
        protected void convert(@NonNull @NotNull PromotionTagHolder promotionTagHolder, TagBean tagBean) {
            TextViewKt.tagStyle(promotionTagHolder.mTitleText, tagBean);
            promotionTagHolder.mDetailText.setText(tagBean.description);
            if (tagBean.csuList != null && tagBean.csuList.size() > 0) {
                promotionTagHolder.mCSUListview.setVisibility(View.VISIBLE);
                promotionTagHolder.mCSUListview.setListData(tagBean.csuList, tagBean, true);
                promotionTagHolder.mCSUListview.setIsMainProductVirtualSupplier(mDataBean.isVirtualSupplier);
            } else {
                promotionTagHolder.mCSUListview.setVisibility(View.GONE);
            }
        }
    }


    private static int CHANGE_SPELL_GROUP_SUCESS = 10;

    private Context context;

    private TextView tvSpellGroupPopName;
    private TextView tvSpellGroupPopSpec;
    private TextView tvSpellGroupPopInventory;
    private TextView tvSpellGroupPopEffective;
    private TextView tvSpellGroupPopInitial;
    private TextView tvSpellGroupPopsubmit;
    private TextView tvTotalAmount;
    private TextView tvNumber;
    private TextView tvTips;
    private TextView tvLimited;
//    private TextView tvTips2;
    private TextView tvSpellGroupPrice;
    private LinearLayout llDiscount;
    private Group groupSpellGroupPrice;
    private AppCompatImageView ivNumSub;
    private AppCompatImageView ivNumAdd;
    private int finalSkuStartNum;
    private Handler handler;
    public BaseFlowData mFlowData;//埋点数据
    public ProductDetailBeanWrapper.ShopInfo mShopInfo;//商铺信息
    private RecyclerView mPromoList;
    private PromotionTagAdapter mPromotionTagAdapter;
    private ProductDetailBean mDataBean;
    private boolean mIsWholeSale;
    private int SPELL_GROUP_RECOMMEND_TYPE = 0;

    private boolean mIsFromList;
    public JgTrackBean jgTrackBean;
    public ReportPDExtendOuterBean jgExtendOuterBean;

    private ListItemAddCartViewModel listItemAddCartViewModel;

    public ShowSpellGroupPopWindow(Context context, ProductDetailBean productDetail, ActPtBean actPtBean, boolean isAssemble, boolean isWholeSale, boolean isFromList) {
        if (productDetail == null) {
            return;
        }
        this.context = context;
        mIsWholeSale = isWholeSale;
        mIsFromList = isFromList;
        setData(productDetail, actPtBean, isAssemble, isWholeSale);
    }

    public ShowSpellGroupPopWindow(Context context, ProductDetailBean productDetail, ActPtBean actPtBean, boolean isAssemble, boolean isWholeSale) {
        this(context, productDetail, actPtBean, isAssemble, isWholeSale, false);
    }

    public ShowSpellGroupPopWindow(Context context, ProductDetailBean productDetail, ActPtBean actPtBean, boolean isAssemble,
                                   boolean isWholeSale, int type) {
        this(context, productDetail, actPtBean, isAssemble, isWholeSale, false);
        this.SPELL_GROUP_RECOMMEND_TYPE = type;
    }

    private void setData(final ProductDetailBean productDetail, ActPtBean actPtBean, boolean isAssemble, boolean isWholeSale) {
        mDataBean = productDetail;
        setBasicInformation(productDetail, actPtBean, isAssemble, isWholeSale);

        bindNumAddOrSub(productDetail, actPtBean);

//        contentView.findViewById(R.id.tv_spell_group_pop_down).setOnClickListener(v -> dismiss());
        tvSpellGroupPopsubmit.setOnClickListener(v -> {
            AddCartPopupWindowReport.trackAddCartBtnClick(context, productDetail, tvSpellGroupPopsubmit.getText().toString(), 4);
            if (SPELL_GROUP_RECOMMEND_TYPE == 3) {
                addShopCart(productDetail.id, tvNumber.getText().toString().trim());
            } else {
                checkoutGotoSettle(productDetail);
                HashMap<String, String> trackParams = new HashMap<>();
                trackParams.put("skuId", productDetail.id + "");
                XyyIoUtil.track("action_FreeShipping_Click", trackParams);
            }

            if (jgTrackBean!= null){
                String btnName = tvSpellGroupPopsubmit.getText().toString();
                JGTrackTopLevelKt.jgTrackProductDetailNewBtnClick(
                        context,
                        jgTrackBean.getUrl(),
                        jgTrackBean.getPageId(),
                        jgTrackBean.getTitle(),
                        btnName,
                        "底部弹窗",
                        jgTrackBean.getJgReferrer(),
                        1,
                        jgTrackBean.getProductId(),
                        jgTrackBean.getProductType(),
                        "",
                        1,
                        jgTrackBean.getModule(),
                        "",
                        ""
                        );
                popWindowButtonClickJGTrack();
            }
        });

        //增加品的点击事件
        mPromoList.setAdapter(mPromotionTagAdapter);
    }


    @Override
    public void showAtTopOfView(View token) {
        super.showAtTopOfView(token);
        loadCSUInfo();
    }

    @Override
    public void show(View token) {
        super.show(token);
        loadCSUInfo();

        String btnName = tvSpellGroupPopsubmit.getText().toString();
        if (jgTrackBean != null){
            // 移除极光埋点 - btn_exposure
            /*
            JGTrackTopLevelKt.jgTrackProductDetailNewBtnExposure(
                    context,
                    jgTrackBean.getUrl(),
                    jgTrackBean.getPageId(),
                    jgTrackBean.getTitle(),
                    btnName,
                    "商详页",
                    jgTrackBean.getJgReferrer(),
                    1,
                    jgTrackBean.getProductId(),
                    jgTrackBean.getProductType(),
                    "",
                    1,
                    jgTrackBean.getModule(),
                    "",
                    ""
            );
            */
        }

    }

    private void popWindowButtonClickJGTrack() {
        // 移除极光埋点 - action_product_button_click
        /*
        ReportPDButtonClick pdBean = new ReportPDButtonClick();
        pdBean.setUrl(jgTrackBean.getUrl());
        pdBean.setReferrer(jgTrackBean.getJgReferrer());
        pdBean.setTitle(jgTrackBean.getTitle());
        pdBean.setAccountId(SpUtil.getAccountId());
        pdBean.setMerchantId(SpUtil.getMerchantid());
        pdBean.setOuterBean(jgExtendOuterBean);
        pdBean.setProductId(mDataBean.id);
        pdBean.setProductName(mDataBean.showName);
        pdBean.setProductFirst(mDataBean.categoryFirstId);
        pdBean.setProductPrice(mDataBean.jgPrice);
        pdBean.setProductType(String.valueOf(mDataBean.productType));
        pdBean.setProductShopCode(mDataBean.shopCode);
        pdBean.setProductShopName(mShopInfo.shopName);
        pdBean.setProductActivityType(mDataBean.productActivityType);
        try{
            pdBean.setProductNumber(Integer.valueOf(tvNumber.getText().toString().trim()));
        }catch (Exception e){
            e.printStackTrace();
        }
        pdBean.setBtnName("确定");
        pdBean.setBtnDesc("商详页底部弹窗");
        pdBean.setDirect("2");
        ReportManager.getInstance().report(pdBean);
        */
    }

    private void loadCSUInfo() {
        if (mDataBean == null) return;
        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.PROM_INFO_URL).addParam("csuId", mDataBean.id + "").build();
        HttpManager.getInstance().post(params, new BaseResponse<SpellGroupPromoBean>() {
            @Override
            public void onSuccess(String content, BaseBean<SpellGroupPromoBean> obj, SpellGroupPromoBean spellGroupPromoBean) {
                super.onSuccess(content, obj, spellGroupPromoBean);
                mPromotionTagAdapter.setNewData(spellGroupPromoBean.getTagList());
                if (spellGroupPromoBean.getTagList() != null && spellGroupPromoBean.getTagList().isEmpty()) {
                    llDiscount.setVisibility(View.GONE);
                } else {
                    llDiscount.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                mPromoList.setVisibility(View.GONE);
            }
        });

        // 加入购物车的情况调用黄标接口
        if (SPELL_GROUP_RECOMMEND_TYPE == 3) {
            if (context instanceof BaseActivity) {
                listItemAddCartViewModel =  new ViewModelProvider((BaseActivity) context).get(ListItemAddCartViewModel.class);
                listItemAddCartViewModel.getListItemAddCartLiveData().observe((BaseActivity) context,listItemAddCardBeanBaseBean -> {
                    if (listItemAddCardBeanBaseBean.data != null && listItemAddCardBeanBaseBean.data.getFreightTips() != null) {
                        if (!listItemAddCardBeanBaseBean.data.getFreightTips().isEmpty()) {
                            tvTips.setVisibility(View.VISIBLE);
                            tvTips.setText(listItemAddCardBeanBaseBean.data.getFreightTips());
                        }
                    }
                });
            }

            if (listItemAddCartViewModel != null) {
                // skuId: String, quantity: String, orgId: String, productPrice: String,entrance:String,
                // activityEntrance:String, productType : Int, freeShippingFlag : Boolean
                TagBean orderFreeShippingTag = null;
                if (mDataBean != null) {
                    if (mDataBean.tags != null) {
                        orderFreeShippingTag = mDataBean.tags.orderFreeShippingTag;
                    }
                }
                listItemAddCartViewModel.addCartTrial(String.valueOf(mDataBean.id), tvNumber.getText().toString(), mDataBean.orgId,
                        String.valueOf(mDataBean.fob), "", "", mDataBean.productType, orderFreeShippingTag != null);
            }
        } else {
            tvTips.setVisibility(View.GONE);
        }
    }


    private void setBasicInformation(ProductDetailBean productDetail, ActPtBean actPtBean, boolean isAssemble, boolean isWholeSale) {
        if (productDetail == null) {
            return;
        }
        String spec = "规格:" + productDetail.spec;
        String availableQty = "库存:" + (productDetail.availableQty > 100 ? "大于100" : "" + productDetail.availableQty);
        int skuStartNum = actPtBean.skuStartNum;
        String suffix = "";
        if (isAssemble) {
            suffix = "起拼";
        } else if (isWholeSale) {
            suffix = "起购";
        }
        String skuStartNumStr = skuStartNum + productDetail.getProductUnit() + suffix;
        final boolean isValidity = ((TextUtils.isEmpty(productDetail.nearEffect)) || "-".equals(productDetail.nearEffect)) || (TextUtils.isEmpty(productDetail.farEffect) || "-".equals(productDetail.farEffect));

        //药品名称
        tvSpellGroupPopName.setText(productDetail.showName);
        //规格
        tvSpellGroupPopSpec.setText(spec);
        //拼团价
        if (actPtBean.isStepPrice()) {
            groupSpellGroupPrice.setVisibility(View.VISIBLE);
//            tvSpellGroupPrice.setText("¥" + UiUtils.transform(actPtBean.getMinSkuPrice()));
        } else {
            groupSpellGroupPrice.setVisibility(View.GONE);
        }
        //库存
        tvSpellGroupPopInventory.setText(availableQty);
        //效期 近效期：nearEffect  远效期：farEffect
        tvSpellGroupPopEffective.setVisibility(View.VISIBLE);
        if (isValidity) {
            tvSpellGroupPopEffective.setText("有效期：-");
        } else if (TextUtils.isEmpty(productDetail.effectStr)) {
            tvSpellGroupPopEffective.setText("有效期：-");
        } else {
            SpannableStringBuilder title = new SpannableStringBuilder("有效期：");
            SpannableStringBuilder content = new SpannableStringBuilder(productDetail.effectStr);
            ForegroundColorSpan span = new ForegroundColorSpan(ContextCompat.getColor(context, R.color.color_292933));
            content.setSpan(span, 0, content.length() - 1, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            title.append(content);
            tvSpellGroupPopEffective.setText(title);
        }
        //起拼数量
        tvSpellGroupPopInitial.setText(skuStartNumStr);
        tvNumber.setText(String.valueOf(skuStartNum));
        try {
            changeNumber(Integer.parseInt(tvNumber.getText().toString().trim()), productDetail.id, Integer.parseInt(actPtBean.marketingId), handler,true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private RequestParams getRefreshParams(ProductDetailBean productDetail) {
        String merchantId = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        params.put("skuId", String.valueOf(productDetail.id));
        params.put("productNum", tvNumber.getText().toString().trim());
        return params;
    }

    /**
     * 校验订单是否可以跳转到待确认订单页
     */
    private void checkoutGotoSettle(ProductDetailBean productDetail) {
        if (productDetail == null) {
            return;
        }

        HttpManager.getInstance().post(AppNetConfig.ORDER_V1_GROUPPURCHASEPRESETTLE, getRefreshParams(productDetail), new BaseResponse<SettleBean>() {
            public void onSuccess(String content, BaseBean<SettleBean> data, SettleBean bean) {

                if (data != null) {
                    if (data.isSuccess()) {
                        if (bean.isShowDialog == 1) {
                            new AlertDialogEx(context)
                                .setMessage("您的资质已过期，请及时更新，以免影响发货")
                                .setCanceledOnTouchOutside(false)
                                .setConfirmButton("我知道了", (dialog, button) -> {
                                    RoutersUtils.open(getJumpUrl(productDetail, bean));
                                }).show();
                        } else {
                            RoutersUtils.open(getJumpUrl(productDetail, bean));
                        }
                        dismiss();
                    }
                }
            }
        });
    }

    /**
     * 获取跳转路由
     * @param productDetail
     * @param bean
     */
    private String getJumpUrl(ProductDetailBean productDetail, SettleBean bean) {
        if (context instanceof ComponentActivity) {
            SpellGroupRecommendGoodsViewModel viewModel = new ViewModelProvider(
                    GlobalViewModelStore.Companion.get().getGlobalViewModelStore(),
                    new SavedStateViewModelFactory(((ComponentActivity) context).getApplication(), (ComponentActivity) context)
            ).get(SpellGroupRecommendGoodsViewModel.class);
            //如果支持随心拼则添加主品
            Map<String, String> params = new HashMap<>();
            if (productDetail.actPtBean != null && productDetail.actPtBean.supportSuiXinPin && mCartDataBean != null) {
                SpellGroupGoodsItem mainCartGoodsInfo = new SpellGroupGoodsItem();
                mainCartGoodsInfo.setGoodsSelectedCount(mCartDataBean.qty);
                viewModel.setMainGoodsCount(mCartDataBean.qty + "");
                mainCartGoodsInfo.setSkuId(productDetail.id + "");
                mainCartGoodsInfo.setGoodsUrl(productDetail.imageUrl);
                mainCartGoodsInfo.setGoodsTitle(productDetail.showName);
                mainCartGoodsInfo.setGoodsUnit(productDetail.productUnit);
                mainCartGoodsInfo.setGoodsPrice(mCartDataBean.price);
                mainCartGoodsInfo.setTotalPrice(mCartDataBean.totalAmount);
                mainCartGoodsInfo.setNearEffect(productDetail.nearEffect);
                SpellGroupRecommendGoodsBean spellGroupRecommendGoodsBean = new SpellGroupRecommendGoodsBean(new ArrayList<>(), mainCartGoodsInfo, new HashMap<>(), new CartGoodsInfo(), false);
                viewModel.updateData(spellGroupRecommendGoodsBean, false);
            }
            params.put("isSupportOldSxp", "1");
            params.put("tranNo", bean.tranNo);
            params.put("skuId", String.valueOf(productDetail.id));
            params.put("productNum", tvNumber.getText().toString().trim());
            params.put("isFromProductDetail", "1");
            if (productDetail.shopCode != null) {
                params.put("shopCode", productDetail.shopCode);
            }
            if (mIsWholeSale) {
                params.put("isPgby", "1");
            }
            if (mFlowData != null) {
                if (mFlowData.getSpType() != null) {
                    params.put("spType", mFlowData.getSpType());
                }
                if (mFlowData.getSpId() != null) {
                    params.put("spId", mFlowData.getSpId());
                }
                if (mFlowData.getSId() != null) {
                    params.put("sId", mFlowData.getSId());
                }
            }

            if (mDataBean.mJgTrackBean != null){
                if (mDataBean.mJgTrackBean.getEntrance() != null){
                    params.put(IntentCanst.JG_ENTRANCE, mDataBean.mJgTrackBean.getEntrance());
                }
                if (mDataBean.mJgTrackBean.getActivityEntrance() !=null){
                    params.put(IntentCanst.ACTIVITY_ENTRANCE, mDataBean.mJgTrackBean.getActivityEntrance());
                }
            }
            return viewModel.getJumpRouter(SpellGroupRecommendGoodsViewModelKt.CURRENT_PAGE_DEFAULT, params);
        }
        return null;
    }

    /*
     * 数量加减
     * */
    private void bindNumAddOrSub(ProductDetailBean productDetail, ActPtBean actPtBean) {
        if (productDetail == null || actPtBean == null) {
            return;
        }

        int skuId = productDetail.id;
        int marketingId = 0;
        try {
            marketingId = Integer.parseInt(actPtBean.marketingId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        int skuStartNum = actPtBean.skuStartNum;
        int split = productDetail.isSplit;
        boolean isSplit = (split == 1);//是否可拆零 0:不可拆零；1:可拆零 默认1

        if (skuStartNum <= 1) {
            skuStartNum = 1;
        }

        // 商品不可拆零：按照商品的中包装数量累加和递减；
        // 商品可拆零：按照商品中包装数量累加，按照1递减；
        // 如果已经等于起拼数量，则不可再减

        // 起拼数量
        finalSkuStartNum = skuStartNum;
        // 中包装数量
        int mediumPackageNum = productDetail.mediumPackageNum;
        int finalMarketingId = marketingId;
        ivNumSub.setOnClickListener(v -> {
            String num = tvNumber.getText().toString().trim();
            if (TextUtils.isEmpty(num) || num.length() > 4) {
                return;
            }
            numOnClick(num, mediumPackageNum, skuId, finalMarketingId, isSplit, false, handler);
        });

        ivNumAdd.setOnClickListener(v -> {
            String num = tvNumber.getText().toString().trim();
            if (TextUtils.isEmpty(num) || num.length() > 4) {
                return;
            }
            numOnClick(num, mediumPackageNum, skuId, finalMarketingId, isSplit, true, handler);
        });
        //编辑弹出对话框加减数量
        tvNumber.setOnClickListener(v -> {
            String num = tvNumber.getText().toString().trim();

            if (context instanceof BaseActivity) {
                ((BaseActivity) context).hideSoftInput();
            }
            DialogUtil.addOrSubDialog(((BaseActivity) v.getContext()), InputType.TYPE_CLASS_NUMBER, num, mediumPackageNum, isSplit, true, new DialogUtil.DialogClickListener() {

                private InputMethodManager mImm;

                @Override
                public void confirm(String content) {
                    int shopNum;
                    try {
                        shopNum = Integer.valueOf(content);
                    } catch (Exception ex) {
                        shopNum = 0;
                    }
                    if (shopNum < finalSkuStartNum) {
                        shopNum = finalSkuStartNum;
                    }
                    changeNumber(shopNum, skuId, finalMarketingId, handler,false);
                }

                @Override
                public void cancel() {

                }

                @Override
                public void showSoftInput(final View view) {
                    try {
                        if (mImm == null) {
                            mImm = (InputMethodManager) (view.getContext()).getSystemService(Context.INPUT_METHOD_SERVICE);
                        }
                        if (mImm != null) {
                            mImm.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT);
                        }
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }
                }
            });
        });
    }

    /**
     * 加减编辑购买数量
     */
    private void numOnClick(String num, int mediumPackageNum, int skuId, int marketingId, boolean isSplit, boolean isAdd, Handler handler) {

        //获取商品的数量
        int shopNum;
        try {
            shopNum = Integer.valueOf(num);
        } catch (Exception ex) {
            shopNum = 0;
        }

        if (!isAdd) {
            if (isSplit) {
                shopNum--;
            } else {
                shopNum -= mediumPackageNum;
            }
            if (shopNum < finalSkuStartNum) {
                shopNum = finalSkuStartNum;
            }
        } else {
            shopNum += mediumPackageNum;
            if (shopNum > 99999) {
                shopNum = 99999;
            }
        }
        changeNumber(shopNum, skuId, marketingId, handler,false);
    }

    private void changeNumber(int amount, int skuId, int promoId, Handler handler,boolean changeTips) {
        String merchantId = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        JgRequestParams jgRequestParams = new JgRequestParams();
        params.put("merchantId", merchantId);
        params.put("amount", String.valueOf(amount));
        params.put("skuId", String.valueOf(skuId));
        params.put("promoId", String.valueOf(promoId));
        params.put("scenceType", mIsWholeSale? "1": "0");//1：批购包邮，0：非批购包邮

        if (mDataBean.mJgTrackBean != null){
            if (mDataBean.mJgTrackBean.getEntrance() != null){
//                params.put("entrance", mDataBean.mJgTrackBean.getEntrance());
                jgRequestParams.setEntrance(mDataBean.mJgTrackBean.getEntrance());
            }
            if (mDataBean.mJgTrackBean.getActivityEntrance() !=null){
//                params.put("activityEntrance", mDataBean.mJgTrackBean.getActivityEntrance());
                jgRequestParams.setActivity_entrance(mDataBean.mJgTrackBean.getActivityEntrance());
            }
            if (JGTrackManager.Companion.getSuperProperty(context,JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID) != null){
                String searchSortStrategyCode = (String)JGTrackManager.Companion.getSuperProperty(context,JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID) ;
//                params.put("searchSortStrategyCode",searchSortStrategyCode);
                jgRequestParams.setSearch_sort_strategy_id(searchSortStrategyCode);
            }
        }
        if(JGTrackManager.GlobalVariable.INSTANCE.getMJgOperationInfo() != null){
            JgOperationPositionInfo mJgOperationInfo = JGTrackManager.GlobalVariable.INSTANCE.getMJgOperationInfo();
            if (mJgOperationInfo.getProductId()!= null && !mJgOperationInfo.getProductId().isEmpty() && Objects.equals(mJgOperationInfo.getProductId(), String.valueOf(skuId))){
                if (mJgOperationInfo.getOperationId()!=null){
//                    params.put("operationId", mJgOperationInfo.getOperationId());
                    jgRequestParams.setOperation_id(mJgOperationInfo.getOperationId());
                }
                if (mJgOperationInfo.getOperationRank() != null){
                    jgRequestParams.setOperation_rank(mJgOperationInfo.getOperationRank());
//                    params.put("operationRank", mJgOperationInfo.getOperationRank().toString());
                }

                if (mJgOperationInfo.getRank() != null){
                    jgRequestParams.setRank(mJgOperationInfo.getRank());
//                    params.put("rank", mJgOperationInfo.getRank().toString());
                }
            }
        }

        if (JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchRowsBean() != null){
            RowsBean mJgSearchRowsBean = JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchRowsBean();
            if (mJgSearchRowsBean.getProductId()!=null && !mJgSearchRowsBean.getProductId().isEmpty() && Objects.equals(mJgSearchRowsBean.getProductId(), String.valueOf(skuId))){
                jgRequestParams.setList_position_type(mJgSearchRowsBean.positionType+"");
                if (mJgSearchRowsBean.positionTypeName != null){
                    jgRequestParams.setList_position_typename(mJgSearchRowsBean.positionTypeName);
                }
                if (mJgSearchRowsBean.searchKeyword != null){
                    jgRequestParams.setKey_word(mJgSearchRowsBean.searchKeyword);
                }
                jgRequestParams.setProduct_id(mJgSearchRowsBean.getProductId());
                jgRequestParams.setProduct_name(mJgSearchRowsBean.getProductName());
                jgRequestParams.setProduct_first(mJgSearchRowsBean.categoryFirstId);
                jgRequestParams.setProduct_number(mJgSearchRowsBean.getProductNumber());
                jgRequestParams.setProduct_price(mJgSearchRowsBean.getJgProductPrice());
                jgRequestParams.setProduct_type(String.valueOf(mJgSearchRowsBean.productType));
                jgRequestParams.setProduct_activity_type(mJgSearchRowsBean.productActivityType);
                jgRequestParams.setProduct_shop_code(mJgSearchRowsBean.shopCode);
                jgRequestParams.setProduct_shop_name(mJgSearchRowsBean.shopName);

                if (JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchSomeField() != null){
                    jgRequestParams.setRank(JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchSomeField().getRank());
                    JGPageListCommonBean mJgPageListCommonBean = JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchSomeField().getMJgPageListCommonBean();
                    if (mJgPageListCommonBean != null){
                        jgRequestParams.setSptype(mJgPageListCommonBean.getSptype());
                        jgRequestParams.setJgspid(mJgPageListCommonBean.getJgspid());
                        jgRequestParams.setSid(mJgPageListCommonBean.getSid());
                        jgRequestParams.setPage_no(mJgPageListCommonBean.getPage_no());
                        jgRequestParams.setResult_cnt(mJgPageListCommonBean.getResult_cnt());
                        jgRequestParams.setPage_size(mJgPageListCommonBean.getPage_size());
                        jgRequestParams.setTotal_page(mJgPageListCommonBean.getTotal_page());
                    }

                }
            }
        }
        jgRequestParams.setProduct_number(amount);
        jgRequestParams.setDirect("2");
        jgRequestParams.setSession_id(com.ydmmarket.report.manager.TrackManager.getSessionId(YBMAppLike.getAppContext()));

        params.put("mddata", new Gson().toJson(jgRequestParams));//极光埋点通用字段
        if (jgTrackBean!= null && jgTrackBean.getEntrance()!=null && jgTrackBean.getEntrance().contains(JGTrackManager.TrackShoppingCart.TITLE)){ //购物车只传个direct = "3"
            params.getParamsMap().remove("mddata");
        }

        HttpManager.getInstance().post(AppNetConfig.CHANGECARTFORPROMOTION, params, new BaseResponse<CartDataBean>() {

            @Override
            public void onSuccess(String content, BaseBean<CartDataBean> obj, CartDataBean baseBean) {

                if (null != obj) {
                    if (obj.isSuccess()) {
                        if (changeTips && !TextUtils.isEmpty(baseBean.actPurchaseTip)) {
                            tvLimited.setVisibility(View.VISIBLE);
                            tvLimited.setText(baseBean.actPurchaseTip);
                        }
                        mCartDataBean = baseBean;
                        Message msg = handler.obtainMessage(CHANGE_SPELL_GROUP_SUCESS, baseBean);
                        msg.sendToTarget();
                    }
                }
            }
        });
    }

    @Override
    protected int getLayoutId() {
        return R.layout.show_spell_group_pop;
    }

    @Override
    protected void initView() {
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT, 0);
        setLayoutParams(lp);
        tvSpellGroupPopName = getView(R.id.tv_spell_group_pop_name);
        tvSpellGroupPopSpec = getView(R.id.tv_spell_group_pop_spec);
        tvSpellGroupPopInventory = getView(R.id.tv_spell_group_pop_inventory);
        tvSpellGroupPopEffective = getView(R.id.tv_spell_group_pop_effective);
        tvSpellGroupPopInitial = getView(R.id.tv_spell_group_pop_initial);
        tvSpellGroupPopsubmit = getView(R.id.tv_spell_group_pop_submit);
        llDiscount = getView(R.id.ll_discount);
        tvTotalAmount = getView(R.id.tv_total_amount);
        tvNumber = getView(R.id.tv_number);
//        tvTips2 = getView(R.id.tvTips2);
        tvTips = getView(R.id.tvTips);
        tvLimited = getView(R.id.tv_limited);
        ivNumSub = getView(R.id.iv_numSub);
        ivNumAdd = getView(R.id.iv_numAdd);
        mPromoList = getView(R.id.promo_list);
        groupSpellGroupPrice = getView(R.id.group_spell_group_price);
        tvSpellGroupPrice = getView(R.id.tv_spell_group_price);
        mPromotionTagAdapter = new PromotionTagAdapter(R.layout.item_spell_group_pop);
        mPromoList.setAdapter(mPromotionTagAdapter);
        LinearLayoutManager ll = new LinearLayoutManager(context);
        ll.setOrientation(LinearLayoutManager.VERTICAL);
        mPromoList.setLayoutManager(ll);
        handler = new ShowShopSpellGroupHandler(tvNumber, tvTotalAmount, tvSpellGroupPrice);
    }

    public static class ShowShopSpellGroupHandler extends Handler {

        private final WeakReference<TextView> tvNumber;
        private final WeakReference<TextView> tvTotalAmount;
        private final WeakReference<TextView> tvSpellGroupPrice;

        public ShowShopSpellGroupHandler(TextView tvNumber, TextView tvTotalAmount, TextView tvSpellGroupPrice) {
            this.tvNumber = new WeakReference<>(tvNumber);
            this.tvTotalAmount = new WeakReference<>(tvTotalAmount);
            this.tvSpellGroupPrice = new WeakReference<>(tvSpellGroupPrice);
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (msg.what == CHANGE_SPELL_GROUP_SUCESS) {
                if (msg.obj instanceof CartDataBean) {
                    CartDataBean bean = (CartDataBean) msg.obj;
                    String numStr = String.valueOf(bean.qty);
                    if (tvNumber.get() != null) {
                        tvNumber.get().setText(numStr);
                    }
                    SpannableString totalAmount = new SpannableString("");
                    if (!TextUtils.isEmpty(bean.totalAmount)) {
                        totalAmount = StringUtil.setDotAfterSize("￥" + UiUtils.transform(bean.totalAmount), 15, R.color.color_ff2121);
                    }
                    if (tvTotalAmount.get() != null && tvTotalAmount.get() != null) {
                        tvTotalAmount.get().setText(totalAmount);
                    }
                    if (!TextUtils.isEmpty(bean.price) && tvSpellGroupPrice.get() != null) {
                        tvSpellGroupPrice.get().setText(bean.price);
                    }
                }
            }
        }
    }

    private void addShopCart(final long id, String number) {
        RequestParams editShopNumberParams = new RequestParams();
        JgRequestParams jgRequestParams = new JgRequestParams();
        try {
            if (JGTrackManager.Companion.getSuperProperty(this.context,
                    JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID) != null){
                String searchSortStrategyCode =
                        (String)JGTrackManager.Companion.getSuperProperty(this.context,
                                JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID) ;
//                params.put("searchSortStrategyCode",searchSortStrategyCode);
                jgRequestParams.setSearch_sort_strategy_id(searchSortStrategyCode);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        if(JGTrackManager.GlobalVariable.INSTANCE.getMJgOperationInfo() != null){
            JgOperationPositionInfo mJgOperationInfo = JGTrackManager.GlobalVariable.INSTANCE.getMJgOperationInfo();
            if (mJgOperationInfo.getProductId()!= null && !mJgOperationInfo.getProductId().isEmpty() && Objects.equals(mJgOperationInfo.getProductId(), String.valueOf(id))){
                if (mJgOperationInfo.getOperationId()!=null){
//                    params.put("operationId", mJgOperationInfo.getOperationId());
                    jgRequestParams.setOperation_id(mJgOperationInfo.getOperationId());
                }
                if (mJgOperationInfo.getOperationRank() != null){
//                    params.put("operationRank", mJgOperationInfo.getOperationRank().toString());
                    jgRequestParams.setOperation_rank(mJgOperationInfo.getOperationRank());
                }

                if (mJgOperationInfo.getRank() != null){
//                    params.put("rank", mJgOperationInfo.getRank().toString());
                    jgRequestParams.setRank(mJgOperationInfo.getRank());
                }
            }
        }
        if (JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchRowsBean() != null){
            RowsBean mJgSearchRowsBean = JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchRowsBean();
            if (mJgSearchRowsBean.getProductId()!=null && !mJgSearchRowsBean.getProductId().isEmpty() && Objects.equals(mJgSearchRowsBean.getProductId(), String.valueOf(id))){
                jgRequestParams.setList_position_type(mJgSearchRowsBean.positionType+"");
                if (mJgSearchRowsBean.positionTypeName != null){
                    jgRequestParams.setList_position_typename(mJgSearchRowsBean.positionTypeName);
                }
                if (mJgSearchRowsBean.searchKeyword != null){
                    jgRequestParams.setKey_word(mJgSearchRowsBean.searchKeyword);
                }
                jgRequestParams.setProduct_id(mJgSearchRowsBean.getProductId());
                jgRequestParams.setProduct_name(mJgSearchRowsBean.getProductName());
                jgRequestParams.setProduct_first(mJgSearchRowsBean.categoryFirstId);
                jgRequestParams.setProduct_number(mJgSearchRowsBean.getProductNumber());
                jgRequestParams.setProduct_price(mJgSearchRowsBean.getJgProductPrice());
                jgRequestParams.setProduct_type(String.valueOf(mJgSearchRowsBean.productType));
                jgRequestParams.setProduct_activity_type(mJgSearchRowsBean.productActivityType);
                jgRequestParams.setProduct_shop_code(mJgSearchRowsBean.shopCode);
                jgRequestParams.setProduct_shop_name(mJgSearchRowsBean.shopName);

                if (JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchSomeField() != null){
                    jgRequestParams.setRank(JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchSomeField().getRank());
                    JGPageListCommonBean mJgPageListCommonBean = JGTrackManager.GlobalVariable.INSTANCE.getMJgSearchSomeField().getMJgPageListCommonBean();
                    if (mJgPageListCommonBean != null){
                        jgRequestParams.setSptype(mJgPageListCommonBean.getSptype());
                        jgRequestParams.setJgspid(mJgPageListCommonBean.getJgspid());
                        jgRequestParams.setSid(mJgPageListCommonBean.getSid());
                        jgRequestParams.setPage_no(mJgPageListCommonBean.getPage_no());
                        jgRequestParams.setResult_cnt(mJgPageListCommonBean.getResult_cnt());
                        jgRequestParams.setPage_size(mJgPageListCommonBean.getPage_size());
                        jgRequestParams.setTotal_page(mJgPageListCommonBean.getTotal_page());
                    }

                }
            }
        }
        try {
            jgRequestParams.setProduct_number(Integer.parseInt(number));
        }catch (Exception e){
            e.printStackTrace();
        }
        jgRequestParams.setDirect("2");
        jgRequestParams.setSession_id(com.ydmmarket.report.manager.TrackManager.getSessionId(YBMAppLike.getAppContext()));

        editShopNumberParams.put("mddata",new Gson().toJson(jgRequestParams));
        if (jgTrackBean!= null && jgTrackBean.getEntrance()!=null && jgTrackBean.getEntrance().contains(JGTrackManager.TrackShoppingCart.TITLE)){ //购物车只传个direct = "3"
            editShopNumberParams.getParamsMap().remove("mddata");
        }
        editShopNumberParams.put("merchantId", HttpManager.getInstance().getMerchant_id());
        editShopNumberParams.setUrl(AppNetConfig.BUY_COMMODITY);

        editShopNumberParams.put("skuId", id + "");
        editShopNumberParams.put("amount", number);
        editShopNumberParams.put("addType", "3");
        if (mFlowData != null) {
            if (mFlowData.getSpType() != null) {
                editShopNumberParams.put("spType", mFlowData.getSpType());
            }
            if (mFlowData.getSpId() != null) {
                editShopNumberParams.put("spId", mFlowData.getSpId());
            }
            if (mFlowData.getSId() != null) {
                editShopNumberParams.put("sId", mFlowData.getSId());
            }
        }

        //spm加购埋点
        String qtData = ChangeCartUtil.getChangeCartParams(context, mDataBean);
        if (qtData != null) {
            editShopNumberParams.put("qtdata", qtData);
        }


        HttpManager.getInstance().post(editShopNumberParams, new BaseResponse<CartDataBean>() {

            @Override
            public void onSuccess(String content, BaseBean<CartDataBean> obj, CartDataBean cartDataBean) {
                if (obj != null && obj.isSuccess()) {
                    int num = 0;
                    if (cartDataBean != null) {
                        num = cartDataBean.qty;
                    }
                    //数据库更新
                    ToastUtils.showShort("加入购物车成功");
                    if (mDataBean != null) {
                        AddToCart addToCart = new AddToCart();
                        String url = "";
                        String referrer = "";
                        String title = "";
                        JGPageListCommonBean jgPageListCommonBean = new JGPageListCommonBean();
                        if (jgExtendOuterBean != null){
                            jgPageListCommonBean.setSptype(jgExtendOuterBean.getSptype());
                            jgPageListCommonBean.setJgspid(jgExtendOuterBean.getJgspid());
                            jgPageListCommonBean.setSid(jgExtendOuterBean.getSid());
                            jgPageListCommonBean.setResult_cnt(jgExtendOuterBean.getResultCnt());
                            jgPageListCommonBean.setPage_no(jgExtendOuterBean.getPageNo());
                            jgPageListCommonBean.setPage_size(jgExtendOuterBean.getPageSize());
                            jgPageListCommonBean.setTotal_page(jgExtendOuterBean.getTotalPage());
                            jgPageListCommonBean.setKey_word(jgExtendOuterBean.getKeyWord());
                            addToCart.setSearch_sort_strategy_id(jgExtendOuterBean.getSearchSortStrategyId());
                            addToCart.setRank(jgExtendOuterBean.getRank());
                            addToCart.setOperation_id(jgExtendOuterBean.getOperationId());
                            addToCart.setOperation_rank(jgExtendOuterBean.getOperationRank());
                            addToCart.setList_position_type(jgExtendOuterBean.getListPositionType());
                            addToCart.setList_position_typename(jgExtendOuterBean.getListPositionTypename());
                        }
                        if (jgTrackBean != null){
                            url = jgTrackBean.getUrl();
                            referrer = jgTrackBean.getJgReferrer();
                            title = jgTrackBean.getTitle();
                        }
                        addToCart.setUrl(url);
                        addToCart.setReferrer(referrer);
                        addToCart.setTitle(title);
                        addToCart.setJGPageListCommonBean(jgPageListCommonBean);
                        addToCart.setProduct_id((long) mDataBean.id);
                        addToCart.setProduct_name(mDataBean.showName);
                        addToCart.setProduct_first(mDataBean.categoryFirstId);
                        addToCart.setProduct_price(mDataBean.jgPrice);
                        addToCart.setProduct_type(String.valueOf(mDataBean.productType));
                        addToCart.setDirect("2");
                        addToCart.setProduct_number(num);
                        addToCart.setProduct_activity_type(mDataBean.productActivityType);
                        addToCart.setProduct_shop_code(mDataBean.shopCode);
                        if (mShopInfo != null){
                            addToCart.setProduct_shop_name(mShopInfo.shopName);
                        }

                        JGTrackTopLevelKt.reportAddToCart(addToCart);
                    }

                    HandlerGoodsDao.getInstance().updateItem(id, num, false);
                    LocalBroadcastManager.getInstance(BaseYBMApp.getAppContext()).sendBroadcast(new Intent(IntentCanst.ACTION_SHOPNUMBER));
                    dismiss();
                } else {
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
            }
        });
    }

    public int getSPELL_GROUP_RECOMMEND_TYPE() {
        return SPELL_GROUP_RECOMMEND_TYPE;
    }

    public void setSPELL_GROUP_RECOMMEND_TYPE(int SPELL_GROUP_RECOMMEND_TYPE) {
        this.SPELL_GROUP_RECOMMEND_TYPE = SPELL_GROUP_RECOMMEND_TYPE;
    }
}
