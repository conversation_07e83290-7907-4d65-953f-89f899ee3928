package com.ybmmarket20.view

import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.SpellGroupGoodsItem
import com.ybmmarket20.view.ProductEditLayoutSuiXinPin.TrackClickListener
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.xyyreport.page.payment.suixinpin.ISuiXinPinStateState
import com.ybmmarket20.xyyreport.page.payment.suixinpin.PaymentSuiXinPinExposureReport
import com.ybmmarket20.xyyreport.page.payment.suixinpin.SuiXinPinState
import com.ybmmarket20.xyyreport.page.payment.suixinpin.SuiXinPinStateCheck

/**
 * 随心拼合顺手买埋点
 */
open class PaymentSpellGroupRecommendGoodsAnalysisAdapter(layoutResId: Int, data: List<SpellGroupGoodsItem>, private val state: SuiXinPinState) :
    YBMBaseAdapter<SpellGroupGoodsItem>(layoutResId, data) {
    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: SpellGroupGoodsItem?) {
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            val pel = holder.getView<ProductEditLayoutSuiXinPin>(R.id.pel)
            SuiXinPinStateCheck.checkoutState(state, object: ISuiXinPinStateState{
                override fun onSuiXinPin() {
                    PaymentSuiXinPinExposureReport.trackGoodsViewExposure(mContext, bean)
                    pel.setOnTrackClickListener(object: TrackClickListener{
                        override fun clickAdd() {
                            PaymentSuiXinPinExposureReport.trackGoodsViewClick(mContext, bean).trackAddClick()
                        }

                        override fun clickAddCart() {
                            PaymentSuiXinPinExposureReport.trackGoodsViewClick(mContext, bean).trackAddCartClick()
                        }

                        override fun clickAddNumber(count: String?) {
                            if (count != null) {
                                PaymentSuiXinPinExposureReport.trackGoodsViewClick(mContext, bean).trackAddNumberClick(count)
                            }
                        }
                    })
                }

                override fun onRecommendPay() {
                    PaymentSuiXinPinExposureReport.trackGoodsViewRecommendExposure(mContext, bean)
                    pel.setOnTrackClickListener(object: TrackClickListener{
                        override fun clickAdd() {
                            PaymentSuiXinPinExposureReport.trackGoodsViewRecommendClick(mContext, bean).trackAddClick()
                        }

                        override fun clickAddCart() {
                            PaymentSuiXinPinExposureReport.trackGoodsViewRecommendClick(mContext, bean).trackAddCartClick()
                        }

                        override fun clickAddNumber(count: String?) {
                            if (count != null) {
                                PaymentSuiXinPinExposureReport.trackGoodsViewRecommendClick(mContext, bean).trackAddNumberClick(count)
                            }
                        }
                    })
                }
            })
        }
    }
}