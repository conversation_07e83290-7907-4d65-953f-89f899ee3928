package com.ybmmarket20.view;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.widget.TextView;

import com.ybmmarket20.R;
import com.ybmmarket20.bean.ModuleViewItem;
import com.ybmmarket20.constant.AppNetConfig;

import java.util.List;

/**
 * 动态头条轮播布局
 */
public class DynamicMarqueeLayout extends BaseDynamicLayout<ModuleViewItem> {
    private TextView marquee_tv;
    private MarqueeView marquee_view;
    public int defHeight = 46;
    private List<ModuleViewItem> data ;
    public DynamicMarqueeLayout(Context context) {
        this(context, null);
    }
    public DynamicMarqueeLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public DynamicMarqueeLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void initViews() {
        marquee_tv = (TextView) findViewById(R.id.tv_title);
        marquee_view = (MarqueeView) findViewById(R.id.marquee_view);
    }

    @Override
    public boolean supportSetHei() {
        return true;
    }

    @Override
    public int getDefHeigth() {
        return defHeight;
    }

    @Override
    public int getLayoutId() {
        return R.layout.dynamic_layout_marquee;
    }

    /**
     * @param data 数据
     */
    @Override
    public void setItemData(final List<ModuleViewItem> data) {
        this.data = data;
        for(ModuleViewItem item:data){
            if(!TextUtils.isEmpty(item.imgUrl) && !item.imgUrl.startsWith("#") && !item.imgUrl.startsWith("http")){//增加对相对路径的支持
                item.imgUrl = AppNetConfig.getCDNHost() + item.imgUrl;
            }
        }
        marquee_view.startWithList(data,moduleView.style);
    }

    @Override
    public void setStyle(int style) {

    }

}
