package com.ybmmarket20.view.payType

import android.content.Context
import android.graphics.Color
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.activity.jdpay.adapter.PayWayV2Adapter
import com.ybmmarket20.bean.PayWayBean
import com.ybmmarket20.viewmodel.PayWayV2ViewModel
import kotlinx.android.synthetic.main.view_pay_type_v2.view.rvPayType
import kotlinx.android.synthetic.main.view_pay_type_v2.view.rvPayWay
import kotlinx.android.synthetic.main.view_pay_type_v2.view.tvPayTypeTips


/**
 * 提单页，支付方式
 */
class PayTypeLayoutV2(context: Context, attr: AttributeSet?) : ConstraintLayout(context, attr) {

    var adapter: PayWayV2Adapter? = null

    var payTypeClickCallback: ((payType: String, payWay: String, payTypeTitleTips: SpannableStringBuilder?) -> Unit)? =
        null
    var payWayClickCallback: ((payCode: String, cardId: String?, payItemType: Int) -> Unit)? = null
    var changeCardCallback: (() -> Unit)? = null
    var showTipsPopCallback: ((confirmCallBack: () -> Unit) -> Unit)? = null
    var mCurrentPosition = 0

    init {
        View.inflate(context, R.layout.view_pay_type_v2, this)
    }

    fun setData(payWayList: MutableList<PayWayBean>?, payWayV2ViewModel: PayWayV2ViewModel) {
        visibility = if (payWayList == null) View.GONE else View.VISIBLE
        payWayList?.let { it ->
            val payTypeAdapterV2 = PayTypeAdapterV2(it)
            rvPayType.layoutManager = GridLayoutManager(context, payWayList.size)
            rvPayType.adapter = payTypeAdapterV2
            payTypeAdapterV2.setOnItemClickListener { _, _, position ->
                if (mCurrentPosition == position) return@setOnItemClickListener
                if (payWayList[position].payType == 3) {
                    showTipsPopCallback?.invoke {
                        // 添加回调逻辑
                        mCurrentPosition = position
                        payTypeClickCallback?.invoke(
                            "${payWayList[position].payType}", payWayList[position].payway, null
                        )
                    }
                    return@setOnItemClickListener
                }
                mCurrentPosition = position
                val payTypeMktTips = if (payWayList[position].payType == 5) {
                    //自有账期
                    val pingAnMktTips = SpannableStringBuilder("可用额度 ")
                    val pingAnBalance = SpannableStringBuilder(payWayList[position].tips)
                    pingAnBalance.setSpan(
                        ForegroundColorSpan(Color.parseColor("#FF4D4D")),
                        0,
                        payWayList[position].tips.length,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    pingAnMktTips.append(pingAnBalance)
                    pingAnMktTips
                } else null
                payTypeClickCallback?.invoke(
                    "${payWayList[position].payType}", payWayList[position].payway, payTypeMktTips
                )
            }
            tvPayTypeTips.visibility = View.GONE
            tvPayTypeTips.text = ""
            it.forEach { payWayBean ->
                val isOnlinePay = payWayBean.payType == 1 && payWayBean.checked
                if (payWayBean.checked && !TextUtils.isEmpty(payWayBean.selectedTips)) {
                    tvPayTypeTips.visibility = View.VISIBLE
                    tvPayTypeTips.text = payWayBean.selectedTips
                }
                rvPayWay.visibility =
                    if (isOnlinePay && payWayBean.cashier.payTypeEntryList.isNotEmpty()) View.VISIBLE else View.GONE
                if (isOnlinePay) {
                    adapter = payWayBean.cashier.payTypeEntryList?.let {
                        PayWayV2Adapter(
                            it, payWayV2ViewModel
                        )
                    }
                    rvPayWay.layoutManager =
                        LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
                    rvPayWay.adapter = adapter
                    adapter?.changeCardCallback = {
                        changeCardCallback?.invoke()
                    }
                    adapter?.selectItemCallback = { payCode, payId, itemType ->
                        payWayClickCallback?.invoke(payCode, payId, itemType)
                    }
                    return
                }
            }
        }
    }

    fun getPayWayV2Adapter(): PayWayV2Adapter? = adapter
}