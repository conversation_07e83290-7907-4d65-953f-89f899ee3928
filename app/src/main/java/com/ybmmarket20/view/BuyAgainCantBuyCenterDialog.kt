package com.ybmmarket20.view

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import com.ybmmarket20.R
import com.ybmmarket20.bean.OrderBuyAgainProduct
import com.ybmmarket20.bean.OrderStockState
import com.ybmmarket20.common.dp

/**
 * @class   BugAgainCantBuyBottomDialog
 * <AUTHOR>
 * @date  2025/1/21
 * @description 再次购买-商品部分可买 或多品均不可买
 */
class BuyAgainCantBuyCenterDialog(val mContext: Context, var mTitle:String = "", val type:Int = TYPE_1,val mDataList:ArrayList<OrderBuyAgainProduct>? = arrayListOf()) : Dialog(mContext) {

    companion object{
        const val TYPE_1 = 1 //不含失效品内容
        const val TYPE_2 = 2 //含失效品内容

        private const val CONTENT_CANCEL = "取消"
        private const val CONTENT_THINK = "再想想"
        private const val CONTENT_SURE = "确定"
        private const val CONTENT_SEE = "去看看"

    }

    private lateinit var mTvTitle: TextView
    private lateinit var mTvLeft: TextView
    private lateinit var mTvRight: TextView
    private lateinit var mMyInvalidProductView: MyInvalidProductView

    var mClickLeftListener: ((BuyAgainCantBuyCenterDialog)->Unit)? = null
    var mClickRightListener: ((BuyAgainCantBuyCenterDialog)->Unit)? = null


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(getLayoutResID())
        val layoutParams = window?.attributes
        layoutParams?.gravity = Gravity.CENTER // 底部显示
        layoutParams?.width = 270.dp
        layoutParams?.height = ConstraintLayout.LayoutParams.WRAP_CONTENT
        window?.attributes = layoutParams
        initUI()
        setCancelable(true)
        initObserver()
        handleUI()
    }

    private fun initUI(){
        mTvTitle = findViewById(R.id.tv_title)
        mTvLeft = findViewById(R.id.tv_left)
        mTvRight = findViewById(R.id.tv_right)
        mMyInvalidProductView = findViewById(R.id.view_my_invalid)
    }

    fun setData(orderBuyAgainProduct:ArrayList<OrderBuyAgainProduct>){
        mMyInvalidProductView.setData(orderBuyAgainProduct)
    }

    private fun initObserver(){

        mTvLeft.setOnClickListener {
            mClickLeftListener?.invoke(this)
        }

        mTvRight.setOnClickListener {
            mClickRightListener?.invoke(this)
        }

    }

    private fun handleUI(){

        when(type){
            TYPE_1 -> {
                mMyInvalidProductView.isVisible = false
                mTvTitle.text = mTitle
                mTvLeft.text = CONTENT_CANCEL
                mTvRight.text = CONTENT_SEE
            }
            TYPE_2 -> {
                mMyInvalidProductView.isVisible = true
                mTvTitle.text = mTitle
                mTvLeft.text = CONTENT_THINK
                mTvRight.text = CONTENT_SURE
                mMyInvalidProductView.setData(mDataList)
            }
        }
    }

    private fun getLayoutResID():Int = R.layout.dialog_bug_again_cant_buy_center
}