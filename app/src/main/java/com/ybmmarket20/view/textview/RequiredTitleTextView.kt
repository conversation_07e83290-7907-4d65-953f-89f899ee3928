package com.ybmmarket20.view.textview

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import android.widget.TextView

/**
 * 必选开头带星号的TextView
 */
@SuppressLint("AppCompatCustomView")
class RequiredTitleTextView(context: Context?, attrs: AttributeSet?) : TextView(context, attrs) {

    override fun setText(text: CharSequence?, type: BufferType?) {
        val startBuilder = SpannableStringBuilder("*")
        startBuilder.setSpan(ForegroundColorSpan(Color.parseColor("#FF2121")), 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        startBuilder.append(text?: "")
        super.setText(startBuilder, type)
    }
}