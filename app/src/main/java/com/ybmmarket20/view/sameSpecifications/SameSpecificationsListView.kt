package com.ybmmarket20.view.sameSpecifications

import android.content.Context
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.ActPtBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.getSingleStepSpannableForCommoditySpecification
import com.ybmmarket20.bean.isStep
import com.ybmmarket20.bean.product_detail.ReportPDExtendOuterBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.glideLoadWithPlaceHolder
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.db.info.HandlerGoodsDao
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.view.ListItemAddCartPopWindow
import com.ybmmarket20.view.ProductEditLayout
import com.ybmmarket20.view.ProductEditLayoutCommodity
import com.ybmmarket20.xyyreport.page.commodity.CommodityDetailReport
import com.ybmmarketkotlin.adapter.SpellGroupPopWindow
import com.ybmmarketkotlin.adapter.goodslist.GoodListBindItemStatus

/**
 * @class   SameSpecificationsListView
 * <AUTHOR>
 * @date  2024/8/28
 * @description  同款规格View
 */
class SameSpecificationsListView @JvmOverloads constructor(
        context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private val mAdapter: SameSpecificationsListAdapter = SameSpecificationsListAdapter()
    var reportPDExtendOuterBean: ReportPDExtendOuterBean? = null
        set(value) {
            field = value
            mAdapter.mReportPDExtendOuterBean = value
        }

    init {

        // 加载布局文件
        LayoutInflater.from(context).inflate(R.layout.layout_same_specifications_list_view, this, true)
        init()
    }

    companion object {
        private val PRICE_SMALL_SIZE = 10 //￥大小
        private val PRICE_BIG_SIZE = 14 //价格大小
    }

    private fun init() {
        // 初始化UI
        val rv = findViewById<RecyclerView>(R.id.rv_content)
        rv.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        rv.adapter = mAdapter
    }

    fun setData(mDataList: List<RowsBean>) {
        if (mDataList.isEmpty()) {
            this.isVisible = false
        } else {
            this.isVisible = true
            mAdapter.setNewData(mDataList)
        }

    }


    private class SameSpecificationsListAdapter : SameSpecificationsAnalysisListAdapter(R.layout.item_same_specifacations_list, arrayListOf()) {

        var mReportPDExtendOuterBean: ReportPDExtendOuterBean? = null

        override fun bindItemView(baseViewHolder: YBMBaseHolder?, rowsBean: RowsBean?) {
            super.bindItemView(baseViewHolder, rowsBean)
            baseViewHolder?.let { holder ->
                rowsBean ?: return
                val ivGoods = holder.itemView.findViewById<ImageView>(R.id.iv_goods)
                val tvSpecification = holder.itemView.findViewById<TextView>(R.id.tv_specification)
                val tvPrice = holder.itemView.findViewById<TextView>(R.id.tv_price)
                val tvPanicBuying = holder.itemView.findViewById<TextView>(R.id.tv_panic_buying) //去抢购
                val tvSeckill = holder.itemView.findViewById<TextView>(R.id.tv_seckill) //秒杀品- 文案也叫去抢购
                val tvGroupBuying = holder.itemView.findViewById<TextView>(R.id.tv_group_buying) //立即参团
                val tvAddToCart = holder.itemView.findViewById<TextView>(R.id.tv_add_to_cart) //加入购物车
                //这个editLayout 不会显示的 只是用里面请求接口
                val editLayout = holder.itemView.findViewById<ProductEditLayoutCommodity>(R.id.edit_layout)
                editLayout.rowsBean = rowsBean
                holder.itemView.context.glideLoadWithPlaceHolder(AppNetConfig.LORD_IMAGE + rowsBean.imageUrl, ivGoods)
                tvSpecification.text = rowsBean.spec ?: ""

                editLayout.bindData(rowsBean.id, rowsBean.status, true,
                    ProductEditLayout.FROMPAGE_COMMODITY, false, rowsBean.mediumPackageNum, rowsBean.isSplit == 1,mReportPDExtendOuterBean)

                when (rowsBean.isGroupBookingRow) {
                    is GoodListBindItemStatus.PGBYStatus -> { //批购包邮
                        tvAddToCart.isVisible = false
                        tvPanicBuying.isVisible = true
                        tvSeckill.isVisible = false
                        tvGroupBuying.isVisible = false
                    }

                    is GoodListBindItemStatus.SeckillStatus -> { //秒杀
                        tvAddToCart.isVisible = false
                        tvPanicBuying.isVisible = false
                        tvSeckill.isVisible = true
                        tvGroupBuying.isVisible = false
                    }

                    is GoodListBindItemStatus.NomalStatus -> { //普通商品
                        tvAddToCart.isVisible = true
                        tvPanicBuying.isVisible = false
                        tvSeckill.isVisible = false
                        tvGroupBuying.isVisible = false
                    }

                    else -> { //拼团
                        tvAddToCart.isVisible = false
                        tvPanicBuying.isVisible = false
                        tvSeckill.isVisible = false
                        tvGroupBuying.isVisible = true
                    }

                }
                setPrice(tvPrice, rowsBean)
                val trackClickBlock: (TextView?) -> Unit = {tv ->
                    CommodityDetailReport.trackSameGoodsSpecGoodsBtnClick(mContext, holder.absoluteAdapterPosition, rowsBean.id, rowsBean.showName, tv?.text?.toString())
                }
                tvPanicBuying.setOnClickListener {
                    trackClickBlock(tvPanicBuying)
                    if (rowsBean.actPgby != null && rowsBean.actPgby.isApplyListShowType) {
                        val actPgby = rowsBean.actPgby
                        val actPt: ActPtBean = ActPtBean().apply {
                            assemblePrice = actPgby.assemblePrice?: 0.0
                            skuStartNum = actPgby.skuStartNum?.toInt()?: 0
                            marketingId = actPgby.marketingId
                            supportSuiXinPin = actPgby.supportSuiXinPin
                            suiXinPinButtonText = actPgby.suiXinPinButtonText
                            suiXinPinButtonBubbleText = actPgby.suiXinPinButtonBubbleText
                            isApplyListShowType = actPgby.isApplyListShowType
                        }
                        rowsBean.actPt = actPt
                        val mPopWindowSpellGroup = SpellGroupPopWindow(
                                mContext,
                                rowsBean,
                                rowsBean.actPt,
                                true,
                                mIsList = true
                        )
                        mPopWindowSpellGroup.show(holder.itemView)
                    }else{
                        var mUrl = "ybmpage://productdetail/" + rowsBean.id
                        RoutersUtils.open(mUrl)
                    }
                }
                tvSeckill.setOnClickListener {
                    trackClickBlock(tvSeckill)
                    var mUrl = "ybmpage://productdetail/" + rowsBean.id
                    RoutersUtils.open(mUrl)
                }

                tvGroupBuying.setOnClickListener {
                    trackClickBlock(tvGroupBuying)
                    if (rowsBean.actPt != null && rowsBean.actPt.isApplyListShowType) {
                        val mPopWindowSpellGroup = SpellGroupPopWindow(
                                mContext,
                                rowsBean,
                                rowsBean.actPt,
                                false,
                                mIsList = true
                        )
                        mPopWindowSpellGroup.show(holder.itemView)
                    } else {
                        var mUrl = "ybmpage://productdetail/" + rowsBean.id
                        RoutersUtils.open(mUrl)
                    }
                }

                tvAddToCart.setOnClickListener {
                    trackClickBlock.invoke(tvAddToCart)
                    val number = HandlerGoodsDao.getInstance().getNumber(rowsBean.id).let {
                        if (it <= 0) "1" else it.toString()
                    }
                    val popWindow = ListItemAddCartPopWindow((mContext as BaseActivity))
                            .setObserver()
                            .setData(rowsBean, baseViewHolder.itemView, number)
                    popWindow.addCartCallback = { integer: Int ->
                        //借用editLayout 来请求加购接口
                        editLayout.sendShopNum(rowsBean.id, integer.toString())
                    }
                    popWindow.show(tvAddToCart)
                }

                holder.itemView.setOnClickListener {
                    CommodityDetailReport.trackSameGoodsSpecGoodsClick(mContext, holder.bindingAdapterPosition, rowsBean.id, rowsBean.showName)
                    var mUrl = "ybmpage://productdetail/" + rowsBean.id
                    RoutersUtils.open(mUrl)
                }
            }
        }

        fun setPrice(tvPrice: TextView, data: RowsBean) {
            val showPriceStr = SpannableStringBuilder()
            when (data.showPriceType()) {
                0, 2 -> {
                    showPriceStr.append("¥")
                    // 拼团中的商品，取拼团价/ 秒杀的商品取秒杀价
                    if (data.actPt != null) { //拼团
                        if (data.actPt.stepPriceStatus == 1) {
                            // 多阶梯
                            tvPrice.text = data.actPt.getSingleStepSpannableForCommoditySpecification()
                        } else {
                            showPriceStr.append(UiUtils.transform(data.actPt.assemblePrice))
                            showPriceStr.setSpan(AbsoluteSizeSpan(PRICE_BIG_SIZE, true), 1, showPriceStr.length - 2, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                            showPriceStr.setSpan(AbsoluteSizeSpan(PRICE_SMALL_SIZE, true), showPriceStr.length - 2, showPriceStr.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                            tvPrice.text = showPriceStr
                        }
                    } else if (data.actSk != null && data.actSk.status == 1) { //秒杀
                        showPriceStr.append(UiUtils.transform(data.actSk.skPrice))
                        showPriceStr.setSpan(AbsoluteSizeSpan(PRICE_BIG_SIZE, true), 1, showPriceStr.length - 2, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                        showPriceStr.setSpan(AbsoluteSizeSpan(PRICE_SMALL_SIZE, true), showPriceStr.length - 2, showPriceStr.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                        tvPrice.text = showPriceStr
                    } else if (data.actPgby != null && data.actPgby.assemblePrice != null) { //包邮
                        showPriceStr.append(UiUtils.transform(data.actPgby.assemblePrice
                                ?: 0.00))
                        showPriceStr.setSpan(AbsoluteSizeSpan(PRICE_BIG_SIZE, true), 1, showPriceStr.length - 2, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                        showPriceStr.setSpan(AbsoluteSizeSpan(PRICE_SMALL_SIZE, true), showPriceStr.length - 2, showPriceStr.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                        tvPrice.text = showPriceStr
                    } else { //普通
                        if (data.rangePriceBean.isStep()) {
                            //阶梯价
                            tvPrice.text = data.rangePriceBean.getSingleStepSpannableForCommoditySpecification()
                        } else {
                            showPriceStr.append(UiUtils.transform(data.fob))
                            showPriceStr.setSpan(AbsoluteSizeSpan(PRICE_BIG_SIZE, true), 1, showPriceStr.length - 2, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                            showPriceStr.setSpan(AbsoluteSizeSpan(PRICE_SMALL_SIZE, true), showPriceStr.length - 2, showPriceStr.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                            tvPrice.text = showPriceStr
                        }
                    }
                }
                // -1 为是否有控销文案
                -1 -> {
                    showPriceStr.append(data.controlTitle)
                    showPriceStr.setSpan(ForegroundColorSpan(UiUtils.getColor(R.color.color_ff982c)), 0, showPriceStr.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                    tvPrice.text = showPriceStr
                }
            }
        }
    }

}