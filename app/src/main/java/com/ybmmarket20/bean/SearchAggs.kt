package com.ybmmarket20.bean

data class SearchAggsBean(
    var aggregations: AggregationsBean? =null
)

class AggregationsBean(
    var specStats: MutableList<SearchFilterBean>,
    var shopStats: MutableList<SearchFilterBean>,
    var catStats: MutableList<CatStatsBean>,
    var dynamicLabelConfig: MutableList<SearchDynamicLabelConfig> //12.0.07 新增。动态标签配置，不含ID
)


data class CatStatsBean(
    var key: String? =null,         // 分类ID
    var showName: String? =null,    // 分类名称
    var parentId: String? =null     // 父级ID
)