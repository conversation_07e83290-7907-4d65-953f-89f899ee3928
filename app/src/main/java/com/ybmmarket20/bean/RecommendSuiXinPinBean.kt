package com.ybmmarket20.bean

class RecommendSuiXinPinBean(
    //商品
    val rowsBean: RowsBean,
    //加购数量
    val count: Int,
    //是否是加购（或减购）
    val isAdd: Boolean
) {
    val spellGroupGoodsItem: SpellGroupGoodsItem? = null
}
class RecommendSuiXinPinBean1(
    //商品
//    val rowsBean: RowsBean,
    val id:String?="",
    val price:String?="",
    //加购数量
    val count: Int
) {
    val spellGroupGoodsItem: SpellGroupGoodsItem? = null
}