package com.ybmmarket20.bean;

/**
 * Created by deng<PERSON><PERSON><PERSON> on 2019/4/2
 * 邮寄凭证
 */
public class MailCertificateBean {

    /**
     * address : 山西省临汾市汾西县河南省郑州市登封市注册地址
     * areaOrgId : xyyhn4300001
     * contactUser : 递四
     * corporationName : 哈利路亚大药房
     * icon : http://t-upload.ybm100.com/ybm/brand/f4eb2c57-1fe8-4309-808d-0bdeb936afc0.jpg
     * imageUrl : /ybm/order/evaluate/96735/3b2d58e7-f604-4bb9-883b-c8d7ff446bfe.png
     * merchantCode : 26615
     * merchantId : 26615
     * merchantName : 谭文松
     * mobile : 13555522222
     * orderno : 22222
     * postName : 百世快递
     * postType : 7
     * remark : 版 本 V B第三个故事的
     * type : 4
     */

    private String address;
    private String areaOrgId;
    private String contactUser;
    private String corporationName;
    private String icon;
    private String imageUrl;
    private String merchantCode;
    private String merchantId;
    private String mobile;
    private String orderno;
    private String postName;
    private String postType;
    private String remark;
    private String type;

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAreaOrgId() {
        return areaOrgId;
    }

    public void setAreaOrgId(String areaOrgId) {
        this.areaOrgId = areaOrgId;
    }

    public String getContactUser() {
        return contactUser;
    }

    public void setContactUser(String contactUser) {
        this.contactUser = contactUser;
    }

    public String getCorporationName() {
        return corporationName;
    }

    public void setCorporationName(String corporationName) {
        this.corporationName = corporationName;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getMerchantCode() {
        return merchantCode;
    }

    public void setMerchantCode(String merchantCode) {
        this.merchantCode = merchantCode;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getOrderno() {
        return orderno;
    }

    public void setOrderno(String orderno) {
        this.orderno = orderno;
    }

    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }

    public String getPostType() {
        return postType;
    }

    public void setPostType(String postType) {
        this.postType = postType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
