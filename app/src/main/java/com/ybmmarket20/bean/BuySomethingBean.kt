package com.ybmmarket20.bean

import java.io.Serializable

data class BuySomethingUpdataBean(
    var skuId: Int,
    var quantity: Int,
    var isReplace: Boolean? = false
) : Serializable

data class BuySomethingCasuallyInfo(
    val shopInfoSxpList: List<ShopInfoSxpList>? = null,
    val buySomethingCasuallySkus: List<String>? = null,
    val isMore: Boolean? = false
)

data class ShopInfoSxpList(
    val shopCode: String? = "",
    val skuids: ArrayList<String>? = null
) : Serializable