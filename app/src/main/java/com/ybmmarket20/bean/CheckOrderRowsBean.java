package com.ybmmarket20.bean;

import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.ybm.app.bean.AbstractMutiItemEntity;
import com.ybmmarket20.xyyreport.paramsInfo.IOrderListItem;

import java.util.ArrayList;

/**
 * 订单列表
 */
public class CheckOrderRowsBean extends AbstractMutiItemEntity implements IOrderListItem {

    public int id;
    public String orderNo;
    public int varietyNum;
    public double money;
    public String contactor;
    public int paytype;
    public int payType;
    public int status;
    public int canConfirmReceipt;
    public long createTime;
    public String imageUrl;
    public String paytypeName;//支付方式名
    public String statusName;//订单状态名
    public int refundCount;//当refundCount>0:有退款 refundCount=0:无
    public int balanceStatus;//余额状态0：未领取 1：已领取 2：不能领取
    public String balanceText;//可领取余额文案
    public String refundText;//领取余额后申请退款文案
    public int appraiseStatus;//待支付1：未评价，2：已经评价，3：评价时间已经过期
    public boolean appraiseStatusFlag;//判断该订单是否是待评价的订单，此状态非服务器返回，由用户点击待评价页面获得！
    public int isThirdCompany;//是否是自营（0：是；1：否）
    public String companyName;//自营或者非自营name
    public String productName;//商品名称
    public String mobile;
    public double subTotal;
    public long paymentTime;

    public int evidenceVerifyStatus;  // * 凭证状态 0-待上传电汇凭证、1-待审核电汇凭证、2-审核通过
    public String transferInfoUrl;               // 上传线下转账的凭证
    public long countDownNewTime;               // 剩余支付时间， 单位s
    public long localtime;                      // ms 值
    public long responseLocalTime;
    public boolean showUploadEvidenceBtn;       // 是否展示上传凭证按钮，true 展示
    public boolean showExpireReminder;          // 是否展示支付截止时间提醒，true 展示（前端还需要根据时间具体判断）

    public int isShowRefund;//（1：隐藏）
    public String showOthersPayState;//是否显示他人代付 0：不显示;1 显示

    /*------------------渠道-------------------*/
    public String channelCode;//渠道编码：默认为 1; 药帮忙 1; 2 宜块钱

    public String origName;//自营或者非自营name-new
    public String orgId;//自营或者非自营name-new
    public String cashPayAmount;
    public String sellerRemark; //卖家备注
    public String shopCode;

    public String sysException;//卡单-系统
    public String supplierException; //卡单-商家
    public boolean hasOrderExceptionFlag; //卡单是否有异常

    public ArrayList<OrderImages> orderImages;

    //public int freightRefundType; //

    public boolean isIsThirdCompany() {
        return isThirdCompany == 0;
    }

    //无卡单
    public static final int ITEM_TYPE_ORDER_LIST_NO_EXCEPTION = 0;
    //两种异常都有
    public static final int ITEM_TYPE_ORDER_LIST_DOUBLE_EXCEPTION = 1;
    //只有一种异常
    public static final int ITEM_TYPE_ORDER_LIST_SINGLE_EXCEPTION = 2;
    //底部提示
    public static final int ITEM_TYPE_ORDER_LIST_BOTTOM_TIPS = 3;

    @Override
    public int getItemType() {
        //如果是底部提示item
        if (itemType == ITEM_TYPE_ORDER_LIST_BOTTOM_TIPS) return ITEM_TYPE_ORDER_LIST_BOTTOM_TIPS;
        //异常相关
        if (!hasOrderExceptionFlag) return ITEM_TYPE_ORDER_LIST_NO_EXCEPTION;
        if (!TextUtils.isEmpty(sysException) && !TextUtils.isEmpty(supplierException)) return ITEM_TYPE_ORDER_LIST_DOUBLE_EXCEPTION;
        return ITEM_TYPE_ORDER_LIST_SINGLE_EXCEPTION;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        CheckOrderRowsBean that = (CheckOrderRowsBean) o;

        if (id != that.id) return false;
        return orderNo != null ? orderNo.equals(that.orderNo) : that.orderNo == null;

    }

    @Override
    public int hashCode() {
        int result = id;
        result = 31 * result + (orderNo != null ? orderNo.hashCode() : 0);
        return result;
    }

    @Nullable
    @Override
    public String getOrderNo() {
        return orderNo;
    }

    @Nullable
    @Override
    public String getOrderStatusText() {
        return statusName;
    }

    @Nullable
    @Override
    public String getOrderShopCode() {
        return id+"";
    }

    @Nullable
    @Override
    public String getOrderShopName() {
        return companyName;
    }

    @Nullable
    @Override
    public int getOrderStatus() {
        return status;
    }

    public class OrderImages{
        private String imageUrl;
        private int productAmount;
        private String id;
        private String skuId;
        private double productPrice;
        private String spec;
        private String productName;

        public String getImageUrl() {
            return imageUrl;
        }

        public void setImageUrl(String imageUrl) {
            this.imageUrl = imageUrl;
        }

        public int getProductAmount() {
            return productAmount;
        }

        public void setProductAmount(int productAmount) {
            this.productAmount = productAmount;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getSkuId() {
            return skuId;
        }

        public void setSkuId(String skuId) {
            this.skuId = skuId;
        }

        public double getProductPrice() {
            return productPrice;
        }

        public void setProductPrice(double productPrice) {
            this.productPrice = productPrice;
        }

        public String getSpec() {
            return spec;
        }

        public void setSpec(String spec) {
            this.spec = spec;
        }

        public String getProductName() {
            return productName;
        }

        public void setProductName(String productName) {
            this.productName = productName;
        }
    }
}
