package com.ybmmarket20.bean

import com.ybmmarket20.bean.loadmore.IPage

class FreightAddOnItemListBean<T>(
        val currentPage: Int,
        val limit: Int,
        val offset: Int,
        val pageCount: Int,
        val rows: List<T>,
        val total: Int
) : IPage<T> {

    override fun getCurPage(): Int = currentPage

    override fun getPageRowSize(): Int = limit

    override fun getTotalPages(): Int = pageCount

    override fun getRowsList(): List<T> = rows
}