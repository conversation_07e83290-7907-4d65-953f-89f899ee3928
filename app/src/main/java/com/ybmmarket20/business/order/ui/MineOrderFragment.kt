package com.ybmmarket20.business.order.ui

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.view.View
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.viewpager.widget.ViewPager
import com.jeremyliao.liveeventbus.LiveEventBus
import com.ybmmarket20.R
import com.ybmmarket20.activity.LoginActivity
import com.ybmmarket20.activity.SearchOrderActivity
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.Coupon
import com.ybmmarket20.bean.PurchaseSwitchBean
import com.ybmmarket20.business.order.adapter.OrderListPagerAdapter
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.LiveEventBusManager
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.common.isLogin
import com.ybmmarket20.common.jgTrackOrderListBtnClick
import com.ybmmarket20.common.ordertopmanager.OrderTopManageableDialog
import com.ybmmarket20.common.ordertopmanager.OrderTopManager
import com.ybmmarket20.common.ordertopmanager.OrderTopParam
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.constant.IntentCanst.ACTION_SWITCH_USER
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.viewmodel.SwitchViewModel
import com.ybmmarket20.xyyreport.page.orderList.OrderListReport
import com.ybmmarket20.xyyreport.page.orderList.OrderlistConstant
import com.ybmmarket20.xyyreport.spm.SpmUtil
import kotlinx.android.synthetic.main.fragment_all_order.cl_order_coupon
import kotlinx.android.synthetic.main.fragment_all_order.cl_search
import kotlinx.android.synthetic.main.fragment_all_order.ll_bill
import kotlinx.android.synthetic.main.fragment_all_order.ll_red_pack
import kotlinx.android.synthetic.main.fragment_all_order.ps_tab
import kotlinx.android.synthetic.main.fragment_all_order.tv_coupon
import kotlinx.android.synthetic.main.fragment_all_order.tv_go_top_up
import kotlinx.android.synthetic.main.fragment_all_order.tv_tips
import kotlinx.android.synthetic.main.fragment_all_order.vp


/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2024/11/29 10:52
 *    desc   :
 */
class MineOrderFragment : MineOrderAnalysisFragment() {
    private var adapter: OrderListPagerAdapter? = null

    private var order_state: String? = null
    private val switchViewModel: SwitchViewModel by viewModels()
    private var br: BroadcastReceiver? = null
    private var orderListFragments = mutableListOf<OrderListFragment>()

    //优惠券 顶部显示优先级
    private val couponOrderTopParam: OrderTopParam by lazy {
        OrderTopParam.Builder().setDialog(object : OrderTopManageableDialog {
            override fun show() {
                cl_order_coupon?.post {
                    cl_order_coupon.isVisible = true
                }
            }

            override fun dismiss(isPushBack: Boolean) {
                cl_order_coupon?.post {
                    cl_order_coupon.isVisible = false
                }
            }

            override fun isCanShow(): Boolean = true
        }).setPriority(99).build()
    }

    //购物金充值 顶部显示优先级
    private val shoppingGoldOrderTopParam: OrderTopParam by lazy {
        OrderTopParam.Builder().setDialog(object : OrderTopManageableDialog {
            override fun show() {
                ll_red_pack?.post {
                    ll_red_pack.isVisible = true
                }
            }

            override fun dismiss(isPushBack: Boolean) {
                ll_red_pack?.post {
                    ll_red_pack.isVisible = false
                }
            }

            override fun isCanShow(): Boolean = true
        }).setPriority(1).build()
    }

    override fun getOrderListAnalysisParams(): IOrderListAnalysisParams? {
        return try {
            orderListFragments[vp.currentItem]
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }

    override fun getVpCurrIndex(): Int {
        return vp?.currentItem?: 0
    }


    fun setPositionListener(position: Int) {
        when (position) {
            0 -> context?.jgTrackOrderListBtnClick("全部", "tab")
            1 -> {
                LocalBroadcastManager.getInstance(requireActivity()).sendBroadcast(Intent(IntentCanst.ACTION_HIDE_ORDER_BUBBLE))
                context?.jgTrackOrderListBtnClick("待支付", "tab")
            }
            2 -> context?.jgTrackOrderListBtnClick("待配送", "tab")
            3 -> context?.jgTrackOrderListBtnClick("配送中", "tab")
            4 -> context?.jgTrackOrderListBtnClick("完成", "tab")
            5 -> context?.jgTrackOrderListBtnClick("退款/售后", "tab")
            6 -> context?.jgTrackOrderListBtnClick("待评价", "tab")
        }
    }

    private fun initSwitchStatus() {
        switchViewModel?.purchaseSwitchStatusLiveData!!.observe(this,
            Observer<BaseBean<PurchaseSwitchBean>> { integerBaseBean: BaseBean<PurchaseSwitchBean> ->
                if (integerBaseBean.isSuccess && integerBaseBean.data != null && integerBaseBean.data?.purchaseOrderSwitch == 1) {
                    ll_bill.setVisibility(View.VISIBLE)
                }
            })
        switchViewModel?.getPurchaseSwitchStatus()

    }

    private fun initGetOrderBubbleCount(){
        LiveEventBus.get(LiveEventBusManager.OrderBus.NO_PAY_ORDERS_BUBBLE, Boolean::class.java).observe(this) {
            switchViewModel?.getOrdersBubbleCount()
        }
        switchViewModel?.ordersBubbleCountData?.observe(requireActivity()) { t ->
            if (t.isSuccess) {
                val data = t.data
                if (data.waitPayNum > 0) {
                    ps_tab.showMsg(1, data.waitPayNum)
                } else {
                    ps_tab.hideMsg(1)
                }

            }
        }
    }
    
    private fun initSceneData(){

        switchViewModel.voucherLiveData.observe(requireActivity()){
            val mUrl = it.first
            val mBean = it.second
            if (mBean.isSuccess){
                RoutersUtils.open(mUrl)
            }
        }

        switchViewModel.couponBeanData.observe(requireActivity()){
            if (it.isSuccess){
                val mBean = it.data
                mBean?.let {

                    var tipsContent = ""
                    var mUrl = ""
                    var couponClaimed = -1
                    var couponId = ""
                    val spannableStringBuilder = SpannableStringBuilder()
                    it.scenes?.forEachIndexed { index, scene ->
                        if (index == 0){
                            scene.coupons?.forEachIndexed { index, coupon ->
                                if (index == 0){
                                    couponId = coupon.couponId?:""
                                    tipsContent = coupon.couponText?:""
                                    mUrl = coupon.hrefUrl?:""
                                    couponClaimed = coupon.claimStatus?:-1
                                    //设置埋点优惠券信息
                                    coupon.sceneGroupId = scene.sceneGroupId
                                    mAnalysisCouponInfo = coupon
                                    orderListFragments.forEach {f ->
                                        f.mAnalysisCouponInfo = mAnalysisCouponInfo
                                    }
                                }
                            }
                        }
                    }

                    if (tipsContent.isNotEmpty()){
                        val tipsStringBuilder = SpannableStringBuilder()

                        tipsStringBuilder.append(tipsContent)
                        val lowerColor = ContextCompat.getColor(requireActivity(), R.color.color_F70015)
                        tipsStringBuilder.setSpan(
                                ForegroundColorSpan(lowerColor),
                                0,
                                tipsContent.length,
                                0
                        )

                        spannableStringBuilder.append("送您一张") //送您一张满1000元减30元跨店券
                        spannableStringBuilder.append(tipsStringBuilder)

                        tv_coupon.text = spannableStringBuilder

                        OrderTopManager.show(couponOrderTopParam)
                    }else{
                        OrderTopManager.dismiss(couponOrderTopParam)
                    }

                    if (mUrl.isNotEmpty()){
                        cl_order_coupon.setOnClickListener {
                            onCouponTipClick()
                            when(couponClaimed){
                                1 ->{
                                    switchViewModel.getVoucher(couponId,mUrl)
                                }

                                2->{
                                    RoutersUtils.open(mUrl)
                                }

                                else ->{}
                            }
                        }
                    }else{
                        cl_order_coupon.setOnClickListener(null)
                    }

                }?: kotlin.run {
                    OrderTopManager.dismiss(couponOrderTopParam)
                }
            }
        }
    }

    private fun initShoppingGoldRecharge(){
        switchViewModel.shoppingGoldRechargeBeanData.observe(requireActivity()){
            if (it.isSuccess){
                val mBean = it.data
                mBean?.let{
                    val upperHalf = it.highLevelRedPacketMsgUpperHalf ?: ""
                    val lowerHalf = it.highLevelRedPacketMsgLowerHalf ?: ""

                    // 创建 SpannableStringBuilder
                    val spannableStringBuilder = SpannableStringBuilder()

                    // 添加上半部分文本并设置颜色
                    if (upperHalf.isNotEmpty()){
                        spannableStringBuilder.append(upperHalf)
                        val upperColor = Color.BLACK
                        spannableStringBuilder.setSpan(
                                ForegroundColorSpan(upperColor),
                                0,
                                upperHalf.length,
                                0
                        )
                    }


                    // 添加下半部分文本并设置颜色
                    if (lowerHalf.isNotEmpty()){
                        spannableStringBuilder.append(lowerHalf)
                        val lowerColor = ContextCompat.getColor(requireActivity(), R.color.color_F70015)
                        spannableStringBuilder.setSpan(
                                ForegroundColorSpan(lowerColor),
                                upperHalf.length,
                                upperHalf.length + lowerHalf.length,
                                0
                        )
                    }

                    tv_tips.text = spannableStringBuilder

                    if (tv_tips.text.isNotEmpty()){
                        OrderTopManager.show(shoppingGoldOrderTopParam)
                    }else{
                        OrderTopManager.dismiss(shoppingGoldOrderTopParam)
                    }

                    tv_go_top_up.setOnClickListener {
                        RoutersUtils.open("ybmpage://myvirtualmoney?showRechargeDialog=1")
                    }
                }?: kotlin.run {
                    OrderTopManager.dismiss(shoppingGoldOrderTopParam)
                }
            }
        }
    }

    override fun onVisibleChanged(isVisible: Boolean) {
        super.onVisibleChanged(isVisible)
        if (isVisible) {
            switchViewModel.getOrdersBubbleCount()
            switchViewModel.getShoppingGoldRechargeBean()
            switchViewModel.getSceneData()

            val properties: HashMap<String, Any> = HashMap()
            properties[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackOrderList.PAGE_ID
            properties[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackOrderList.TITLE
            properties[JGTrackManager.FIELD.FIELD_URL] = this.getFullClassName()
            JGTrackManager.pageViewTrack(requireActivity(), JGTrackManager.TrackOrderList.TITLE, properties)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initBroadCastReceiver()
    }

    /**
     * 初始化广播
     */
    private fun initBroadCastReceiver() {
        br = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                if (ACTION_SWITCH_USER == intent.action) {
                    changeTab("0")
                    initData("")
                }
            }
        }
        val intentFilter = IntentFilter(ACTION_SWITCH_USER)
        LocalBroadcastManager.getInstance(notNullActivity).registerReceiver(br as BroadcastReceiver, intentFilter)
    }

    override fun initData(content: String?) {
        if (!isLogin()) {
            //未登录
            val it = Intent(context, LoginActivity::class.java)
            startActivity(it)
            return
        }
        initSwitchStatus()
        initGetOrderBubbleCount()
        initShoppingGoldRecharge()
        initSceneData()
//        order_state = getIntent().getStringExtra(IntentCanst.ORDER_STATE)
        val fragment1 = OrderListFragment.getInstance(0, "全部")
        val fragment7 = OrderListFragment.getInstance(10, "待支付")
        val fragment2 = OrderListFragment.getInstance(1, "待配送")
        val fragment3 = OrderListFragment.getInstance(2, "配送中")
        //        OrderListFragment fragment5 = OrderListFragment.getInstance(4, "待收货");
        val fragment4 = OrderListFragment.getInstance(3, "完成")
        val fragment6 = OrderListFragment.getInstance(101, "待评价") //101请勿动
        val fragment8 = OrderListFragment.getInstance(90, "退款/售后")
        orderListFragments = ArrayList<OrderListFragment>()
        orderListFragments.add(fragment1)
        orderListFragments.add(fragment7)
        orderListFragments.add(fragment2)
        orderListFragments.add(fragment3)
        //        orderListFragments.add(fragment5);
        orderListFragments.add(fragment4)
        orderListFragments.add(fragment8)
        orderListFragments.add(fragment6)
        adapter = OrderListPagerAdapter(childFragmentManager, orderListFragments)
        vp.adapter = adapter
        vp.setScroll(false)
        vp.offscreenPageLimit = orderListFragments.size+1
        ps_tab.setViewPager(vp)
        ps_tab.setIndicatorWidthEqualTitleHalf(true)
        ps_tab.addUnrelatedVpIndex(orderListFragments.indexOf(fragment8))
        ps_tab.setOnUnrelatedVpIndexCallback { index: Int ->
            RoutersUtils.open(
                "ybmpage://refundoraftersales"
            )
        }

        changeTab(order_state)
        vp.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
            }

            override fun onPageSelected(position: Int) {
                setPositionListener(position)
                SpmUtil.checkAnalysisContext(requireActivity()) {
                    val isTrackPv = it.getExtensionValue(OrderlistConstant.ORDER_LIST_TRACK_PV_SWITCH)
                    if (isTrackPv == null) {
                        orderListFragments[position].pvTrack()
                    } else {
                        val isTrackPvBoolean = isTrackPv as Boolean
                        if (isTrackPvBoolean) {
                            orderListFragments[position].pvTrack()
                        } else {
                            it.putExtension(OrderlistConstant.ORDER_LIST_TRACK_PV_SWITCH, true)
                        }
                    }
                }

            }

            override fun onPageScrollStateChanged(state: Int) {}
        })

        ll_bill.setOnClickListener {
            RoutersUtils.open("ybmpage://purchasereconciliation")
            val map = HashMap<String, String>()
            map["text"] = "我的账单"
            XyyIoUtil.track("page_OrderList_purchaseOrder", map)
            context?.jgTrackOrderListBtnClick("我的账单", "功能")
        }
        cl_search.setOnClickListener {
            OrderListReport.trackSearchClick(requireActivity())
            val intent = Intent(context, SearchOrderActivity::class.java)
            startActivity(intent)
            context?.jgTrackOrderListBtnClick("搜索框", "功能")
        }
        pvTrackFirst()
    }

    override fun initTitle() {
    }

    override fun getParams() = RequestParams()

    override fun getUrl() = null

    override fun getLayoutId() = R.layout.fragment_all_order

    override fun onDestroyView() {
        super.onDestroyView()
        br?.let {
            LocalBroadcastManager.getInstance(notNullActivity).unregisterReceiver(it)
        }
    }

    fun changeTab(state: String?) {
        var position = when (state) {
            "0" -> 0
            "10" -> 1
            "1" -> 2
            "2" -> 3
            "3" -> 4
            "101" -> 6
            "90" -> 5
            else -> 0
        }
        vp.setCurrentItem(position, false)
    }

    fun getTabInt(state: String?): Int {
        return when (state) {
            "0" -> 0
            "10" -> 1
            "1" -> 2
            "2" -> 3
            "3" -> 4
            "101" -> 6
            "90" -> 5
            else -> 0
        }
    }
}