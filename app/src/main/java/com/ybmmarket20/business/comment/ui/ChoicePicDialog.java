package com.ybmmarket20.business.comment.ui;

import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import androidx.annotation.NonNull;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.apkfuns.logutils.LogUtils;
import com.iflytek.cloud.ErrorCode;
import com.ybmmarket20.R;
import com.ybmmarket20.common.VoiceSearchManager;
import com.ybmmarket20.view.TransparentDialog;

public class ChoicePicDialog extends TransparentDialog {

    private EditText editText;

    private VoiceSearchManager recorder;

    private ImageView iv;

    private TextView title;
    private Button btEnd;

    private TextView takephoto;
    private TextView picPhoto;
    private TextView cancel;

    public ChoicePicDialog(@NonNull Context context) {
        super(context);
        this.editText = editText;
        takephoto = findViewById(R.id.tv_take_photo);
        picPhoto = findViewById(R.id.tv_pic_photo);
        cancel = findViewById(R.id.tv_cancel);


        takephoto.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (listener != null) {
                    ItemInfo itemInfo = new ItemInfo();
                    itemInfo.id = R.id.tv_take_photo;
                    listener.itemSelect(itemInfo);
                    dismiss();
                }

            }
        });

        picPhoto.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (listener != null) {
                    ItemInfo itemInfo = new ItemInfo();
                    itemInfo.id = R.id.tv_pic_photo;
                    listener.itemSelect(itemInfo);
                    dismiss();
                }
            }
        });
        cancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
    }


    @Override
    public int getLayoutId() {
        return R.layout.dialog_choice_pic;
    }


}
