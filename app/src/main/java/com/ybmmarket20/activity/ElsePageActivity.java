package com.ybmmarket20.activity;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.lifecycle.ViewModelProvider;

import com.analysys.ANSAutoPageTracker;
import com.github.mzule.activityrouter.annotation.Router;
import com.tencent.smtt.sdk.QbSdk;
import com.ybm.app.bean.NetError;
import com.ybmmarket20.BuildConfig;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.IdentityInfo;
import com.ybmmarket20.bean.InfoByMerchantIdBean;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.AppUtilKt;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.JGTrackManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.utils.CommonUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.viewmodel.ElsePageViewModel;

import java.util.HashMap;
import java.util.Map;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 设置界面
 */
@Router("elsepage")
public class ElsePageActivity extends BaseActivity implements ANSAutoPageTracker {

    @Bind(R.id.iv_else_pwd_mention)
    TextView mPwdMention;
    @Bind(R.id.tv_certification)
    TextView tvCertification;
    @Bind(R.id.iv_remind_flag)
    ImageView ivRemindFlag;
    @Bind(R.id.account_certification)
    ConstraintLayout accountCertification;
    @Bind(R.id.rl_environment)
    RelativeLayout rlEnvironment;
    @Bind(R.id.rl_clear_cache)
    RelativeLayout rlClearCache;
    @Bind(R.id.rl_real_name)
    RelativeLayout rlRealName;
    @Bind(R.id.rl_real_name_divider)
    View rlRealNameDivider;
    @Bind(R.id.tv_real_name_text)
    TextView tvRealNameText;

    private ElsePageViewModel mViewModel;
    private IdentityInfo mIdentityInfo;

    public static void jgTrackBtnClick(Context mContext, String btnName) {
        HashMap<String, Object> properties = new HashMap<>();
        properties.put(JGTrackManager.FIELD.FIELD_PAGE_ID, JGTrackManager.TrackSetting.PAGE_ID);
        properties.put(JGTrackManager.FIELD.FIELD_TITLE, JGTrackManager.TrackSetting.TITLE);
        properties.put(JGTrackManager.FIELD.FIELD_URL, "com.ybmmarket20.activity.ElsePageActivity");
        properties.put(JGTrackManager.FIELD.FIELD_REFERRER, "com.ybmmarket20.activity.ElsePageActivity");
        properties.put(JGTrackManager.FIELD.FIELD_MODULE, "功能");
        properties.put(JGTrackManager.FIELD.FIELD_BTN_NAME, btnName);
    }

    @Override
    protected void initData() {
        mViewModel = new ViewModelProvider(this).get(ElsePageViewModel.class);
        setTitle("设置");
        getInfoByMerchantId();
        if (SpUtil.readBoolean("isRedDot", true)) {
            ivRemindFlag.setVisibility(View.VISIBLE);
        } else {
            ivRemindFlag.setVisibility(View.GONE);
        }

        if (BuildConfig.DEBUG || BuildConfig.APPLICATION_ID.equalsIgnoreCase("com.ybmmarket20.debug")) {
            rlEnvironment.setVisibility(View.VISIBLE);
        } else {
            rlEnvironment.setVisibility(View.GONE);
        }
        mViewModel.getIdentityInfoLiveData().observe(this, identityInfoBaseBean -> {
            dismissProgress();
            if (identityInfoBaseBean.isSuccess()) {
                if (identityInfoBaseBean.data != null
                        && !TextUtils.isEmpty(identityInfoBaseBean.data.getCertId())
                        && !TextUtils.isEmpty(identityInfoBaseBean.data.getName())) {
                    mIdentityInfo = identityInfoBaseBean.data;
                    rlRealName.setVisibility(View.VISIBLE);
                    rlRealNameDivider.setVisibility(View.VISIBLE);
                    tvRealNameText.setText("已认证");
                }
            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        showProgress();
        //可能会存在实名认证清除了，所以每次回到这个页面都重新刷新一下数据
        mViewModel.queryIdentityInfo();
        RequestParams params = new RequestParams();
        String merchantId = SpUtil.getMerchantid();
        params.put("merchantId", merchantId);
        HttpManager.getInstance().post(AppNetConfig.PASSWORD_VERIFY, params, new BaseResponse<Boolean>() {

            @Override
            public void onSuccess(String content, BaseBean<Boolean> obj, Boolean aBoolean) {
                if (aBoolean != null && !aBoolean) {
                    //密码简单请及时修改
                    mPwdMention.setText("安全提示：密码简单，请及时修改");
                    mPwdMention.setTextColor(Color.RED);
                } else {
                    //安全
                    mPwdMention.setText("安全");
                    mPwdMention.setTextColor(Color.GREEN);
                }
            }

            public void onFailure(NetError error) {

            }
        });
    }

    @Override
    protected void initHead() {
        super.initHead();
//        new DefaultNavigationBar.Builder(this).setTitle("设置").build();
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_else;
    }

    @OnClick({R.id.ll_about, R.id.account_certification, R.id.ll_msg, R.id.ll_pwd, R.id.btn_logout
            , R.id.ll_notification, R.id.ll_permission, R.id.rl_user_protocol, R.id.rl_privacy_protocol,
            R.id.rl_environment, R.id.rl_unregister, R.id.rl_privacy_protocol_setting, R.id.rl_clear_cache, R.id.rl_real_name,
            R.id.rl_certificate_info,R.id.rl_pay_setting
    })
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.ll_about:   //关于药帮忙
                gotoAtivity(AboutActivity.class, null);
//                FlutterUtils.INSTANCE.openPage(this, "/AboutPage");
                jgTrackBtnClick(this, "关于我们");
                break;
            case R.id.ll_msg:   //通知设置
                gotoAtivity(MsgSettingActivity.class);
                jgTrackBtnClick(this, "消息通知");
                break;
            case R.id.ll_pwd:   //修改密码
                gotoAtivity(AlterPasswordActivity.class);
                jgTrackBtnClick(this, "修改密码");
                break;
            case R.id.btn_logout:  //退出登陆
                XyyIoUtil.track(XyyIoUtil.ACTION_EXIT);
                loginOut();
                ((YBMAppLike) getApplication()).saasOrderSourcePath = null;
                gotoAtivity(LoginActivity.class);
                jgTrackBtnClick(this, "退出");
                break;

            case R.id.ll_permission:  //系统权限
                RoutersUtils.open("ybmpage://commonh5activity?url=" + AppNetConfig.PERMISSION + "&head_menu=" + 0);
                jgTrackBtnClick(this, "系统权限");
                break;
            case R.id.ll_notification:  //通知栏快捷设置
                RoutersUtils.open("ybmpage://setnotification");
                jgTrackBtnClick(this, "通知栏快捷设置");
                break;
            case R.id.account_certification:  //被委托人信息确认
                SpUtil.writeBoolean("isRedDot", false);
                ivRemindFlag.setVisibility(View.GONE);
                CommonUtil.INSTANCE.skipCertification();
                jgTrackBtnClick(this, "被委托人信息认证");
                break;
            case R.id.rl_user_protocol: //用户服务条款
                mViewModel.getLoginAgreement(2);
                jgTrackBtnClick(this, "用户服务协议");
                break;
            case R.id.rl_privacy_protocol: //隐私协议
                mViewModel.getLoginAgreement(1);
                jgTrackBtnClick(this, "隐私政策");
                break;
            case R.id.rl_environment:
                startActivity(new Intent(getApplicationContext(), DebugActivity.class));
                break;
//            case R.id.rl_unregister:
//                RoutersUtils.open("ybmpage://unregister");
//                break;
            case R.id.rl_privacy_protocol_setting: //推荐管理
                RoutersUtils.open("ybmpage://recommendmanageractivity");
                jgTrackBtnClick(this, "推荐管理");
                break;
            case R.id.rl_clear_cache: //清理缓存
                new AlertDialogEx(this)
                        .setTitle("清理缓存")
                        .setMessage("确认要清理缓存吗？\n （清理会保留已登录的账号信息）")
                        .setCancelButton("取消", (AlertDialogEx.OnClickListener) (dialog, button) -> dialog.dismiss())
                        .setConfirmButton("确认", (AlertDialogEx.OnClickListener) (dialog, button) -> {
                            try {
                                QbSdk.clearAllWebViewCache(ElsePageActivity.this, true);
                                ToastUtils.showShort("清理成功");
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }).show();
                jgTrackBtnClick(this, "清理缓存");
                break;

            case R.id.rl_real_name:
                //实名认证
                if (mIdentityInfo == null) return;
                RoutersUtils.open("ybmpage://realnameauthnetication?name=" + mIdentityInfo.getName() + "&idCardNo=" + mIdentityInfo.getCertId());
                jgTrackBtnClick(this, "实名认证");
                break;

            case R.id.rl_certificate_info:
                //证照信息
                String url = "";
                if (BuildConfig.DEBUG) {
                    url = "http://xyy-ec-test.oss-cn-beijing.aliyuncs.com/ybm/pingan/excel/idCard.pdf";
                } else {
                    url = "http://xyy-ec.oss-cn-hangzhou.aliyuncs.com/ybm/pingan/excel/idCard.pdf";
                }
                RoutersUtils.open("ybmpage://tbspdfdisplay?fileurl=" + url + "&title=证照信息&isShowShare=0");
                jgTrackBtnClick(this, "证照信息");
                break;
            case R.id.rl_pay_setting:
                RoutersUtils.open("ybmpage://paysetting");
                break;
        }
    }

    /**
     * 查看认证信息
     */
    private void getInfoByMerchantId() {
        RequestParams params = new RequestParams();
        params.put("merchantId", SpUtil.getMerchantid());
        HttpManager.getInstance().post(AppNetConfig.GET_INFOBY_MERCHANTID, params, new BaseResponse<InfoByMerchantIdBean>() {

            @Override
            public void onSuccess(String content, BaseBean<InfoByMerchantIdBean> obj, InfoByMerchantIdBean infoByMerchantIdBean) {
                super.onSuccess(content, obj, infoByMerchantIdBean);
                if (infoByMerchantIdBean != null) {
                    //AuthSwitch这个字段  1是开  0是关
                    int authSwitch = infoByMerchantIdBean.authSwitch;
                    if (authSwitch == 1) {
                        accountCertification.setVisibility(View.VISIBLE);
                        //0-未认证，1-审核中，2-审核失败，3-审核成功，4-失效
                        if (infoByMerchantIdBean.status == 0) {//失败
                            tvCertification.setText("去认证");
                        } else if (infoByMerchantIdBean.status == 1) {//成功
                            tvCertification.setText("审核中");
                        } else if (infoByMerchantIdBean.status == 2) {//成功
                            tvCertification.setText("审核失败");
                        } else if (infoByMerchantIdBean.status == 3) {//成功
                            tvCertification.setText("审核成功");
                        } else if (infoByMerchantIdBean.status == 4) {//成功
                            tvCertification.setText("信息失效");
                        } else {
                            tvCertification.setText("");
                        }
                    } else {
                        accountCertification.setVisibility(View.GONE);
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
            }
        });
    }

    @Override
    public Map<String, Object> registerPageProperties() {
        Map<String, Object> properties = new HashMap<>();
        properties.put(JGTrackManager.FIELD.FIELD_PAGE_ID, JGTrackManager.TrackSetting.PAGE_ID);
        properties.put(JGTrackManager.FIELD.FIELD_TITLE, JGTrackManager.TrackSetting.TITLE);
        return properties;
    }

    @Override
    public String registerPageUrl() {
        return AppUtilKt.getFullClassName(ElsePageActivity.this);
    }
}
