package com.ybmmarket20.activity.jdpay

import android.content.Intent
import android.text.TextUtils
import android.widget.LinearLayout
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.ybm.app.common.SmartExecutorManager
import com.ybmmarket20.R
import com.ybmmarket20.activity.jdpay.adapter.PayWayV2Adapter
import com.ybmmarket20.bean.PayDialogBean
import com.ybmmarket20.bean.PayTypeBankCard
import com.ybmmarket20.bean.PayTypeEntry
import com.ybmmarket20.bean.payment.VirtualGoldRechargeBean
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.ViewOnClickListener
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.AlertDialogHtml.Companion.showAlertDialogAuthorization
import com.ybmmarket20.utils.FingerprintUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.YBMPayUtil
import com.ybmmarket20.view.CheckPayFingerprintDialog
import com.ybmmarket20.view.CheckPayPasswordDialog
import com.ybmmarket20.view.VerificationCodeDialog
import com.ybmmarket20.viewmodel.CheckPayPasswordViewModel
import com.ybmmarket20.viewmodel.PayWayV2ViewModel
import com.ybmmarket20.viewmodel.VIRTUAL_PAY_ID_ADD_BANK_CARD
import kotlinx.coroutines.launch

abstract class PayWayBaseV2Activity : BaseActivity() {

    @JvmField
    var mPayWayV2Adapter: PayWayV2Adapter? = null
    var mOrderId: String? = null
    var mOrderInfo: String? = null
    var mAmount: String? = null
    var mOrderNo: String? = null
    var mPayRoute: String? = null
    var mPayReqNo: String? = null //购物金支付时返回的payReqNo

    // 是否需要在轮训结果后做 提示以及后续操作
    var needNotification = true
    //购物金已经支付了
    var isVirtualGoldRechargePay = false

    private val checkPayPasswordViewModel: CheckPayPasswordViewModel by viewModels()

    //支付的验证密码弹窗
    private var mCheckPayPasswordForPayDialog: CheckPayPasswordDialog? = null
    //添加银行卡的验证密码弹窗
    private var mCheckPayPasswordForAddBankCardDialog: CheckPayPasswordDialog? = null

    //当前选中的购物金充值项
    var mCurrentVirtualGoldRechargeBean: VirtualGoldRechargeBean? = null

    var endTime = "3天"
    //是否来自购物金充值收银台
    var isFromShoppingRecharge = false

    abstract fun getViewModel(): PayWayV2ViewModel

    abstract fun getReqScene(): String

    private fun getStr(id: Int): String? {
        return YBMAppLike.getAppContext().getString(id)
    }

    /**
     * 修改密码后处理
     * return true:重写方法并处理，false：不处理
     */
    open fun handleSetPassword(): Boolean = false

    /**
     * 银联支付回调
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //生成订单后修改密码
        if (resultCode == SET_PAY_PASSWORD_RESULT_ORDER) {
            RoutersUtils.open("ybmpage://myorderlist/0")
            finish()
            return
        }

        //通过返回按钮返回
        if (resultCode == SET_PAY_PASSWORD_RESULT_CODE_BACK) {
            handleSetPassword()
            return
        }
        if (resultCode == SET_PAY_PASSWORD_RESULT_CODE) {
            //设置密码返回
            RoutersUtils.openForResult(
                "ybmpage://addbankcard?bindResultFrom=${bindFromKey()}",
                100
            )
            YBMAppLike.handler.postDelayed({
                getViewModel().isBindCardStatus = true
            }, 200)
            return
        }

        if (!TextUtils.equals(getViewModel().mSelectedPayCode, YBMPayUtil.PAY_YLSDK)) {
            return
        }
        /*
         * 支付控件返回字符串:success、fail、cancel 分别代表支付成功，支付失败，支付取消
         */
        val str = data?.extras?.getString("pay_result")
        if (TextUtils.isEmpty(str)) {
            return
        }
        val result = data?.extras?.getString("result_data")
        if (str.equals("success", ignoreCase = true)) {
            // 支付成功后，extra中如果存在result_data，取出校验
            // result_data结构见c）result_data参数说明
            //"支付成功！";
            YBMPayUtil.callUnionPayResult(
                YBMPayUtil.RET_CODE_SUCCESS,
                getStr(R.string.payway_result_succ),
                result
            )
        } else if (str.equals("fail", ignoreCase = true)) {
            //"支付失败！";
            YBMPayUtil.callUnionPayResult(
                YBMPayUtil.RET_CODE_FAIL_SDK,
                getStr(R.string.payway_result_error_sdk),
                result
            )
        } else if (str.equals("cancel", ignoreCase = true)) {
            //"用户取消了支付";
            YBMPayUtil.callUnionPayResult(
                YBMPayUtil.RET_CODE_USER_CANCEL,
                getStr(R.string.payway_result_cancel),
                result
            )
        }
    }

    /**
     * 返回提示
     */
    fun showBackDialog(endTime: String,hasTips:Boolean = false) {

        if (isFromShoppingRecharge){
            val dialogEx = AlertDialogEx(this)
            var message = if (hasTips)"充值限时享优惠，是否放弃本次付款？" else "是否放弃本次付款？"

            dialogEx.setTitle("")
                    .setMessage(
                            """${message}
            """.trimIndent()
                    )
                    .setCancelButton(
                            "放弃",
                            AlertDialogEx.OnClickListener { _: AlertDialogEx?, _: Int ->
                                RoutersUtils.open("ybmpage://myvirtualmoney")
                                finish()
                            }).setConfirmButton("继续付款", null)
                    .show()
        }else{
            val dialogEx = AlertDialogEx(this)
            dialogEx.setTitle("")
                    .setMessage(
                            """
            下单后${endTime}内未支付，订单
            将会被取消，请尽快支付
            """.trimIndent()
                    )
                    .setCancelButton(
                            "放弃支付",
                            AlertDialogEx.OnClickListener { _: AlertDialogEx?, _: Int ->
                                finish()
                            }).setConfirmButton("继续支付", null)
                    .show()
        }
    }

    /**
     * 支付后提示Dialog
     */
    fun processNotifyDialog(payDialogBeanBaseBean: PayDialogBean) {
        val dialogEx = AlertDialogEx(
            this,
            if (payDialogBeanBaseBean.styleTemplate == 1) LinearLayout.HORIZONTAL else LinearLayout.VERTICAL
        )
        dialogEx.setMessage(payDialogBeanBaseBean.title)
        dialogEx.setCanceledOnTouchOutside(false)
        for (payDialogBtnItem in payDialogBeanBaseBean.btnList!!) {
            //根据服务端返回添加按钮
            if (TextUtils.isEmpty(payDialogBtnItem.color)) {
                payDialogBtnItem.color = "#292933"
            }
            dialogEx.addButton(
                payDialogBtnItem.btnType,
                payDialogBtnItem.btnText,
                PayDialogViewOnClickListener(payDialogBtnItem.btnType),
                payDialogBtnItem.color
            )
        }
        dialogEx.setTitle(null)
        dialogEx.show()
    }

    inner class PayDialogViewOnClickListener(private val btnType: Int) : ViewOnClickListener {

        //已完成付款
        private val PAY_DIALOG_FINISH = 1

        //付款失败
        private val PAY_DIALOG_FAILURE = 2

        //切换支付渠道
        private val PAY_DIALOG_SWITCH_OTHER = 3

        //遇到提示支付存在风险，点此重新支付
        private val PAY_DIALOG_REPAY = 4

        override fun onClick(dialog: AlertDialogEx, button: Int) {
            when (btnType) {
                PAY_DIALOG_FINISH -> {
                    //已完成付款
                    needNotification = true
                    getViewModel().getPayResultQueryParams(mOrderId ?: "", mOrderNo?: "", mPayRoute ?: "")
                }
                PAY_DIALOG_FAILURE ->                     //付款失败
                    needNotification = false
                PAY_DIALOG_SWITCH_OTHER -> {}
                PAY_DIALOG_REPAY -> {
                    //遇到提示支付存在风险，点此重新支付,降级
                    pay(payChannel = "1")
                }
            }
        }
    }

    /**
     * 支付
     */
    fun pay(payChannel: String? = "") {
        var amount = ""
        var rechargeType = ""
        if (isFromShoppingRecharge){
                amount = mAmount?:""
                rechargeType = "2"
        }else{
            if (isUseVirtualMoney() && mCurrentVirtualGoldRechargeBean != null) { //仅有购物金充值选项时带此参数
                amount = mCurrentVirtualGoldRechargeBean?.amount?.toString() ?: ""
                rechargeType = "2"
            }
        }

        getViewModel().getPayParams(mOrderId?: "", mPayRoute?: "", payChannel, mOrderNo, getReqScene(),amount,rechargeType)
    }

    open fun switchBankCard() {}

    fun setBaseObserver() {
        //选中
        getViewModel().selectPayTypeItemLiveData.observe(this) {
            mPayWayV2Adapter?.notifyItemChanged(it.newPosition)
            mPayWayV2Adapter?.notifyItemChanged(it.oldPosition)
            updateSelectPayType(it.selectedBankCard)
        }

        //支付弹窗
        getViewModel().payDialogLiveData.observe(this) {
            if (it != null
                && it.isSuccess
                && it.data != null
                && it.data.btnList != null
            ) {
                processNotifyDialog(it.data)
            }
        }

        getViewModel().jDPayPWSettingLiveData.observe(this) {
            dismissProgress()
            if (it.data?.pwSettingStatus != 1) {
                //未设置支付密码
                val dialogEx = AlertDialogEx(this)
                dialogEx.setTitle("")
                    .setMessage("为了您的资金安全，请先设置支付密码")
                    .setCancelButton("稍后设置", "#9494A5",
                        AlertDialogEx.OnClickListener { _, _ -> })
                    .setConfirmButton("去设置",
                        AlertDialogEx.OnClickListener { _, _ ->
                            RoutersUtils.openForResult("ybmpage://setpaypw?settingStatus=$SET_PAY_PASSWORD_SETTING_PAY", 100)
                            getViewModel().isBindCardStatus = true
                        })
                    .show()
            } else {
                checkSubmitOrder()
            }
        }

        //是否设置支付密码
        getViewModel().jDPWSettingLiveData.observe(this) {
            dismissProgress()
            if (it.data.pwSettingStatus != 1) {
                //未设置支付密码
                val dialogEx = AlertDialogEx(this)
                dialogEx.setTitle("")
                    .setMessage("为了您的资金安全，请先设置支付密码")
                    .setCancelButton("稍后设置", "#9494A5",
                        AlertDialogEx.OnClickListener { _, _ -> })
                    .setConfirmButton("去设置",
                        AlertDialogEx.OnClickListener { _, _ ->
                            RoutersUtils.openForResult("ybmpage://setpaypw?settingStatus=$SET_PAY_PASSWORD_SETTING", 100)
                            getViewModel().isBindCardStatus = true
                        })
                    .show()
            } else {
                //已设置支付密码
                if (mCheckPayPasswordForAddBankCardDialog == null) {
                    mCheckPayPasswordForAddBankCardDialog = CheckPayPasswordDialog(this, checkPayPasswordViewModel)
                }
                mCheckPayPasswordForAddBankCardDialog?.apply {
                    setOnCheckPasswordCallback {
                        dismissProgress()
                        RoutersUtils.openForResult("ybmpage://addbankcard?bindResultFrom=${bindFromKey()}", 100)
                        //标记当前是绑卡状态
                        getViewModel().isBindCardStatus = true
                    }
                    setOnForgetPassword {
                        getViewModel().isBindCardStatus = true
                        RoutersUtils.openForResult("ybmpage://setpaypw?settingStatus=$SET_PAY_PASSWORD_SETTING_PAY", 100)
                        dismiss()
                    }
                    setInit()
                    show()
                }
            }
        }

        //判断是否需要身份认证弹窗
        getViewModel().checkPayAuthenticationLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                val showPopBean = it.data
                val code: Int = showPopBean.code
                if (code == 40000) { //需要弹框
                    lifecycleScope.launch {
                        VerificationCodeDialog.show(
                            supportFragmentManager,
                            showPopBean.msg,
                            merchant_id,
                            mOrderId
                        )
                        VerificationCodeDialog.verifyListener = { aBoolean: Boolean? ->
                            if (aBoolean != null && aBoolean == true) {
                                pay(showPopBean.payChannel)
                            }
                        }
                    }
                } else if (code == 10000) { //不弹
                    pay(showPopBean.payChannel)
                }
            }
        }

        //支付参数
        getViewModel().getPayParamsLiveData.observe(this) {
            if (isNeedVirtualMoneyRecharge()){
                isVirtualGoldRechargePay = true
            }
            YBMPayUtil.getInstance().ybmPay(it, PayCallBack())
        }

        //查询支付结果参数
        getViewModel().getPayResultParamsLiveData.observe(this) {
            YBMPayUtil.getInstance()
                .queryPayResult(it, PayCallBack(), needNotification)
        }

        //获取选中的PayType
        getViewModel().getPayTypeLiveData.observe(this) {
            dismissProgress()
            prePay(it)
        }

        getViewModel().jumpToAddBankCardLiveData.observe(this) {
            val pairParams = getViewModel().getSelectedPayRouter(bindFromKey(), VIRTUAL_PAY_ID_ADD_BANK_CARD)
            if (pairParams != null) {
                if (TextUtils.isEmpty(pairParams.first)) {
                    if (!TextUtils.isEmpty(pairParams.second)) {
                        RoutersUtils.open(pairParams.second)
                    }
                } else {
                    showAlertDialogAuthorization(
                        this,
                        (if (pairParams.first == null) "" else pairParams.first)!!,
                        if (pairParams.second == null) "" else pairParams.second
                    ) { s: String? ->
                        RoutersUtils.open(s)
                        null
                    }
                }
            }
        }
    }

    abstract fun bindFromKey(): String

    //是否已经为购物金验证过密码
    var isCheckedPwForVirtualGold = false



    fun prePay(payType: PayTypeEntry) {
        if (isCheckedPwForVirtualGold) {
            isCheckedPwForVirtualGold = false
            pay()
            return
        }

        if (payType is PayTypeBankCard || (isUseVirtualMoney() && !isCheckedPwForVirtualGold)) {
            if (payType.deviceStatusOnPay?.switchStatus == 1
                && payType.deviceStatusOnPay?.deviceStatus == 1
                && SpUtil.getPayFingerprintStatus()
            ) {
                //已设置支付密码并已经开启指纹
                CheckPayFingerprintDialog(this)
                    .setCheckFingerprint {
                        FingerprintUtil.fingerprintAuthenticate(
                            this@PayWayBaseV2Activity,
                            "您正在使用指纹进行支付",
                            "请将手指放在感应区进行指纹验证",
                            "取消",
                            object : FingerprintUtil.FingerprintCallback {
                                override fun onSuccess() {
                                    if (isUseVirtualMoney()) {
                                        if (isNeedVirtualMoneyRecharge()) {
                                            pay()
                                        } else {
                                            isCheckedPwForVirtualGold = true
                                            toOrderForVirtualMoney()
                                        }
                                    } else {
                                        pay()
                                    }
                                }

                                override fun onFail(errorCode: Int, errString: CharSequence) {
                                    setPayFingerprintTip(errString.toString(), "#FE2021")
                                }

                            }
                        )
                    }
                    .setUsePWCallback {
                        dismiss()
                        payType.deviceStatusOnPay?.switchStatus = 0
                        prePay(payType)
                    }
                    .setCloseCheckFingerprintDialog(::onClosePayPWDialogCallback)
                    .showAndCannotDismiss()
            } else {
                // 校验支付密码
                if (mCheckPayPasswordForPayDialog == null) {
                    mCheckPayPasswordForPayDialog = CheckPayPasswordDialog(this, checkPayPasswordViewModel)
                }
                mCheckPayPasswordForPayDialog?.apply {
                    setOnCheckPasswordCallback {checkPayPasswordBean ->
                        dismissProgress()
                        //保存生成的token
                        checkPayPasswordBean.token?.let { it1 -> getViewModel().saveToken(it1) }
                        if (isUseVirtualMoney()) {
                            if (isNeedVirtualMoneyRecharge()) {
                                pay()
                            } else {
                                isCheckedPwForVirtualGold = true
                                toOrderForVirtualMoney()
                            }
                        } else {
                            pay()
                        }
                    }
                    setOnForgetPassword {
                        jumpSetPw()
                        getViewModel().isBindCardStatus = true
                        dismiss()
                    }
                    mOrderId?.let { it1 -> setOrderId(it1) }
                    mOrderNo?.let { it1 -> setOrderNo(it1)}
                    setTranNo(getTranNo())
                    setInit(isPayCheck = true)
                    clearReceive()
                    showAndCannotDismiss()
                    mCheckPayPasswordForPayDialog?.closePayPWDialogCallback = ::onClosePayPWDialogCallback
                }
            }
        } else {
            //清空token
            if (!isUseVirtualMoney()) {
                getViewModel().saveToken("")
            }
            pay()
        }
    }

    open fun jumpSetPw() {}

    /**
     * 监听验证支付密码弹框关闭
     */
    open fun onClosePayPWDialogCallback() {}

    open fun updateSelectPayType(payTypeBankCard: PayTypeBankCard?) {}

    open fun checkSubmitOrder() {}

    open fun isUseVirtualMoney(): Boolean {
        return false
    }

    open fun isNeedVirtualMoneyRecharge(): Boolean {
        return false
    }

    open fun getTranNo(): String = ""

    open fun toOrderForVirtualMoney() {}

    open fun payment() {}

    open fun jump2ShoppingGoldResult(){

    }

    inner class PayCallBack : YBMPayUtil.PayCallBackListener {

        override fun setOrderNoAndPayReqNo(orderNo: String?,payReqNo:String?) {
            super.setOrderNoAndPayReqNo(orderNo,payReqNo)
            if (!orderNo.isNullOrEmpty()){
                mOrderNo = orderNo
            }
            if (!payReqNo.isNullOrEmpty()){
                mPayReqNo = payReqNo
            }
        }

        override fun xydCallBack(paymentKey: String?) {
            paymentKey?.let {
                RoutersUtils.open("ybmpage://commonh5activity?url=${it}")
            }
        }

        override fun payCallBack(resultCode: Int, msg: String) {
            SmartExecutorManager.getInstance().executeUI(Runnable {
                //sdk回调后 请求查询结果调一次 回来onResume 查询结果又回调了一次 第一次finish了 后面就拦截掉
                if(<EMAIL>){
                    return@Runnable
                }
                if (needNotification) {
                    ToastUtils.showShort(msg)
                } else {
                    return@Runnable
                }
                if (resultCode == YBMPayUtil.RET_CODE_PROCESS || resultCode == YBMPayUtil.RET_CODE_SUCCESS) {
                    if (this@PayWayBaseV2Activity is PayWayV2Activity) {
                        if (isFromShoppingRecharge){
                            jump2ShoppingGoldResult()
                        }else{
                            RoutersUtils.open("ybmpage://payresultactivity/" + mOrderId + "/" + getViewModel().mSelectedPayCode + "/" + mAmount + "/" + mOrderNo)
                        }
                        //支付成功刷新首页
                        SmartExecutorManager.getInstance().handler.postDelayed({
                            LocalBroadcastManager.getInstance(applicationContext)
                                    .sendBroadcast(Intent(IntentCanst.ACTION_SWITCH_HOME_INFO))
                            LocalBroadcastManager.getInstance(applicationContext)
                                    .sendBroadcast(Intent(IntentCanst.ACTION_ORDER_LIST_REFRESH))
                        }, 2000)
                        finish()

                    }else{
                        if (isUseVirtualMoney()) {  //  购物金支付流程是先支付再提单
                            payment()
                        } else {
                            RoutersUtils.open("ybmpage://payresultactivity/" + mOrderId + "/" + getViewModel().mSelectedPayCode + "/" + mAmount + "/" + mOrderNo)
                            //支付成功刷新首页
                            SmartExecutorManager.getInstance().handler.postDelayed({
                                LocalBroadcastManager.getInstance(applicationContext)
                                        .sendBroadcast(Intent(IntentCanst.ACTION_SWITCH_HOME_INFO))
                                LocalBroadcastManager.getInstance(applicationContext)
                                        .sendBroadcast(Intent(IntentCanst.ACTION_ORDER_LIST_REFRESH))
                            }, 2000)
                            finish()
                        }
                    }
                }else{
                    isVirtualGoldRechargePay = false
                }
            })
        }
    }

}