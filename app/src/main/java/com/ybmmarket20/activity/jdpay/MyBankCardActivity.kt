package com.ybmmarket20.activity.jdpay

import android.content.Intent
import androidx.activity.viewModels
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.bean.BankCardItem
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.view.CheckPayPasswordDialog
import com.ybmmarket20.viewmodel.CheckPayPasswordViewModel
import com.ybmmarket20.viewmodel.UnBindBankCardViewModel
import kotlinx.android.synthetic.main.activity_my_bank_card.*

/**
 * 我的银行卡
 */
@Router("mybankcardlist")
class MyBankCardActivity: BaseActivity() {

    private var bankCard: BankCardItem? = null
    private val unBindBankCardViewModel: UnBindBankCardViewModel by viewModels()
    private val checkPayPasswordViewModel: CheckPayPasswordViewModel by viewModels()

    override fun getContentViewId(): Int = R.layout.activity_my_bank_card

    override fun initData() {
        setTitle("我的银行卡")
        val bankCardInfoJson = intent.getStringExtra("bankInfo")
        bankCard = RoutersUtils.getBase64StringToBean(bankCardInfoJson, BankCardItem::class.java)
        bankCard?.apply {
            ImageUtil.load(this@MyBankCardActivity, bankLogo, ivLogo)
            tvBankName.text = bankShortName
            tvBankCardType.text = cardTypeStr
            tvBankCardNum.text = cardNo
        }

        rtvUnBind.setOnClickListener {
            showProgress()
            checkPayPasswordViewModel.queryPWSettingStatus()
        }

        unBindBankCardViewModel.unBindCardLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                ToastUtils.showShort("解绑成功")
                dismissProgress()
                RoutersUtils.open("ybmpage://bankcard")
            }
        }

        checkPayPasswordViewModel.jDPWSettingLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                if (it.data.pwSettingStatus != 1) {
                    //未设置支付密码
                    val dialogEx = AlertDialogEx(this)
                    dialogEx.setTitle("")
                        .setMessage("为了您的资金安全，请先设置支付密码")
                        .setCancelButton("稍后设置", "#9494A5",
                            AlertDialogEx.OnClickListener { _, _ -> })
                        .setConfirmButton("去设置",
                            AlertDialogEx.OnClickListener { _, _ ->
                                RoutersUtils.openForResult("ybmpage://setpaypw?settingStatus=$SET_PAY_PASSWORD_SETTING", 100)
                            })
                        .show()
                } else {
                    AlertDialogEx(this)
                        .setTitle("温馨提示")
                        .setMessage("确定要解绑吗？解绑后将无法使用此卡进行支付")
                        .setCorner()
                        .setCancelButton("仍要解绑", "#9494A5") { _, _ ->
                            dismissProgress()
                            CheckPayPasswordDialog(this, checkPayPasswordViewModel).apply {
                                setOnCheckPasswordCallback {
                                    showProgress()
                                    unBindCard()
                                }
                                setOnForgetPassword {
                                    RoutersUtils.open("ybmpage://setpaypw?settingStatus=$SET_PAY_PASSWORD_MODIFY")
                                    dismissProgress()
                                    dismiss()
                                }
                                setInit()
                                show()
                            }
                        }.setConfirmButton("保持绑定", "#00B377") { _, _ -> }
                        .show()
                }
            }
        }
    }

    /**
     * 解绑
     */
    private fun unBindCard() {
        bankCard?.reqNo?.let { it1 -> unBindBankCardViewModel.unBindBankCard(it1) }
    }

//    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
//        super.onActivityResult(requestCode, resultCode, data)
//        if (resultCode == SET_PAY_PASSWORD_RESULT_CODE) {
//            //忘记密码返回
//            showProgress()
//            unBindCard()
//        }
//    }

}