package com.ybmmarket20.activity;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.text.ClipboardManager;
import android.text.Html;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ForegroundColorSpan;
import android.text.util.Linkify;
import android.view.Gravity;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;
import androidx.lifecycle.SavedStateViewModelFactory;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SimpleItemAnimator;

import com.github.mzule.activityrouter.annotation.Router;
import com.xyy.canary.utils.LogUtil;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.mailcertificate.MailCertificateActivity;
import com.ybmmarket20.adapter.OrderDetailtAdapter;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.CheckOrderDetailBean;
import com.ybmmarket20.bean.CheckOrderRowsBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.ImPackUrlBean;
import com.ybmmarket20.bean.LogisticsBean;
import com.ybmmarket20.bean.OrderActionBean;
import com.ybmmarket20.bean.OrderDeliveryLogisticsDetailList;
import com.ybmmarket20.bean.OrderDeliveryMessageListBean;
import com.ybmmarket20.bean.PdfUrlBean;
import com.ybmmarket20.bean.RefundProductListBean;
import com.ybmmarket20.business.snapshot.ui.TradingSnapshotListActivity;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.OnResult;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.utils.DateTimeUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.DividerLine;
import com.ybmmarket20.view.FormPdfListDialog;
import com.ybmmarket20.view.OrderActionLayout;
import com.ybmmarket20.view.OrderDetailPop;
import com.ybmmarket20.view.OrderItemAptitudeView;
import com.ybmmarket20.view.OrderServiceView;
import com.ybmmarket20.viewmodel.OrderDetailViewModel;
import com.ybmmarket20.viewmodel.PaymentGoodsViewModel;
import com.ybmmarket20.viewmodel.viewstore.GlobalViewModelStore;
import com.ybmmarketkotlin.utils.TextViewKt;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 订单详情
 * 订单状态：2017-4-28 更新增加完整订单状态
 * 1 审核中，订单审核中 action 再次购买，申请退款（全,只有详情有）
 * 2 配送中 action 再次购买，申请退款（选择对话框,只有详情有）
 * 3 已完成，action 再次购买，领取余额，申请退款（部分，只有详情有），可能还有查看退款功能（列表详情都有）
 * 4 取消 action 再次购买
 * 5 已删除 action 再次购买
 * 6 已拆单
 * 7 出库中  action 再次购买，申请退款（全,只有详情有）
 * 10 待支付，待付款 action 取消订单，立即支付
 * 21 已拒签 action 再次购买，申请退款（只有详情有）
 * 20 已送达 action 再次购买，确认收货，申请退货（部分，只有详情有）
 * 90 已申请退款 action 查看退款，再次购买
 * 91 退款完成 action 查看退款，再次购买
 * 92 退款失败 action 查看退款，再次购买
 */
@Router({"orderdetail", "orderdetail/:order_id", "orderdetail/:order_id/order_no"})
public class OrderDetailActivity extends OrderDetailAnalysisActivity {

    @Bind(R.id.tv_name)
    TextView tvName;
    @Bind(R.id.tv_phone)
    TextView tvPhone;
    @Bind(R.id.tv_address)
    TextView tvAddress;
    @Bind(R.id.ll_address)
    ConstraintLayout llAddress;
    @Bind(R.id.tv_order_state)
    TextView tvOrderState;
    @Bind(R.id.ll_unpay_layout)
    LinearLayout llUnpayLayout;
    @Bind(R.id.tv_unpay_time_countdown)
    TextView tvUnpayTimeCountdown;
    @Bind(R.id.tv_view_refund)
    TextView tvViewRefund;
    @Bind(R.id.tv_payway)
    TextView tvPayway;
    @Bind(R.id.tv_payway_desc)
    TextView tvPaywayDesc;
    @Bind(R.id.tv_balance_desc)
    TextView tvBalanceDesc;
    @Bind(R.id.ll_balance)
    LinearLayout llBalance;
    @Bind(R.id.tv_order_detail_no)
    TextView tvOrderDetailNo;
    @Bind(R.id.tv_order_created_time)
    TextView tvOrderCreatedTime;
    @Bind(R.id.tv_order_pay_time)
    TextView tvOrderPayTime;
    @Bind(R.id.tv_order_end_time)
    TextView tvOrderEndTime;
    @Bind(R.id.tv_kefu)
    TextView tvKefu;
    @Bind(R.id.tv_im)
    TextView tvIm;
    @Bind(R.id.ll_kufu)
    LinearLayout llKufu;
    @Bind(R.id.cl_kefu)
    ConstraintLayout clKufu;
    @Bind(R.id.tv_product_num)
    TextView tvProductNum;
    @Bind(R.id.lv_product)
    RecyclerView rvProduct;
    @Bind(R.id.order_detail_pay_num)
    TextView orderDetailPayNum;
    @Bind(R.id.order_detail_show)
    TextView orderDetailShow;
    @Bind(R.id.ll_btn)
    OrderActionLayout llBtn;
    @Bind(R.id.btn_order_balance)
    TextView btnBalance;
    @Bind(R.id.tv_company_name)
    TextView tvCompanyName;
    @Bind(R.id.cl_logistics)
    ConstraintLayout llLogistics;
    @Bind(R.id.iv_logistics_car)
    ImageView ivLogisticsCar;
    @Bind(R.id.tv_content)
    TextView tvContent;
    @Bind(R.id.tv_time)
    TextView tvTime;
    @Bind(R.id.tv_delivery_time)
    TextView tvDeliveryTime;
    @Bind(R.id.ll_order_pictorial)
    LinearLayout llOrderPictorial;
    @Bind(R.id.tv_order_detail_no_copy)
    TextView tvOrderDetailNoCopy;
    @Bind(R.id.tv_order_remark)
    TextView tvOrderRemark;
    @Bind(R.id.mStoreName)
    TextView mStoreName;
    @Bind(R.id.mOpenAccountLayout)
    LinearLayout mOpenAccountLayout;
    @Bind(R.id.mOpenAccountRemindTv)
    TextView mOpenAccountRemindTv;
    @Bind(R.id.ll_order_trading_snapshot)
    LinearLayout mTradingSnapshotLayout;
    @Bind({R.id.tv_order_trading_snapshot})
    TextView tvOrderTradingSnapshot;
    @Bind(R.id.ll_payment_num)
    LinearLayout llPaymentNum;
    @Bind(R.id.ll_refund_num)
    LinearLayout llRefundNum;
    @Bind(R.id.order_detail_refund_num)
    TextView tvDetailRefundNum;
    @Bind(R.id.ll_bottom)
    LinearLayout llBottom;
    @Bind(R.id.tv_call_service)
    TextView tvCallService;
    @Bind(R.id.cl_call_service)
    ConstraintLayout clCallService;
    @Bind(R.id.cl_upload_voucher_tips)
    ConstraintLayout clUploadVoucherTips;
    @Bind(R.id.cl_no_upload_voucher_tips)
    ConstraintLayout clNoUploadVoucherTips;
    @Bind(R.id.tv_call_service_bubble)
    TextView tvCallServiceBubble;
    @Bind(R.id.tv_virtual_gold_tips)
    TextView tvVirtualGoldTips;
    @Bind(R.id.cl_seller_remark)
    ConstraintLayout clSellerRemark;
    @Bind(R.id.tv_seller_remark_content)
    TextView tvSellerRemarkContent;
    @Bind(R.id.llPop)
    LinearLayout llPop;
    @Bind(R.id.tvCompanyPopName)
    TextView tvCompanyPopName;
    @Bind(R.id.tvOriginalName)
    TextView tvOriginalName;
    @Bind(R.id.aptitudeView)
    OrderItemAptitudeView aptitudeView;
    @Bind(R.id.clSystemException)
    ConstraintLayout clSystemException;
    @Bind(R.id.oderServiceView)
    OrderServiceView oderServiceView;
    @Bind(R.id.afterSalesGuide)
    ConstraintLayout afterSalesGuide;
    @Bind(R.id.llLicense)
    LinearLayout llLicense;
    @Bind(R.id.tvLicenseArrow)
    TextView tvLicenseArrow;
    @Bind(R.id.tvLicenseContent)
    TextView tvLicenseContent;
    @Bind(R.id.ivAfterSalesGuide)
    ImageView ivAfterSalesGuide;
    @Bind(R.id.ivAfterSalesGuide2)
    ImageView ivAfterSalesGuide2;

    private SimpleDateFormat dateFormat;
    private SimpleDateFormat dateFormat02;
    private OrderDetailtAdapter adapter;
    protected View bgView;
    private String orderId;
    private List<RefundProductListBean> rowsBeans = new ArrayList<>();
    private CheckOrderDetailBean order;
    private String orderNo;
    private OrderDetailViewModel mViewModel;
    private PaymentGoodsViewModel paymentGoodsViewModel;
    private int guideCounter = 1;


    @Override
    protected void initData() {
        mViewModel = new ViewModelProvider(this).get(OrderDetailViewModel.class);
        paymentGoodsViewModel = new ViewModelProvider(GlobalViewModelStore.Companion.get().getGlobalViewModelStore(), new SavedStateViewModelFactory(
                getApplication(),
                OrderDetailActivity.this
        )).get(PaymentGoodsViewModel.class);
        setTitle("订单详情");
        dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        dateFormat02 = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        orderId = getIntent().getStringExtra(IntentCanst.ORDER_ID);
        bgView = findViewById(R.id.bg);
        tvOrderTradingSnapshot.setText(Html.fromHtml(getResources().getString(R.string.trading_snapshot_tips)));
        adapter = new OrderDetailtAdapter(rowsBeans);
        adapter.setShowFindSameGoodsBtn(true);
        rvProduct.setNestedScrollingEnabled(false);
        DividerLine divider = new DividerLine(DividerLine.VERTICAL);
        divider.setSize(1);
        divider.setColor(0xffF5F5F5);
        rvProduct.addItemDecoration(divider);
        rvProduct.setAdapter(adapter);
        rvProduct.setLayoutManager(new WrapLinearLayoutManager(getMySelf()));
        rvProduct.setEnabled(false);
        ((SimpleItemAnimator) rvProduct.getItemAnimator()).setSupportsChangeAnimations(false);
        mViewModel.getFormLiveData().observe(this, new FormPdfObserver());
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_order_detail;
    }


    //查看订单详情
    private void getOrderDetail(String id) {
        showProgress();
        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);
        if (!TextUtils.isEmpty(id)) {
            params.put("id", id);
        }
        params.put("sceneType", "1");
        HttpManager.getInstance().post(AppNetConfig.ORDER_DETAIL, params, new BaseResponse<CheckOrderDetailBean>() {

            @Override
            public void onSuccess(String content, BaseBean<CheckOrderDetailBean> listBean, CheckOrderDetailBean data) {
                if (tvPayway == null) {
                    return;
                }
                dismissProgress();
                if (listBean != null && listBean.isSuccess() && data != null) {
                    if (listBean.isSuccess()) {
                        orderNo = data.orderNo;
                        pvTrack(orderNo);
                        setOrderData(data);
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }
        });
    }

    private String shopCode = "";

    private void handleSystemAndMerchantException(Context context, String content, String orderNo) {
        new AlertDialogEx(context)
                .setMessage(content)
                .setMessageGravity(Gravity.START)
                .setTitle("资质异常提醒")
                .setCorner()
                .setCancelButton("稍后更新", "#9494A5", (dialog, button) -> {})
                .setConfirmButton("去更新", "##00B377", (dialog, button) -> {
                    RoutersUtils.open("ybmpage://aptitude");
                    HashMap<String, String> trackMap = new HashMap<>();
                    trackMap.put("order_no", orderNo);
                    trackMap.put("page_source", "reminder_popup");
                    XyyIoUtil.track("Update_Qualification", trackMap);
                })
                .show();
    }

    //设置订单数据
    public void setOrderData(final CheckOrderDetailBean order) {
        if (order == null) {
            return;
        }
        setLicence(order);
        //卡单
        if (order.hasOrderExceptionFlag) {
            CheckOrderRowsBean checkOrderRowsBean = new CheckOrderRowsBean();
            checkOrderRowsBean.hasOrderExceptionFlag = order.hasOrderExceptionFlag;
            checkOrderRowsBean.sysException = order.sysException;
            checkOrderRowsBean.supplierException = order.supplierException;
            checkOrderRowsBean.evidenceVerifyStatus = order.evidenceVerifyStatus;
            aptitudeView.setFromPage("order_detail");
            aptitudeView.setCanTrack(true);
            aptitudeView.setVisibility(View.VISIBLE);
            clSystemException.setVisibility(View.VISIBLE);
            checkOrderRowsBean.orderNo = orderNo;
            aptitudeView.setData(checkOrderRowsBean);
            aptitudeView.setCanTrack(true);
            aptitudeView.setMerchantExceptionCheckCallback(s -> {
                handleSystemAndMerchantException(OrderDetailActivity.this, checkOrderRowsBean.supplierException, checkOrderRowsBean.orderNo);
                return null;
            });
            aptitudeView.setSystemExceptionCheckCallback(s -> {
                handleSystemAndMerchantException(OrderDetailActivity.this, checkOrderRowsBean.sysException, checkOrderRowsBean.orderNo);
                return null;
            });
            HashMap<String, String> trackMap = new HashMap<>();
            trackMap.put("order_no", checkOrderRowsBean.orderNo);
            trackMap.put("system_reminder", !TextUtils.isEmpty(checkOrderRowsBean.sysException)?"1": "0");
            trackMap.put("business_reminder", !TextUtils.isEmpty(checkOrderRowsBean.supplierException)?"1": "0");
            trackMap.put("page_source", "order_detail");
            XyyIoUtil.track("Qualification_Exception_Reminder_Exposure", trackMap);
        }
        shopCode = order.mainShopCode;
        getData(order.orderNo);
        this.order = order;
        adapter.setVirtualSupplier(order.isVirtualSupplier);
        rowsBeans = OrderProductListActivity.getListData(order, false);
        adapter.setOrderNo(orderNo);
        adapter.setNewData(rowsBeans);
        final int payType = order.payType;
        if (llBtn != null) {
            OrderActionBean bean = new OrderActionBean(order.id + "", order.money + "", order.payType, order.status, order.balanceStatus,
                    order.balanceText, order.refundText, order.refundCount, order.canConfirmReceipt, order.appraiseStatus, false, order.orderNo,
                    order.isThirdCompany, order.isShowRefund, order.showUploadEvidenceBtn,
                    order.transferInfo.transferInfoUrl, order.freightRefundType, order.evidenceVerifyStatus, order.reminderStatus,order.orgId,order.companyName);
            bean.isFbp = order.isFbp;
            bean.applyForAfterSales = order.applyForAfterSales;
            if (order.status == 10 && !TextUtils.isEmpty(order.getPayEndTime()) && order.payType == 1) {//目前只有在线支付可以支付倒计时
                bean.payTime = DateTimeUtil.getSecond(order.getPayEndTime());
            }
            bean.result = new OnResult<OrderActionBean>() {
                @Override
                public void onResult(boolean succ, OrderActionBean actionBean) {

                }

                @Override
                public void onRefresh(OrderActionBean bean) {
                    getOrderDetail(orderId);
                }
            };
            bean.rowsBeans = rowsBeans;
            llBtn.bindData(bean, false, isKaUser);

            if (bean.status == 3) {
                btnBalance.setVisibility(View.VISIBLE);
                if (bean.balanceStatus == 0) {//没有领取
                    btnBalance.setText("领取余额");
                } else if (bean.balanceStatus == 1) {//已经领取
                    btnBalance.setText("查看余额");
                } else {//不能领取
                    btnBalance.setVisibility(View.GONE);
                }
            } else if (bean.status == 10) {
                llBtn.setRepositoryPrice(true);
            }
            btnBalance.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    if (bean.balanceStatus == 1) {//查看余额
                        viewBalance();
                    } else if (bean.balanceStatus == 0) {//领取余额
                        getBalance(bean);
                    }
                }
            });

        }

        //设置线下支付 未支付 上传凭证的提示语
        if (order.status == 10){
            switch (order.evidenceVerifyStatus){
                case 0:
                    //待上传
                    if (order.payType == 3) {
                        //线下转账
                        clNoUploadVoucherTips.setVisibility(View.VISIBLE);
                    } else {
                        clNoUploadVoucherTips.setVisibility(View.GONE);
                    }
                    clUploadVoucherTips.setVisibility(View.GONE);
                    break;
                case 1:
                    clNoUploadVoucherTips.setVisibility(View.GONE);
                    clUploadVoucherTips.setVisibility(View.VISIBLE);
                    break;
                default:
                    clNoUploadVoucherTips.setVisibility(View.GONE);
                    clUploadVoucherTips.setVisibility(View.GONE);
            }
        }else {
            clNoUploadVoucherTips.setVisibility(View.GONE);
            clUploadVoucherTips.setVisibility(View.GONE);
        }

        // 开户相关
        mStoreName.setText(order.origName);
        //是第三方药店时，显示资质信息
        mOpenAccountLayout.setVisibility(order.isThirdCompany == 1 ? View.VISIBLE : View.GONE);
        if (!TextUtils.isEmpty(order.companyName)) {
            llPop.setVisibility(View.VISIBLE);
            tvCompanyName.setVisibility(View.GONE);
            tvCompanyPopName.setText(order.origName);
            tvOriginalName.setText("企业名称：" + order.companyName);
        } else {
            llPop.setVisibility(View.GONE);
            tvCompanyName.setVisibility(View.VISIBLE);
            if (!TextUtils.isEmpty(order.origName)) {
                tvCompanyName.setText(order.origName);
            }
            //第三方药店
            if (order.isThirdCompany == 1) {
                tvCompanyName.setCompoundDrawablesWithIntrinsicBounds(getResources().getDrawable(R.drawable.icon_payment_pop), null, getResources().getDrawable(R.drawable.right_new), null);
            } else {
                tvCompanyName.setCompoundDrawablesWithIntrinsicBounds(getResources().getDrawable(R.drawable.icon_autotrophy_new), null, null, null);
            }
        }

        if (order.isThirdCompany == 1 && !order.isOpenAccount()) {
            mOpenAccountRemindTv.setVisibility(View.VISIBLE);
            mOpenAccountLayout.setVisibility(View.VISIBLE);
            findViewById(R.id.mStep1Layout).setVisibility(order.showAptitude() ? View.VISIBLE : View.GONE);
            findViewById(R.id.mStep2Layout).setVisibility(order.showMailCertificate() ? View.VISIBLE : View.GONE);
            TextView mailCertificateTv = findViewById(R.id.mStep2Tv);
            if (!order.showAptitude() && order.showMailCertificate()) {
                mailCertificateTv.setText("1.证件复印件盖章邮寄，上传快递单照片");
            } else {
                mailCertificateTv.setText("2.证件复印件盖章邮寄，上传快递单照片");
            }
        } else {
            mOpenAccountRemindTv.setVisibility(View.GONE);
            mOpenAccountLayout.setVisibility(View.GONE);
        }

        configKefu(order);

        if (TextUtils.isEmpty(order.balanceRemark)) {
            llBalance.setVisibility(View.GONE);
        } else {
            llBalance.setVisibility(View.VISIBLE);
            tvBalanceDesc.setText(order.balanceRemark);
        }
        tvName.setText(order.contactor);
        tvPhone.setText(order.mobile);
        tvAddress.setText(order.address);

        tvOrderRemark.setVisibility(!TextUtils.isEmpty(order.remark) ? View.VISIBLE : View.GONE);
        tvOrderRemark.setText("订单备注：" + order.remark);
        if (!TextUtils.isEmpty(order.virtualGoldTips)) {
            tvVirtualGoldTips.setVisibility(View.VISIBLE);
            tvVirtualGoldTips.setText(order.virtualGoldTips);
        }
        if (TextUtils.isEmpty(order.payTypeName)) {
            tvPayway.setText("支付方式：");
        } else {
            tvPayway.setText(getSpannableString("支付方式：", order.payTypeName));
        }
        tvProductNum.setText("订单商品 (" + order.varietyNum + ")");
        orderDetailPayNum.setText("¥" + String.valueOf(UiUtils.transform(order.money)));
        final OrderDetailPop pop = new OrderDetailPop(this);
        pop.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                if (isFinishing() || isDestroy || orderDetailShow == null) {
                    return;
                }
                orderDetailShow.setActivated(false);
                orderDetailShow.setText("展开明细");
                bgView.setVisibility(View.GONE);
            }
        });
        pop.initData(order.totalAmount, order.promoDiscountAmount, order.rebate, order.freightAmount, order.voucherDiscountAmount, order.balanceAmount, order.fixedPriceAmount, order.redPacketAmount, order.payDiscount,order.limitTimeFullDiscount);
        //ka用户隐藏运费，展示满减和满折优惠金额；
        if (isKaUser) { //KA隐藏运费逻辑会和initData的费用为0时隐藏逻辑冲突。只在KA时判断，就不改动更多逻辑了。
            pop.hiddenCountentForKA(isKaUser);
        }
        pop.setFreightTipBtnShow(order.freightTipsShowStatus != 0, shopCode);

        orderDetailShow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                showPop(pop, v);
            }
        });
        tvOrderDetailNo.setText(getSpannableString("订单编号：", order.orderNo));
        String format = dateFormat.format(new Date(order.createTime));
        tvOrderCreatedTime.setText(getSpannableString("下单时间：", format));
        if (order.finishTime > 10) {
            tvOrderEndTime.setVisibility(View.VISIBLE);
            tvOrderEndTime.setText(getSpannableString("完成时间：", dateFormat.format(new Date(order.finishTime))));
        } else {
            tvOrderEndTime.setVisibility(View.GONE);
        }

        if (payType == 1 && order.payTime > 0) {
            tvOrderPayTime.setVisibility(View.VISIBLE);
            tvOrderPayTime.setText(getSpannableString("支付时间：", dateFormat.format(new Date(order.payTime))));
        } else if (payType == 3) { //线下转账
            if (order.paymentTime > 0) {
                tvOrderPayTime.setVisibility(View.VISIBLE);
                tvOrderPayTime.setText(getSpannableString("到账时间：", dateFormat.format(new Date(order.paymentTime))));
            } else {
                tvOrderPayTime.setVisibility(View.GONE);
            }
            tvPaywayDesc.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (order != null && order.transferInfo != null) {
                        RoutersUtils.open(order.transferInfo.transferInfoUrl);
                    }
                    //RoutersUtils.open("ybmpage://commonh5activity?url=" + AppNetConfig.getStaticHost2Https() + "xyyvue/dist/#/onlinepay?ybm_title=转账说明&head_menu=0&merchantId=" + SpUtil.getMerchantid());
                }
            });
            if (order.evidenceVerifyStatus == 2){ //电汇凭证审核通过才显示
                tvPaywayDesc.setVisibility(View.VISIBLE);
            }else {
                tvPaywayDesc.setVisibility(View.GONE);
            }
        } else {
            tvOrderPayTime.setVisibility(View.GONE);
        }
        // 支付状态
        if (!TextUtils.isEmpty(order.statusName)) {
            tvOrderState.setText(order.statusName);
        }
        if (order.status == 10 && order.countDownNewTime > 0 && order.showExpireReminder) {
            // 待支付订单
            llUnpayLayout.setVisibility(View.VISIBLE);
//            ExtensionClassKt.addCountDown(tvUnpayTimeCountdown, (order.payExpireTime - System.currentTimeMillis()), null, null, null, null);
            TextViewKt.addCountDown(tvUnpayTimeCountdown, order.countDownNewTime * 1000, null, null, () -> {
                getOrderDetail(orderId);
                return null;
            });

        }
        //物流信息
        llOrderPictorial.setBackgroundResource(getState(order.status));
        tvViewRefund.setVisibility(order.refundCount > 0 ? View.VISIBLE : View.GONE);
        tvViewRefund.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                RoutersUtils.open("ybmpage://refundlist/" + order.id + "?billType="+order.billType);
                RoutersUtils.open("ybmpage://refundoraftersales?orderNo=" + order.orderNo);
            }
        });

        //交易快照入口 0则无交易快照 1则有交易快照
        if (order.snapshotShow == 1) {
            mTradingSnapshotLayout.setVisibility(View.VISIBLE);
            mTradingSnapshotLayout.setOnClickListener(v -> {
                Intent tradingSnapshotIntent = new Intent(OrderDetailActivity.this, TradingSnapshotListActivity.class);
                tradingSnapshotIntent.putExtra(IntentCanst.ORDER_NO, order.orderNo);
                startActivity(tradingSnapshotIntent);
            });
        } else {
            mTradingSnapshotLayout.setVisibility(View.GONE);
        }
        setRefund(order);
//        orderInfoForm(order);
        oderServiceView.setData(order, mViewModel, paymentGoodsViewModel, () -> {
            afterSalesGuide.setVisibility(View.VISIBLE);
            ivAfterSalesGuide.setVisibility(View.VISIBLE);
            ivAfterSalesGuide2.setVisibility(View.GONE);
            afterSalesGuide.setOnClickListener(v -> {
                if (guideCounter == 1) {
                    guideCounter ++;
                    ivAfterSalesGuide.setVisibility(View.GONE);
                    ivAfterSalesGuide2.setVisibility(View.VISIBLE);
                } else {
                    v.setVisibility(View.GONE);
                }
            });
            return null;
        });
        setSellerRemark(order);
    }

    /**
     * 配置客服
     * @param order
     */
    private void configKefu(CheckOrderDetailBean order) {
        if (order.isTelegraphicTransferBusiness()) { //电汇商业
            clKufu.setVisibility(View.VISIBLE);
            tvKefu.setVisibility(View.GONE);
            clCallService.setVisibility(View.GONE);
            clKufu.setOnClickListener(
                    v -> sendOnLineService(order.isThirdCompany,order.isTelegraphicTransferBusiness())
            );
        }else { //电汇平台
            clKufu.setVisibility(View.GONE);
            clCallService.setVisibility(View.VISIBLE);
            if (order.isThirdCompany == 1 && TextUtils.isEmpty(order.merchantPhone)) {
                tvKefu.setVisibility(View.GONE);
            } else {
                tvKefu.setVisibility(View.VISIBLE);
            }
//            tvCallService.setText("联系商家");
//            tvCallService.setCompoundDrawablesWithIntrinsicBounds(getResources().getDrawable(R.drawable.icon_contact_pop_kefu), null, null, null);
//            tvCallServiceBubble.setVisibility(View.GONE);
            clCallService.setOnClickListener(v -> sendOnLineService(order.isThirdCompany,order.isTelegraphicTransferBusiness()));
        }
    }

    /**
     * 设置卖家备注
     * @param order
     */
    private void setSellerRemark(CheckOrderDetailBean order) {
        if (order != null && !TextUtils.isEmpty(order.sellerRemark)) {
            clSellerRemark.setVisibility(View.VISIBLE);
            tvSellerRemarkContent.setText(order.sellerRemark);
        } else {
            clSellerRemark.setVisibility(View.GONE);
        }
    }

    /**
     * 设置退款金额
     *
     * @param order
     */
    @SuppressLint("SetTextI18n")
    private void setRefund(CheckOrderDetailBean order) {
        LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) llPaymentNum.getLayoutParams();
        if (order.status == 90 || order.status == 91 || order.status == 92) {
            //90 已申请退款 action 查看退款，再次购买
            //91 退款完成 action 查看退款，再次购买
            //92 退款失败 action 查看退款，再次购买
            llRefundNum.setVisibility(View.VISIBLE);
//            try {
//                String refundCashPayAmount = order.detailList.get(0).refundCashPayAmount;
//                tvDetailRefundNum.setText("¥" + UiUtils.transform(refundCashPayAmount));
//            } catch (Exception e) {
//                e.printStackTrace();
//                tvDetailRefundNum.setText("¥" + UiUtils.transform("0"));
//            }
            tvDetailRefundNum.setText("¥" + UiUtils.transform(order.refundCashPayAmount));
            orderDetailPayNum.setTextColor(ContextCompat.getColor(this, R.color.color_676773));
            lp.bottomMargin = ConvertUtils.dp2px(9);
        } else {
            llRefundNum.setVisibility(View.GONE);
            tvDetailRefundNum.setTextColor(ContextCompat.getColor(this, R.color.color_ff2121));
            lp.bottomMargin = ConvertUtils.dp2px(12);
        }
        llPaymentNum.setLayoutParams(lp);
    }

    private SpannableStringBuilder getSpannableString(String spanPrefix, String spanText) {
        SpannableStringBuilder spannableString = new SpannableStringBuilder(spanPrefix + spanText);
        int startIndex = spanPrefix.length();
        int endIndex = spanPrefix.length() + spanText.length();
        spannableString.setSpan(new ForegroundColorSpan(ContextCompat.getColor(this, R.color.color_292933)), startIndex, endIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    public int getState(int status) {

        int bg = R.drawable.transparent;

        switch (status) {
            case 10:
                bg = R.drawable.icon_order_payment;
                break;
            case 1:
                bg = R.drawable.icon_order_the_delivery;
                break;
            case 2:
                bg = R.drawable.icon_order_the_goods;
                break;
            case 3:
                bg = R.drawable.icon_order_complete;
                break;
            case 4:
                bg = R.drawable.icon_order_cancel;
                break;
            case 5:
                bg = R.drawable.icon_order_delete;
                break;
            case 6:
                bg = R.drawable.icon_order_split_open_single;
                break;
            case 7:
                bg = R.drawable.icon_order_outbound;
                break;
            case 101:
                bg = R.drawable.icon_order_evaluate;
                break;
            case 90:
            case 91:
                bg = R.drawable.icon_order_reimburse;
                break;
            default:
                bg = R.drawable.icon_order_default;
                break;
        }
        return bg;
    }

    //查看余额
    private static void viewBalance() {
        RoutersUtils.open("ybmpage://balanceactivity/");
    }

    //领取余额
    private void getBalance(final OrderActionBean bean) {
        if (bean == null) {
            return;
        }
        if (TextUtils.isEmpty(bean.balanceText)) {
            bean.balanceText = "确认收货后，可领取余额，同时此订单将无法申请退款";
        }
        final AlertDialogEx alert = new AlertDialogEx(this);
        alert.setMessage(bean.balanceText);
        alert.setConfirmButton("同意并领取", new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                showProgress();
                RequestParams params = new RequestParams();
                params.put("merchantId", SpUtil.getMerchantid());
                params.put("orderId", bean.id);
                HttpManager.getInstance().post(AppNetConfig.ORDERS_BALANCE, params, new BaseResponse<EmptyBean>() {
                    @Override
                    public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean data) {
                        dismissProgress();
                        if (obj != null && obj.isSuccess()) {
                            UiUtils.toast("领取余额成功");
                            if (bean.result != null) {
                                bean.balanceStatus = 1;
                                btnBalance.setText("查看余额");

                            }
                            return;
                        }
                    }

                    @Override
                    public void onFailure(NetError error) {
                        dismissProgress();
                    }

                });
            }
        });
        alert.setCancelButton("暂不领取", null);
        alert.show();
    }

    /**
     * 设置距离底部尺寸
     */
    public void setPopMarginBottom(OrderDetailPop pop) {
        pop.setMarginBottom(llBottom.getMeasuredHeight());
    }

    public void showPop(final OrderDetailPop pop, final View view) {
        final int duration = 70;
        bgView.setAlpha(0);
        bgView.setVisibility(View.VISIBLE);
        ObjectAnimator animator = ObjectAnimator.ofFloat(bgView, View.ALPHA, 0).setDuration(duration);
        animator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);

            }

            @Override
            public void onAnimationStart(Animator animation) {
                super.onAnimationStart(animation);
                bgView.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        pop.show(view);
                        view.setActivated(true);
                        orderDetailShow.setText("收起明细");
                        setPopMarginBottom(pop);
                    }
                }, 20);
            }
        });
        animator.start();
    }

    /*
     * 获取物流信息
     * orderId 订单id
     * */
    public void getData(String orderNo) {
        if (tvContent == null) {
            return;
        }
        RequestParams params = new RequestParams();
        params.put("merchantId", SpUtil.getMerchantid());
        if (!TextUtils.isEmpty(orderNo)) {
            params.put("orderNo", orderNo);
        }
        HttpManager.getInstance().post(AppNetConfig.GET_ORDER_DELIVERY_BY_ORDER_NO, params, new BaseResponse<LogisticsBean>() {
            @Override
            public void onSuccess(String content, BaseBean<LogisticsBean> obj, LogisticsBean data) {
                dismissProgress();
                if (obj != null && obj.isSuccess()) {
                    if (data != null) {
                        List<OrderDeliveryMessageListBean> orderDeliveryMessageList = data.orderDeliveryMessageList;

                        if (orderDeliveryMessageList != null && orderDeliveryMessageList.size() > 0) {
                            handlerList(orderDeliveryMessageList);
                        }
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }

        });

    }

    /*
     * 设置物流信息
     * */
    private void handlerList(List<OrderDeliveryMessageListBean> list) {
        if (tvContent == null) {
            return;
        }

        //取物流list的第一条数据下的第一条物流信息
        if (list != null && !list.isEmpty()) {

            if (list.get(0) != null && list.get(0).orderDeliveryLogisticsDetailList != null) {
//                if (list.get(0).arrivalTime > 0 && order.status == 2) {
//                    tvDeliveryTime.setVisibility(View.VISIBLE);
//                    tvDeliveryTime.setText("预计送达: " + getTime(dateFormat02, list.get(0).arrivalTime));
//                } else {
//                    tvDeliveryTime.setVisibility(View.GONE);
//                }
                if (list.get(0).isSign == 1) {
                    ivLogisticsCar.setImageResource(R.drawable.icon_logistics_complete);
                }
                List<OrderDeliveryLogisticsDetailList> orderDeliveryLogisticsDetailList = list.get(0).orderDeliveryLogisticsDetailList;
                if (orderDeliveryLogisticsDetailList.get(0) != null && !TextUtils.isEmpty(orderDeliveryLogisticsDetailList.get(0).description)) {
                    llLogistics.setVisibility(View.VISIBLE);
                    if (!TextUtils.isEmpty(orderDeliveryLogisticsDetailList.get(0).description)) {
                        tvContent.setText(Html.fromHtml(orderDeliveryLogisticsDetailList.get(0).description));
                        tvContent.setAutoLinkMask(Linkify.ALL);
                        tvContent.setMovementMethod(LinkMovementMethod.getInstance());
                        tvContent.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                llLogistics.performClick();

                                if (tvContent.getSelectionStart() == -1 && tvContent.getSelectionEnd() == -1) {

                                }
                            }
                        });
                    }
                    tvTime.setText(getTime(dateFormat, orderDeliveryLogisticsDetailList.get(0).deliveryTime));
                }
            }

        } else {
            llLogistics.setVisibility(View.GONE);
        }
    }

    @OnClick({R.id.llPop, R.id.tv_company_name, R.id.cl_logistics, R.id.tv_order_detail_no_copy, R.id.mOpenAccountTv, R.id.mAptitudeTv, R.id.mMailCertificateTv, R.id.tv_kefu, R.id.cl_call_service})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.cl_logistics:
                if (order.id > 0) {
                    RoutersUtils.open("ybmpage://logistics?order_id=" + order.orderNo);
                }
                break;
            case R.id.llPop:
            case R.id.tv_company_name:
                if (order != null && order.isThirdCompany == 1) {
                    RoutersUtils.open("ybmpage://shopactivity?orgId=" + order.orgId);
                }
                break;
            case R.id.tv_order_detail_no_copy:

                if (order != null && !TextUtils.isEmpty(order.orderNo)) {
                    ClipboardManager cm = (ClipboardManager) tvOrderDetailNoCopy.getContext().getSystemService(Context.CLIPBOARD_SERVICE);
                    cm.setText(order.orderNo);
                    ToastUtils.showShort("复制成功");
                }
                break;
            case R.id.mOpenAccountTv:
                //查看开户流程
                RoutersUtils.open("ybmpage://shopactivity/" + order.orgId + "/showaccount");
                break;
            case R.id.mAptitudeTv:
                //我的资质信息
                RoutersUtils.open("ybmpage://aptitude");
                break;
            case R.id.mMailCertificateTv:
                //上传邮寄凭证
                MailCertificateActivity.jumpTo(this, order.orderNo);
                break;
            case R.id.cl_call_service:
                sendOnLineService(order != null ? order.isThirdCompany : 0,order != null ?
                        order.isTelegraphicTransferBusiness() : false);
                break;
            case R.id.tv_kefu:
                //客服电话
                if (order == null) return;
                if (order.isTelegraphicTransferBusiness()) {
                    RoutersUtils.telKefu(true, order.merchantPhone, "呼叫：");
                } else {
                    RoutersUtils.telKefu(true, false);
                }
                break;
        }
    }

    /*
     * 在线客服
     * */
    private void sendOnLineService(int isThirdCompany,Boolean isTelegraphicTransferBusiness) {

        RequestParams params = new RequestParams();
        params.put("isThirdCompany", isThirdCompany + "");

        HttpManager.getInstance().post(AppNetConfig.GET_IM_PACKURL, params, new BaseResponse<ImPackUrlBean>() {

            @Override
            public void onSuccess(String content, BaseBean<ImPackUrlBean> obj, ImPackUrlBean baseBean) {

                if (obj != null && obj.isSuccess()) {

                    if (baseBean != null) {
                        if (isTelegraphicTransferBusiness) {//商家客服
                            RoutersUtils.open(RoutersUtils.getRouterPopCustomerServiceUrl(baseBean.IM_PACK_URL, order.orgId, order.orderNo, order.companyName));
                        } else {//平台客服
                            RoutersUtils.open(RoutersUtils.getRouterYbmOrderCustomerServiceUrl(baseBean.IM_PACK_URL,order.orgId, order.orderNo,order.companyName));
                        }
                    }
                }
            }

        });

    }

    private String getTime(SimpleDateFormat dateFormat, long time) {
        String createTime = null;
        try {
            createTime = dateFormat.format(new Date(time));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return createTime;
    }

    /**
     * 设置随货资质
     */
    private void setLicence(CheckOrderDetailBean orderDetailBean) {
        llLicense.setVisibility(View.VISIBLE);
        if (orderDetailBean.isNeedCredential == 1) {
            tvLicenseContent.setText("查看详情");
            tvLicenseArrow.setVisibility(View.VISIBLE);
            llLicense.setOnClickListener(v -> {
                {
                    paymentGoodsViewModel.clearData();
                    paymentGoodsViewModel.setStaticShopName(orderDetailBean.origName);
                    paymentGoodsViewModel.setStaticData(orderDetailBean);
                    RoutersUtils.open("ybmpage://licenserequirementwithorderstatic?shopCode="+orderDetailBean.orgId);
                }
            });
        } else {
            tvLicenseContent.setText("无需求");
            tvLicenseArrow.setVisibility(View.GONE);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (llBtn != null) {
            llBtn.onDestroy();
        }
        paymentGoodsViewModel.clearData();
    }

    @Override
    protected void onResume() {
        super.onResume();
        getOrderDetail(orderId);
        if (llBtn != null) {
            llBtn.onResume();
        }
    }

    public void refreshData() {
        getOrderDetail(orderId);
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (llBtn != null) {
            llBtn.onPause();
        }
    }

    class FormPdfObserver implements Observer<BaseBean<List<PdfUrlBean>>> {

        @Override
        public void onChanged(BaseBean<List<PdfUrlBean>> listBaseBean) {
            dismissProgress();
            if (listBaseBean != null
                    && listBaseBean.isSuccess()
                    && listBaseBean.data != null
                    && !listBaseBean.data.isEmpty()) {
                if (listBaseBean.data.size() == 1) {
                    PdfUrlBean pdfUrlBean = listBaseBean.data.get(0);
                    String title;
                    if (pdfUrlBean.getFormType() == 2) {
                        title = "仓库交接单";
                    } else {
                        title = "电子出库单";
                    }
                    RoutersUtils.open("ybmpage://browsepdfactivity?fileUrl="+ pdfUrlBean.getUrl() + "&formType=" + pdfUrlBean.getFormType() +"&title=" + title + "&orderNo=" + orderNo);
                } else {
                    new FormPdfListDialog(OrderDetailActivity.this, listBaseBean.data, orderNo).show();
                }
            }
        }
    }

}
