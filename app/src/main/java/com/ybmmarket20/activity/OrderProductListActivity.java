package com.ybmmarket20.activity;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.style.ForegroundColorSpan;
import android.view.KeyEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.SimpleItemAnimator;

import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.OrderDetailtAdapter;
import com.ybmmarket20.bean.CheckOrderDetailBean;
import com.ybmmarket20.bean.CheckOrderDetailRowsBean;
import com.ybmmarket20.bean.RefundProduct;
import com.ybmmarket20.bean.RefundProductListBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.StringUtil;
import com.ybmmarket20.view.DividerLine;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;
import butterknife.ButterKnife;

/**
 * 下单商品明细
 */
public class OrderProductListActivity extends BaseActivity {

    @Bind(R.id.rv_product)
    CommonRecyclerView rvProduct;
    @Bind(R.id.tv_tip)
    TextView tvTip;

    public static List<RefundProductListBean> rowsBeans;
    private static int total = 0;
    protected static YBMBaseMultiItemAdapter adapter;
    protected String giftId = "";
    private static final int GIFT_ID = 20;
    @Bind(R.id.iv_back)
    ImageView ivBack;
    @Bind(R.id.tv_title)
    TextView tvTitle;
    @Bind(R.id.tv_right)
    TextView tvRight;
    @Bind(R.id.iv_right)
    ImageView ivRight;
    @Bind(R.id.ll_title)
    RelativeLayout llTitle;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initReceiver();
    }

    @Override
    protected void initData() {
        setTitle("商品明细");
        setTitleRight();
        adapter = new OrderDetailtAdapter(rowsBeans, false, false, true, true, true, mHandler);
        DividerLine divider = new DividerLine(DividerLine.VERTICAL);
        divider.setSize(1);
        divider.setColor(0xffF5F5F5);
        rvProduct.addItemDecoration(divider);
        rvProduct.setAdapter(adapter);
        rvProduct.setEmptyView(R.layout.empty_view_product);
        rvProduct.setEnabled(false);
        rvProduct.setLoadMoreEnable(false);
        rvProduct.setShowAutoRefresh(false);
        ((SimpleItemAnimator) rvProduct.getRecyclerView().getItemAnimator()).setSupportsChangeAnimations(false);
        ((SimpleItemAnimator)rvProduct.getRecyclerView().getItemAnimator()).setSupportsChangeAnimations(false);
        SpannableStringBuilder builder = new SpannableStringBuilder("实付价、成本价计算结果存在四舍五入情况，价格存在差异仅供参考");
        builder.setSpan(new ForegroundColorSpan(Color.parseColor("#FE2021")), 0, 7, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        tvTip.setText(builder);
    }

    @Override
    protected void onResume() {
        super.onResume();

        setLeft(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                setGiftId();
            }
        });
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {

        if (keyCode == KeyEvent.KEYCODE_BACK) {
            setGiftId();
        }
        return false;
    }

    private void setGiftId() {
        finish();
    }

    @SuppressLint("HandlerLeak")
    Handler mHandler = new Handler() {

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (msg.what == GIFT_ID) {
                if (rvProduct == null) {
                    return;
                }
                giftId = (String) msg.obj;
            }

        }
    };

    public static void setData(RefundProduct bean) {
        if (bean == null) {
            return;
        }
        rowsBeans = new ArrayList<RefundProductListBean>();
        //添加套餐商品
        if (bean.activityPackageList != null && bean.activityPackageList.size() > 0) {
            RefundProductListBean productListBean = new RefundProductListBean();
            total += bean.activityPackageList.size();
            for (int a = 0; a < bean.activityPackageList.size(); a++) {
                productListBean = new RefundProductListBean();
                productListBean.setItemType(RefundProductListBean.ITEMTYPE_PACKAGE_TITLE);
                productListBean.productId = bean.activityPackageList.get(a).id;
                productListBean.productName = bean.activityPackageList.get(a).title;
                productListBean.productAmount = bean.activityPackageList.get(a).packageCount + "";
                rowsBeans.add(productListBean);
                if (bean.activityPackageList.get(a).orderRefundVOList != null && bean.activityPackageList.get(a).orderRefundVOList.size() > 0) {
                    for (RefundProductListBean listBean : bean.activityPackageList.get(a).orderRefundVOList) {
                        listBean.setItemType(RefundProductListBean.ITEMTYPE_PACKAGE_CONTENT);
                        rowsBeans.add(listBean);
                    }
                }
            }
        }
        //添加普通商品
        if (bean.refundOrderDetailList != null) {
            if (bean.refundOrderDetailList.rows != null && bean.refundOrderDetailList.rows.size() > 0) {
                total += bean.refundOrderDetailList.rows.size();
                rowsBeans.addAll(bean.refundOrderDetailList.rows);
            }
        }
    }

    public static List<RefundProductListBean> getListData(CheckOrderDetailBean bean) {
        return getListData(bean, false);
    }

    public static List<RefundProductListBean> getListData(CheckOrderDetailBean bean, boolean onlyContent) {
        if (bean == null) {
            return null;
        }
        rowsBeans = new ArrayList<RefundProductListBean>();
        //添加套餐商品
        RefundProductListBean productListBean;
        total = 0;
        if (bean.getPackageList() != null && bean.getPackageList().size() > 0) {
            total += bean.getPackageList().size();
            for (int a = 0; a < bean.getPackageList().size(); a++) {
                productListBean = new RefundProductListBean();
                int size = 0;
                if (bean.getPackageList().get(a).orderDetailList != null && bean.getPackageList().get(a).orderDetailList.size() > 0) {
                    size = bean.getPackageList().get(a).orderDetailList.size();
                }
                if (!onlyContent) {
                    productListBean.setItemType(RefundProductListBean.ITEMTYPE_PACKAGE_TITLE);
                    productListBean.productId = bean.getPackageList().get(a).id;
                    productListBean.productName = bean.getPackageList().get(a).title;
                    productListBean.productAmount = bean.getPackageList().get(a).packageCount + "";
                    rowsBeans.add(productListBean);
                }
                if (size > 0) {
                    for (CheckOrderDetailRowsBean listBean : bean.getPackageList().get(a).orderDetailList) {
                        addProduct(listBean, RefundProductListBean.ITEMTYPE_PACKAGE_CONTENT);
                    }
                }
                if (!onlyContent) {
                    productListBean = new RefundProductListBean();
                    productListBean.setItemType(RefundProductListBean.ITEMTYPE_PACKAGE_SUBTITLE);
                    productListBean.productPrice = bean.getPackageList().get(a).totalPrice;
                    productListBean.subtotal = bean.getPackageList().get(a).subtotalPrice;
                    rowsBeans.add(productListBean);
                }
            }
        }
        //添加普通商品
        if (bean.getDetailList() != null && bean.getDetailList().size() > 0) {
            total += bean.getDetailList().size();
            int id = 0;
            for (CheckOrderDetailRowsBean listBean : bean.getDetailList()) {
                if (listBean.giftId > 0) {
                    if (id != listBean.giftId) {
                        RefundProductListBean listBean_title = new RefundProductListBean();
                        listBean_title.setItemType(RefundProductListBean.ITEMTYPE_GIFT_CONTENT);
                        listBean_title.id = listBean.giftId;
                        listBean_title.isRandom = listBean.isRandom;
                        listBean_title.priceDes = listBean.priceDesc;
                        rowsBeans.add(listBean_title);
                        id = listBean.giftId;
                    }
                }
                addProduct(listBean, RefundProductListBean.ITEMTYPE_CONTENT);
            }
        }
        return rowsBeans;
    }

    private static void addProduct(CheckOrderDetailRowsBean listBean, int itemType) {
        if (listBean == null) {
            return;
        }
        RefundProductListBean productListBean = new RefundProductListBean();
        productListBean.setItemType(itemType);
        productListBean.imageUrl = listBean.imageUrl;
        productListBean.productId = listBean.getProductId() + "";
        productListBean.productName = listBean.productName;
        productListBean.productPrice = listBean.productPrice;
        productListBean.productAmount = listBean.productAmount + "";
        productListBean.spec = listBean.spec;
        productListBean.fob = listBean.fob;
        productListBean.rkPrice = listBean.purchasePrice;
        productListBean.manufacturer = listBean.manufacturer;
        productListBean.blackProductText = listBean.blackProductText;
        productListBean.tagList = listBean.tagList;
        productListBean.balanceAmount = listBean.balanceAmount;
        productListBean.realPayAmount = listBean.realPayAmount;
        productListBean.useBalanceAmount = listBean.useBalanceAmount;
        productListBean.discountAmount = listBean.discountAmount;
        productListBean.productActivityTag = listBean.productActivityTag;
        productListBean.costPrice=listBean.costPrice;//成本价
        productListBean.isRandom=listBean.isRandom;
        productListBean.priceDes = listBean.priceDesc;
        productListBean.extraGift = listBean.extraGift;
        productListBean.packageId = listBean.packageId;
        if (listBean.giftId > 0) {
            productListBean.type = 5;
        }
        productListBean.subtotal = StringUtil.DecimalFormat2Double(listBean.productPrice * listBean.productAmount);
        rowsBeans.add(productListBean);
    }


    private void setTitleRight() {
        setRigthText(total + "件");
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_order_product_list;
    }

    public static Intent getIntent2Me(Context context, List<RefundProductListBean> list, int totalProduct) {
        Intent intent = new Intent(context, OrderProductListActivity.class);
        if (rowsBeans == null) {
            rowsBeans = new ArrayList<>();
        } else {
            rowsBeans.clear();
        }
        if (list != null) {
            rowsBeans.addAll(list);
        }
        total = totalProduct;
        return intent;
    }

    public void setNewData(List<RefundProductListBean> list, int totalProduct) {
        if (rowsBeans == null) {
            rowsBeans = new ArrayList<>();
        } else {
            rowsBeans.clear();
        }
        if (list != null) {
            rowsBeans.addAll(list);
        }
        total = totalProduct;
        setTitleRight();
        adapter.setNewData(rowsBeans);
    }

    private void initReceiver() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(IntentCanst.ACTION_GIFT_PRODUCT_LIST);
        LocalBroadcastManager.getInstance(this.getApplicationContext()).registerReceiver(mRefreshBroadcastReceiver, intentFilter);
    }

    /*
     * 广播
     * */
    private BroadcastReceiver mRefreshBroadcastReceiver = new BroadcastReceiver() {

        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (IntentCanst.ACTION_GIFT_PRODUCT_LIST.equals(action)) {
                List<RefundProductListBean> detailList = (List<RefundProductListBean>) intent.getSerializableExtra("detailList");
                int total = intent.getIntExtra("total", 0);
                setNewData(detailList, total);
            }
        }
    };

    @Override
    protected void onDestroy() {
        if (rowsBeans != null) {
            rowsBeans.clear();
        }
        rowsBeans = null;
        super.onDestroy();
        ButterKnife.unbind(this);
        if (mRefreshBroadcastReceiver != null) {
            LocalBroadcastManager.getInstance(this.getApplicationContext()).unregisterReceiver(mRefreshBroadcastReceiver);
        }
    }

}
