package com.ybmmarket20.common

import com.ybmmarket20.utils.analysis.XyyIoUtil
import org.json.JSONObject
import java.util.HashMap


/**
 * 网络响应(返回dialog字段)弹窗埋点
 */


//点击
const val RESPONSE_DIALOG_ANALYSIS_CLICK = "1"
//领券
const val RESPONSE_DIALOG_ANALYSIS_GET_COUPON = "2"
//关闭
const val RESPONSE_DIALOG_ANALYSIS_CLOSE = "3"

/**
 * 点击
 */
fun responseDialogAnalysisClick(sceneType: String?, link: String?) {
    if (sceneType == null || link == null) return
    val obj = JSONObject().apply {
        put("type", sceneType)
        put("actionType", RESPONSE_DIALOG_ANALYSIS_CLICK)
        put("link", link)
    }
    XyyIoUtil.track(XyyIoUtil.ACTION_NEWPOPUP_CLICK, obj)
}

/**
 * 获取优惠券
 */
fun responseDialogAnalysisGetCoupon(sceneType: String?, link: String?) {
    if (sceneType == null || link == null) return
    val obj = JSONObject().apply {
        put("type", sceneType)
        put("actionType", RESPONSE_DIALOG_ANALYSIS_GET_COUPON)
        put("link", link)
    }
    XyyIoUtil.track(XyyIoUtil.ACTION_NEWPOPUP_CLICK, obj)
}

/**
 * 支付成功自动领券
 */
fun responseDialogAnalysisAutoGetCoupon(sceneType: String?, couponId: String?) {
    if (sceneType == null || couponId == null) return
    val obj = JSONObject().apply {
        put("type", sceneType)
        put("coupon_id", couponId)
    }
    XyyIoUtil.track(XyyIoUtil.ACTION_AUTO_COUPON_CLICK, obj)
}

/**
 * 支付成功自动领券
 */
fun responseDialogAnalysisAutoGetCouponExposure(sceneType: String?, couponId: String?) {
    if (sceneType == null || couponId == null) return
    val obj = JSONObject().apply {
        put("type", sceneType)
        put("coupon_id", couponId)
    }
    XyyIoUtil.track(XyyIoUtil.ACTION_AUTO_COUPON_EXPOSURE, obj)
}

/**
 * 支付成功自动领券
 */
fun responseDialogAnalysisAutoGetRedEnvelopeExposure(sceneType: String?, activityId: String?, templateId: String?) {
    XyyIoUtil.track(XyyIoUtil.ACTION_AUTO_COUPON_EXPOSURE, hashMapOf(
        "type" to (sceneType?: ""),
        "activity_id" to (activityId?: ""),
        "redpacket_id" to (templateId?: "")
    ))
}

/**
 * 关闭
 */
fun responseDialogAnalysisClose(sceneType: String?) {
    if (sceneType == null) return
    val obj = JSONObject().apply {
        put("type", sceneType)
        put("actionType", RESPONSE_DIALOG_ANALYSIS_CLOSE)
    }
    XyyIoUtil.track(XyyIoUtil.ACTION_NEWPOPUP_CLICK, obj)
}

/**
 * 曝光
 */
fun responseDialogAnalysisExposure(sceneType: String?) {
    if (sceneType == null) return
    val content = HashMap<String, String>()
    content["type"] = sceneType
    XyyIoUtil.track(XyyIoUtil.ACTION_NEWPOPUP_EXPOSURE, content)
}