package com.ybmmarket20.common

import android.content.Context
import android.util.Log
import com.ybmmarket20.bean.CouponRowBean
import com.ybmmarket20.bean.ProductDetailBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SearchRowsBean
import com.ybmmarket20.reportBean.AddToCart
import com.ydmmarket.report.ReportManager

/**
 * <AUTHOR>
 * @date  2024/6/7
 * @description  极光埋点 拓展函数
 */

/**
 * https://wiki.int.ybm100.com/pages/resumedraft.action?draftId=829162149&draftShareId=b191df05-2256-4918-8698-315f01bca20f
 * 參考jgspid生成逻辑
 * 生成拼接jgspid
 * 生成规则（所有文字内容禁止包含下划线）
 * 每一项长度限制15字节（UTF-8）
 * @param a_page_type String? 页面类型  前缀_a
 * @param b_page_id String? 页面ID  前缀_b
 * @param c_page_name String? 页面名称  前缀_c
 * @param e_page_main_tab String? 页面主tab  前缀_e
 * @param g_component_position String? 组件序号  前缀_g
 * @param h_component_name String? 组件名称  前缀_h
 * @param i_component_title String? 组件标题  前缀_i
 * @param l_sub_module_type String? 子模块类型  前缀_l
 * @param m_sub_module_position String? 子模块序号  前缀_m
 * @param n_sub_module_name String? 子模块名称  前缀_n
 * @param s_search_type String? 搜索功能  前缀_s
 * "_s100"-APP中间页搜索词搜索
 * "_s101"-APP中间页热词搜索
 * "_s102"-APP中间页历史搜索词搜索
 * "_s103"-APP联想sug搜索
 * "_s104"-APP首页扫一扫搜索
 * "_s105"-APP首页语音搜索
 * "_s106"-APP中间页搜索发现
 * "_s107"-APP中间页扫一扫搜索
 * "_s108"-APP中间页语音搜索
 */
fun splitJointJgspid(
        a_page_type:String? = "",
        b_page_id:String? = "",
        c_page_name:String?="",
        e_page_main_tab:String?="",
        g_component_position:String?="",
        h_component_name:String?="",
        i_component_title:String?="",
        l_sub_module_type:String?="",
        m_sub_module_position:String?="",
        n_sub_module_name:String?="",
        s_search_type:String?=""
):String{
    var jgspid = ""
    if (!a_page_type.isNullOrEmpty()){
        jgspid += "_a$a_page_type"
    }
    if (!b_page_id.isNullOrEmpty()){
        jgspid += "_b$b_page_id"
    }
    if (!c_page_name.isNullOrEmpty()){
        jgspid += "_c$c_page_name"
    }
    if (!e_page_main_tab.isNullOrEmpty()){
        jgspid += "_e$e_page_main_tab"
    }
    if (!g_component_position.isNullOrEmpty()){
        jgspid += "_g$g_component_position"
    }
    if (!h_component_name.isNullOrEmpty()){
        jgspid += "_h$h_component_name"
    }
    if (!i_component_title.isNullOrEmpty()){
        jgspid += "_i$i_component_title"
    }
    if (!l_sub_module_type.isNullOrEmpty()){
        jgspid += "_l$l_sub_module_type"
    }
    if (!m_sub_module_position.isNullOrEmpty()){
        jgspid += "_m$m_sub_module_position"
    }
    if (!n_sub_module_name.isNullOrEmpty()){
        jgspid += "_n$n_sub_module_name"
    }
    if (!s_search_type.isNullOrEmpty()){
        jgspid += "_s$s_search_type"
    }
    return jgspid
}

/**
 * 部分替换jgspid
 * @param mJgspid String?
 * @param mPrefix String? 前缀_a  _b  ....
 * @param mReplaceContent String?
 * @return String
 */
fun replacePartJgspid(
        mJgspid: String? = "",
        mPrefix:String? ="",
        mReplaceContent:String?=""
):String{
    if (mJgspid.isNullOrEmpty()) return ""
    var jgspid = mJgspid
    if (!mPrefix.isNullOrEmpty()){
        val index = jgspid.indexOf(mPrefix)
        if (index != -1){
            jgspid = jgspid.substring(0,index)+mReplaceContent
        }else{
            jgspid += mReplaceContent
        }

        //可能是没有这个前缀但是想拼上去
        if (!mReplaceContent.isNullOrEmpty() && !jgspid.contains(mPrefix)){
            jgspid += mReplaceContent
        }
    }
    Log.d("lijiang","origin: $mJgspid  now: $jgspid")
    return jgspid
}



/**
 *
 * 商品详情页底部的按钮和加购底部弹窗里面的按钮点击
 * https://wiki.int.ybm100.com/pages/viewpage.action?pageId=797410324
 * 新的 ？ 商品详情页btn按钮点击埋点 具体需要看PRD
 * @receiver Context
 * @param url String
 * @param pageId String 页面ID / 页面标题
 * @param btnName String 控件名字
 * @param btnDesc String 按钮描述
 * @param jgReferrer String 来源
 * @param rank Int
 * @param productId String
 * @param productType String
 * @param operationId String
 * @param operationRank Int
 * @param module String
 * @param navigation1 String
 * @param navigation2 String
 */
fun Context.jgTrackProductDetailNewBtnClick(
        url: String?,
        pageId: String?,
        title:String?,
        btnName: String?,
        btnDesc: String? = "底部弹窗",
        jgReferrer: String?,
        rank: Int?=1,
        productId: String?,
        productType: String?,
        operationId: String? = "",
        operationRank: Int? = 1,
        module: String?,
        navigation1 : String?="",
        navigation2 : String?=""
) {

    var mProductId = 0L
    try {
        mProductId = productId?.toLong() ?: 0L
    } catch (e: Exception) {
        e.printStackTrace()
    }

    val map = HashMap<String, Any>()
    map[JGTrackManager.FIELD.FIELD_URL] = url ?: ""
    map[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = url ?: ""
    map[JGTrackManager.FIELD.FIELD_REFERRER] = jgReferrer ?: ""
    map[JGTrackManager.FIELD.FIELD_PAGE_ID] = pageId ?: ""
    map[JGTrackManager.FIELD.FIELD_TITLE] = title ?: ""
    map[JGTrackManager.FIELD.FIELD_BTN_NAME] = btnName ?: ""
    map[JGTrackManager.FIELD.FIELD_BTN_DESC] = btnDesc ?: ""
    map[JGTrackManager.FIELD.FIELD_RANK] = rank ?: 1
    map[JGTrackManager.FIELD.FIELD_PRODUCT_ID] = mProductId
    map[JGTrackManager.FIELD.FIELD_PRODUCT_TYPE] = productType ?: ""
    if (!operationId.isNullOrEmpty()) {
        map[JGTrackManager.FIELD.FIELD_OPERATION_ID] = operationId
        map[JGTrackManager.FIELD.FIELD_OPERATION_RANK] = operationRank ?: 1
    }
    map[JGTrackManager.FIELD.FIELD_MODULE] = module ?: ""
    map[JGTrackManager.FIELD.FIELD_NAVIGATION_1] = navigation1 ?: ""
    map[JGTrackManager.FIELD.FIELD_NAVIGATION_2] = navigation2 ?: ""

    // 移除极光埋点 - btn_click
    // JGTrackManager.eventTrack(this, JGTrackManager.TrackProductDetail.EVENT_BTN_CLICK, map)
}

/**
 *
 * 商品详情页底部的按钮和加购底部弹窗里面的按钮曝光
 * https://wiki.int.ybm100.com/pages/viewpage.action?pageId=797410324
 * 新的 ？ 商品详情页btn按钮点击埋点 具体需要看PRD
 * @receiver Context
 * @param url String
 * @param pageId String 页面ID / 页面标题
 * @param btnName String 控件名字
 * @param btnDesc String 按钮描述
 * @param jgReferrer String 来源
 * @param rank Int
 * @param productId String
 * @param productType String
 * @param operationId String
 * @param operationRank Int
 * @param module String
 * @param navigation1 String
 * @param navigation2 String
 */
fun Context.jgTrackProductDetailNewBtnExposure(
        url: String?,
        pageId: String?,
        title: String?,
        btnName: String?,
        btnDesc: String?,
        jgReferrer: String?,
        rank: Int?,
        productId: String?,
        productType: String?,
        operationId: String? = "",
        operationRank: Int? = 1,
        module: String?,
        navigation1 : String?,
        navigation2 : String?,
                                           ) {

    var mProductId = 0L
    try {
        mProductId = productId?.toLong() ?: 0L
    } catch (e: Exception) {
        e.printStackTrace()
    }

    val map = HashMap<String, Any>()
    map[JGTrackManager.FIELD.FIELD_URL] = url ?: ""
    map[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = url ?: ""
    map[JGTrackManager.FIELD.FIELD_REFERRER] = jgReferrer ?: ""
    map[JGTrackManager.FIELD.FIELD_PAGE_ID] = pageId ?: ""
    map[JGTrackManager.FIELD.FIELD_TITLE] = title ?: ""
    map[JGTrackManager.FIELD.FIELD_BTN_NAME] = btnName ?: ""
    map[JGTrackManager.FIELD.FIELD_BTN_DESC] = btnDesc ?: ""
    map[JGTrackManager.FIELD.FIELD_RANK] = rank?: 1
    map[JGTrackManager.FIELD.FIELD_PRODUCT_ID] = mProductId
    map[JGTrackManager.FIELD.FIELD_PRODUCT_TYPE] = productType ?: ""
    if (!operationId.isNullOrEmpty()) {
        map[JGTrackManager.FIELD.FIELD_OPERATION_ID] = operationId
        map[JGTrackManager.FIELD.FIELD_OPERATION_RANK] = operationRank?: 1
    }
    map[JGTrackManager.FIELD.FIELD_MODULE] = module ?: ""
    map[JGTrackManager.FIELD.FIELD_NAVIGATION_1] = navigation1 ?: ""
    map[JGTrackManager.FIELD.FIELD_NAVIGATION_2] = navigation2 ?: ""


    JGTrackManager.eventTrack(this, JGTrackManager.TrackProductDetail.EVENT_BTN_EXPOSURE, map)
}

/**
 * 商品详情页btn按钮点击埋点
 * @receiver Context
 * @param btnName String 控件名字
 * @param jgReferrer String 来源
 */
fun Context.jgTrackProductDetailBtnClick(
    btnName: String?,
    jgReferrer: String?
) {
    val map = HashMap<String, Any>()
    map[JGTrackManager.FIELD.FIELD_URL] = JGTrackManager.TrackProductDetail.URL
    map[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = JGTrackManager.TrackProductDetail.URL
    map[JGTrackManager.FIELD.FIELD_REFERRER] = jgReferrer ?: ""
    map[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackProductDetail.PAGE_ID
    map[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackProductDetail.TITLE
    map[JGTrackManager.FIELD.FIELD_BTN_NAME] = btnName ?: ""

    // 移除极光埋点 - btn_click
    // JGTrackManager.eventTrack(this, JGTrackManager.TrackProductDetail.EVENT_BTN_CLICK, map)
}

/**
 * 注册页btn按钮点击埋点
 * @receiver Context
 * @param btnName String 控件名字
 */
fun Context.jgTrackRegisterBtnClick(
    btnName: String?
) {
    val map = HashMap<String, Any>()
    map[JGTrackManager.FIELD.FIELD_URL] = JGTrackManager.TrackRegister.URL
    map[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = JGTrackManager.TrackRegister.URL
    map[JGTrackManager.FIELD.FIELD_REFERRER] = "com.ybmmarket20.activity.LoginActivity"
    map[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackRegister.PAGE_ID
    map[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackRegister.TITLE
    map[JGTrackManager.FIELD.FIELD_BTN_NAME] = btnName ?: ""

    // 移除极光埋点 - btn_click
    // JGTrackManager.eventTrack(this, JGTrackManager.TrackRegister.EVENT_BTN_CLICK, map)
}

/**
 * 登录页btn按钮点击埋点
 * @receiver Context
 * @param btnName String 控件名字
 */
fun Context.jgTrackLoginBtnClick(
    btnName: String?
) {
    val map = HashMap<String, Any>()
    map[JGTrackManager.FIELD.FIELD_URL] = JGTrackManager.TrackLogin.URL
    map[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = JGTrackManager.TrackLogin.URL
    map[JGTrackManager.FIELD.FIELD_REFERRER] = JGTrackManager.TrackLogin.URL
    map[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackRegister.PAGE_ID
    map[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackRegister.TITLE
    map[JGTrackManager.FIELD.FIELD_BTN_NAME] = btnName ?: ""

    // 移除极光埋点 - btn_click
    // JGTrackManager.eventTrack(this, JGTrackManager.TrackRegister.EVENT_BTN_CLICK, map)
}

/**
 * 我的-资质认证btn按钮点击埋点
 * @receiver Context
 * @param btnName String 控件名字
 */
fun Context.jgTrackAptitudeBtnClick(
    btnName: String?
) {
    val map = HashMap<String, Any>()
    map[JGTrackManager.FIELD.FIELD_URL] = JGTrackManager.TrackAptitude.URL
    map[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = JGTrackManager.TrackAptitude.URL
    map[JGTrackManager.FIELD.FIELD_REFERRER] = JGTrackManager.TrackAptitude.URL
    map[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackAptitude.PAGE_ID
    map[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackAptitude.TITLE
    map[JGTrackManager.FIELD.FIELD_BTN_NAME] = btnName ?: ""
    map[JGTrackManager.FIELD.FIELD_MODULE] = JGTrackManager.TrackAptitude.MODULE

    JGTrackManager.eventTrack(this, JGTrackManager.TrackAptitude.EVENT_BTN_CLICK, map)
}

/**
 * 我的-收藏btn按钮点击埋点
 * @receiver Context
 * @param btnName String 控件名字
 * @param module String
 */
fun Context.jgTrackCollectBtnClick(
    btnName: String?,
    module: String?
) {
    val map = HashMap<String, Any>()
    map[JGTrackManager.FIELD.FIELD_URL] = JGTrackManager.TrackMineCollect.URL
    map[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = JGTrackManager.TrackMineCollect.URL
    map[JGTrackManager.FIELD.FIELD_REFERRER] = JGTrackManager.TrackMineCollect.URL
    map[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackMineCollect.PAGE_ID
    map[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackMineCollect.TITLE
    map[JGTrackManager.FIELD.FIELD_BTN_NAME] = btnName ?: ""
    map[JGTrackManager.FIELD.FIELD_MODULE] = module ?: ""

    JGTrackManager.eventTrack(this, JGTrackManager.TrackMineCollect.EVENT_BTN_CLICK, map)
}

/**
 * 我的-订单列表btn按钮点击埋点
 * @receiver Context
 * @param btnName String 控件名字
 * @param module String
 */
fun Context.jgTrackOrderListBtnClick(
    btnName: String?,
    module: String?
) {
    val map = HashMap<String, Any>()
    map[JGTrackManager.FIELD.FIELD_URL] = JGTrackManager.TrackOrderList.URL
    map[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = JGTrackManager.TrackOrderList.URL
    map[JGTrackManager.FIELD.FIELD_REFERRER] = JGTrackManager.TrackOrderList.URL
    map[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackOrderList.PAGE_ID
    map[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackOrderList.TITLE
    map[JGTrackManager.FIELD.FIELD_BTN_NAME] = btnName ?: ""
    map[JGTrackManager.FIELD.FIELD_MODULE] = module ?: ""

    JGTrackManager.eventTrack(this, JGTrackManager.TrackOrderList.EVENT_BTN_CLICK, map)
}

/**
 * 我的-常够清单btn按钮点击埋点
 * @receiver Context
 * @param btnName String 控件名字
 * @param module String
 */
fun Context.jgTrackOftenBuyBtnClick(
    btnName: String?,
    module: String?
) {
    val map = HashMap<String, Any>()
    map[JGTrackManager.FIELD.FIELD_URL] = JGTrackManager.TrackOftenBuy.URL
    map[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = JGTrackManager.TrackOftenBuy.URL
    map[JGTrackManager.FIELD.FIELD_REFERRER] = JGTrackManager.TrackOftenBuy.URL
    map[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackOftenBuy.PAGE_ID
    map[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackOftenBuy.TITLE
    map[JGTrackManager.FIELD.FIELD_BTN_NAME] = btnName ?: ""
    map[JGTrackManager.FIELD.FIELD_MODULE] = module ?: ""

    JGTrackManager.eventTrack(this, JGTrackManager.TrackOftenBuy.EVENT_BTN_CLICK, map)
}

/**
 * 资质/配送btn按钮点击埋点
 * @receiver Context
 * @param btnName String 控件名字
 * @param module String
 */
fun Context.jgTrackQualificationAndAfterSaleBtnClick(
    btnName: String?
) {
    val map = HashMap<String, Any>()
    map[JGTrackManager.FIELD.FIELD_URL] = JGTrackManager.TrackQualificationAndAfterSale.URL
    map[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = JGTrackManager.TrackQualificationAndAfterSale.URL
    map[JGTrackManager.FIELD.FIELD_REFERRER] = JGTrackManager.TrackQualificationAndAfterSale.URL
    map[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackQualificationAndAfterSale.PAGE_ID
    map[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackQualificationAndAfterSale.TITLE
    map[JGTrackManager.FIELD.FIELD_BTN_NAME] = btnName ?: ""

    JGTrackManager.eventTrack(
        this,
        JGTrackManager.TrackQualificationAndAfterSale.EVENT_BTN_CLICK,
        map
    )
}

/**
 * 优惠券弹出页btn按钮点击埋点
 * @receiver Context
 * @param btnName String 控件名字
 * @param url String
 */
fun Context.jgTrackCartCouponBtnClick(
    btnName: String?,
    url: String?
) {
    val map = HashMap<String, Any>()
    map[JGTrackManager.FIELD.FIELD_URL] = url ?: ""
    map[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = url ?: ""
    map[JGTrackManager.FIELD.FIELD_REFERRER] = url ?: ""
    map[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackCartCoupon.PAGE_ID
    map[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackCartCoupon.TITLE
    map[JGTrackManager.FIELD.FIELD_BTN_NAME] = btnName ?: ""
    map[JGTrackManager.FIELD.FIELD_MODULE] = "功能"

    JGTrackManager.eventTrack(this, JGTrackManager.TrackCartCoupon.EVENT_BTN_CLICK, map)
}

/**
 * 活动页btn按钮点击埋点
 * @receiver Context
 * @param btnName String 控件名字
 * @param referrer String 页面来源
 */
fun Context.jgTrackCommonBtnClick(
    btnName: String?,
    referrer: String?
) {
    val map = HashMap<String, Any>()
    map[JGTrackManager.FIELD.FIELD_URL] = JGTrackManager.TrackCommonH5.URL
    map[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = JGTrackManager.TrackCommonH5.URL
    map[JGTrackManager.FIELD.FIELD_REFERRER] = referrer ?: ""
    map[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackCommonH5.PAGE_ID
    map[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackCommonH5.TITLE
    map[JGTrackManager.FIELD.FIELD_BTN_NAME] = btnName ?: ""
    map[JGTrackManager.FIELD.FIELD_MODULE] = "功能"

    JGTrackManager.eventTrack(this, JGTrackManager.TrackCommonH5.EVENT_BTN_CLICK, map)
}

/**
 * 随心拼btn按钮点击埋点
 * @receiver Context
 * @param btnName String 控件名字
 */
fun Context.jgTrackSpellGroupRecommendSelectedGoodsBtnClick(
    btnName: String?
) {
    val map = HashMap<String, Any>()
    map[JGTrackManager.FIELD.FIELD_URL] = JGTrackManager.TrackSpellGroupRecommendSelectedGoods.URL
    map[JGTrackManager.FIELD.FIELD_URL_DOMAIN] =
        JGTrackManager.TrackSpellGroupRecommendSelectedGoods.URL
    map[JGTrackManager.FIELD.FIELD_REFERRER] =
        JGTrackManager.TrackSpellGroupRecommendSelectedGoods.URL
    map[JGTrackManager.FIELD.FIELD_PAGE_ID] =
        JGTrackManager.TrackSpellGroupRecommendSelectedGoods.PAGE_ID
    map[JGTrackManager.FIELD.FIELD_TITLE] =
        JGTrackManager.TrackSpellGroupRecommendSelectedGoods.TITLE
    map[JGTrackManager.FIELD.FIELD_BTN_NAME] = btnName ?: ""
    map[JGTrackManager.FIELD.FIELD_MODULE] = "功能"

    JGTrackManager.eventTrack(
        this,
        JGTrackManager.TrackSpellGroupRecommendSelectedGoods.EVENT_BTN_CLICK,
        map
    )
}

/**
 * 首页极光btn按钮点击埋点
 * @receiver Context
 * @param module String
 * @param btnName String
 */
fun Context.jgTrackHomeBtnClick(
    url: String?,
    module: String?,
    btnName: String?
) {
    val map = HashMap<String, Any>()
    map[JGTrackManager.FIELD.FIELD_URL] = url ?: ""
    map[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = url ?: ""
    map[JGTrackManager.FIELD.FIELD_REFERRER] =
        "com.ybmmarket20.home.newpage.HomeSteadyLayoutFragmentV3"
    map[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackHomePage.PAGE_ID
    map[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackHomePage.TITLE
    map[JGTrackManager.FIELD.FIELD_MODULE] = module ?: ""
    map[JGTrackManager.FIELD.FIELD_BTN_NAME] = btnName ?: ""

    JGTrackManager.eventTrack(this, JGTrackManager.TrackHomePage.EVENT_BTN_CLICK, map)
}
/**
 * 首页资源位点击
 * @receiver Context
 * @param url String
 * @param module String
 * @param navigation String
 */
fun Context.jgTrackHomeResourceClick(
    url: String?,
    module: String?,
    navigation: String?
) {
    val map = HashMap<String, Any>()
    map[JGTrackManager.FIELD.FIELD_URL] = url ?: ""
    map[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = url ?: ""
    map[JGTrackManager.FIELD.FIELD_REFERRER] =
        "com.ybmmarket20.home.newpage.HomeSteadyLayoutFragmentV3"
    map[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackHomePage.PAGE_ID
    map[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackHomePage.TITLE
    map[JGTrackManager.FIELD.FIELD_MODULE] = module ?: ""
    map[JGTrackManager.FIELD.FIELD_NAVIGATION_1] = navigation ?: ""

    JGTrackManager.eventTrack(this, JGTrackManager.TrackHomePage.EVENT_RESOURCE_CLICK, map)
}
/**
 * 全平台商品点击
 * @receiver Context
 * @param url String
 * @param module String
 * @param referrer String
 * @param pageId String
 * @param title String
 * @param resourceId String
 * @param resourceName String
 * @param resourceType String
 * @param position Int
 * @param productId String
 * @param productName String
 * @param productType String
 * @param productPrice Double
 * @param productLabel String
 * @param entrance String
 * @param navigation String
 */
fun Context.jgTrackResourceProductClick(
    url: String?,
    module: String?,
    referrer: String?,
    pageId: String?,
    title: String?,
    resourceId: String?,
    resourceName: String?,
    resourceType: String?,
    position: Int?,
    productId: String?,
    productName: String?,
    productType: String?,
    productPrice: Double?,
    productLabel: String?,
    entrance: String?,
    navigation: String? = "",
    keyword: String? = "",
) {
    var mProductId = 0L
    try {
        mProductId = productId?.toLong() ?: 0L
    } catch (e: Exception) {
        e.printStackTrace()
    }
    val map = HashMap<String, Any>()
    map[JGTrackManager.FIELD.FIELD_URL] = url ?: ""
    map[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = url ?: ""
    map[JGTrackManager.FIELD.FIELD_REFERRER] = referrer ?: ""
    map[JGTrackManager.FIELD.FIELD_PAGE_ID] = pageId ?: ""
    map[JGTrackManager.FIELD.FIELD_TITLE] = title ?: ""
    map[JGTrackManager.FIELD.FIELD_MODULE] = module ?: ""
    map[JGTrackManager.FIELD.FIELD_NAVIGATION_1] = navigation ?: ""
    map[JGTrackManager.FIELD.FIELD_RESOURCE_ID] = resourceId ?: ""
    map[JGTrackManager.FIELD.FIELD_RESOURCE_NAME] = resourceName ?: ""
    map[JGTrackManager.FIELD.FIELD_RESOURCE_TYPE] = resourceType ?: ""
    map[JGTrackManager.FIELD.FIELD_RANK] = position?.let { it + 1 } ?: 1
    map[JGTrackManager.FIELD.FIELD_PRODUCT_ID] = mProductId ?: ""
    map[JGTrackManager.FIELD.FIELD_PRODUCT_NAME] = productName ?: ""
    map[JGTrackManager.FIELD.FIELD_PRODUCT_TYPE] = productType ?: ""
    map[JGTrackManager.FIELD.FIELD_PRODUCT_PRESENT_PRICE] = productPrice ?: 0
    map[JGTrackManager.FIELD.FIELD_PRODUCT_LABEL] = productLabel ?: ""
    map[JGTrackManager.FIELD.FIELD_ENTRANCE] = entrance ?: ""
    map[JGTrackManager.FIELD.FIELD_KEY_WORD] = keyword ?: ""

    JGTrackManager.eventTrack(this, JGTrackManager.Common.EVENT_RESOURCE_CLICK, map)
}

//搜索页-点击
fun Context.jgTrackSearchResourceClick(
    content: String?,
    position: Int?,
    searchModule: String?,
    referrer: String?,
) {

    val map = HashMap<String, Any>()
    map[JGTrackManager.FIELD.FIELD_URL] = JGTrackManager.TrackSearchIntermediateState.URL
    map[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = JGTrackManager.TrackSearchIntermediateState.URL
    map[JGTrackManager.FIELD.FIELD_REFERRER] = referrer ?: ""
    map[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackSearchIntermediateState.PAGE_ID
    map[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackSearchIntermediateState.TITLE
    map[JGTrackManager.FIELD.FIELD_MODULE] = searchModule ?: ""
    map[JGTrackManager.FIELD.FIELD_RESOURCE_NAME] = content ?: ""
    map[JGTrackManager.FIELD.FIELD_RANK] = position?.let { it + 1 } ?: 1

    JGTrackManager.eventTrack(
        this,
        JGTrackManager.TrackSearchIntermediateState.EVENT_RESOURCE_CLICK,
        map
    )
}

//搜索中间页-点击事件
fun Context.jgTrackSearchBtnClick(
    content: String?,
    searchModule: String?,
    referrer: String?
) {

    val map = HashMap<String, Any>()
    map[JGTrackManager.FIELD.FIELD_URL] = JGTrackManager.TrackSearchIntermediateState.URL
    map[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = JGTrackManager.TrackSearchIntermediateState.URL
    map[JGTrackManager.FIELD.FIELD_REFERRER] = referrer ?: ""
    map[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackSearchIntermediateState.PAGE_ID
    map[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackSearchIntermediateState.TITLE
    map[JGTrackManager.FIELD.FIELD_MODULE] = searchModule ?: ""
    map[JGTrackManager.FIELD.FIELD_BTN_NAME] = content ?: ""
    map[JGTrackManager.FIELD.FIELD_BTN_DESC] = content ?: ""

    JGTrackManager.eventTrack(
        this,
        JGTrackManager.TrackSearchIntermediateState.EVENT_BTN_CLICK,
        map
    )
}


/**
 * 搜索页 商品点击埋点在里面  因为粒度是商品
 * @param searchBean SearchRowsBean
 * @param position Int
 */
fun Context.searchResourceClickJGTrack(
		searchBean: SearchRowsBean?, position: Int?, resourceName: String?, module: String?,
		entrance: String?, operationId: String? = "", operationRank: Int? = 1,
		listPositionType: Int? = 0, listPositionTypeName: String? = "") {

    try {
        val rowsBean = searchBean?.productInfo
        val isOperation = searchBean?.cardType == 2
        val mOperationInfo = searchBean?.operationInfo

        var productType = ""
        var productTag = ""
        productType = rowsBean?.jgProductType ?: ""

        rowsBean?.tags?.productTags?.let { tagList ->
            tagList.forEachIndexed { index, tagBean ->
                if (index != tagList.size - 1) {
                    productTag += tagBean.text + "，"
                } else {
                    productTag += tagBean.text
                }
            }
        }

		val map = HashMap<String, Any>()
		map[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackSearchResult.PAGE_ID
		map[JGTrackManager.FIELD.FIELD_URL] = JGTrackManager.TrackSearchResult.TRACK_URL
		map[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = JGTrackManager.TrackSearchResult.TRACK_URL
		map[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackSearchResult.TITLE
		map[JGTrackManager.FIELD.FIELD_REFERRER] = JGTrackManager.TrackSearchResult.TRACK_URL
		map[JGTrackManager.FIELD.FIELD_MODULE] = module?:""
		map[JGTrackManager.FIELD.FIELD_RESOURCE_NAME] = resourceName?:""
		map[JGTrackManager.FIELD.FIELD_RESOURCE_TYPE] ="内容卡片"
		map[JGTrackManager.FIELD.FIELD_RANK] = position?.let { it+1 }?:1
		map[JGTrackManager.FIELD.FIELD_PRODUCT_ID] = rowsBean?.id?:0
		map[JGTrackManager.FIELD.FIELD_PRODUCT_NAME] = rowsBean?.productName ?: ""
		map[JGTrackManager.FIELD.FIELD_PRODUCT_TYPE] = productType
		map[JGTrackManager.FIELD.FIELD_PRODUCT_PRESENT_PRICE] = rowsBean?.jgProductPrice ?: 0.0
		map[JGTrackManager.FIELD.FIELD_PRODUCT_LABEL] = productTag
		map[JGTrackManager.FIELD.FIELD_SHOP_ID] = if (isOperation) mOperationInfo?.shopCode
				?: "" else ""
		map[JGTrackManager.FIELD.FIELD_SHOP_NAME] = if (isOperation) mOperationInfo?.shopName
				?: "" else ""
		map[JGTrackManager.FIELD.FIELD_KEY_WORD] = searchBean?.keyword
				?: "" //        map[JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID] = rowsBean?.searchSortStrategyCode?:""
		map[JGTrackManager.FIELD.FIELD_ENTRANCE] = entrance ?: ""
		map[JGTrackManager.FIELD.FIELD_LIST_POSITION_TYPE] = listPositionType?.toString() ?: "0"
		map[JGTrackManager.FIELD.FIELD_LIST_POSITION_TYPENAME] = listPositionTypeName ?: ""
		if (!operationId.isNullOrEmpty()) {
			map[JGTrackManager.FIELD.FIELD_OPERATION_ID] = operationId
			map[JGTrackManager.FIELD.FIELD_OPERATION_RANK] = operationRank ?: 1
		}
		JGTrackManager.eventTrack(
				this,
				JGTrackManager.TrackSearchResult.EVENT_RESOURCE_CLICK,
				map)
	}catch (e:Exception){
		e.printStackTrace()
	}

}

/**
 * 加入购物车埋点在里面  因为粒度是商品
 * @param searchBean SearchRowsBean
 * @param position Int
 */
@Deprecated("改用reportAddToCart")
@JvmOverloads
fun Context.addToCartJGTrack(
    rowsBean: RowsBean?,
    jgTrackBean: JgTrackBean?,
    productDetail: ProductDetailBean? = null,
    position: Int? = 0,
    addNumber: String? = "",
) {
    val map = HashMap<String, Any>()
    var productId = 0L
    var productName = ""
    var productPrice = 0.0
    var productNumber = 0
    rowsBean?.let {
        productId = it.id
        productName = it.productName ?: ""
        productPrice = it.fob
    } ?: kotlin.run {
        productDetail?.let {
            productId = it.id.toLong()
            productName = it.showName ?: ""
            productPrice = it.fob
        }
    }
    try {
        productNumber = addNumber?.toInt() ?: 0
    } catch (e: Exception) {
        e.printStackTrace()
    }
    map[JGTrackManager.FIELD.FIELD_PAGE_ID] = jgTrackBean?.pageId ?: ""
    map[JGTrackManager.FIELD.FIELD_TITLE] = jgTrackBean?.title ?: ""
    map[JGTrackManager.FIELD.FIELD_MODULE] = jgTrackBean?.module ?: ""
    map[JGTrackManager.FIELD.FIELD_ADDCARTTIME] = System.currentTimeMillis()
    map[JGTrackManager.FIELD.FIELD_PRODUCT_ID] = productId
    map[JGTrackManager.FIELD.FIELD_PRODUCT_NAME] = productName
    map[JGTrackManager.FIELD.FIELD_PRODUCT_NUMBER] = productNumber
    map[JGTrackManager.FIELD.FIELD_PRODUCT_PRESENT_PRICE] = productPrice
    map[JGTrackManager.FIELD.FIELD_ENTRANCE] = jgTrackBean?.entrance ?: ""
    map[JGTrackManager.FIELD.FIELD_RANK] = position?.let { it + 1 } ?: 1
    map[JGTrackManager.FIELD.FIELD_OPERATE_TYPE] = "点击加购按钮"
    map[JGTrackManager.FIELD.FIELD_ACTIVITY_ENTRANCE] = jgTrackBean?.activityEntrance ?: ""

    JGTrackManager.eventTrack(this, JGTrackManager.Common.EVENT_ADD_TO_CART, map)

}


fun reportAddToCart(addToCart:AddToCart?){
    addToCart?:return
    //购物车加购 不上报
    if (addToCart.url == JGTrackManager.TrackShoppingCart.TRACK_URL) return
    ReportManager.getInstance().report(addToCart)
}

/**
 * 用户点击领取，获取优惠券时上报；
 * 领券中心、店铺主页、购物车、商详页、活动页
 * @receiver Context
 */
fun Context.jgGetCouponsTrack(
    jgTrackBean: JgTrackBean?, couponRowBean: CouponRowBean?
) {

    val couponPrice: String = couponRowBean?.let {
        if (it.discount > 0) {
            "${it.moneyInVoucher}折"
        } else {
            "¥${it.moneyInVoucher}"
        }
    } ?: ""


    jgGetCouponsTrack(
        jgTrackBean,
        couponRowBean?.voucherTypeDesc ?: "",
        couponPrice,
        couponRowBean?.validDateToString ?: "",
        couponRowBean?.expireDateToString ?: ""
    )
}

/**
 * 用户点击领取，获取优惠券时上报；
 * 领券中心、店铺主页、购物车、商详页、活动页
 * @receiver Context
 */
fun Context.jgGetCouponsTrack(
    jgTrackBean: JgTrackBean?, couponType: String?, couponPrice: String?, useStartTime: String?,
    useEndTime: String?
) {
    val map = HashMap<String, Any>()

    map[JGTrackManager.FIELD.FIELD_PAGE_ID] = jgTrackBean?.pageId ?: ""
    map[JGTrackManager.FIELD.FIELD_TITLE] = jgTrackBean?.title ?: ""
    map[JGTrackManager.FIELD.FIELD_URL] = jgTrackBean?.url ?: ""
    map[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = jgTrackBean?.url ?: ""
    map[JGTrackManager.FIELD.FIELD_REFERRER] = jgTrackBean?.jgReferrer ?: ""
    map[JGTrackManager.FIELD.FIELD_COUPON_TYPE] = couponType ?: ""
    map[JGTrackManager.FIELD.FIELD_COUPON_PRICE] = couponPrice ?: ""
    map[JGTrackManager.FIELD.FIELD_USE_START_TIME] = useStartTime ?: ""
    map[JGTrackManager.FIELD.FIELD_USE_END_TIME] = useEndTime ?: ""

    JGTrackManager.eventTrack(
        this,
        JGTrackManager.Common.EVENT_COUPONS_GET,
        map
    )
}

/**
 * 店铺主页资源位点击
 * @receiver Context
 * @param jgTrackBean JgTrackBean?
 * @param activityId String?
 * @param activityName String?
 * @param shopId String?
 * @param shopName String?
 * @param locationUrl String?
 */
fun Context.jgShopMainResourceClickTrack(
    jgTrackBean: JgTrackBean?,
    activityId: String? = "",
    activityName: String? = "",
    shopId: String? = "",
    shopName: String? = "",
    locationUrl: String? = ""
) {
    val map = HashMap<String, Any>()

    map[JGTrackManager.FIELD.FIELD_PAGE_ID] = jgTrackBean?.pageId ?: ""
    map[JGTrackManager.FIELD.FIELD_TITLE] = jgTrackBean?.title ?: ""
    map[JGTrackManager.FIELD.FIELD_URL] = jgTrackBean?.url ?: ""
    map[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = jgTrackBean?.url ?: ""
    map[JGTrackManager.FIELD.FIELD_MODULE] = jgTrackBean?.module ?: ""
    map[JGTrackManager.FIELD.FIELD_ACTIVITY_ID] = activityId ?: ""
    map[JGTrackManager.FIELD.FIELD_ACTIVITY_NAME] = activityName ?: ""
    map[JGTrackManager.FIELD.FIELD_SHOP_ID] = shopId ?: ""
    map[JGTrackManager.FIELD.FIELD_SHOP_NAME] = shopName ?: ""
    map[JGTrackManager.FIELD.FIELD_LOCATION_URL] = locationUrl ?: ""

    JGTrackManager.eventTrack(
        this,
        JGTrackManager.Common.EVENT_RESOURCE_CLICK,
        map
    )
}

fun Context.jgReport(bean: Any) {
    ReportManager.getInstance().report(bean)
}


/**
 * 给Url拼接参数
 * @param url String
 * @param params Map<String, Any>
 * @return String
 */
fun splicingUrlWithParams(url: String?, params: Map<String, Any>? = mapOf()): String {
    try {
        var mUrl = url?.ifEmpty { return "" } ?: return ""

        if (!params.isNullOrEmpty()) {
            if (!mUrl.trim().contains('?')) {
                mUrl += "?"
            }            //路由跳转h5时,如果链接会带参h5url=xxxxx.html  此时需拼接到H5链接里面 但是已经有问号 所有这里需要再拼接 不然有问题
            if (mUrl.trim().endsWith("html")) {
                mUrl += "?"
            }
            params.forEach { entry ->
                if (mUrl.endsWith('?')) {
                    mUrl += "${entry.key}=${entry.value}"
                } else {
                    mUrl += "&${entry.key}=${entry.value}"
                }
            }

            if (mUrl.endsWith('?')) {
                mUrl += "distinctId=${JGTrackManager.getDistinctId()}"
            } else {
                mUrl += "&distinctId=${JGTrackManager.getDistinctId()}"
            }
        }
        return mUrl
    } catch (e: Exception) {
        e.printStackTrace()
        return url ?: ""
    }
}

// entrance来源拼接页面标题
fun splicingPageTitle2Entrance(
    entrance: String?,
    pageTitle: String?
): String {
    var result: String? = ""
    result = if (entrance.isNullOrEmpty()) {
        pageTitle
    } else {
        if (!pageTitle.isNullOrEmpty()) {
            "$entrance-${pageTitle}"
        } else {
            entrance
        }
    }
    return result ?: ""
}

//entrance来源拼接页面模块
fun splicingModule2Entrance(
    entrance: String?,
    module: String?
): String {
    var result = ""
    result = if (!entrance.isNullOrEmpty()) {
        if (!module.isNullOrEmpty()) {
            "$entrance(${module})"
        } else {
            entrance
        }
    } else ""
    return result
}