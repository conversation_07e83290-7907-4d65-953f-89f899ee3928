package com.ybmmarket20.home.newpage.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ybmmarket20.bean.homesteady.ComponentBean
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.TrackManager
import com.ybmmarket20.common.glideLoadWithPlaceHolder
import com.ybmmarket20.common.jgTrackResourceProductClick
import com.ybmmarket20.databinding.ItemBannerFeedStreamBinding
import com.ybmmarket20.home.newpage.bean.HomeFeedStreamBean
import com.youth.banner.adapter.BannerAdapter

/**
 * @class   FeedStreamBannerAdapter
 * <AUTHOR>
 * @date  2024/4/16
 * @description
 */
class FeedStreamBannerAdapter(val mDataList:MutableList<HomeFeedStreamBean.HomeFeedBanner>) : BannerAdapter<HomeFeedStreamBean.HomeFeedBanner, FeedStreamBannerAdapter.FeedStreamBannerVH>(mDataList) {

	var onItemClickListener: ((position:Int,url:String)->Unit)? = null
	var pageId = 0
	var navigation = ""
	var bannerComponent: ComponentBean? = null

	private val resourceViewTrackMap = hashMapOf<String, Long>()
	companion object {
		private const val TRACK_DURATION = 2 * 60 * 1000 //2分钟内不上报
	}
	override fun onCreateHolder(
			parent: ViewGroup,
			viewType: Int): FeedStreamBannerVH {

		return FeedStreamBannerVH(ItemBannerFeedStreamBinding.inflate(LayoutInflater.from(parent.context), parent, false))
	}

	override fun onBindView(
			holder: FeedStreamBannerVH,
			data: HomeFeedStreamBean.HomeFeedBanner,
			position: Int,
			size: Int) {

		holder.mBinding.apply {

			root.context.glideLoadWithPlaceHolder(data.bannerImgs?:"",ivAd, isCenterCrop = true)
			root.setOnClickListener {
                onItemClickListener?.invoke(position,data.hrefUrls?: "")
				jgBannerClick(holder, data)
            }

			val map = HashMap<String,Any>().apply {
				put(TrackManager.FIELD_PAGE_ID,pageId)
				put(TrackManager.FIELD_OFFSET,holder.bindingAdapterPosition+1)
			}
			TrackManager.exposureEventTrack(TrackManager.TrackHome.EVENT_CAROUSEL_FEED_EXPOSURE,map)
        }

		if (!data.bannerImgs.isNullOrEmpty()){
			resourceViewTrackMap[data.bannerImgs]?.let {
				if (System.currentTimeMillis() - it > TRACK_DURATION){
					resourceViewTrackMap[data.bannerImgs] = System.currentTimeMillis()
				}
			}?: kotlin.run {
				resourceViewTrackMap[data.bannerImgs] = System.currentTimeMillis()
			}
		}

	}

	private fun jgBannerClick(holder: FeedStreamBannerVH,data: HomeFeedStreamBean.HomeFeedBanner){
		// holder.mBinding.root.context.jgTrackResourceProductClick(
		//		url = JGTrackManager.TrackHomePage.TRACK_HOME_INNER_URL,
		//		module = "首页商品列表banner",
		//		referrer = JGTrackManager.TrackHomePage.TRACK_HOME_INNER_URL,
		//		pageId = JGTrackManager.TrackHomePage.PAGE_ID,
		//		title = JGTrackManager.TrackHomePage.TITLE,
		//		resourceId = data.activityId?:"",
		//		resourceName = data.activityName?:"",
		//		resourceType ="广告轮播",
		//		position = holder.bindingAdapterPosition,
		//		productId = "",
		//		productName = "",
		//		productType = "",
		//		productPrice = 0.0,
		//		productLabel = "",
		//		entrance ="首页(商品列表)",
		//		navigation = navigation) // 极光埋点移除：移除首页Feed流Banner resource_click事件
	}

	class FeedStreamBannerVH(val mBinding: ItemBannerFeedStreamBinding) : RecyclerView.ViewHolder(mBinding.root)


}

