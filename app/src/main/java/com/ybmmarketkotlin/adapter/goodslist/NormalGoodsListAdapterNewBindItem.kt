package com.ybmmarketkotlin.adapter.goodslist

import android.content.Context
import android.os.CountDownTimer
import android.util.SparseArray
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean
import com.ybmmarketkotlin.utils.TextWithPrefixTag

open class NormalGoodsListAdapterNewBindItem(
    mContext: Context,
    baseViewHolder: YBMBaseHolder,
    rowsBean: RowsBean,
    countDownTimerMap: SparseArray<CountDownTimer>,
    isAddCartShowPopupWindow: Boolean,
    isHiddenPromotionMore: Boolean = false
): AbstractGoodsListAdapterNewBindItem(mContext, baseViewHolder, rowsBean, countDownTimerMap, isAddCartShowPopupWindow, isHiddenPromotionMore) {

    override fun onGoodsNameWithTags() {
        val goodsName = baseViewHolder.getView<TextView>(R.id.shop_name)
        goodsName.TextWithPrefixTag(rowsBean.tags?.titleTags, "${rowsBean.productName}/${rowsBean.spec}")
        goodsName.setLineSpacing(0f, 1.1f)
        goodsName.maxLines = 2
    }

    override fun onSpellGroupPreHot() {
        super.onSpellGroupPreHot()
        hideGroupBookingView(baseViewHolder)
    }

    private fun hideGroupBookingView(baseViewHolder: YBMBaseHolder) {
        baseViewHolder.getView<TextView>(R.id.tv_countdown)?.visibility = View.GONE
        baseViewHolder.getView<ConstraintLayout>(R.id.cl_groupbooking)?.visibility = View.GONE
    }

    override fun onGoodsSpec() {
        super.onGoodsSpec()
        baseViewHolder.setGone(R.id.tv_goods_spec, false)
        baseViewHolder.setGone(R.id.iv_divider_of_spec_name, false)
    }

    override fun onRetailPrice() {
        // 控销价或零售价 毛利
        val tvRetailPrice: TextView = baseViewHolder.getView(R.id.tv_retail_price)
        tvRetailPrice.visibility = View.GONE

        // 处理单价显示
        handleUnitPrice()
    }
}