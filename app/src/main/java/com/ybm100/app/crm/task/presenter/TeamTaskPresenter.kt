package com.ybm100.app.crm.task.presenter

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer
import com.ybm100.app.crm.task.bean.TeamTaskBean
import com.ybm100.app.crm.task.contract.TeamTaskContract
import com.ybm100.app.crm.task.model.TeamTaskModel

class TeamTaskPresenter : BasePresenter<TeamTaskContract.ITeamTaskModel, TeamTaskContract.ITeamTaskView>() {
    var mQueryMap: MutableMap<String, String> = mutableMapOf()
    private var mOffset:Int = 0

    init {
        initQueryMap()
    }

    private fun initQueryMap() {
        mOffset = 0
        mQueryMap["offset"] = "$mOffset"
        mQueryMap["limit"] = "20"
    }

    override fun getModel(): TeamTaskContract.ITeamTaskModel {
        return TeamTaskModel()
    }

    fun getTeamTask(isRefresh: Boolean) {
        if (isRefresh){
            mOffset = 0
            mQueryMap["offset"] = "$mOffset"
        }
        mRxManager.register(mIModel.getTeamTask(mQueryMap)
                .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<TeamTaskBean?>?>(mIView, "") {
                    override fun onSuccess(requestBaseBean: RequestBaseBean<TeamTaskBean?>?) {
                        mQueryMap["offset"] = "${++mOffset}"
                        mIView.onGetTeamTaskSuccess(requestBaseBean, isRefresh, requestBaseBean?.data?.lastPage ?: false)
                    }

                }, SimpleErrorConsumer(mIView)))
    }
}