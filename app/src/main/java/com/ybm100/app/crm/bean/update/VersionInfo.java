package com.ybm100.app.crm.bean.update;

import java.io.Serializable;

/**
 * 版本更新信息
 */
public class VersionInfo implements Serializable {

    /**
     * 更新内容
     */
    private String updateContentBody;
    /**
     * 更新内容标题
     */
    private String updateContentHeader;
    /**
     * 更新说明2.0.0 | 2.0.2 备注
     */
    private String remark;
    /**
     * 下载地址
     */
    private String appDownloadUrl;
    /**
     * 是否强制更新
     */
    private boolean isForceUpdate;
    /**
     * 是否需要更新
     */
    private boolean upgrade;
    /**
     * apk文件的MD5值
     */
    private String md5;
    /**
     * 版本号
     */
    private String appVersion;

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public String getUpdateContentBody() {
        return updateContentBody;
    }

    public void setUpdateContentBody(String updateContentBody) {
        this.updateContentBody = updateContentBody;
    }

    public String getUpdateContentHeader() {
        return updateContentHeader;
    }

    public void setUpdateContentHeader(String updateContentHeader) {
        this.updateContentHeader = updateContentHeader;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getAppDownloadUrl() {
        return appDownloadUrl;
    }

    public void setAppDownloadUrl(String appDownloadUrl) {
        this.appDownloadUrl = appDownloadUrl;
    }

    public boolean isForceUpdate() {
        return isForceUpdate;
    }

    public void setForceUpdate(boolean forceUpdate) {
        isForceUpdate = forceUpdate;
    }

    public boolean isUpgrade() {
        return upgrade;
    }

    public void setUpgrade(boolean upgrade) {
        this.upgrade = upgrade;
    }
}