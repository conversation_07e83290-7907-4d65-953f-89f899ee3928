package com.ybm100.app.crm.task.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.ybm100.app.crm.R
import com.ybm100.app.crm.task.bean.SelectCustomersBean


class SelectCustomersAdapter : BaseQuickAdapter<SelectCustomersBean.Row, BaseViewHolder>(R.layout.item_select_customers) {
    override fun convert(helper: BaseViewHolder, item: SelectCustomersBean.Row?) {
        item?.run {
            helper.setText(R.id.tv_name, "${name ?: ""}")
                    .setText(R.id.tv_address, "${address ?: ""}")
            if (isSelected == true) {
                helper.setImageResource(R.id.iv_radio, R.drawable.icon_radio_btn_selected)
            } else {
                helper.setImageResource(R.id.iv_radio, R.drawable.icon_radio_btn_normal)
            }
        }
    }

}