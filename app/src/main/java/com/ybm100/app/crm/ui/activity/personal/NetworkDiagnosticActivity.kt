package com.ybm100.app.crm.ui.activity.personal

import android.os.Bundle
import android.view.View
import com.xyy.common.navigationbar.AbsNavigationBar
import com.xyy.common.navigationbar.DefaultNavigationBar
import com.xyy.common.util.ToastUtils
import com.xyy.networkdiagnosticlib.task.TraceTask
import com.xyy.utilslibrary.base.activity.BaseCompatActivity
import com.xyy.utilslibrary.utils.AppUtils
import com.ybm100.app.crm.R
import kotlinx.android.synthetic.main.activity_network_diagnostic.*

class NetworkDiagnosticActivity : BaseCompatActivity(), View.OnClickListener {
    override fun getLayoutId(): Int = R.layout.activity_network_diagnostic

    override fun initHead(): AbsNavigationBar<*> {
        val bar = DefaultNavigationBar.Builder(this)
                .setTitle("网络诊断")
                .builder()
        bar.rightImgView.visibility = View.GONE
        return bar
    }

    override fun initView(savedInstanceState: Bundle?) {
        btn_start_diagnostic.setOnClickListener(this)
        btn_api_url_diagnostic.setOnClickListener(this)
        btn_downloaded_url_diagnostic.setOnClickListener(this)
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.btn_start_diagnostic -> {
                if (et_url?.text.isNullOrEmpty()){
                    ToastUtils.showShort("请输入网络地址")
                    return
                }
                startDiagnostic(et_url?.text?.toString())
            }
            R.id.btn_api_url_diagnostic -> {
                et_url.setText("crm-app.ybm100.com")
            }
            R.id.btn_downloaded_url_diagnostic -> {
                et_url.setText("downloads.ybm100.com")
            }
        }
    }

    private fun startDiagnostic(url: String? = "www.ybm100.com") {
        try {
            TraceTask(this, url, tv_result, AppUtils.getAppName(), AppUtils.getAppVersionName(this), AppUtils.getAppVersionCode(this).toString()).doTask()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


}
