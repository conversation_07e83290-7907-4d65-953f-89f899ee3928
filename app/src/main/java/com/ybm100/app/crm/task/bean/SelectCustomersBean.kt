package com.ybm100.app.crm.task.bean

import android.os.Parcel
import android.os.Parcelable

data class SelectCustomersBean(
        val currentPage: Int? = 0, // 15186
        val lastPage: Boolean? = false, // true
        val rows: List<Row?>? = listOf(),
        val total: Int? = 0 // 67105
) {
    data class Row(
            val address: String? = "", // 测试内容b6o8
            val id: String? = "", // 75083
            val name: String? = "", // 测试内容r0y3
            var isSelected: Boolean? = false
    ) : Parcelable {
        constructor(parcel: Parcel) : this(
                parcel.readString(),
                parcel.readString(),
                parcel.readString(),
                parcel.readValue(Boolean::class.java.classLoader) as? Boolean)

        override fun writeToParcel(parcel: Parcel, flags: Int) {
            parcel.writeString(address)
            parcel.writeString(id)
            parcel.writeString(name)
            parcel.writeValue(isSelected)
        }

        override fun describeContents(): Int {
            return 0
        }

        companion object CREATOR : Parcelable.Creator<Row> {
            override fun createFromParcel(parcel: Parcel): Row {
                return Row(parcel)
            }

            override fun newArray(size: Int): Array<Row?> {
                return arrayOfNulls(size)
            }
        }
    }
}