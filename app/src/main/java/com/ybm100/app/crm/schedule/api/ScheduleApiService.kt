package com.ybm100.app.crm.schedule.api

import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.schedule.model.ContactJobEnum
import io.reactivex.Observable
import retrofit2.http.*
import java.util.*

interface ScheduleApiService {

    /**
     * 创建待完善拜访
     */
    @FormUrlEncoded
    @POST("visit/doCallAddVisit")
    fun createNoCompletedSchedule(@FieldMap paramsMap: HashMap<String, Any>): Observable<RequestBaseBean<String?>?>

    /**
     * 获取联系人岗位枚举
     */
    @GET("contact/getContactJobs")
    fun getContactJobEnum(): Observable<RequestBaseBean<List<ContactJobEnum>?>?>


    /**
     * 新建拜访-选择拜访对象-判断是否有待完成的任务
     */
    @GET("task/v2/isHaveTask")
    fun isHaveTask(@Query("customerId") customerId: String?): Observable<RequestBaseBean<Int>?>


}