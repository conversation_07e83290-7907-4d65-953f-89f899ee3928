package com.ybm100.app.crm.widget.popwindow;

import android.content.Context;
import android.view.Gravity;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.goods.PopListBean;
import com.ybm100.app.crm.constant.Constants;
import com.ybm100.app.crm.ui.adapter.goods.GoodsCommendPopListAdapter;

import java.util.ArrayList;

import razerdp.basepopup.BasePopupWindow;

/**
 * @author: zcj
 * @time:2019/11/8. Description:
 */
public class GoodsRecommendPopup extends BasePopupWindow {
    private ArrayList<PopListBean> popList;
    private final int mCheckMode;
    private StringBuilder sb;
    private boolean useCode ;

    public GoodsRecommendPopup(Context context, ArrayList<PopListBean> popList, int checkMode, boolean useCode, int height) {
        super(context);
        this.popList = popList;
        this.mCheckMode = checkMode;
        this.useCode = useCode;
        sb = new StringBuilder();
        initRecyclerView();
        initOperation();
        setOutSideTouchable(true);
        setPopupGravity(Gravity.BOTTOM);
        setAlignBackgroundGravity(Gravity.TOP);
        setAlignBackground(true);//背景是否对齐到PopupWindow
        setOutSideDismiss(true);// 点击popupwindow背景部分不隐藏
        setMaxHeight(height);

    }

    public GoodsRecommendPopup(Context context, ArrayList<PopListBean> popList, int checkMode, int height) {
        super(context);
        this.popList = popList;
        this.mCheckMode = checkMode;
        sb = new StringBuilder();
        initRecyclerView();
        initOperation();
        setOutSideTouchable(true);
        setPopupGravity(Gravity.BOTTOM);
        setAlignBackgroundGravity(Gravity.TOP);
        setAlignBackground(true);//背景是否对齐到PopupWindow
        setOutSideDismiss(true);// 点击popupwindow背景部分不隐藏
        setMaxHeight(height);

    }

    public void setPopList(ArrayList<PopListBean> popList) {
        this.popList = popList;
        sb = new StringBuilder();
        initRecyclerView();
    }

    public ArrayList<PopListBean> getPopList() {
        return popList;
    }


    private void initRecyclerView() {
        RecyclerView rvPopDate = findViewById(R.id.rv_pop_goods_recommend);
        rvPopDate.setLayoutManager(new LinearLayoutManager(getContext()));
        GoodsCommendPopListAdapter popListAdapter = new GoodsCommendPopListAdapter(R.layout.item_pop_goods_recommend, popList, mCheckMode);
        popListAdapter.setOnItemClickListener((adapter, view, position) -> {
            ((GoodsCommendPopListAdapter) adapter).selectItem(position);
        });
        rvPopDate.setAdapter(popListAdapter);
    }

    private void initOperation() {
        //重置
        findViewById(R.id.tv_reset).setOnClickListener(v -> {
            if (clickListener != null) {
                if (mCheckMode == Constants.GoodsRecommendCheck.FLOW_TAG_CHECKED_MULTI_DEFAULT) {
                    clickListener.onItemClick(popList, false, "1");
                } else {
                    clickListener.onItemClick(popList, false, "");
                }
            }
            dismiss();
        });
        //确认
        findViewById(R.id.tv_confirm).setOnClickListener(v -> {
            if (clickListener != null) {
                boolean selectAll = useCode ? getSelectByCode() : getSelect();
                String select = sb.toString();
                clickListener.onItemClick(popList, selectAll, select.length() > 0 ? select.substring(0, select.length() - 1) : select);
            }
            dismiss();
        });
    }

    private boolean getSelect() {
        int a = 0;
        for (int i = 0; i < popList.size(); i++) {
            if (popList.get(i).isSelected()) {
                a++;
                sb.append(i + 1);
                sb.append(",");
            }
        }
        return a == popList.size();
    }

    private boolean getSelectByCode() {
        int a = 0;
        for (int i = 0; i < popList.size(); i++) {
            if (popList.get(i).isSelected()) {
                a++;
                sb.append(popList.get(i).getCode());
                sb.append(",");
            }
        }
        return a == popList.size();
    }

    @Override
    public View onCreateContentView() {
        return createPopupById(R.layout.list_popup_goods_recommend);
    }

    private OnPopItemClickListener clickListener;

    public void setOnPopItemClickListener(OnPopItemClickListener clickListener) {
        this.clickListener = clickListener;
    }

    public interface OnPopItemClickListener {
        void onItemClick(ArrayList<PopListBean> selectList, boolean selectAll, String selectText);
    }

    public static ArrayList<PopListBean> getPopListBean(Context context, int resId, int defaultIndex) {
        String[] strings = context.getResources().getStringArray(resId);
        ArrayList<PopListBean> result = new ArrayList<PopListBean>();
        for (int i = 0; i < strings.length; i++) {
            PopListBean dateBean = new PopListBean();
            dateBean.setContent(strings[i]);
            dateBean.setSelected(i == defaultIndex);
            result.add(dateBean);
        }
        return result;
    }

    public static ArrayList<PopListBean> reset(int defaultIndex, ArrayList<PopListBean> popList) {
        ArrayList<PopListBean> result = new ArrayList<PopListBean>();
        for (int i = 0; i < popList.size(); i++) {
            PopListBean dateBean = popList.get(i);
            dateBean.setSelected(i == defaultIndex);
            result.add(dateBean);
        }
        return result;
    }
}
