package com.ybm100.app.crm.widget.webview;

import android.content.Context;
import android.graphics.Color;
import android.net.Uri;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import com.xyy.utilslibrary.utils.LogUtils;
import com.ybm100.app.crm.utils.router.RoutersUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @file YbmWebView.java
 * @brief
 * @date 2018/12/28
 * Copyright (c) 2018, 北京小药药
 * All rights reserved.
 */
public class YbmWebView extends WebView {
    private WebViewProgressBar progressBar;
    private final WebView wbH5;
    private boolean isCache = false;
    public static final String CACHE_NO = "0";//不使用
    public static final String CACHE_NATIVE = "1";//使用native 保存文件
    public static final String CACHE_WEBVIEW = "2";//使用webview 自己的缓存文件
    public static final String CACHE_NET = "3";//当有网络时不使用缓存
    private WebViewListener webViewListener;
    protected Hybrid hybrid;

    public YbmWebView(Context context) {
        this(context, null);
    }

    public YbmWebView(Context context, AttributeSet attrs) {
        super(context, attrs);
        int style = -1;
        if (attrs != null) {
            style = attrs.getAttributeIntValue("http://schemas.android.com/apk/res/android", "style", -1);
        }
        if (style != 100) {
            progressBar = new WebViewProgressBar(context);
            progressBar.setLayoutParams(new ViewGroup.LayoutParams
                    (ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
            progressBar.setVisibility(INVISIBLE);
            addView(progressBar);
        }
        wbH5 = this;
        initWebView();
    }

    private void initWebView() {
        wbH5.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onReceivedTitle(WebView webView, String s) {
                super.onReceivedTitle(webView, s);
                if (webViewListener != null) {
                    webViewListener.onReceivedTitle(webView, s);
                }
            }

            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                if (progressBar == null) {
                    return;
                }
                if (newProgress == 100) {
                    progressBar.setProgress(100);
                    if (progressBar == null) {
                        return;
                    }
                    progressBar.setVisibility(View.INVISIBLE);
                } else if (progressBar.getVisibility() == View.INVISIBLE) {
                    progressBar.setVisibility(View.VISIBLE);
                }
                if (newProgress < 5) {
                    newProgress = 5;
                }
                progressBar.setProgress(newProgress);
                super.onProgressChanged(view, newProgress);
            }

        });
        wbH5.setWebViewClient(new WebViewClient() {


            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                if (webViewListener != null) {
                    webViewListener.onReceivedError(view, errorCode, description, failingUrl);
                }
            }


            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                LogUtils.d("shouldOverrideUrlLoading:" + url);
                if (url == null) {
                    return false;
                }
                if (RoutersUtils.appScheme(url)) {
                    RoutersUtils.open(url);
                    return true;
                } else if (url.startsWith("http") || url.startsWith("Http")) {
                    return super.shouldOverrideUrlLoading(view, url);
                } else if (url.startsWith("tel:") || url.startsWith("sms:") || url.startsWith("qq:")) {//sms tel qq
                    if (url.startsWith("tel:")) {
                        RoutersUtils.open("crmaction://tel?num=" + url.substring(4) + "&show=1");
                        return true;
                    } else if (url.startsWith("sms:")) {
                        RoutersUtils.open("crmaction://sms?num=" + url.substring(4));
                        return true;
                    } else if (url.startsWith("qq:")) {
                        RoutersUtils.open("crmaction://qq?num=" + url.substring(4));
                        return true;
                    } else {
                        return super.shouldOverrideUrlLoading(view, url);
                    }
                } else {
                    return super.shouldOverrideUrlLoading(view, url);
                }

            }

            //资源重定向
            @Override
            public WebResourceResponse shouldInterceptRequest(WebView view, WebResourceRequest request) {
                return super.shouldInterceptRequest(view, request);
            }

            @Override
            public WebResourceResponse shouldInterceptRequest(WebView view, String url) {
     /*           if (isCache) {
                    return UrlCacheManager.getInstance().load(url);
                } else {
                    return super.shouldInterceptRequest(view, url);
                }*/
                return super.shouldInterceptRequest(view, url);
            }
        });
        wbH5.setHorizontalScrollbarOverlay(false);
        wbH5.setScrollBarStyle(View.SCROLLBARS_OUTSIDE_OVERLAY);
        wbH5.setVerticalScrollbarOverlay(false);
        wbH5.setHorizontalScrollBarEnabled(false);
        wbH5.setScrollbarFadingEnabled(false);
        WebSettings webSetting = wbH5.getSettings();
        webSetting.setAllowFileAccess(true);
        webSetting.setSupportZoom(false);
        webSetting.setBuiltInZoomControls(false);
        webSetting.setUseWideViewPort(true);
        webSetting.setSupportMultipleWindows(false);
        webSetting.setLoadWithOverviewMode(true);
        webSetting.setAppCacheEnabled(true);
        webSetting.setDatabaseEnabled(true);
        webSetting.setDomStorageEnabled(true);
        webSetting.setJavaScriptEnabled(true);
        webSetting.setGeolocationEnabled(true);
        webSetting.setAppCacheMaxSize(500 * 1024 * 1024);
        hybrid = new Hybrid(this);
        wbH5.addJavascriptInterface(hybrid, "hybrid");
    }

    public void setCacheMode(String cache) {
        WebSettings webSetting = wbH5.getSettings();
        if (TextUtils.isEmpty(cache) || cache.equals("null") || cache.equals("NULL")) {
            cache = CACHE_NO;
        }
        switch (cache) {
            case CACHE_NATIVE:
                isCache = true;
                webSetting.setCacheMode(WebSettings.LOAD_CACHE_ELSE_NETWORK);
                break;
            case CACHE_NET:
                isCache = false;
                webSetting.setCacheMode(WebSettings.LOAD_CACHE_ELSE_NETWORK);
                break;
            case CACHE_NO:
                isCache = false;
                webSetting.setCacheMode(WebSettings.LOAD_NO_CACHE);
                break;
            case CACHE_WEBVIEW:
                isCache = false;
                webSetting.setCacheMode(WebSettings.LOAD_DEFAULT);
                break;
            default:
                isCache = false;
                webSetting.setCacheMode(WebSettings.LOAD_CACHE_ELSE_NETWORK);
                break;
        }
    }

    //设置监听
    public void setWebViewListener(WebViewListener webViewListener) {
        this.webViewListener = webViewListener;
    }


    public WebViewListener getWebViewListener() {
        return webViewListener;
    }

    public WebView getWbH5() {
        return wbH5;
    }

    public interface WebViewListener {
        void onReceivedTitle(WebView webView, String s);

        void setStatusColor(WebView webView, int color);

        void onReceivedError(WebView view, int errorCode, String description, String failingUrl);

        void onScrollChanged(int x, int y, int oldx, int oldy);
    }

    protected int getStrColor(String color) {
        try {
            return Color.parseColor(color);
        } catch (Exception e) {
            return 0;
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (wbH5 != null && wbH5.getVisibility() == View.VISIBLE && wbH5.canGoBack()) {
                wbH5.goBack();
                return true;
            }
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void loadUrl(String url) {
        super.loadUrl(url);
        if (url != null && (url.endsWith("html") || url.endsWith("htm"))) {
        } else {
            loadUrlAddMerchant(url);
        }
    }

    public void loadUrlAddMerchant(String url) {
        if (url != null && hybrid != null) {
            Uri uri = Uri.parse(url);
            if (uri == null || TextUtils.isEmpty(RoutersUtils.getParameter(uri, "merchantId"))) {
                url += "&merchantId=" + hybrid.getMerchantId();
            }
        }
        super.loadUrl(url);
    }

    @Override
    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
        super.onScrollChanged(l, t, oldl, oldt);
        if (webViewListener != null) {
            webViewListener.onScrollChanged(l, t, oldl, oldl);
        }
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        return false;
    }
}