package com.ybm100.app.crm.home.sales.util;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.RecyclerView;

import com.xyy.common.util.ConvertUtils;
import com.ybm100.app.crm.home.sales.adapter.SalesDataConfigAdapter;
import com.ybm100.app.crm.home.sales.bean.ConfigAdapterModel;
import com.ybm100.app.crm.home.sales.bean.ItemType;

public class ConfigItemTouchCallback extends ItemTouchHelper.Callback {

    private final SalesDataConfigAdapter adapter;

    public ConfigItemTouchCallback(SalesDataConfigAdapter adapter) {
        this.adapter = adapter;
    }

    @Override
    public int getMovementFlags(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder) {
        int position = viewHolder.getAdapterPosition();
        ConfigAdapterModel item = adapter.getItem(position);
        boolean canDrop = item != null &&
                item.getItemType() == ItemType.DATA_VALUE
                && item.isMyData();
        int dragFlags = canDrop ? ItemTouchHelper.UP | ItemTouchHelper.DOWN | ItemTouchHelper.LEFT | ItemTouchHelper.RIGHT : 0;
        return makeMovementFlags(dragFlags, 0);
    }

    @Override
    public boolean onMove(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder, @NonNull RecyclerView.ViewHolder target) {
        // 交换在数据源中相应数据源的位置
        return adapter.onItemMove(viewHolder.getAdapterPosition(), target.getAdapterPosition());
    }

    @Override
    public int getBoundingBoxMargin() {
        return ConvertUtils.dp2px(5f);
    }

    @Override
    public void clearView(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder) {
        super.clearView(recyclerView, viewHolder);
        adapter.onItemMoved();
    }


    @Override
    public boolean canDropOver(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder current, @NonNull RecyclerView.ViewHolder target) {
        int currentPosition = current.getAdapterPosition();
        int targetPosition = target.getAdapterPosition();
        ConfigAdapterModel currentItem = adapter.getItem(currentPosition);
        ConfigAdapterModel targetItem = adapter.getItem(targetPosition);
        boolean currentCanDrop = currentItem != null &&
                currentItem.getItemType() == ItemType.DATA_VALUE
                && currentItem.isMyData();
        boolean targetCanDrop = targetItem != null &&
                targetItem.getItemType() == ItemType.DATA_VALUE
                && targetItem.isMyData();
        Log.e("salesConfig", "current:" + currentCanDrop + ",target:" + targetCanDrop);
        return currentCanDrop && targetCanDrop;
    }

    @Override
    public void onSwiped(@NonNull RecyclerView.ViewHolder viewHolder, int i) {

    }
}
