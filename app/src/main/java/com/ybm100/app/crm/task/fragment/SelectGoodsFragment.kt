package com.ybm100.app.crm.task.fragment

import android.os.Bundle
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.fold.recyclyerview.flexibledivider.HorizontalDividerItemDecoration
import com.scwang.smartrefresh.layout.api.RefreshLayout
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener
import com.xyy.common.util.ConvertUtils
import com.xyy.common.util.ScreenUtils
import com.xyy.common.widget.RoundTextView
import com.xyy.userbehaviortracking.utils.UserBehaviorTrackingUtils
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.base.fragment.BaseMVPCompatFragment
import com.xyy.utilslibrary.rxbus.RxBus
import com.xyy.utilslibrary.rxbus.Subscribe
import com.ybm100.app.crm.R
import com.ybm100.app.crm.constant.Constants
import com.ybm100.app.crm.constant.RxBusCode
import com.ybm100.app.crm.goods.ui.GoodsDetailActivity
import com.ybm100.app.crm.task.activity.BaseTaskSearchActivity
import com.ybm100.app.crm.task.activity.SelectCustomersActivity
import com.ybm100.app.crm.task.activity.SelectGoodsActivity.Companion.sGoodsCartList
import com.ybm100.app.crm.task.adapter.TaskGoodsDetailAdapter
import com.ybm100.app.crm.task.bean.ShareConfirm
import com.ybm100.app.crm.task.bean.TaskGoodsDetailDataBean
import com.ybm100.app.crm.task.contract.TaskGoodsDetailContract
import com.ybm100.app.crm.task.presenter.TaskGoodsDetailPresenter
import com.ybm100.app.crm.utils.ShareHelper
import com.ybm100.app.crm.widget.BadgeView
import com.ybm100.app.crm.widget.CustomBottomSheetDialog
import com.ybm100.app.crm.widget.ShareGoodsDialog
import kotlinx.android.synthetic.main.bottom_cart.*
import kotlinx.android.synthetic.main.fragment_select_goods.*


/**
 * 选择商品
 */
class SelectGoodsFragment : BaseMVPCompatFragment<TaskGoodsDetailPresenter>(), TaskGoodsDetailContract.ITaskGoodsDetailView, OnRefreshLoadMoreListener, View.OnClickListener {
    private var isTaskProgressDescent = true
    private var isPriceDescent = false
    private var mAdapter: TaskGoodsDetailAdapter? = null
    private var mCartDialog: CustomBottomSheetDialog? = null
    private var mCartLayout: View? = null
    private var mCartAdapter: TaskGoodsDetailAdapter? = null
    private var mSearchType = Constants.Task.CONSTANT_SEARCH_TYPE_GOODS
    private var mTaskId = ""
    private var mKeyWords = ""
    private var mTimeFilter = ""
    private var shareType: ShareHelper.Companion.SocialMedia? = null
    private var mIsRefresh = true
    private val mMaxCartHeight = ConvertUtils.px2dp((ScreenUtils.getScreenHeight() * 0.9).toFloat()).toFloat()

    override fun initData() {
        super.initData()
        arguments?.let {
            mTaskId = it.getString(Constants.Task.ARG_TASK_ID, "")
            mSearchType = it.getInt(Constants.Task.ARG_SEARCH_TYPE, Constants.Task.CONSTANT_SEARCH_TYPE_GOODS)
            mKeyWords = it.getString(Constants.Task.ARG_KEY_WORDS, "")
            mTimeFilter = it.getString(Constants.Task.ARG_TASK_TIME_FILTER, "")
        }
    }

    override fun initPresenter(): BasePresenter<*, *> {
        return TaskGoodsDetailPresenter()
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_select_goods
    }

    override fun initUI(view: View?, savedInstanceState: Bundle?) {
        if (mSearchType == Constants.Task.CONSTANT_TYPE_GOODS_RECOMMENDATION) {
            RxBus.get().register(this)
        }

        iv_customer_cart.setImageDrawable(ContextCompat.getDrawable(mContext, R.drawable.ic_goods_cart))

        registerListener()

        initSmartRefreshLayout()

        initRecyclerView()

        initCartDialog()

        mPresenter.mQueryMap["taskId"] = mTaskId

        when (mSearchType) {
            Constants.Task.CONSTANT_SEARCH_TYPE_GOODS -> {
                bottom_cart.visibility = View.GONE
                group_goods_search.visibility = View.GONE
                group.visibility = View.GONE
                val layoutParams = ConstraintLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
                layoutParams.setMargins(0, 0, 0, 0)
                svl.layoutParams = layoutParams
                mPresenter.mQueryMap["skuName"] = mKeyWords
                mPresenter.mQueryMap["filter"] = mTimeFilter
            }
            Constants.Task.CONSTANT_TYPE_GOODS_RECOMMENDATION -> {
//                group.visibility = View.GONE
                mPresenter.mQueryMap["skuName"] = mKeyWords
                mPresenter.mQueryMap["filter"] = mTimeFilter
                updateBottomCart()
                mCartAdapter?.setNewData(sGoodsCartList)
            }
            Constants.Task.CONSTANT_SEARCH_TYPE_GOODS_RECOMMENDATION -> {
                group.visibility = View.GONE
                mPresenter.mQueryMap["skuName"] = mKeyWords
                mPresenter.mQueryMap["filter"] = mTimeFilter
                updateBottomCart()
                mCartAdapter?.setNewData(sGoodsCartList)
            }
        }
        mPresenter.getTaskGoodsDetailList(true)
    }

    override fun onDestroy() {
        super.onDestroy()

        if (mSearchType == Constants.Task.CONSTANT_TYPE_GOODS_RECOMMENDATION) {
            RxBus.get().unRegister(this)
        }
    }

    override fun onBackPressedSupport(): Boolean {
        onCancelPressed()
        return true
    }

    override fun onLoadMore(refreshLayout: RefreshLayout) {
        mPresenter.getTaskGoodsDetailList(false)
    }

    override fun onRefresh(refreshLayout: RefreshLayout) {
        //refresh()
    }

    override fun onGetTaskGoodsDetailSuccess(data: RequestBaseBean<TaskGoodsDetailDataBean?>?, isRefresh: Boolean, isLastPage: Boolean) {
        mIsRefresh = isRefresh

        data?.data?.rows?.let {
            if (isLastPage) {
                srl.finishLoadMoreWithNoMoreData()
            }
            updateListSelectedStatus(it)

            if (isRefresh) {
                mAdapter?.setNewData(it)
                if (it.isEmpty()) {
                    svl.showEmpty()
                } else {
                    svl.showContent()
                }
            } else {
                mAdapter?.addData(it)
            }
        }
    }

    private fun updateListSelectedStatus(recyclerViewDataList: List<TaskGoodsDetailDataBean.Row?>?, shouldNotifyDataSetChange: Boolean = false) {
        if (recyclerViewDataList == null) {
            return
        }

        if (shouldNotifyDataSetChange) {
            recyclerViewDataList.forEachIndexed { index, item ->
                item?.isSelected = false
            }
        }

        sGoodsCartList.forEachIndexed { index, itemCart ->
            recyclerViewDataList.forEachIndexed { index, item ->
                if (itemCart.skuId == item?.skuId) {
                    item?.isSelected = true
                }
            }
        }
        if (shouldNotifyDataSetChange) {
            mAdapter?.notifyDataSetChanged()
            updateBottomCart()
        }
    }

    override fun onGetTaskGoodsDetailFail() {
        if (mIsRefresh) {
            svl.showError()
        }
    }

    override fun onRequestShareConfirmSuccess(data: RequestBaseBean<ShareConfirm?>?) {
        if (shareType != null) {
            ShareHelper.shareUrl(mActivity, shareType!!, data?.data?.shareUrl, data?.data?.appName,
                    data?.data?.content, data?.data?.appLogo, object : ShareHelper.ShareCallback {
                override fun onCancel(platform: String?) {
                }

                override fun onCompleted(platform: String?) {
                }

                override fun onError(platform: String?, errMsg: String?) {
                }

                override fun onStart(platform: String?) {
                }

            })
        }
    }

    override fun onRequestShareConfirmFail() {

    }

    override fun showNetError() {
        if (mIsRefresh) {
            svl.showError()
        }
    }

    @Subscribe(code = RxBusCode.RX_BUS_UPDATE_GOODS_LIST_AND_CART)
    fun rxBusEvent() {
        updateListSelectedStatus(mAdapter?.data, true)
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.iv_back -> {
                mActivity.finish()
            }
            R.id.rtv_search -> {
                BaseTaskSearchActivity.startActivity(mActivity, Constants.Task.CONSTANT_SEARCH_TYPE_GOODS_RECOMMENDATION, mTaskId, mTimeFilter)
            }
            R.id.tv_sort_task_progress -> {
                setSortArrow(tv_sort_price, false, true)
                isPriceDescent = false

                if (!isTaskProgressDescent) {
                    mPresenter.mQueryMap["sortType"] = "9"
                } else {
                    mPresenter.mQueryMap["sortType"] = "10"
                }
                isTaskProgressDescent = !isTaskProgressDescent

                setSortArrow(tv_sort_task_progress, isTaskProgressDescent)

                refresh()
            }
            R.id.tv_sort_price -> {
                setSortArrow(tv_sort_task_progress, false, true)
                isTaskProgressDescent = false

                if (!isPriceDescent) {
                    mPresenter.mQueryMap["sortType"] = "3"
                } else {
                    mPresenter.mQueryMap["sortType"] = "4"
                }
                isPriceDescent = !isPriceDescent

                setSortArrow(tv_sort_price, isPriceDescent)

                refresh()
            }
            R.id.iv_customer_cart -> {
                updateCartHeight()
                if (sGoodsCartList.size > 0) {
                    mCartDialog?.show()
                } else {

                }
            }
            R.id.tv_clear -> {
                sGoodsCartList.clear()
                mCartAdapter?.setNewData(sGoodsCartList)
                mAdapter?.data?.forEachIndexed { index, item ->
                    if (item?.isSelected == true) {
                        item.isSelected = false
                    }
                }
                mAdapter?.notifyDataSetChanged()
                updateBottomCart()
                mCartDialog?.hide()
            }

            R.id.rtv_confirm_recommendation -> {
                UserBehaviorTrackingUtils.track(Constants.Task.MC_TASK_PRODUCT_RECOMMEND_WITH_PRODUCT)

                val dialog = ShareGoodsDialog(mContext).also { shareDialog ->
                    shareDialog.onClickCallBack = {
                        shareType = null
                        when (it) {
                            1 -> {
                                UserBehaviorTrackingUtils.track(Constants.Task.MC_TASK_PRODUCT_RECOMMEND_TYPE2)

                                shareType = ShareHelper.Companion.SocialMedia.PLATFORM_WECHAT
                                mPresenter.requestShareConfirm(mTaskId, getGoodsStr())
                            }
                            2 -> {
                                UserBehaviorTrackingUtils.track(Constants.Task.MC_TASK_PRODUCT_RECOMMEND_TYPE3)

                                shareType = ShareHelper.Companion.SocialMedia.PLATFORM_WECHAT_CIRCLE
                                mPresenter.requestShareConfirm(mTaskId, getGoodsStr())
                            }
                            3 -> {
                                UserBehaviorTrackingUtils.track(Constants.Task.MC_TASK_PRODUCT_RECOMMEND_TYPE1)

                                SelectCustomersActivity.startActivity(mActivity, mTaskId, getGoodsStr(),
                                        Constants.Task.CONSTANT_FROM_TASK_RECOMMENDATION)
                            }
                        }
                    }
                }
                dialog.show()
            }
        }
    }

    private fun getGoodsStr(): String {
        val sbGoods = StringBuilder()
        sGoodsCartList.forEachIndexed { index, item ->
            sbGoods.append("${item.skuId},")
        }
        return sbGoods.toString()
    }

    private fun updateCartHeight() {
        if (sGoodsCartList.size > 0) {

            var height = INITIAL_HEIGHT + ITEM_HEIGHT * sGoodsCartList.size
            if (height > mMaxCartHeight) height = mMaxCartHeight.toFloat()

            mCartDialog?.setPeekHeight(ConvertUtils.dp2px(height))
            mCartDialog?.setMaxHeight(ConvertUtils.dp2px(height))
        }
    }

    private fun updateBottomCart() {
        val tvTitle = mCartLayout?.findViewById<TextView>(R.id.tv_title)
        val tvDescription = mCartLayout?.findViewById<TextView>(R.id.tv_description)
        val rtvConfirmRecommendation = mCartLayout?.findViewById<RoundTextView>(R.id.rtv_confirm_recommendation)
        val badgeView = mCartLayout?.findViewById<BadgeView>(R.id.bv_count)

        tvTitle?.text = "已选${sGoodsCartList.size}个商品"

        tv_description.text = "已选${sGoodsCartList.size}个商品"
        tvDescription?.text = "已选${sGoodsCartList.size}个商品"

        bv_count.text = "${sGoodsCartList.size}"
        badgeView?.text = "${sGoodsCartList.size}"

        if (sGoodsCartList.size > 0) {
            rtv_confirm_recommendation.visibility = View.VISIBLE
            rtvConfirmRecommendation?.visibility = View.VISIBLE
        } else {
            rtv_confirm_recommendation.visibility = View.GONE
            rtvConfirmRecommendation?.visibility = View.GONE
        }

    }

    private fun registerListener() {
        iv_back.setOnClickListener(this)
        rtv_search.setOnClickListener(this)
        tv_sort_task_progress.setOnClickListener(this)
        tv_sort_price.setOnClickListener(this)
        iv_customer_cart.setOnClickListener(this)
        rtv_confirm_recommendation.setOnClickListener(this)
        svl.setOnRetryListener {
            mPresenter.getTaskGoodsDetailList(true)
        }
    }

    private fun initSmartRefreshLayout() {
        setRefreshLayout(srl)
        srl.setEnableRefresh(false)
        srl.setEnableLoadMore(true)
        srl.setOnRefreshLoadMoreListener(this)
    }

    private fun initRecyclerView() {
        recycler_view.apply {
            layoutManager = androidx.recyclerview.widget.LinearLayoutManager(mContext)
            addItemDecoration(HorizontalDividerItemDecoration.Builder(mContext)
                    .size(ConvertUtils.dp2px(1f))
                    .margin(ConvertUtils.dp2px(48f), ConvertUtils.dp2px(0f))
                    .color(ContextCompat.getColor(mContext, R.color.text_color_F6F6F6))
                    .build())
            mAdapter = if (mSearchType == Constants.Task.CONSTANT_SEARCH_TYPE_GOODS_RECOMMENDATION || mSearchType == Constants.Task.CONSTANT_TYPE_GOODS_RECOMMENDATION) {
                TaskGoodsDetailAdapter(true)
            } else {
                TaskGoodsDetailAdapter(false)
            }

            adapter = mAdapter
        }
        mAdapter?.setOnItemClickListener { adapter, view, position ->
            if (mSearchType == Constants.Task.CONSTANT_SEARCH_TYPE_GOODS) {
                GoodsDetailActivity.start(mContext, mAdapter?.data?.getOrNull(position)?.skuId, mTaskId,
                        mAdapter?.data?.getOrNull(position)?.branchCode)
            } else {
                mAdapter?.data?.getOrNull(position)?.isSelected = !(mAdapter?.data?.getOrNull(position)?.isSelected
                        ?: false)
                mAdapter?.notifyItemChanged(position)

                if (mAdapter?.data?.getOrNull(position)?.isSelected == true) {
                    sGoodsCartList.add(mAdapter?.data?.getOrNull(position)!!)
                } else {
                    repeat(sGoodsCartList.size) {
                        if (sGoodsCartList.getOrNull(it)?.skuId == mAdapter?.data?.getOrNull(position)?.skuId) {
                            sGoodsCartList.removeAt(it)
                        }
                    }
                }
                mCartAdapter?.setNewData(sGoodsCartList)

                updateBottomCart()
            }
        }
    }

    private fun setSortArrow(view: TextView, isDescent: Boolean, isNormal: Boolean = false) {
        view.apply {
            if (isNormal) {
                setTextColor(ContextCompat.getColor(mContext, R.color.color_676773))
                setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_sort_normal, 0)
            } else {
                if (isDescent) {
                    setTextColor(ContextCompat.getColor(mContext, R.color.color_292933))
                    setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_sort_desc, 0)
                } else {
                    setTextColor(ContextCompat.getColor(mContext, R.color.color_292933))
                    setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_sort_asc, 0)
                }
            }
        }
    }

    private fun initCartDialog() {
        mCartDialog = CustomBottomSheetDialog(mContext, ConvertUtils.dp2px(INITIAL_HEIGHT + ITEM_HEIGHT), ConvertUtils.dp2px(INITIAL_HEIGHT + ITEM_HEIGHT))

        mCartLayout = layoutInflater.inflate(R.layout.dialog_cart, null)

        mCartDialog?.setContentView(mCartLayout)

        mCartLayout?.findViewById<ImageView>(R.id.iv_customer_cart)?.setImageDrawable(ContextCompat.getDrawable(mContext, R.drawable.ic_goods_cart))
        mCartLayout?.findViewById<TextView>(R.id.tv_clear)?.setOnClickListener(this@SelectGoodsFragment)
        mCartLayout?.findViewById<RoundTextView>(R.id.rtv_confirm_recommendation)?.setOnClickListener(this@SelectGoodsFragment)

        val recyclerView = mCartLayout?.findViewById<androidx.recyclerview.widget.RecyclerView>(R.id.recycler_view)
        recyclerView?.apply {
            layoutManager = androidx.recyclerview.widget.LinearLayoutManager(mContext)
            mCartAdapter = TaskGoodsDetailAdapter(false, true, R.layout.item_cart_select_goods)
            adapter = mCartAdapter
        }

        mCartAdapter?.setOnItemChildClickListener { adapter, view, position ->
            when (view?.id) {
                R.id.tv_delete -> {
                    mAdapter?.data?.forEachIndexed { index, item ->
                        if (item?.skuId == sGoodsCartList[position].skuId) {
                            mAdapter?.data?.getOrNull(index)?.isSelected = false
                            mAdapter?.notifyDataSetChanged()
                        }
                    }

                    sGoodsCartList.removeAt(position)
                    mCartAdapter?.notifyItemRemoved(position)

                    updateCartHeight()

                    updateBottomCart()

                    if (sGoodsCartList.size < 1) {
                        mCartDialog?.hide()
                    }
                }
            }
        }
    }

    private fun refresh() {
        srl.setEnableLoadMore(true)
        mPresenter.getTaskGoodsDetailList(true)
    }

    fun search(keywords: String) {
        mPresenter.mQueryMap["skuName"] = keywords
        refresh()
    }

    fun onCancelPressed() {
        RxBus.get().send(RxBusCode.RX_BUS_UPDATE_GOODS_LIST_AND_CART)
        mActivity.finish()
    }

    companion object {
        private const val ITEM_HEIGHT = 155f
        private const val INITIAL_HEIGHT = 130f

        @JvmStatic
        fun newInstance(bundle: Bundle?) =
                SelectGoodsFragment().apply {
                    arguments = Bundle().apply {
                        if (bundle != null) {
                            this.putAll(bundle)
                        }
                    }
                }
    }
}
