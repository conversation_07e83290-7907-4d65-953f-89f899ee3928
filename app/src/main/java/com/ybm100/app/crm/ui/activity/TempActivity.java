package com.ybm100.app.crm.ui.activity;

import android.os.Bundle;
import android.view.View;

import com.xyy.common.navigationbar.AbsNavigationBar;
import com.xyy.common.navigationbar.DefaultNavigationBar;
import com.xyy.utilslibrary.base.activity.BaseCompatActivity;
import com.ybm100.app.crm.R;

/**
 * @author: zcj
 * @time:2020/2/28.
 * Description:
 */
public class TempActivity extends BaseCompatActivity {

    @Override
    protected int getLayoutId() {
        return R.layout.activity_temp;
    }

    @Override
    protected AbsNavigationBar initHead() {
        DefaultNavigationBar bar = new DefaultNavigationBar.Builder(this)
                .setTitle("")
                .builder();
        bar.rightImgView.setVisibility(View.GONE);
        return bar;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {

    }
}
