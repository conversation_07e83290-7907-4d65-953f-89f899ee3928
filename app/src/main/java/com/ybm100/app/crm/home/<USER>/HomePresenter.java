package com.ybm100.app.crm.home.presenter;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.lzcustomer.LzRoleBean;
import com.ybm100.app.crm.contract.home.HomeContract;
import com.ybm100.app.crm.home.bean.ModuleConfig;
import com.ybm100.app.crm.home.api.HomeModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

import java.util.List;

/**
 * Created by XyyMvpSportTemplate on 12/19/2018 19:27
 */
public class HomePresenter extends BasePresenter<HomeContract.IHomeModel, HomeContract.IHomeView> {

    public static HomePresenter newInstance() {
        return new HomePresenter();
    }

    @Override
    protected HomeModel getModel() {
        return HomeModel.newInstance();
    }

    public void getRoles() {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.getRoles().subscribe(new SimpleSuccessConsumer<RequestBaseBean<List<LzRoleBean>>>(mIView) {
            @Override
            public void onSuccess(RequestBaseBean<List<LzRoleBean>> bean) {
                mIView.getRolesSuccess(bean.getData());
            }

            @Override
            public void onFailure(int errorCode) {
                mIView.getRolesFail();
            }
        }, new SimpleErrorConsumer(mIView) {
            @Override
            protected void onError(Throwable throwable, String msg) {
                mIView.getRolesFail();
            }
        }));
    }

    public void getModulesConfig() {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.getModulesConfig().subscribe(new SimpleSuccessConsumer<RequestBaseBean<List<ModuleConfig>>>(mIView) {
            @Override
            public void onSuccess(RequestBaseBean<List<ModuleConfig>> bean) {
                mIView.getModulesConfigSuccess(bean.getData());
            }

            @Override
            public void onFailure(int errorCode) {
                mIView.getModulesConfigFail();
            }
        }, new SimpleErrorConsumer(mIView) {
            @Override
            protected void onError(Throwable throwable, String msg) {
                mIView.getModulesConfigFail();
            }
        }));
    }
}
