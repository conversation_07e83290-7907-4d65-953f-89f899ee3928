package com.ybm100.app.crm.ui.fragment.drugstore;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.flyco.tablayout.SlidingTabLayout;
import com.xyy.common.util.ToastUtils;
import com.xyy.utilslibrary.adapter.CommonPageAdapter;
import com.xyy.utilslibrary.base.fragment.BaseCompatFragment;
import com.xyy.utilslibrary.base.fragment.BaseMVPCompatFragment;
import com.xyy.utilslibrary.rxbus.RxBus;
import com.xyy.utilslibrary.rxbus.Subscribe;
import com.xyy.utilslibrary.utils.DisplayUtils;
import com.xyy.utilslibrary.utils.ViewUtils;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.lzcustomer.LzRoleBean;
import com.ybm100.app.crm.constant.DrugstoreConstants;
import com.ybm100.app.crm.constant.LzRoleConstant;
import com.ybm100.app.crm.constant.RxBusCode;
import com.ybm100.app.crm.ui.activity.drugstore.DrugstoreSearchActivity;
import com.ybm100.app.crm.ui.fragment.hycustomer.HyCustomerSearchActivity;
import com.ybm100.app.crm.ui.fragment.hycustomer.HyPrivateListFragment;
import com.ybm100.app.crm.ui.fragment.hycustomer.HyPublicListFragment;
import com.ybm100.app.crm.ui.fragment.lzcustomer.LzCustomerSearchActivity;
import com.ybm100.app.crm.ui.fragment.lzcustomer.LzPrivateFragment;
import com.ybm100.app.crm.ui.fragment.lzcustomer.LzPublicFragment;
import com.ybm100.app.crm.utils.LzRoleInfoManager;
import com.ybm100.app.crm.utils.SnowGroundUtils;
import com.ybm100.app.crm.widget.popwindow.CustomerDetailPopup;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;

/**
 * 药店根fragment
 */
public class CustomRootFragment extends BaseCompatFragment {
    @BindView(R.id.tabLayout_drugstore)
    SlidingTabLayout tabLayoutDrugstore;
    @BindView(R.id.vp_view)
    ViewPager viewPager;
    @BindView(R.id.iv_search)
    ImageButton ivSearch;
    @BindView(R.id.iv_filter)
    ImageButton ivFilter;
    @BindView(R.id.layout_operation)
    LinearLayout layoutOperation;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.iv_down)
    ImageView ivDown;
    @BindView(R.id.iv_search_lz)
    ImageButton ivSearchLz;
    @BindView(R.id.iv_filter_lz)
    ImageButton ivFilterLz;
    @BindView(R.id.layout_lz_title)
    ConstraintLayout layoutLzTitle;
    @BindView(R.id.layout_title)
    LinearLayout layoutTitle;
    CommonPageAdapter drugStoreFragmentPagerAdapter;
    private int currTab = 0;
    private int queryType = 0;
    private List<LzRoleBean> lzRoleBeans = new ArrayList<>();

    public static CustomRootFragment newInstance() {
        Bundle args = new Bundle();
        CustomRootFragment fragment = new CustomRootFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_custome_root_lz;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        RxBus.get().unRegister(this);
    }

    @Override
    public void onLazyInitView(@Nullable Bundle savedInstanceState) {
        super.onLazyInitView(savedInstanceState);
        initLzTitle();
        if (savedInstanceState == null) {
            showTypeFragment(getRoleType());
        }
    }

    @Override
    public void initUI(View view, @Nullable Bundle savedInstanceState) {
        RxBus.get().register(this);
        resetSearchStatus();
        ivSearch.setOnClickListener(v -> searchDrugstore());
        ivFilter.setOnClickListener(v -> filterDrugstore());
        ivSearchLz.setOnClickListener(v -> {
            if (getRoleType() == LzRoleConstant.ROLE_TYPE_LZ) {
                searchLzCustomer();
            } else if (getRoleType() == LzRoleConstant.ROLE_TYPE_HY) {
                // 荷叶搜索
                searchHyCustomer();
            } else {
                searchDrugstore();
            }
        });
        ivFilterLz.setOnClickListener(v -> filterDrugstore());
        showTypeFragment(getRoleType());
    }

    private void showTypeFragment(int roleType) {
        showTypeFragment(roleType, 0);
    }

    private void showTypeFragment(int roleType, int index) {
        //初始化fragment
        //从搜索页放置后台，重启启动后，此界面有可能被杀死，故恢复，否则会空指针崩溃
        List<BaseMVPCompatFragment> drugstoreFragments = new ArrayList<>();
        if (roleType == LzRoleConstant.ROLE_TYPE_LZ) {
            LzPrivateFragment lzCustomPrivateFragment = LzPrivateFragment.newInstance(false);
            drugstoreFragments.add(lzCustomPrivateFragment);
            drugstoreFragments.add(LzPublicFragment.newInstance(false));
            ivFilterLz.setVisibility(View.GONE);
        } else if (roleType == LzRoleConstant.ROLE_TYPE_HY) {
            //  荷叶健康公私海
            HyPrivateListFragment hyPrivateListFragment = HyPrivateListFragment.newInstance(false);
            drugstoreFragments.add(hyPrivateListFragment);
            drugstoreFragments.add(HyPublicListFragment.newInstance(false));
            ivFilterLz.setVisibility(View.VISIBLE);
        } else {
            CustomPrivateFragment customPrivateFragment;
            if (queryType == 0) {
                customPrivateFragment = CustomPrivateFragment.newInstance(false);
            } else {
                customPrivateFragment = CustomPrivateFragment.newInstance(false, queryType);
            }
            queryType = 0;
            drugstoreFragments.add(customPrivateFragment);
            drugstoreFragments.add(CustomPublicFragment.newInstance(false));
            ivFilterLz.setVisibility(View.VISIBLE);
        }
        String[] titles = new String[]{mActivity.getResources().getString(R.string.private_custom), mActivity.getResources().getString(R.string.public_custom)};
        if (drugStoreFragmentPagerAdapter != null) {
            drugStoreFragmentPagerAdapter = null;
            Log.e("guan", "viewPager replace datasource");
        }
        drugStoreFragmentPagerAdapter = new CommonPageAdapter(getChildFragmentManager(), drugstoreFragments, titles);
        viewPager.setAdapter(drugStoreFragmentPagerAdapter);
        tabLayoutDrugstore.setViewPager(viewPager, titles);
        //隐藏展示筛选按钮
        viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int i, float v, int i1) {

            }

            @Override
            public void onPageSelected(int i) {
                if (getRoleType() == LzRoleConstant.ROLE_TYPE_YBM || getRoleType() == LzRoleConstant.ROLE_TYPE_HY) {
                    switch (i) {
                        case 0:
                            ivFilter.setVisibility(View.VISIBLE);
                            ivFilterLz.setVisibility(View.VISIBLE);
                            break;
                        case 1:
                            if (getRoleType() != LzRoleConstant.ROLE_TYPE_YBM) {
                                ivFilter.setVisibility(View.GONE);
                                ivFilterLz.setVisibility(View.GONE);
                            }
                            break;
                    }
                } else {
                    ivFilterLz.setVisibility(View.GONE);
                }
                currTab = i;
            }

            @Override
            public void onPageScrollStateChanged(int i) {

            }
        });
        if (index != 0) {

            viewPager.postDelayed(new Runnable() {
                @Override
                public void run() {
                    viewPager.setCurrentItem(index, false);
                }
            }, 1000);
        }
    }

    /**
     * 搜索药店
     */
    private void searchDrugstore() {
        if (mActivity != null && ViewUtils.isClickable()) {
            Intent intent = new Intent(mActivity, DrugstoreSearchActivity.class);
            Bundle bundle = new Bundle();
            bundle.putInt(DrugstoreConstants.INTENT_KEY_TYPE, viewPager.getCurrentItem());
            intent.putExtras(bundle);
            startActivityForResult(intent, DrugstoreConstants.REQUEST_SEARCH);

            String snowGroundName = "Event-PrivateSea-Search";
            if (viewPager.getCurrentItem() == 1) {
                snowGroundName = "Event-PublicSea-Search";
            }
            SnowGroundUtils.track(snowGroundName);
        }
    }

    /**
     * 灵芝问诊搜索
     */
    private void searchLzCustomer() {
        if (mActivity != null && ViewUtils.isClickable()) {
            Intent intent = new Intent(mActivity, LzCustomerSearchActivity.class);
            Bundle bundle = new Bundle();
            bundle.putInt(DrugstoreConstants.INTENT_KEY_TYPE, viewPager.getCurrentItem());
            intent.putExtras(bundle);
            startActivityForResult(intent, DrugstoreConstants.REQUEST_SEARCH_LZ);
            String snowGroundName = "Event-LZPrivateSea-Search";
            if (viewPager.getCurrentItem() == 1) {
                snowGroundName = "Event-LZPublicSea-Search";
            }
            SnowGroundUtils.track(snowGroundName);
        }
    }

    /**
     * 荷叶健康搜索
     */
    private void searchHyCustomer() {
        if (mActivity != null && ViewUtils.isClickable()) {
            Intent intent = new Intent(mActivity, HyCustomerSearchActivity.class);
            Bundle bundle = new Bundle();
            bundle.putInt(DrugstoreConstants.INTENT_KEY_TYPE, viewPager.getCurrentItem());
            intent.putExtras(bundle);
            startActivityForResult(intent, DrugstoreConstants.REQUEST_SEARCH_HY);
            String snowGroundName = "Event-HYPrivateSea-Search";
            if (viewPager.getCurrentItem() == 1) {
                snowGroundName = "Event-HYPublicSea-Search";
            }
            SnowGroundUtils.track(snowGroundName);
        }
    }

    /**
     * 筛选药店
     */
    private void filterDrugstore() {
        Fragment item = drugStoreFragmentPagerAdapter.getItem(0);
        Fragment secondItem = drugStoreFragmentPagerAdapter.getItem(1);
        if (item instanceof CustomPrivateFragment) {
            if (currTab == 0) {
                ((CustomPrivateFragment) item).filter(mActivity);
            } else if (secondItem instanceof CustomPublicFragment) {
                ((CustomPublicFragment) secondItem).areaSelect();
            }

        } else if (item instanceof LzPrivateFragment) {
            //线上有一个崩溃，不知道为什么列表里是灵芝的fragment，但是调用到了这个方法里，所以这里判断下类型，防止崩溃
            ToastUtils.showShort("灵芝暂不支持筛选");
        } else if (item instanceof HyPrivateListFragment) {
            ((HyPrivateListFragment) item).filter(mActivity);
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK && requestCode == DrugstoreConstants.REQUEST_SEARCH) {
            //搜索回调，数据刷新
            // 不知道有啥用，先注释了观察观察
//            if (drugStoreFragmentPagerAdapter == null) return;
//            switch (currTab) {
//                case 0:
//                    Fragment privateFragment = drugStoreFragmentPagerAdapter.getItem(currTab);
//                    if (privateFragment instanceof CustomPrivateFragment) {
//                        ((CustomPrivateFragment) privateFragment).refresh();
//                    }
//                    break;
//                case 1:
//                    Fragment publicFragment = drugStoreFragmentPagerAdapter.getItem(currTab);
//                    if (publicFragment instanceof CustomPublicFragment) {
//                        ((CustomPublicFragment) publicFragment).refresh();
//                    }
//                    break;
//            }
        } else if (resultCode == RESULT_OK && requestCode == DrugstoreConstants.REQUEST_SEARCH_LZ) {
            //灵芝问诊 搜索回调，数据刷新
            if (drugStoreFragmentPagerAdapter == null) return;
            switch (currTab) {
                case 0:
                    Fragment privateFragment = drugStoreFragmentPagerAdapter.getItem(currTab);
                    if (privateFragment instanceof LzPrivateFragment) {
                        ((LzPrivateFragment) privateFragment).refresh();
                    }
                    break;
                case 1:
                    Fragment publicFragment = drugStoreFragmentPagerAdapter.getItem(currTab);
                    if (publicFragment instanceof LzPublicFragment) {
                        ((LzPublicFragment) publicFragment).refresh();
                    }
                    break;
            }
        }
    }

    /**
     * 重置药店线索搜索状态
     */
    public void resetSearchStatus() {
        DrugstoreConstants.type = DrugstoreConstants.TAB_PRIVATE;
        DrugstoreConstants.select = 0;
    }

    /**
     * 首页资质临期、过期 弹窗跳转
     *
     * @param bundle
     */
    @Subscribe(code = RxBusCode.RX_BUS_ENTER_CLIENT_WITH_CONDITIONS_NEXT)
    public void enterClient(Bundle bundle) {
        post(new Runnable() {
            @Override
            public void run() {
                queryType = bundle.getInt("queryType", 0);
                int index = bundle.getInt("index", 0);
                turnOnYBM(index);
            }
        });

    }

    //初始化title弹窗
    private void initLzTitle() {
        lzRoleBeans = LzRoleInfoManager.INSTANCE.getLzRoleBeans();
        switch (lzRoleBeans.size()) {
            case 1:
                //如果只有1个并且为灵芝时
                if (LzRoleConstant.ROLE_TYPE_LZ == lzRoleBeans.get(0).getRoleCode()) {
                    showLzView();
                } else if (LzRoleConstant.ROLE_TYPE_HY == lzRoleBeans.get(0).getRoleCode()) {
                    // 荷叶
                    showHyView();
                } else {
                    showYBMView();
                }
                break;
            case 2:
            case 3:
                showBothView();
                break;
            default:
                showYBMView();
                break;
        }
    }

    // 用来控制 tab 左右边距 保持居中
    private void setNoPadding() {
        ConstraintLayout.LayoutParams lp = (ConstraintLayout.LayoutParams) tabLayoutDrugstore.getLayoutParams();
        lp.leftMargin = 0;
        tabLayoutDrugstore.setLayoutParams(lp);
    }

    private int getRoleType() {
        return LzRoleInfoManager.INSTANCE.getRoleType();
    }

    private void showDialog() {
        List<LzRoleBean> dataList = LzRoleInfoManager.INSTANCE.getLzRoleBeans();
        List<String> dataStrs = new ArrayList<>();
        for (int i = 0; i < dataList.size(); i++) {
            dataStrs.add(dataList.get(i).getRoleName());
        }
        CustomerDetailPopup detailPopup = new CustomerDetailPopup(mActivity, dataStrs);
        detailPopup.setBackgroundDrawable(R.drawable.pop_bg_right);
        detailPopup.setOnPopItemClickListener(position -> {
            tvTitle.setText(dataStrs.get(position));
            LzRoleInfoManager.INSTANCE.setCurrRoleType(dataList.get(position));
            showTypeFragment(getRoleType());
        });
        int[] location = new int[2];
        tvTitle.getLocationOnScreen(location);
        detailPopup.showPopupWindow(location[0] + tvTitle.getWidth() + DisplayUtils.dp2px(15f),
                location[1] + tvTitle.getHeight() + DisplayUtils.dp2px(5f));
    }

    private void turnOnYBM(int index) {
        List<LzRoleBean> roleBeans = LzRoleInfoManager.INSTANCE.getLzRoleBeans();
        if (roleBeans != null && roleBeans.size() > 1) {
            LzRoleBean roleBean = null;
            for (int i = 0; i < roleBeans.size(); i++) {
                if (roleBeans.get(i).getRoleCode() == LzRoleConstant.ROLE_TYPE_YBM) {
                    roleBean = roleBeans.get(i);
                    break;
                }
            }
            if (roleBean != null) {
                tvTitle.setText(roleBean.getRoleName());
                LzRoleInfoManager.INSTANCE.setCurrRoleType(roleBean);
                showTypeFragment(LzRoleConstant.ROLE_TYPE_YBM, index);
//                if (drugStoreFragmentPagerAdapter != null) {
//                    Fragment privateFragment = drugStoreFragmentPagerAdapter.getItem(0);
//                    if (privateFragment instanceof CustomPrivateFragment) {
//                        viewPager.setCurrentItem(index, false);
//                        if (index == 0) {
//                            ((CustomPrivateFragment) privateFragment).setQueryType(queryType);
//                        }
//                    } else {
//                        showTypeFragment(LzRoleConstant.ROLE_TYPE_YBM, index);
//                    }
//                } else {
//                    showTypeFragment(LzRoleConstant.ROLE_TYPE_YBM, index);
//                }
            }
        } else if (roleBeans != null && roleBeans.size() == 1 && roleBeans.get(0).getRoleCode() == LzRoleConstant.ROLE_TYPE_YBM) {
            if (drugStoreFragmentPagerAdapter != null) {
                CustomPrivateFragment mineFragment = ((CustomPrivateFragment) drugStoreFragmentPagerAdapter.getItem(0));
                if (mineFragment != null) {
                    viewPager.setCurrentItem(index, false);
                    if (index == 0) {
                        mineFragment.setQueryType(queryType);
                    }
                }
            } else {
                showTypeFragment(LzRoleConstant.ROLE_TYPE_YBM, index);
            }
        }
    }

    private void showLzView() {
        tvTitle.setText(lzRoleBeans.get(0).getRoleName());
        ivDown.setVisibility(View.GONE);
        layoutTitle.setOnClickListener(null);
        layoutOperation.setVisibility(View.GONE);
        ivSearchLz.setVisibility(View.VISIBLE);
        layoutLzTitle.setVisibility(View.VISIBLE);
        setNoPadding();
    }

    private void showHyView() {
        tvTitle.setText(lzRoleBeans.get(0).getRoleName());
        ivDown.setVisibility(View.GONE);
        layoutTitle.setOnClickListener(null);
        layoutOperation.setVisibility(View.GONE);
        ivSearchLz.setVisibility(View.VISIBLE);
        layoutLzTitle.setVisibility(View.VISIBLE);
        setNoPadding();
    }

    private void showYBMView() {
        //如果只有1个并且为药帮忙时
        layoutLzTitle.setVisibility(View.GONE);
        layoutOperation.setVisibility(View.VISIBLE);
    }

    private void showBothView() {
        //多个权限
        ivDown.setVisibility(View.VISIBLE);
        tvTitle.setText(LzRoleInfoManager.INSTANCE.getCurrRoleBean().getRoleName());
        layoutTitle.setOnClickListener(v -> showDialog());
        layoutOperation.setVisibility(View.GONE);
        ivSearchLz.setVisibility(View.VISIBLE);
        layoutLzTitle.setVisibility(View.VISIBLE);
        setNoPadding();
    }
}
