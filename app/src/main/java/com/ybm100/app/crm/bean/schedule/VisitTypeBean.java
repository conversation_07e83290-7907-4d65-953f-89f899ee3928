package com.ybm100.app.crm.bean.schedule;

import java.util.ArrayList;
import java.util.List;

/**
 * 日程类型
 */
public class VisitTypeBean {

    public static final int TYPE_PHONE_VISIT = 1;//电话拜访
    public static final int TYPE_SHANG_MEN_VISIT = 2;//上门拜访
    public static final int TYPE_SI_HAI_VISIT = 3;//私海拜访
    public static final int TYPE_STRANGE_VISIT = 4;//陌生拜访
    public static final int TYPE_TRAIN = 5;//培训
    public static final int TYPE_CONFERENCE = 6;//会议
    public static final int TYPE_ACCOMPANY_VISIT = 9;//陪访
    private String name;
    private int type;

    private static final VisitTypeBean shangMen;
    private static final VisitTypeBean siHai;
    private static final VisitTypeBean strange;
    private static final VisitTypeBean train;
    private static final VisitTypeBean conference;
    private static final VisitTypeBean phone;

    static {
        phone = new VisitTypeBean("电话拜访", TYPE_PHONE_VISIT);
        shangMen = new VisitTypeBean("上门拜访", TYPE_SHANG_MEN_VISIT);
        siHai = new VisitTypeBean("私海拜访", TYPE_SI_HAI_VISIT);
        strange = new VisitTypeBean("陌生拜访", TYPE_STRANGE_VISIT);
        train = new VisitTypeBean("培训", TYPE_TRAIN);
        conference = new VisitTypeBean("会议", TYPE_CONFERENCE);
    }

    private static List<VisitTypeBean> listAll;
    private static List<VisitTypeBean> listVip;
    private static List<VisitTypeBean> listSiHai;
    private static List<VisitTypeBean> listGJR;

    public VisitTypeBean(String name, int type) {
        this.name = name;
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public static List<VisitTypeBean> getAllType() {
        if (listAll == null) {
            listAll = new ArrayList<>();
            listAll.add(phone);
            listAll.add(shangMen);
            /**
             * 290 移除
             */
//            listAll.add(siHai);
//            listAll.add(strange);
//            listAll.add(train);
//            listAll.add(conference);
        }
        return listAll;
    }

    public static List<VisitTypeBean> geGJRType() {
        if (listGJR == null) {
            listGJR = new ArrayList<>();
            listGJR.add(phone);
            listGJR.add(shangMen);
            /**
             * 290 移除
             */
//            listGJR.add(train);
//            listGJR.add(conference);
        }
        return listGJR;
    }

    public static List<VisitTypeBean> getVipTypes() {
        if (listVip == null) {
            listVip = new ArrayList<>();
            listVip.add(phone);
            listVip.add(shangMen);
        }
        return listVip;
    }

    public static List<VisitTypeBean> getSiHaiTypes() {
        if (listSiHai == null) {
            listSiHai = new ArrayList<>();
            /**
             * 290 顺序修改
             */
            listVip.add(phone);
            listVip.add(shangMen);
        }
        return listSiHai;
    }

    /**
     * 获取日程类型
     *
     * @param type
     * @return
     */
    public static String getTypeById(int type) {
        String typeStr = "";
        switch (type) {
            case VisitTypeBean.TYPE_PHONE_VISIT:
                typeStr = "电话拜访";
                break;
            case VisitTypeBean.TYPE_SHANG_MEN_VISIT:
                typeStr = "上门拜访";
                break;
            case VisitTypeBean.TYPE_SI_HAI_VISIT:
                typeStr = "私海拜访";
                break;
            case VisitTypeBean.TYPE_STRANGE_VISIT:
                typeStr = "陌生拜访";
                break;
            case VisitTypeBean.TYPE_TRAIN:
                typeStr = "培训";
                break;
            case VisitTypeBean.TYPE_CONFERENCE:
                typeStr = "会议";
                break;
        }
        return typeStr;
    }

}
