package com.ybm100.app.crm.task.activity

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import android.view.View
import android.widget.TextView
import com.fold.recyclyerview.flexibledivider.HorizontalDividerItemDecoration
import com.scwang.smartrefresh.layout.api.RefreshLayout
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener
import com.xyy.common.navigationbar.AbsNavigationBar
import com.xyy.common.navigationbar.DefaultNavigationBar
import com.xyy.common.util.ConvertUtils
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.activity.BaseMVPCompatActivity
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.rxbus.RxBus
import com.xyy.utilslibrary.rxbus.Subscribe
import com.ybm100.app.crm.R
import com.ybm100.app.crm.constant.Constants
import com.ybm100.app.crm.constant.ExtraConstants
import com.ybm100.app.crm.constant.RxBusCode
import com.ybm100.app.crm.order.activity.OrderExecutorActivity
import com.ybm100.app.crm.task.adapter.TeamTaskAdapter
import com.ybm100.app.crm.task.bean.TeamTaskBean
import com.ybm100.app.crm.task.contract.TeamTaskContract
import com.ybm100.app.crm.task.presenter.TeamTaskPresenter
import com.ybm100.app.crm.utils.SpannableStringUtils
import kotlinx.android.synthetic.main.activity_team_task.*
import java.lang.Exception
import java.text.DecimalFormat

class TeamTaskActivity : BaseMVPCompatActivity<TeamTaskPresenter>(), TeamTaskContract.ITeamTaskView, OnRefreshLoadMoreListener {
    private var mAdapter: TeamTaskAdapter? = null
    private var mTaskId: String = ""
    private lateinit var mRightText: TextView
    private var mIsRefresh = true

    override fun initTransferData() {
        super.initTransferData()
        try {

        mTaskId = intent?.data?.getQueryParameter("taskId") ?: ""
        if (mTaskId.isEmpty()) {
            mTaskId = intent?.extras?.getString(Constants.Task.ARG_TASK_ID, "") ?: ""
        }
        }catch (e:Exception){

        }
    }

    override fun initPresenter(): BasePresenter<*, *> {
        return TeamTaskPresenter()
    }

    override fun getLayoutId(): Int {
        return R.layout.activity_team_task
    }

    override fun initHead(): AbsNavigationBar<*> {
        val toolbar = DefaultNavigationBar.Builder(this).setTitle("团队任务")
                .setRightClickListener {
                    OrderExecutorActivity.startActivity(this, mTaskId)
                }
                .builder()
        mRightText = toolbar.rightTextView
        toolbar.rightTextView.apply {
            text = "全部"
            setTextColor(ContextCompat.getColor(mContext, R.color.color_292933))
            compoundDrawablePadding = ConvertUtils.dp2px(2f)
            setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_team_task_arrow, 0)
        }
        return toolbar
    }

    override fun initView(savedInstanceState: Bundle?) {
        RxBus.get().register(this)

        svl.setOnRetryListener {
            mPresenter.getTeamTask(true)
        }

        initSmartRefreshLayout()

        initRecyclerView()

        mPresenter.mQueryMap["taskId"] = mTaskId
        mPresenter.getTeamTask(true)
    }

    override fun onDestroy() {
        super.onDestroy()
        RxBus.get().unRegister(this)
    }

    @Subscribe(code = RxBusCode.RX_BUS_UPDATE_TEAM_TASK)
    fun rxBusEvent(bundle: Bundle?) {
        val name = bundle?.getString(ExtraConstants.NAME, "") ?: ""
        val id = bundle?.getString(ExtraConstants.ID, "") ?: ""
        val isGroup = bundle?.getBoolean(ExtraConstants.IS_GROUP, false) ?: false
        if (name.isNotEmpty()) {
            mRightText.text = name
        }
        if (isGroup) {
            mPresenter.mQueryMap["groupId"] = id
        } else {
            mPresenter.mQueryMap["oaIds"] = id
        }
        mPresenter.getTeamTask(true)
    }


    private fun initSmartRefreshLayout() {
        setRefreshLayout(srl)
        srl.setEnableRefresh(false)
        srl.setEnableLoadMore(false)
        srl.setOnRefreshLoadMoreListener(this)
    }

    override fun onLoadMore(refreshLayout: RefreshLayout) {
        mPresenter.getTeamTask(false)
    }

    override fun onRefresh(refreshLayout: RefreshLayout) {
        //refresh()
    }

    override fun onGetTeamTaskSuccess(data: RequestBaseBean<TeamTaskBean?>?, isRefresh: Boolean, isLastPage: Boolean) {
        mIsRefresh = isRefresh

        data?.data?.run {
            tv_organizational_structure.text = "$departStr"
            tv_sales_degree_of_completion.text = SpannableStringUtils.highlightMiddle(mContext, R.color.color_00B377, "${typeStr}：", "$achieveGoal", "/${goal}")
            val formatTaskRate = DecimalFormat("#.##").format(rate)
            tv_progress_bar_value.text = SpannableStringUtils.highlightEnd(mContext, R.color.color_00B377, "达成", "${formatTaskRate}%")
            progress_bar.progress = rate?.toInt() ?: 0
            if (goal?.equals("--") == true) {
                progress_bar.visibility = View.GONE
                tv_progress_bar_value.visibility = View.GONE
            } else {
                progress_bar.visibility = View.VISIBLE
                tv_progress_bar_value.visibility = View.VISIBLE
            }
        }
        data?.data?.rows?.let {
            if (isLastPage) {
                srl.finishLoadMoreWithNoMoreData()
            }
            if (isRefresh) {
                mAdapter?.setNewData(it)
                if (it.isEmpty()) {
                    svl.showEmpty()
                } else {
                    svl.showContent()
                }
            } else {
                mAdapter?.addData(it)
            }
        }
    }

    override fun onGetTeamTaskFail() {
        if (mIsRefresh) {
            svl.showError()
        }
    }

    override fun showNetError() {
        if (mIsRefresh) {
            svl.showError()
        }
    }

    private fun initRecyclerView() {
        recycler_view.apply {
            layoutManager = androidx.recyclerview.widget.LinearLayoutManager(mContext)
            addItemDecoration(HorizontalDividerItemDecoration.Builder(mContext)
                    .size(ConvertUtils.dp2px(1f))
                    .margin(ConvertUtils.dp2px(0f), ConvertUtils.dp2px(0f))
                    .color(ContextCompat.getColor(mContext, R.color.text_color_F6F6F6))
                    .build())
            mAdapter = TeamTaskAdapter()
            adapter = mAdapter
        }
    }

    private fun refresh() {
        srl.setEnableLoadMore(false)
        mPresenter.getTeamTask(true)
    }

    companion object {
        /**
         * @param activity
         * @param taskID 任务 ID
         */
        @JvmStatic
        fun startActivity(activity: Activity?, taskID: String?) {
            val intent = Intent(activity, TeamTaskActivity::class.java)
            val bundle = Bundle()
            bundle.putString(Constants.Task.ARG_TASK_ID, taskID ?: "")
            intent.putExtras(bundle)
            activity?.startActivity(intent)
        }
    }
}
