package com.ybm100.app.crm.model.home;

import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiService;
import com.ybm100.app.crm.api.LZApiService;
import com.ybm100.app.crm.bean.home.NotificationBean;
import com.ybm100.app.crm.bean.home.PwdFlagBean;
import com.ybm100.app.crm.bean.lzcustomer.LzRoleBean;
import com.ybm100.app.crm.contract.home.MainContract;
import com.ybm100.app.crm.model.CommonModel;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import java.util.HashMap;
import java.util.List;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/19/2018 18:18
 */
public class MainModel extends CommonModel implements MainContract.IMainModel {

    public static MainModel newInstance() {
        return new MainModel();
    }


    @Override
    public Observable<RequestBaseBean<NotificationBean>> requestNotification(HashMap<String, Object> map) {
        return RetrofitCreateHelper.createApi(ApiService.class).requestNotification(map).compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean<PwdFlagBean>> changePwdFlag() {
        return RetrofitCreateHelper.createApi(ApiService.class).changePwdFlag().compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean<List<LzRoleBean>>> getRoles() {
        return RetrofitCreateHelper.createApi(LZApiService.class).getRoles().compose(RxHelper.rxSchedulerHelper());
    }
}