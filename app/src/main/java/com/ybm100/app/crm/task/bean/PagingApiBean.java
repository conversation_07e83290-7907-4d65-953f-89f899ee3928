package com.ybm100.app.crm.task.bean;

import java.util.List;

/**
 * Created by dengmingjia on 2018/12/25
 */
public class PagingApiBean<T> {
    /**
     * 每页条数
     */
    private int limit;
    /**
     * 每条开始数
     */
    private int offset;
    /**
     * 总页数
     */
    private int pageCount;
    /**
     * 总条数
     */
    private int total;
    private List<T> rows;
    private boolean lastPage;

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public int getPageCount() {
        return pageCount;
    }

    public void setPageCount(int pageCount) {
        this.pageCount = pageCount;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public List<T> getRows() {
        return rows;
    }

    public void setRows(List<T> data) {
        this.rows = data;
    }

    public boolean isLastPage() {
        return lastPage;
    }

    public void setLastPage(boolean lastPage) {
        this.lastPage = lastPage;
    }
}
