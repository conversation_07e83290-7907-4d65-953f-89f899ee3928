package com.ybm100.app.crm.utils;

import android.app.Application;
import android.text.TextUtils;
import android.util.Log;

import com.tencent.tinker.entry.ApplicationLike;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Objects;

import okhttp3.ResponseBody;

public class FileUtil {

    /**
     * 将下载的文件写入到指定位置
     * @param body
     * @param targetPath
     * @return
     */
    public static String writeFileToSDCard(ResponseBody body, String targetPath) {
        try {
            File targetFile = new File(targetPath);
            InputStream inputStream = null;
            OutputStream outputStream = null;
            try {
                byte[] fileReader = new byte[4096];
                long fileSize = body.contentLength();
                long fileSizeDownloaded = 0;
                inputStream = body.byteStream();
                outputStream = new FileOutputStream(targetFile);
                while (true) {
                    int read = inputStream.read(fileReader);
                    if (read == -1) {
                        break;
                    }
                    outputStream.write(fileReader, 0, read);
                    fileSizeDownloaded += read;
                    Log.d("download", "file download: " + fileSizeDownloaded + " of " + fileSize);
                }
                outputStream.flush();
                return targetPath;
            } catch (IOException e) {
                return null;
            } finally {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (outputStream != null) {
                    outputStream.close();
                }
            }
        } catch (IOException e) {
            return null;
        }
    }

    /**
     * 根据url生成文件名
     * @param fileUrl
     * @return
     */
    public static String getFileNameFromUrl(String fileUrl) {
        Log.i("getFileNameFromUrl", fileUrl);
        if (TextUtils.isEmpty(fileUrl)) return randomFileName("");
        try {
            if (fileUrl.contains("filename=")) {
                URL url = new URL(fileUrl);
                String query = url.getQuery();
                if (!TextUtils.isEmpty(query)) {
                    String[] queryArr = query.split("&");
                    for (String s : queryArr) {
                        if (!TextUtils.isEmpty(s)) {
                            if (TextUtils.equals(s.split("=")[0], "filename")) {
                                return s.split("=")[1];
                            }
                        }
                    }
                }
                return randomFileName("");
            } else {
                String[] splits = fileUrl.split("/");
                String fileName = splits[splits.length-1];
                if (TextUtils.isEmpty(fileName)) {
                    return randomFileName("");
                } else return fileName;
            }
        } catch (MalformedURLException e) {
            e.printStackTrace();
            return randomFileName("suffix");
        }
    }

    /**
     * 生成随机文件名
     * @param suffix
     * @return
     */
    public static String randomFileName(String suffix) {
        return System.currentTimeMillis() + suffix;
    }

    public static String getExternalFilePath(Application application) {
        return Objects.requireNonNull(application.getExternalFilesDir(null)).getAbsolutePath();
    }
}
