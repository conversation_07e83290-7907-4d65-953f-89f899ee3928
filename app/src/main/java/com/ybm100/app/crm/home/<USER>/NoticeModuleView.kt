package com.ybm100.app.crm.home.module

import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.recyclerview.widget.RecyclerView
import com.xyy.common.util.ConvertUtils
import com.xyy.userbehaviortracking.utils.UserBehaviorTrackingUtils
import com.xyy.utilslibrary.rxbus.RxBus
import com.ybm100.app.crm.R
import com.ybm100.app.crm.bean.home.NotificationBean
import com.ybm100.app.crm.bean.marquee.MarqueeBean
import com.ybm100.app.crm.constant.DrugstoreConstants
import com.ybm100.app.crm.constant.RoleTypeConfig
import com.ybm100.app.crm.constant.RxBusCode
import com.ybm100.app.crm.home.presenter.NoticeModulePresenter
import com.ybm100.app.crm.utils.module.module.BaseModule
import kotlinx.android.synthetic.main.layout_home_module_notice.view.*
import java.util.*

class NoticeModuleView(context: Context) : BaseModule<NoticeModulePresenter>(context), LifecycleObserver {

    private var mStartNotice = true


    override fun getContentLayoutId(): Int {
        return R.layout.layout_home_module_notice
    }

    override fun onInit() {

        getActivity()?.let {
            it.lifecycle.addObserver(this)
        }
    }

    /**
     * 请求通知栏信息
     */
    private fun requestNotificationList() {
        if (RoleTypeConfig.isBDMOrGJRBDM() || RoleTypeConfig.isLzType()) {
//            setMarginBottom()
            dismissNotice()
//            refreshCallback?.refreshFinish(HomeModuleFactory.HomeModule.NOTICE.typeId, mapOf("shouldRemoveMargin" to true))
            return
        }
        val map = HashMap<String?, Any?>()
        map["isDialog"] = 0
        mPresenter?.requestNotification(map)
    }


    fun onRequestNotificationSuccess(bean: NotificationBean?) {
        if (bean == null || bean.msgList == null || bean.msgList.size <= 0) {
            dismissNotice()
//            refreshCallback?.refreshFinish(HomeModuleFactory.HomeModule.NOTICE.typeId, mapOf("shouldRemoveMargin" to true))
            return
        }
        val msgList = bean.msgList
        val list = ArrayList<MarqueeBean>()
        for (i in msgList.indices) {
            val marqueeBean = msgList[i]
            if (marqueeBean.count > 0) { // 数量为0不展示
                val marBean = MarqueeBean()
                marBean.count = marqueeBean.count
                marBean.type = marqueeBean.type
                when (marqueeBean.type) {
                    DrugstoreConstants.NOTIFICATION_IN_PERIOD -> marBean.message = "您当前有" + marqueeBean.count + "个客户资质即将过期。"
                    DrugstoreConstants.NOTIFICATION_OVERDUE -> marBean.message = "您当前有" + marqueeBean.count + "个客户资质已经过期。"
                    else -> {
                    }
                }
                list.add(marBean)
            }
        }
        if (list.size > 0) {
            initNotice(list)
        } else {
            dismissNotice()
        }
    }

    private fun dismissNotice() {
        post {
            refreshCallback?.refreshFinish(HomeModuleFactory.HomeModule.NOTICE.typeId, mapOf("shouldRemoveMargin" to true))
        }
//        cl_notice?.visibility = View.GONE
//        hideSelf()
//        setMarginBottom()
    }

    private fun setMarginBottom() {
        try {
            layoutParams = (layoutParams as RecyclerView.LayoutParams).apply {
                setMargins(0, 0, 0, ConvertUtils.dp2px(5f))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    private fun initNotice(noticeList: List<MarqueeBean>?) {
        showSelf()
        cl_notice?.visibility = View.VISIBLE
        marqueeView?.setOnItemClickListener { position, textView, bean ->

            when (bean.type) {
                DrugstoreConstants.NOTIFICATION_IN_PERIOD, DrugstoreConstants.NOTIFICATION_OVERDUE -> showClientList(bean.type)
                else -> {
                }
            }
        }
        startMarqueeView(noticeList)

        tv_dismiss?.setOnClickListener {
            dismissNotice()

            UserBehaviorTrackingUtils.track("mc-homepage-notice-close")
        }
    }


    private fun showClientList(queryType: Int) {
        val bundle = Bundle()
        bundle.putInt("position", 1)
        bundle.putInt("queryType", queryType)
        RxBus.get().send(RxBusCode.RX_BUS_ENTER_CLIENT_WITH_CONDITIONS, bundle)

        UserBehaviorTrackingUtils.track("mc-homepage-notice-open")
    }

    /**
     * 开启通知轮播
     */
    private fun startMarqueeView(list: List<MarqueeBean>?) {
        if (list == null || list.isEmpty()) return
        marqueeView?.startWithList(list)
        if (marqueeView?.isFlipping == true) {
            marqueeView?.startFlip()
        }
    }


    override fun onRefresh() {
    }

    override fun onRefreshSpecial() {
        super.onRefreshSpecial()
        requestNotificationList()
    }


    @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
    fun onResume() {
        marqueeView?.let {
            if (!it.isFlipping) {
                it.startFlip()
            }
        }
    }

    override fun getPresenter(): Class<NoticeModulePresenter>? {
        return NoticeModulePresenter::class.java
    }

}