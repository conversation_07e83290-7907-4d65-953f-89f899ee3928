package com.ybm100.app.crm.task.fragment;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.xyy.common.widget.DefaultItemDecoration;
import com.xyy.utilslibrary.base.fragment.BaseCompatFragment;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.task.bean.ExecutorLevelItem;
import com.ybm100.app.crm.task.adapter.ExecutorSingleListAdapter;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;

/**
 * Created by dengmingjia on 2019/1/13
 */
public class ExecutorSingleListFragment extends BaseCompatFragment {
    @BindView(R.id.recyclerView)
    RecyclerView mRecyclerView;

    private ExecutorSingleListAdapter mAdapter;
    private ExecutorLevelItem mItem;
    private ExecutorSingleListAdapter.ActionListener mListener;

    @Override
    public int getLayoutId() {
        return R.layout.fragment_executor_list;
    }

    @Override
    public void initUI(View view, @Nullable Bundle savedInstanceState) {
        initRecyclerView();
    }

    private void initRecyclerView() {
        mRecyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        mRecyclerView.addItemDecoration(new DefaultItemDecoration(getContext()));
        mAdapter = new ExecutorSingleListAdapter();
        mRecyclerView.setAdapter(mAdapter);
        loadData();
    }

    private void loadData() {
        if (mAdapter == null) return;
        if (mListener != null) {
            mAdapter.setActionListener(mListener);
        }
        if (mItem != null) {
            List<ExecutorLevelItem> list = new ArrayList<>();
            if (mItem.getChild() != null) {
                list.addAll(mItem.getChild());
            }
            mAdapter.setNewData(list);
        }
    }

    public void setData(ExecutorLevelItem item, ExecutorSingleListAdapter.ActionListener lis) {
        mItem = item;
        mListener = lis;
        loadData();
    }

    public void refreshData() {
        if (mAdapter != null) mAdapter.notifyDataSetChanged();
    }

    @Override
    public boolean onBackPressedSupport() {
        if (mListener != null) {
            mListener.onItemPop(mItem);
        }
        return super.onBackPressedSupport();
    }
}
