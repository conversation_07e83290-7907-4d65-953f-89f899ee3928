package com.ybm100.app.crm.model.lzcustomer;

import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.LZApiService;
import com.ybm100.app.crm.bean.lzcustomer.LzPrivateDetailBean;
import com.ybm100.app.crm.contract.lzcustomer.LzPrivateDetailContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import io.reactivex.Observable;

/**
 * <AUTHOR>
 * @date 2019/1/7
 */
public class LzPrivateDetailModel implements LzPrivateDetailContract.ILzPrivateDetailModel {
    public static LzPrivateDetailModel newInstance() {
        return new LzPrivateDetailModel();
    }


    @Override
    public Observable<RequestBaseBean<LzPrivateDetailBean>> privateSeaCustomerDetail(String id) {
        return RetrofitCreateHelper.createApi(LZApiService.class).privateSeaCustomerDetail(id)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean> releaseCustomer(String id) {
        return RetrofitCreateHelper.createApi(LZApiService.class).releaseCustomer(id)
                .compose(RxHelper.rxSchedulerHelper());
    }

}
