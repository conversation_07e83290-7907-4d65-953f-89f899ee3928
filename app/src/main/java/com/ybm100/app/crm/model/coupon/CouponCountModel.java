package com.ybm100.app.crm.model.coupon;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiDrugstoreService;
import com.ybm100.app.crm.bean.coupon.VoucherBean;
import com.ybm100.app.crm.contract.coupon.CouponCountContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import java.util.HashMap;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 04/16/2019 11:26
 */
public class CouponCountModel extends BaseModel implements CouponCountContract.ICouponCountModel {

    public static CouponCountModel newInstance() {
        return new CouponCountModel();
    }

    @Override
    public Observable<RequestBaseBean<VoucherBean>> getVoucherData(HashMap<String,String> map) {
        return RetrofitCreateHelper.createApi(ApiDrugstoreService.class).getVoucherData(map)
                .compose(RxHelper.rxSchedulerHelper());
    }
}