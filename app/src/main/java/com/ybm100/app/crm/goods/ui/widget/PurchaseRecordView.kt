package com.ybm100.app.crm.goods.ui.widget

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import com.fold.recyclyerview.flexibledivider.HorizontalDividerItemDecoration
import com.xyy.common.util.ConvertUtils
import com.ybm100.app.crm.R
import com.ybm100.app.crm.goods.adapter.PurchaseRecordListAdapter
import com.ybm100.app.crm.goods.bean.PurchaseRecord
import kotlinx.android.synthetic.main.layout_goods_purchase_record.view.*

class PurchaseRecordView : ConstraintLayout {

    var unit = ""
    var adapter: PurchaseRecordListAdapter? = null

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        initContentView(context)
    }

    private fun initContentView(context: Context) {
        LayoutInflater.from(context).inflate(R.layout.layout_goods_purchase_record, this, true)
        setEmptyStatus(true)
    }


    fun setData(purchaseRecordList: List<PurchaseRecord?>?) {
        if (purchaseRecordList.isNullOrEmpty()) {
            setEmptyStatus(true)
        } else {
            setEmptyStatus(false)
            if (purchaseRecordList.size > 3) {
                setPurchaseRecordListData(purchaseRecordList.subList(0, 2))
            } else {
                setPurchaseRecordListData(purchaseRecordList)
            }
        }
    }

    fun setProductUnit(unit: String) {
        this.unit = unit
        adapter?.notifyDataSetChanged()
    }

    private fun setPurchaseRecordListData(subList: List<PurchaseRecord?>) {
        rv_recent_purchase_list.layoutManager = LinearLayoutManager(context)
        rv_recent_purchase_list.adapter = PurchaseRecordListAdapter(subList, unit).also {
            adapter = it
        }
        rv_recent_purchase_list.addItemDecoration(HorizontalDividerItemDecoration.Builder(context)
                .size(ConvertUtils.dp2px(0.5f))
                .margin(ConvertUtils.dp2px(27f), ConvertUtils.dp2px(15f))
                .color(Color.parseColor("#e1e1e5"))
                .build())
    }

    private fun setEmptyStatus(isEmpty: Boolean) {
        rv_recent_purchase_list.visibility = if (isEmpty) View.GONE else View.VISIBLE
        group_empty_status.visibility = if (isEmpty) View.VISIBLE else View.GONE
    }
}