package com.ybm100.app.crm.goods.ui

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.core.content.ContextCompat
import android.text.Editable
import android.text.TextWatcher
import android.view.Gravity
import android.view.View
import com.xyy.common.navigationbar.AbsNavigationBar
import com.xyy.common.navigationbar.DefaultNavigationBar
import com.xyy.common.util.ToastUtils
import com.xyy.common.widget.flowtag.FlowTagLayout
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.activity.BaseMVPCompatActivity
import com.xyy.utilslibrary.rxbus.RxBus
import com.ybm100.app.crm.R
import com.ybm100.app.crm.constant.RxBusCode
import com.ybm100.app.crm.goods.adapter.SellPointTagAdapter
import com.ybm100.app.crm.goods.bean.CustomTagBean
import com.ybm100.app.crm.goods.bean.TagBean
import com.ybm100.app.crm.goods.presenter.AddTagPresenter
import com.ybm100.app.crm.utils.InputFilter.EditUtil
import kotlinx.android.synthetic.main.activity_add_tips.*

/**
 * @author: zcj
 * @time:2020/7/27.
 * Description: 添加自定义标签
 */
class AddTagActivity : BaseMVPCompatActivity<AddTagPresenter>() {
    private var skuId: String? = null
    private var mTagList: ArrayList<TagBean>? = null
    override fun initPresenter(): BasePresenter<*, *> = AddTagPresenter()

    override fun getLayoutId(): Int = R.layout.activity_add_tips

    override fun initHead(): AbsNavigationBar<*> {
        return DefaultNavigationBar.Builder(this).setTitle("添加自定义标签").builder()
    }

    override fun initView(savedInstanceState: Bundle?) {
        skuId = intent.getStringExtra(SP_KEY_SKU_ID)
        mTagList = intent.getParcelableArrayListExtra(SP_KEY_HISTORY_LIST)
        if (skuId.isNullOrEmpty()) {
            showToast("商品id异常")
            finish()
            return
        }

        mPresenter.queryTags()
        EditUtil.setEditTextInhibitInputIllegaCharacter(et_tips, 8, EditUtil.DEFAULT_PATTERN_NAME)
        et_tips.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val length = s?.length ?: 0
                val text = "${length}/8"
                tv_num.text = text
                if (length <= 0) {
                    tv_add_confirm.setBackgroundColor(ContextCompat.getColor(this@AddTagActivity, R.color.color_D8D8D8))
                    tv_add_confirm.isEnabled = false
                } else {
                    tv_add_confirm.setBackgroundColor(ContextCompat.getColor(this@AddTagActivity, R.color.color_00B377))
                    tv_add_confirm.isEnabled = true
                }
            }
        })

        tv_add_confirm.setOnClickListener {
            val content = et_tips.text
            if (content.isNullOrEmpty()) {
                ToastUtils.showLong("请输入标签")
                return@setOnClickListener
            } else {
                mPresenter.addTag(content.toString(), skuId!!)
            }
        }
    }

    fun queryTagListSuccess(tagList: CustomTagBean?) {
        updateTagList(tagList?.rows)
    }

    fun queryTagListFail() {
        group_history.visibility = View.GONE
    }

    fun addTagSuccess(bean: TagBean?) {
        if (bean == null) {
            ToastUtils.showLong("添加失败")
            tfl_history_tags.resetStatus()
        } else {
            val find = mTagList?.find {
                it.label == bean.label
            }
            if (find == null) {
                ToastUtils.showLong("添加成功")
                setResultAndFinish(bean)
            } else {
                ToastUtils.setGravity(Gravity.CENTER, 0, 0)
                ToastUtils.showLong("标签内容不可重复")
            }
        }
    }

    private fun setResultAndFinish(bean: TagBean) {
        val bundle = Bundle()
        bean.closeable = true
        bean.isChecked = false
        bean.isLocal = true
        bundle.putParcelable(SP_KEY_TAG, bean)
        RxBus.get().send(RxBusCode.RX_BUS_TAG_ADD, bundle)
        finish()
    }

    fun toastMsg(msg: String) {
        ToastUtils.showLong(msg)
    }

    private fun updateTagList(tagList: List<TagBean>?) {
        if (tagList.isNullOrEmpty()) {
            group_history.visibility = View.GONE
        } else {
            group_history.visibility = View.VISIBLE
            val adapter = SellPointTagAdapter(R.layout.item_tag_sell_point, tagList)
            tfl_history_tags.setTagCheckedMode(FlowTagLayout.FLOW_TAG_CHECKED_SINGLE)
            tfl_history_tags.setTagShowMode(FlowTagLayout.FLOW_TAG_SHOW_FREE)
            tfl_history_tags.setTagCancelable(true)
            tfl_history_tags.adapter = adapter
            tfl_history_tags.setOnTagClickListener { parent, view, position ->
                val bean = parent.adapter.getItem(position) as TagBean
                val find = mTagList?.find {
                    it.label == bean.label
                }
                if (find == null) {
                    setResultAndFinish(bean)
                } else {
                    ToastUtils.setGravity(Gravity.CENTER, 0, 0)
                    ToastUtils.showLong("标签内容不可重复")
                }
            }
        }
    }

    override fun showNetError() {}

    companion object {
        private const val SP_KEY_SKU_ID = "sp_key_sku_id"
        private const val SP_KEY_HISTORY_LIST = "sp_key_history_list"
        const val SP_KEY_TAG = "sp_key_tag"
        fun start(context: Context?, skuId: String?, tagList: ArrayList<TagBean>?) {
            context?.run {
                val intent = Intent(this, AddTagActivity::class.java).also {
                    it.putExtra(SP_KEY_SKU_ID, skuId)
                    it.putParcelableArrayListExtra(SP_KEY_HISTORY_LIST, tagList)
                }
                startActivity(intent)
            }
        }
    }
}