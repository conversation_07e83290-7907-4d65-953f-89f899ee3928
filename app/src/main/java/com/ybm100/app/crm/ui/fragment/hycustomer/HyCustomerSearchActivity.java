package com.ybm100.app.crm.ui.fragment.hycustomer;

import android.os.Bundle;
import android.text.InputType;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.flyco.tablayout.SlidingTabLayout;
import com.xyy.utilslibrary.adapter.CommonPageAdapter;
import com.xyy.utilslibrary.base.activity.BaseCompatActivity;
import com.xyy.utilslibrary.base.fragment.BaseMVPCompatFragment;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.constant.DrugstoreConstants;
import com.ybm100.app.crm.ui.activity.CustomerViewpager;
import com.ybm100.app.crm.utils.InputFilter.EditUtil;
import com.ybm100.app.crm.widget.popwindow.CustomerDetailPopup;
import com.ybm100.app.crm.widget.searchview.SearchView;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import androidx.viewpager.widget.ViewPager;
import butterknife.BindView;
import butterknife.OnClick;

/**
 * 荷叶健康客户搜索
 */
public class HyCustomerSearchActivity extends BaseCompatActivity {
    @BindView(R.id.search_view)
    SearchView mSearchView;
    @BindView(R.id.ll_container_option)
    LinearLayout mContainerOption;
    @BindView(R.id.tv_title)
    TextView mTvTitle;
    @BindView(R.id.search_tab_layout)
    SlidingTabLayout mSearchTabLayout;
    @BindView(R.id.search_viewpager)
    CustomerViewpager mSearchViewpager;

    private int currTab = DrugstoreConstants.TAB_PUBLIC;
    CommonPageAdapter drugStoreFragmentPagerAdapter;

    private String searchPrivate;
    private String searchPublic;

    private final String [] options=new String[]{"荷叶信息","POI信息"};
    private boolean isHyInfoSearch=true;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_drugstore_search_hy;
    }

    @Override
    protected void initTransferData() {
        super.initTransferData();
        currTab = getIntent().getIntExtra(DrugstoreConstants.INTENT_KEY_TYPE, currTab);
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        initSearchView();
        String[] titles = new String[]{getResources().getString(R.string.private_custom), getResources().getString(R.string.public_custom)};
        List<BaseMVPCompatFragment> drugstoreFragments = new ArrayList<>();
        HyPrivateListFragment fragment = findFragment(HyPrivateListFragment.class);
        if (fragment == null) {
            drugstoreFragments.add(HyPrivateListFragment.newInstance(true));
            drugstoreFragments.add(HyPublicListFragment.newInstance(true));
        } else {
            drugstoreFragments.add(fragment);
            drugstoreFragments.add(findFragment(HyPublicListFragment.class));
        }
        mSearchViewpager.setOffscreenPageLimit(1);
        drugStoreFragmentPagerAdapter = new CommonPageAdapter(getSupportFragmentManager(), drugstoreFragments, titles);
        mSearchViewpager.setAdapter(drugStoreFragmentPagerAdapter);
        mSearchTabLayout.setViewPager(mSearchViewpager, titles);
        mSearchViewpager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int i, float v, int i1) {

            }

            @Override
            public void onPageSelected(int i) {
                currTab = i;
                switch (i) {
                    case 0:
                        mSearchView.setText(searchPrivate);
                        break;
                    case 1:
                        mSearchView.setText(searchPublic);
                        break;
                }
            }

            @Override
            public void onPageScrollStateChanged(int i) {

            }
        });
        mSearchViewpager.setCurrentItem(currTab);
    }


    //初始化搜索
    private void initSearchView() {
        /*KeyboardUtils.showSoftInput(mSearchView.getEt_search());
        InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.toggleSoftInput(0, InputMethodManager.HIDE_NOT_ALWAYS);*/

        //搜索控件隐藏右侧按钮
        mSearchView.setTextHint("荷叶健康的门店ID编号");
        mSearchView.getEt_search().setInputType(InputType.TYPE_CLASS_NUMBER);
        mSearchView.setOnClickSearch(key -> {
            hiddenKeyboard();
            if (drugStoreFragmentPagerAdapter == null) return;
            switch (currTab) {
                case 0:
                    HyPrivateListFragment privateFragment = ((HyPrivateListFragment) drugStoreFragmentPagerAdapter.getItem(currTab));
                    if (privateFragment != null) {
                        privateFragment.search(key,isHyInfoSearch);
                    }
                    searchPrivate = key;
                    break;
                case 1:
                    HyPublicListFragment publicFragment = ((HyPublicListFragment) drugStoreFragmentPagerAdapter.getItem(currTab));
                    if (publicFragment != null) {
                        publicFragment.search(key,isHyInfoSearch);
                    }
                    searchPublic = key;
                    break;
            }
        });
        EditUtil.setEditTextInhibitInputIllegaCharacter(mSearchView.getEt_search(), EditUtil.DEFAULT_PATTERN);
        mSearchView.setOnClickBack(() -> {
            setResult(RESULT_OK);
            finish();
        });
    }

    @Override
    protected void onDestroy() {
        if (drugStoreFragmentPagerAdapter != null) {
            switch (currTab) {
                case DrugstoreConstants.TAB_PUBLIC:
                    ((HyPublicListFragment) drugStoreFragmentPagerAdapter.getItem(currTab)).clearSearch();
                    break;
                case DrugstoreConstants.TAB_PRIVATE:
                    ((HyPrivateListFragment) drugStoreFragmentPagerAdapter.getItem(currTab)).clearSearch();
                    break;
            }
        }
        super.onDestroy();
    }
    @OnClick({R.id.ll_container_option})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ll_container_option://选择poi信息搜索、荷叶信息搜索
                    showDialog();
                break;
        }
    }
    private void showDialog() {
        CustomerDetailPopup detailPopup = new CustomerDetailPopup(mContext, Arrays.asList(options));
        detailPopup.setBackgroundDrawable(R.drawable.pop_bg_right);
        detailPopup.setOnPopItemClickListener(position -> {
            mTvTitle.setText(options[position]);
            isHyInfoSearch=position==0;
            mSearchView.setTextHint(isHyInfoSearch?"荷叶健康的门店ID编号":"客户名称/手机号/POI编号");
            mSearchView.getEt_search().setInputType(isHyInfoSearch? InputType.TYPE_CLASS_NUMBER:InputType.TYPE_CLASS_TEXT);
            mSearchView.setText("");
        });
        detailPopup.showPopupWindow(mTvTitle);
//        int[] location = new int[2];
//        tvTitle.getLocationOnScreen(location);
//        detailPopup.showPopupWindow((int) (location[0] + tvTitle.getWidth() + DisplayUtils.dp2px(15f)),
//                location[1] + tvTitle.getHeight() + DisplayUtils.dp2px(5f));
    }
}
