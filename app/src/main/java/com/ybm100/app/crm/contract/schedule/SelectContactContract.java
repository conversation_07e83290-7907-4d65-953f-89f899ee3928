package com.ybm100.app.crm.contract.schedule;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.schedule.ContactListBean;

import java.util.List;

import io.reactivex.Observable;

public interface SelectContactContract {

    interface ISelectContactModel extends IBaseModel {
        Observable<RequestBaseBean<List<ContactListBean>>> getContactList(String merchantId);
    }

    interface ISelectContactView extends IBaseActivity {
        void getContactListSuccess(List<ContactListBean> bean);
        void getContactListFailed();
    }

}
