package com.ybm100.app.crm.order.adapter;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import android.text.TextUtils;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.xyy.utilslibrary.utils.DisplayUtils;
import com.xyy.utilslibrary.utils.GlideLoadUtils;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.api.ApiUrl;
import com.ybm100.app.crm.utils.ShowBigBitmapPopPublish;

import java.util.List;

/**
 * 门头照
 */

public class PublicDetailPicAdapter extends BaseQuickAdapter<String, BaseViewHolder> {
    public boolean withEdit;
    public static final String EDIT_FLAG = "#add_item#";
    public int maxSize = 3;
    public LinearLayoutManager linearLayoutManager;

    public PublicDetailPicAdapter(int layoutResId, @Nullable List<String> data, boolean withEdit, LinearLayoutManager linearLayoutManager) {
        super(layoutResId, data);
        this.withEdit = withEdit;
        this.linearLayoutManager = linearLayoutManager;
    }

    @Override
    protected void convert(final BaseViewHolder helper, final String item) {
        if (helper == null || item == null) return;
        final ImageView imgCover = helper.getView(R.id.iv_cover);
        RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) imgCover.getLayoutParams();
        lp.width = DisplayUtils.dip2px(mContext, 85);
        lp.height = DisplayUtils.dip2px(mContext, 85);
        imgCover.setLayoutParams(lp);
        imgCover.setImageDrawable(null);
        if (!TextUtils.isEmpty(item)) {
            GlideLoadUtils.loadImg(mContext, imgCover, getUrl(item));
        }
        imgCover.setOnClickListener(v -> new ShowBigBitmapPopPublish(item).show(imgCover));
    }

    /**
     * 检查地址，防止地址异常
     */
    private String getUrl(String url) {
        if (TextUtils.isEmpty(url)) {
            return null;
        }
        if (url.toLowerCase().startsWith("http")) {
            return url;
        }
        if (url.startsWith("/")) {
            return ApiUrl.CDN_URL.concat(url);
        } else {
            return ApiUrl.CDN_URL.concat("/").concat(url);
        }
    }
}
