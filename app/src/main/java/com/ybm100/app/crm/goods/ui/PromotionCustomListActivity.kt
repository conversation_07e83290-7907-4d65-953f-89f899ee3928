package com.ybm100.app.crm.goods.ui

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import com.scwang.smartrefresh.layout.api.RefreshLayout
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener
import com.xyy.common.navigationbar.AbsNavigationBar
import com.xyy.common.navigationbar.DefaultNavigationBar
import com.xyy.common.util.ToastUtils
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.activity.BaseMVPCompatActivity
import com.ybm100.app.crm.R
import com.ybm100.app.crm.goods.bean.CustomListModel
import com.ybm100.app.crm.goods.presenter.PromotionCustomPresenter
import kotlinx.android.synthetic.main.activity_goods_promotion_custom.*

class PromotionCustomListActivity : BaseMVPCompatActivity<PromotionCustomPresenter>(), OnRefreshLoadMoreListener {

    companion object {

        const val INTENT_KEY_GOODS_ID = "goods_id"
        const val INTENT_KEY_PROMO_ID = "promo_id"

        fun newInstance(context: Context, goodsId: String?, promoId: String?) {
            if (goodsId.isNullOrBlank()) {
                ToastUtils.showShort("商品id错误")
                return
            }
            if (promoId.isNullOrBlank()) {
                ToastUtils.showShort("促销id错误")
                return
            }
            context.startActivity(Intent(context, PromotionCustomListActivity::class.java).also {
                it.putExtra(INTENT_KEY_GOODS_ID, goodsId)
                it.putExtra(INTENT_KEY_PROMO_ID, promoId)
            })
        }
    }


    private var goodsId: String? = ""
    private var promoId: String? = ""
    private lateinit var listAdapter: GoodsPromotionCustomListAdapter


    override fun initPresenter(): BasePresenter<*, *> {
        return PromotionCustomPresenter()
    }

    override fun initHead(): AbsNavigationBar<*> {
        return DefaultNavigationBar.Builder(this)
                .setTitle("客户列表")
                .setLeftIcon(R.drawable.nav_return).builder()
    }


    override fun initTransferData() {
        super.initTransferData()
        goodsId = intent.getStringExtra(INTENT_KEY_GOODS_ID)
        promoId = intent.getStringExtra(INTENT_KEY_PROMO_ID)

    }

    override fun getLayoutId(): Int {
        return R.layout.activity_goods_promotion_custom
    }

    override fun initView(savedInstanceState: Bundle?) {
        if (goodsId.isNullOrBlank()) {
            ToastUtils.showShort("商品id错误")
            layout_status_view.showError("商品id错误")
            return
        }
        if (promoId.isNullOrBlank()) {
            ToastUtils.showShort("促销id错误")
            layout_status_view.showError("促销id错误")
            return
        }
        setRefreshLayout(srl_refresh)
        //初始化上拉加载下拉刷新
        srl_refresh.setEnableLoadMore(true)
        srl_refresh.setEnableRefresh(true)
        srl_refresh.setOnRefreshLoadMoreListener(this)
        rv_content.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(this, androidx.recyclerview.widget.LinearLayoutManager.VERTICAL, false)
        listAdapter = GoodsPromotionCustomListAdapter().apply {
            setHeaderAndEmpty(false)
            setEnableLoadMore(false)
        }
        rv_content.adapter = listAdapter
        layout_status_view.setOnRetryListener {
            onRefresh(srl_refresh)
        }
        srl_refresh.setNoMoreData(false)
        onRefresh(srl_refresh)
    }

    private fun requestGoodsList(refresh: Boolean) {
        mPresenter.requestCustomList(refresh, goodsId, promoId)
    }

    fun requestCustomListSuccess(refresh: Boolean, data: CustomListModel?) {
        hideWaitDialog()
        if (data != null) {
            if (refresh && data.rows.isNullOrEmpty()) {
                showEmpty()
                return
            }
            if (refresh) {
                listAdapter.setNewData(data.rows)
            } else {
                listAdapter.addData(data.rows?.toMutableList() ?: emptyList())
            }
            layout_status_view.showContent()
        } else {
            showEmpty()
        }
    }

    fun requestCustomListFail(refresh: Boolean, msg: String?) {
        srl_refresh.finishRefresh(false)
        srl_refresh.finishLoadMore(false)
        if (refresh) {
            layout_status_view.showError()
        } else {
            ToastUtils.showShort(msg)
        }
    }

    override fun showNetError() {

    }

    override fun onLoadMore(refreshLayout: RefreshLayout) {
        requestGoodsList(false)
    }

    override fun onRefresh(refreshLayout: RefreshLayout) {
        listAdapter.setNewData(null)
        requestGoodsList(true)
    }

    fun enableLoadMore(enable: Boolean) {
        srl_refresh.setEnableLoadMore(enable)
    }

    fun loadMoreComplete() {
        srl_refresh.finishLoadMoreWithNoMoreData()
    }

    fun showEmpty() {
        layout_status_view.showEmpty()
        hideWaitDialog()
    }
}