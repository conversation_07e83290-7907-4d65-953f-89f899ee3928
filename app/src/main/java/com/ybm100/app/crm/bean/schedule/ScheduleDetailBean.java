package com.ybm100.app.crm.bean.schedule;

import com.ybm100.app.crm.bean.contact.ContactBean;

/**
 * Created by dengmingjia on 2018/12/27
 * 日程bean
 */
public class ScheduleDetailBean {

    /**
     * domainPath        图片base
     * address	         药店地址	    string	@mock=重庆市天意街道101号
     * contactor	     联系人	    object
     * customer	         药店简要信息	object
     * merchantBasicInfo 药店经营信息	object
     * personalSchedule	 日程信息 	object
     * merchantVisit     药店拜访
     * task
     */
    private String address;
    private String domainPath;
    private ContactBean contactor;
    private Customer customer;
    private MerchantBasicInfo merchantBasicInfo;
    private Schedule personalSchedule;
    private MerchantVisit merchantVisit;
    private Task task;

    public class Customer {
        /**
         * extfield1	是否为会员
         * id	        药店ID	string	@mock=9
         * name	        药店名称	string	@mock=重庆天意医药药房
         * value		        string	@mock=重庆天意医药药房(会员)
         */
        private String extfield1;
        private String id;
        private String name;
        private String value;

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public String getExtfield1() {
            return extfield1;
        }

        public void setExtfield1(String extfield1) {
            this.extfield1 = extfield1;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public class MerchantBasicInfo {
        /**
         * areaSize	药店面积	number	103.33
         * aroundEnvName	周边环境	string	近校园
         * buySkus	药店需求SKU数	number	@mock=0
         * buySkusName	药店需求SKU数	string
         * buyersAmountText	药店人流量（客户流量）	string	50-100
         * buyersTypeName	主要消费人群	string	儿童,青少年
         * clerkNum	药店人数（客户人数）	string	@mock=2
         * frameVarieties	在架品种	string	@mock=啊啊啊啊啊啊啊啊
         * mainlyConsumeMedTypesName	主要消费结构	string	中西成药;养生保健;食品保健;医疗器械
         * medicalInsuranceText	是否有医保	string	是 / 否
         * merchantDemand	商家需求	string	@mock=账期
         * merchantType	客户类型	number
         * merchantTypeName	客户类型	string	@mock=单体
         * monthBuyAmt	月采购数量	string	@mock=
         * monthlySales	月销售额	number	@mock=0
         * needClerkTrainsText	是否需要店员培训	string
         * needMerchantDiagnoseText	是否需要门店诊断	string
         * needPullSalesText	是否需要动销	string	是/否
         * purchaseWay	核心供应商	string	九州通，药师帮
         * remark	日程备注	string	推推推推推推推推
         * shortOfTypes	缺失品类	string	品类1品类2品类3
         */
        private Double areaSize;
        private String aroundEnvName;
        private String buySkus;
        private String buySkusName;
        private String buyersAmountText;
        private String buyersTypeName;
        private String clerkNum;
        private String frameVarieties;
        private String mainlyConsumeMedTypesName;
        private String medicalInsuranceText;
        private String merchantDemand;
        private String merchantType;
        private String merchantTypeName;
        private String monthBuyAmt;
        private Double monthlySales;
        private String needClerkTrainsText;
        private String needMerchantDiagnoseText;
        private String needPullSalesText;
        private String purchaseWay;
        private String remark;
        private String shortOfTypes;

        public Double getAreaSize() {
            return areaSize;
        }

        public void setAreaSize(Double areaSize) {
            this.areaSize = areaSize;
        }

        public String getAroundEnvName() {
            return aroundEnvName;
        }

        public void setAroundEnvName(String aroundEnvName) {
            this.aroundEnvName = aroundEnvName;
        }

        public String getBuySkus() {
            return buySkus;
        }

        public void setBuySkus(String buySkus) {
            this.buySkus = buySkus;
        }

        public String getBuySkusName() {
            return buySkusName;
        }

        public void setBuySkusName(String buySkusName) {
            this.buySkusName = buySkusName;
        }

        public String getBuyersAmountText() {
            return buyersAmountText;
        }

        public void setBuyersAmountText(String buyersAmountText) {
            this.buyersAmountText = buyersAmountText;
        }

        public String getBuyersTypeName() {
            return buyersTypeName;
        }

        public void setBuyersTypeName(String buyersTypeName) {
            this.buyersTypeName = buyersTypeName;
        }

        public String getClerkNum() {
            return clerkNum;
        }

        public void setClerkNum(String clerkNum) {
            this.clerkNum = clerkNum;
        }

        public String getFrameVarieties() {
            return frameVarieties;
        }

        public void setFrameVarieties(String frameVarieties) {
            this.frameVarieties = frameVarieties;
        }

        public String getMainlyConsumeMedTypesName() {
            return mainlyConsumeMedTypesName;
        }

        public void setMainlyConsumeMedTypesName(String mainlyConsumeMedTypesName) {
            this.mainlyConsumeMedTypesName = mainlyConsumeMedTypesName;
        }

        public String getMedicalInsuranceText() {
            return medicalInsuranceText;
        }

        public void setMedicalInsuranceText(String medicalInsuranceText) {
            this.medicalInsuranceText = medicalInsuranceText;
        }

        public String getNeedClerkTrainsText() {
            return needClerkTrainsText;
        }

        public void setNeedClerkTrainsText(String needClerkTrainsText) {
            this.needClerkTrainsText = needClerkTrainsText;
        }

        public String getNeedMerchantDiagnoseText() {
            return needMerchantDiagnoseText;
        }

        public void setNeedMerchantDiagnoseText(String needMerchantDiagnoseText) {
            this.needMerchantDiagnoseText = needMerchantDiagnoseText;
        }

        public String getMerchantDemand() {
            return merchantDemand;
        }

        public void setMerchantDemand(String merchantDemand) {
            this.merchantDemand = merchantDemand;
        }

        public String getMerchantType() {
            return merchantType;
        }

        public void setMerchantType(String merchantType) {
            this.merchantType = merchantType;
        }

        public String getMerchantTypeName() {
            return merchantTypeName;
        }

        public void setMerchantTypeName(String merchantTypeName) {
            this.merchantTypeName = merchantTypeName;
        }

        public String getMonthBuyAmt() {
            return monthBuyAmt;
        }

        public void setMonthBuyAmt(String monthBuyAmt) {
            this.monthBuyAmt = monthBuyAmt;
        }

        public Double getMonthlySales() {
            return monthlySales;
        }

        public void setMonthlySales(Double monthlySales) {
            this.monthlySales = monthlySales;
        }

        public String getNeedPullSalesText() {
            return needPullSalesText;
        }

        public void setNeedPullSalesText(String needPullSalesText) {
            this.needPullSalesText = needPullSalesText;
        }

        public String getPurchaseWay() {
            return purchaseWay;
        }

        public void setPurchaseWay(String purchaseWay) {
            this.purchaseWay = purchaseWay;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getShortOfTypes() {
            return shortOfTypes;
        }

        public void setShortOfTypes(String shortOfTypes) {
            this.shortOfTypes = shortOfTypes;
        }
    }

    public class Schedule {
        /**
         * createTime	    创建时间
         * endTime  	    结束时间
         * id	            日程ID
         * remark	        日程内容
         * scheduleTheme	日程主题
         * startTime	    开始时间
         * taskCode	        任务编号
         * taskId	        任务ID
         * timeType	        时间类型 1 周一 2 周二
         * type	            日程类型 1电话拜访 2上门拜访 5培训 6会议 3私海拜访 4默认拜访
         * userId	        执行人ID
         * visitId	        拜访ID
         */
        private long createTime;
        private long endTime;
        private String id;
        private String remark;
        private String scheduleTheme;
        private long startTime;
        private String taskCode;
        private String taskId;
        private String timeType;
        private int type;
        private String userId;
        private String visitId;
        private long alertTime;
        private String image;
        private Boolean effective; // 是否有效日程
        private String userName; // 执行人姓名

        public long getAlertTime() {
            return alertTime;
        }

        public void setAlertTime(long alertTime) {
            this.alertTime = alertTime;
        }

        public String getImage() {
            return image;
        }

        public void setImage(String image) {
            this.image = image;
        }

        public long getCreateTime() {
            return createTime;
        }

        public void setCreateTime(long createTime) {
            this.createTime = createTime;
        }

        public long getEndTime() {
            return endTime;
        }

        public void setEndTime(long endTime) {
            this.endTime = endTime;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getScheduleTheme() {
            return scheduleTheme;
        }

        public void setScheduleTheme(String scheduleTheme) {
            this.scheduleTheme = scheduleTheme;
        }

        public long getStartTime() {
            return startTime;
        }

        public void setStartTime(long startTime) {
            this.startTime = startTime;
        }

        public String getTaskCode() {
            return taskCode;
        }

        public void setTaskCode(String taskCode) {
            this.taskCode = taskCode;
        }

        public String getTaskId() {
            return taskId;
        }

        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }

        public String getTimeType() {
            return timeType;
        }

        public void setTimeType(String timeType) {
            this.timeType = timeType;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getVisitId() {
            return visitId;
        }

        public void setVisitId(String visitId) {
            this.visitId = visitId;
        }

        public Boolean getEffective() {
            return effective;
        }

        public void setEffective(Boolean effective) {
            this.effective = effective;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }
    }

    public class MerchantVisit {
        /**
         * id : 1554
         * visitNo : null
         * sysUserId : 2296
         * merchantId : 42028
         * merchantName : 测试店铺
         * contactor : 张三
         * mobile : ***********
         * visitTime : 1546963200000
         * merchantType : null
         * visitType : 2
         * businessType : 1
         * visitReason : null
         * visitObjective : null
         * visitDemo : null
         * visitContent : null
         * isEffective : 1
         * isSign : 1
         * isDimprice : 0
         * createType : 0
         * signTime : null
         * status : 1
         * createTime : 1546940989000
         * statusName : 未完成
         * isSaveMap : null
         * visitPlanTime : null
         * beginTime : null
         * endTime : null
         * branchCode : XS500000
         * branchName : 重庆子公司
         * fromType : null
         * sysRealName : 80088001
         * sysJobNumber : 80088001
         * registerCode : null
         * registerCodes : null
         * registerBranch : null
         * registerBranchName : null
         * groupIdArray : null
         * businessTypeName : null
         * isEffectiveName : null
         * isDimpriceName : null
         * visitTypeName : null
         * visitReasonName : null
         * updateTime : 1546940989000
         * image :
         * lng : null
         * lat : null
         * address : null
         * seaCode : null
         * createStartTime : null
         * createEndTime : null
         * branchList : null
         * basicInfo : null
         * userIds : null
         * scheduleId : null
         * scheduleName : null
         */

        private int id;
        private String visitNo;
        private int sysUserId;
        private String merchantName;
        private String contactor;
        private String mobile;
        private long visitTime;
        private String merchantType;
        private int visitType;
        private int businessType;
        private String visitReason;
        private String visitObjective;
        private String visitDemo;
        private String visitContent;
        private int isEffective;
        private int isSign;
        private int isDimprice;
        private int createType;
        private String signTime;
        private int status;
        private long createTime;
        private String statusName;
        private String isSaveMap;
        private String visitPlanTime;
        private String beginTime;
        private String endTime;
        private String branchCode;
        private String branchName;
        private String fromType;
        private String sysRealName;
        private String sysJobNumber;
        private String registerCode;
        private String registerCodes;
        private String registerBranch;
        private String registerBranchName;
        private String groupIdArray;
        private String businessTypeName;
        private String isEffectiveName;
        private String isDimpriceName;
        private String visitTypeName;
        private String visitReasonName;
        private long updateTime;
        private String image;
        private String lng;
        private String lat;
        private String address;
        private String seaCode;
        private String createStartTime;
        private String createEndTime;
        private String branchList;
        private String basicInfo;
        private String userIds;
        private String scheduleId;
        private String scheduleName;
        private String talkTimeText;
        private String accompanyName;

        public String getAccompanyName() {
            return accompanyName;
        }

        public void setAccompanyName(String accompanyName) {
            this.accompanyName = accompanyName;
        }

        public String getTalkTimeText() {
            return talkTimeText;
        }

        public void setTalkTimeText(String talkTimeText) {
            this.talkTimeText = talkTimeText;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getVisitNo() {
            return visitNo;
        }

        public void setVisitNo(String visitNo) {
            this.visitNo = visitNo;
        }

        public int getSysUserId() {
            return sysUserId;
        }

        public void setSysUserId(int sysUserId) {
            this.sysUserId = sysUserId;
        }

        public String getMerchantName() {
            return merchantName;
        }

        public void setMerchantName(String merchantName) {
            this.merchantName = merchantName;
        }

        public String getContactor() {
            return contactor;
        }

        public void setContactor(String contactor) {
            this.contactor = contactor;
        }

        public String getMobile() {
            return mobile;
        }

        public void setMobile(String mobile) {
            this.mobile = mobile;
        }

        public long getVisitTime() {
            return visitTime;
        }

        public void setVisitTime(long visitTime) {
            this.visitTime = visitTime;
        }

        public String getMerchantType() {
            return merchantType;
        }

        public void setMerchantType(String merchantType) {
            this.merchantType = merchantType;
        }

        public int getVisitType() {
            return visitType;
        }

        public void setVisitType(int visitType) {
            this.visitType = visitType;
        }

        public int getBusinessType() {
            return businessType;
        }

        public void setBusinessType(int businessType) {
            this.businessType = businessType;
        }

        public String getVisitReason() {
            return visitReason;
        }

        public void setVisitReason(String visitReason) {
            this.visitReason = visitReason;
        }

        public String getVisitObjective() {
            return visitObjective;
        }

        public void setVisitObjective(String visitObjective) {
            this.visitObjective = visitObjective;
        }

        public String getVisitDemo() {
            return visitDemo;
        }

        public void setVisitDemo(String visitDemo) {
            this.visitDemo = visitDemo;
        }

        public String getVisitContent() {
            return visitContent;
        }

        public void setVisitContent(String visitContent) {
            this.visitContent = visitContent;
        }

        public int getIsEffective() {
            return isEffective;
        }

        public void setIsEffective(int isEffective) {
            this.isEffective = isEffective;
        }

        public int getIsSign() {
            return isSign;
        }

        public void setIsSign(int isSign) {
            this.isSign = isSign;
        }

        public int getIsDimprice() {
            return isDimprice;
        }

        public void setIsDimprice(int isDimprice) {
            this.isDimprice = isDimprice;
        }

        public int getCreateType() {
            return createType;
        }

        public void setCreateType(int createType) {
            this.createType = createType;
        }

        public String getSignTime() {
            return signTime;
        }

        public void setSignTime(String signTime) {
            this.signTime = signTime;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public long getCreateTime() {
            return createTime;
        }

        public void setCreateTime(long createTime) {
            this.createTime = createTime;
        }

        public String getStatusName() {
            return statusName;
        }

        public void setStatusName(String statusName) {
            this.statusName = statusName;
        }

        public String getIsSaveMap() {
            return isSaveMap;
        }

        public void setIsSaveMap(String isSaveMap) {
            this.isSaveMap = isSaveMap;
        }

        public String getVisitPlanTime() {
            return visitPlanTime;
        }

        public void setVisitPlanTime(String visitPlanTime) {
            this.visitPlanTime = visitPlanTime;
        }

        public String getBeginTime() {
            return beginTime;
        }

        public void setBeginTime(String beginTime) {
            this.beginTime = beginTime;
        }

        public String getEndTime() {
            return endTime;
        }

        public void setEndTime(String endTime) {
            this.endTime = endTime;
        }

        public String getBranchCode() {
            return branchCode;
        }

        public void setBranchCode(String branchCode) {
            this.branchCode = branchCode;
        }

        public String getBranchName() {
            return branchName;
        }

        public void setBranchName(String branchName) {
            this.branchName = branchName;
        }

        public String getFromType() {
            return fromType;
        }

        public void setFromType(String fromType) {
            this.fromType = fromType;
        }

        public String getSysRealName() {
            return sysRealName;
        }

        public void setSysRealName(String sysRealName) {
            this.sysRealName = sysRealName;
        }

        public String getSysJobNumber() {
            return sysJobNumber;
        }

        public void setSysJobNumber(String sysJobNumber) {
            this.sysJobNumber = sysJobNumber;
        }

        public String getRegisterCode() {
            return registerCode;
        }

        public void setRegisterCode(String registerCode) {
            this.registerCode = registerCode;
        }

        public String getRegisterCodes() {
            return registerCodes;
        }

        public void setRegisterCodes(String registerCodes) {
            this.registerCodes = registerCodes;
        }

        public String getRegisterBranch() {
            return registerBranch;
        }

        public void setRegisterBranch(String registerBranch) {
            this.registerBranch = registerBranch;
        }

        public String getRegisterBranchName() {
            return registerBranchName;
        }

        public void setRegisterBranchName(String registerBranchName) {
            this.registerBranchName = registerBranchName;
        }

        public String getGroupIdArray() {
            return groupIdArray;
        }

        public void setGroupIdArray(String groupIdArray) {
            this.groupIdArray = groupIdArray;
        }

        public String getBusinessTypeName() {
            return businessTypeName;
        }

        public void setBusinessTypeName(String businessTypeName) {
            this.businessTypeName = businessTypeName;
        }

        public String getIsEffectiveName() {
            return isEffectiveName;
        }

        public void setIsEffectiveName(String isEffectiveName) {
            this.isEffectiveName = isEffectiveName;
        }

        public String getIsDimpriceName() {
            return isDimpriceName;
        }

        public void setIsDimpriceName(String isDimpriceName) {
            this.isDimpriceName = isDimpriceName;
        }

        public String getVisitTypeName() {
            return visitTypeName;
        }

        public void setVisitTypeName(String visitTypeName) {
            this.visitTypeName = visitTypeName;
        }

        public String getVisitReasonName() {
            return visitReasonName;
        }

        public void setVisitReasonName(String visitReasonName) {
            this.visitReasonName = visitReasonName;
        }

        public long getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(long updateTime) {
            this.updateTime = updateTime;
        }

        public String getImage() {
            return image;
        }

        public void setImage(String image) {
            this.image = image;
        }

        public String getLng() {
            return lng;
        }

        public void setLng(String lng) {
            this.lng = lng;
        }

        public String getLat() {
            return lat;
        }

        public void setLat(String lat) {
            this.lat = lat;
        }

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public String getSeaCode() {
            return seaCode;
        }

        public void setSeaCode(String seaCode) {
            this.seaCode = seaCode;
        }

        public String getCreateStartTime() {
            return createStartTime;
        }

        public void setCreateStartTime(String createStartTime) {
            this.createStartTime = createStartTime;
        }

        public String getCreateEndTime() {
            return createEndTime;
        }

        public void setCreateEndTime(String createEndTime) {
            this.createEndTime = createEndTime;
        }

        public String getBranchList() {
            return branchList;
        }

        public void setBranchList(String branchList) {
            this.branchList = branchList;
        }

        public String getBasicInfo() {
            return basicInfo;
        }

        public void setBasicInfo(String basicInfo) {
            this.basicInfo = basicInfo;
        }

        public String getUserIds() {
            return userIds;
        }

        public void setUserIds(String userIds) {
            this.userIds = userIds;
        }

        public String getScheduleId() {
            return scheduleId;
        }

        public void setScheduleId(String scheduleId) {
            this.scheduleId = scheduleId;
        }

        public String getScheduleName() {
            return scheduleName;
        }

        public void setScheduleName(String scheduleName) {
            this.scheduleName = scheduleName;
        }
    }

    public class Task {
        /**
         * code		string	@mock=2018112632659
         * createTime		number	@mock=1543215161000
         * creatorId		number	@mock=1
         * creatorName		string	@mock=管理员
         * customerId		number	@mock=16
         * customerName		string	@mock=武汉可仁堂大药房有限公司
         * customerType		number	@mock=1
         * endTime		number	@mock=1546181700000
         * id		number	@mock=32659
         * imageUrl
         * lastVisitTime
         * parentId		number	@mock=1543215160504
         * remark		string	@mock=Admin任务02Admin任务02Admin任务02
         * status		number	@mock=0
         * sysUserId		number	@mock=1
         * sysUserName
         * theme		string	@mock=Admin任务02
         * type		number	@mock=2
         * vidioUrl
         */
        private String code;
        private String createTime;
        private String creatorId;
        private String creatorName;
        private String customerId;
        private String customerName;
        private String customerType;
        private long endTime;
        private String id;
        private String imageUrl;
        private String lastVisitTime;
        private String parentId;
        private String remark;
        private String status;
        private String sysUserId;
        private String sysUserName;
        private String theme;
        private String type;
        private String vidioUrl;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getCreatorId() {
            return creatorId;
        }

        public void setCreatorId(String creatorId) {
            this.creatorId = creatorId;
        }

        public String getCreatorName() {
            return creatorName;
        }

        public void setCreatorName(String creatorName) {
            this.creatorName = creatorName;
        }

        public String getCustomerId() {
            return customerId;
        }

        public void setCustomerId(String customerId) {
            this.customerId = customerId;
        }

        public String getCustomerName() {
            return customerName;
        }

        public void setCustomerName(String customerName) {
            this.customerName = customerName;
        }

        public String getCustomerType() {
            return customerType;
        }

        public void setCustomerType(String customerType) {
            this.customerType = customerType;
        }

        public long getEndTime() {
            return endTime;
        }

        public void setEndTime(long endTime) {
            this.endTime = endTime;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getImageUrl() {
            return imageUrl;
        }

        public void setImageUrl(String imageUrl) {
            this.imageUrl = imageUrl;
        }

        public String getLastVisitTime() {
            return lastVisitTime;
        }

        public void setLastVisitTime(String lastVisitTime) {
            this.lastVisitTime = lastVisitTime;
        }

        public String getParentId() {
            return parentId;
        }

        public void setParentId(String parentId) {
            this.parentId = parentId;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getSysUserId() {
            return sysUserId;
        }

        public void setSysUserId(String sysUserId) {
            this.sysUserId = sysUserId;
        }

        public String getSysUserName() {
            return sysUserName;
        }

        public void setSysUserName(String sysUserName) {
            this.sysUserName = sysUserName;
        }

        public String getTheme() {
            return theme;
        }

        public void setTheme(String theme) {
            this.theme = theme;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getVidioUrl() {
            return vidioUrl;
        }

        public void setVidioUrl(String vidioUrl) {
            this.vidioUrl = vidioUrl;
        }
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public ContactBean getContactor() {
        return contactor;
    }

    public void setContactor(ContactBean contactor) {
        this.contactor = contactor;
    }

    public Customer getCustomer() {
        return customer;
    }

    public void setCustomer(Customer customer) {
        this.customer = customer;
    }

    public MerchantBasicInfo getMerchantBasicInfo() {
        return merchantBasicInfo;
    }

    public void setMerchantBasicInfo(MerchantBasicInfo merchantBasicInfo) {
        this.merchantBasicInfo = merchantBasicInfo;
    }

    public Task getTask() {
        return task;
    }

    public void setTask(Task task) {
        this.task = task;
    }

    public String getDomainPath() {
        return domainPath;
    }

    public void setDomainPath(String domainPath) {
        this.domainPath = domainPath;
    }

    public Schedule getPersonalSchedule() {
        return personalSchedule;
    }

    public void setPersonalSchedule(Schedule personalSchedule) {
        this.personalSchedule = personalSchedule;
    }

    public MerchantVisit getMerchantVisit() {
        return merchantVisit;
    }

    public void setMerchantVisit(MerchantVisit merchantVisit) {
        this.merchantVisit = merchantVisit;
    }
}
