package com.ybm100.app.crm.task.activity;

import android.content.Intent;
import androidx.annotation.NonNull;

import com.xyy.utilslibrary.base.BasePresenter;
import com.ybm100.app.crm.task.bean.TaskExecutorBean;
import com.ybm100.app.crm.task.presenter.TaskSearchExecutorPresenter;
import com.ybm100.app.crm.ui.activity.BaseSearchActivity;
import com.ybm100.app.crm.task.adapter.BaseSearchAdapter;

import java.util.ArrayList;

import static com.ybm100.app.crm.constant.ExtraConstants.DATA;

/**
 * Created by dengmingjia on 2018/12/26
 * 选择执行人页面
 */
public class TaskSearchExecutorActivity extends BaseSearchActivity<TaskSearchExecutorPresenter, TaskExecutorBean> {
//    LinkedHashMap<Long, TaskExecutorBean> mSelectedMap = new LinkedHashMap<>();

    @Override
    public BaseSearchAdapter.OnSelectedListener<TaskExecutorBean> getOnSelectedListener() {
        return new BaseSearchAdapter.OnSelectedListener<TaskExecutorBean>() {
            @Override
            public boolean isSelected(TaskExecutorBean item) {
//                return mSelectedMap.containsKey(item.getId());
                return false;
            }

            @Override
            public void onSelected(TaskExecutorBean item) {
//                if (mSelectedMap.containsKey(item.getId())) {
//                    mSelectedMap.remove(item.getId());
//                } else {
//                    mSelectedMap.put(item.getId(), item);
//                }
//                mAdapter.notifyDataSetChanged();
                ArrayList result = new ArrayList();
                result.add(item);
                Intent intent = new Intent();
                intent.putExtra(DATA, result);
                setResult(RESULT_OK, intent);
                finish();

            }

            @Override
            public BaseSearchAdapter.SelectStatus selectStatus(TaskExecutorBean taskExecutorBean) {
                return null;
            }
        };
    }

    @Override
    protected void onStartSearch() {
//        mSelectedMap.clear();
    }

    @Override
    protected void onSubmit() {
//        ArrayList result = new ArrayList();
//        for (TaskExecutorBean bean : mSelectedMap.values()) {
//            result.add(bean);
//        }
//        Intent intent = new Intent();
//        intent.putExtra(EXTRA_DATA, result);
//        setResult(RESULT_OK, intent);
//        finish();
    }

    @NonNull
    @Override
    public BasePresenter initPresenter() {
        return new TaskSearchExecutorPresenter();
    }

    @Override
    public void showNetError() {

    }
}
