package com.ybm100.app.crm.order.model

import com.xyy.utilslibrary.base.BaseModel
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.api.ApiService
import com.ybm100.app.crm.contract.BaseExecutorContract
import com.ybm100.app.crm.net.RetrofitCreateHelper
import com.ybm100.app.crm.order.bean.OrderExecutorBean
import io.reactivex.Observable

/**
 * Created by dengmingjia on 2019/1/4
 */
class OrderExecutorModel(private val isTeamTask: Boolean = false, private val taskID: String = "",private val isPop: Boolean = false) : BaseModel(), BaseExecutorContract.IModel<OrderExecutorBean> {
    override fun getData(): Observable<RequestBaseBean<OrderExecutorBean>>? {
        return if (isTeamTask){
            RetrofitCreateHelper.createApi(ApiService::class.java).getTaskExecutors(taskID)
        }else if(isPop){
            RetrofitCreateHelper.createApi(ApiService::class.java).popOrderExecutors
        }else{
            RetrofitCreateHelper.createApi(ApiService::class.java).orderExecutors
        }
    }

    override fun getUserLevel(oaId: String): Observable<RequestBaseBean<Boolean>> {
        return RetrofitCreateHelper.createApi(ApiService::class.java).getUserLevel(oaId)
    }
}