package com.ybm100.app.crm.presenter.drugstore;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.contact.ContactBean;
import com.ybm100.app.crm.bean.drugstore.PublicCustomerDetailBean;
import com.ybm100.app.crm.contract.drugstore.PublicCustomerDetailContract;
import com.ybm100.app.crm.model.drugstore.BaseInfoPublicCustomerDetailModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

import java.util.List;

import io.reactivex.disposables.Disposable;

/**
 * 基本信息未注册Presenter
 */
public class BaseInfoPublicCustomerDetailPresenter extends BasePresenter<PublicCustomerDetailContract.IBaseInfoPublicCustomerDetailModel, PublicCustomerDetailContract.IBaseInfoPublicCustomerDetailView> {
    public static BaseInfoPublicCustomerDetailPresenter newInstance() {
        return new BaseInfoPublicCustomerDetailPresenter();
    }

    @Override
    protected BaseInfoPublicCustomerDetailModel getModel() {
        return BaseInfoPublicCustomerDetailModel.newInstance();
    }

    /**
     * 公海详情
     *
     * @param id 客户ID
     */
    public void searchOpenSeaDetail(String id) {
        if (mIView == null || mIModel == null) return;
        Disposable subscribe = mIModel.searchOpenSeaDetail(id)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean<PublicCustomerDetailBean>>(mIView, "") {
                    @Override
                    public void onSuccess(RequestBaseBean<PublicCustomerDetailBean> baseBean) {
                        if (mIView == null) return;
                        mIView.searchOpenSeaDetailSuccess(baseBean);
                    }
                }, new SimpleErrorConsumer(mIView));
        mRxManager.register(subscribe);
    }

    /**
     * 认领
     *
     * @param id 客户ID
     */
    public void receive(String id) {
        // do nothing
    }
    /**
     * 荷叶基本信息联系人列表
     */
    public void getContactList(String poiId) {
        String json="[\n" +
                "    {\n" +
                "        \"id\": 117065,\n" +
                "        \"merchantId\": 1500124084,\n" +
                "        \"poiId\": 171525,\n" +
                "        \"contactName\": \"fw\",\n" +
                "        \"contactMobile\": \"18611767335\",\n" +
                "        \"contactJob\": \"Boss\",\n" +
                "        \"createTime\": 1628070761000,\n" +
                "        \"updateTime\": 1628070761000,\n" +
                "        \"merchantName\": \"郭先生测客户资质湖北武汉一店\",\n" +
                "        \"contactSex\": 1,\n" +
                "        \"contactBirth\": 1628006400000,\n" +
                "        \"contactTag\": \"\",\n" +
                "        \"disabled\": 0,\n" +
                "        \"searchIndex\": \"郭先生测客户资质湖北武汉一店fw18611767335\",\n" +
                "        \"createType\": 1,\n" +
                "        \"creator\": 3042,\n" +
                "        \"contactJobName\": \"老板\",\n" +
                "        \"isEffective\": 2\n" +
                "    },\n" +
                "    {\n" +
                "        \"id\": 117064,\n" +
                "        \"merchantId\": 1500124084,\n" +
                "        \"poiId\": 171525,\n" +
                "        \"contactName\": \"注册电话\",\n" +
                "        \"contactMobile\": \"18518611111\",\n" +
                "        \"createTime\": 1628065733000,\n" +
                "        \"updateTime\": 1628065733000,\n" +
                "        \"merchantName\": \"郭先生测客户资质湖北武汉一店\",\n" +
                "        \"disabled\": 0,\n" +
                "        \"createType\": 0,\n" +
                "        \"contactJobName\": \"其他\",\n" +
                "        \"isEffective\": 2\n" +
                "    }\n" +
                "]";
//        RequestBaseBean<List<ContactBean>> baseBean=new RequestBaseBean<>();
//        baseBean.setData(new Gson().fromJson(json,new TypeToken<List<ContactBean>>(){}.getType()));
//        mIView.getContactListSuccess(baseBean);
        if (mIView == null || mIModel == null) return;
        Disposable subscribe = mIModel.getContactList(poiId)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean<List<ContactBean>>>(mIView, "") {
                    @Override
                    public void onSuccess(RequestBaseBean<List<ContactBean>> baseBean) {
                        if (mIView == null) return;
                        mIView.getContactListSuccess(baseBean);
                    }
                }, new SimpleErrorConsumer(mIView));
        mRxManager.register(subscribe);
    }
}
