package com.ybm100.app.crm.home.presenter

import android.annotation.SuppressLint
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.helper.RxHelper
import com.ybm100.app.crm.api.ApiService
import com.ybm100.app.crm.bean.home.NotificationBean
import com.ybm100.app.crm.home.module.NoticeModuleView
import com.ybm100.app.crm.net.RetrofitCreateHelper
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer
import com.ybm100.app.crm.utils.module.presenter.BaseModulePresenter
import java.util.*

class NoticeModulePresenter : BaseModulePresenter<NoticeModuleView>() {


    @SuppressLint("CheckResult")
    fun requestNotification(map: HashMap<String?, Any?>?) {
        if (mIView == null) return

        mRxManager.register(RetrofitCreateHelper.createApi(ApiService::class.java)
                .requestNotification(map)
                .compose(RxHelper.rxSchedulerHelper())
                .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<NotificationBean?>?>(mIView) {

                    override fun onSuccess(bean: RequestBaseBean<NotificationBean?>?) {
                        mIView?.onRequestNotificationSuccess(bean?.data)
                    }

                    override fun onFailure(errorCode: Int) {
                        mIView?.onRequestNotificationSuccess(null)
                    }
                }, SimpleErrorConsumer(mIView)))
    }
}
