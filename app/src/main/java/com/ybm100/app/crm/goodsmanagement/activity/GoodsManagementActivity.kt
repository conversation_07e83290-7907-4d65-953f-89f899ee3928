package com.ybm100.app.crm.goodsmanagement.activity

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import androidx.fragment.app.Fragment
import androidx.viewpager.widget.ViewPager
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.xyy.common.util.PreferencesUtil
import com.xyy.userbehaviortracking.utils.UserBehaviorTrackingUtils
import com.xyy.utilslibrary.adapter.CommonPageAdapter
import com.ybm100.app.crm.R
import com.ybm100.app.crm.constant.Constants
import com.ybm100.app.crm.goodsmanagement.bean.GoodsManagementAreaListBean
import com.ybm100.app.crm.goodsmanagement.bean.Zone
import com.ybm100.app.crm.goodsmanagement.fragment.GoodsManagementFilterFragment
import com.ybm100.app.crm.goodsmanagement.fragment.GoodsManagementFragment
import kotlinx.android.synthetic.main.activity_goods_management_content_main.*
import kotlinx.android.synthetic.main.guide_view.*

@Route(path = "/crm/mine/GoodsManagement")
class GoodsManagementActivity : BaseDrawerActivity(), GoodsManagementFilterFragment.DrawerListener {
    private var mTabTitles = arrayOf("我的收藏", "全部商品")
    private var mFragmentList = mutableListOf<Fragment>()

    override fun getContentMainLayoutID(): Int {
        return R.layout.activity_goods_management_content_main
    }

    override fun initContentMain() {
        ARouter.getInstance().inject(this)

        mFragmentList.add(GoodsManagementFragment.newInstance(Bundle().apply {
            putInt(Constants.GoodsManagement.ARG_FRAGMENT_TYPE, Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION)
        }))
        mFragmentList.add(GoodsManagementFragment.newInstance(Bundle().apply {
            putInt(Constants.GoodsManagement.ARG_FRAGMENT_TYPE, Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_ALL_GOODS)
        }))

        view_pager.apply {
            isHorizontallyScrollable = false
            adapter = CommonPageAdapter(supportFragmentManager, mFragmentList, mTabTitles)
            offscreenPageLimit = mTabTitles.size
        }

        stl_tab.apply {
            setViewPager(view_pager)
        }

        stl_tab.currentTab = intent?.extras?.getInt(Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_SELECTED_TAB, Constants.GoodsManagement.CONSTANT_GOODS_MANAGEMENT_TAB_MY_COLLECTION)
                ?: Constants.GoodsManagement.CONSTANT_GOODS_MANAGEMENT_TAB_MY_COLLECTION

        iv_back.setOnClickListener {
            finish()
        }
        ic_search.setOnClickListener {
            if (stl_tab.currentTab == 0) {
                BaseSearchActivity.startActivity(this, Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_SEARCH, areaCode = (mFragmentList[0] as GoodsManagementFragment).mAreaCode)

                UserBehaviorTrackingUtils.track("mc-myreserve-search")
            } else {
                BaseSearchActivity.startActivity(this, Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_ALL_GOODS_SEARCH, areaCode = (mFragmentList[1] as GoodsManagementFragment).mAreaCode)
            }

        }

        view_pager.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrollStateChanged(p0: Int) {

            }

            override fun onPageScrolled(p0: Int, p1: Float, p2: Int) {

            }

            override fun onPageSelected(p0: Int) {
                if (p0 == 1){
                    if (PreferencesUtil.get(Constants.GoodsManagement.KEY_SHOULD_SHOW_GUIDE_VIEW, true)){
                        val guideView = LayoutInflater.from(mContext).inflate(R.layout.guide_view, null)
                        val decorView = window.decorView as FrameLayout
                        decorView.addView(guideView)

                        cl_guide_view.setOnClickListener {
                            cl_guide_view.visibility = View.GONE
                        }

                        PreferencesUtil.put(Constants.GoodsManagement.KEY_SHOULD_SHOW_GUIDE_VIEW, false)
                    }
                }
            }
        })
    }

    override fun getDrawerFragments(): List<Fragment> {
        return listOf<Fragment>(GoodsManagementFilterFragment.newInstance(Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION, null).apply {
            setDrawerListener(this@GoodsManagementActivity)
        })
    }

    /**
     * 发现 - 商品管理
     * 我的收藏删除，调用全部商品刷新
     * 全部商品收藏，调用我的收藏刷新
     */
    fun refresh(pos: Int){
        (mFragmentList[pos] as GoodsManagementFragment).refresh()
    }

    companion object {
        /**
         * @param activity
         * @param selectedTab 选中的tab
         */
        @JvmStatic
        fun startActivity(activity: Activity?, selectedTab: Int? = Constants.GoodsManagement.CONSTANT_GOODS_MANAGEMENT_TAB_MY_COLLECTION) {
            val intent = Intent(activity, GoodsManagementActivity::class.java)
            val bundle = Bundle()
            bundle.putInt(Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_SELECTED_TAB, selectedTab
                    ?: Constants.GoodsManagement.CONSTANT_GOODS_MANAGEMENT_TAB_MY_COLLECTION)
            intent.putExtras(bundle)
            activity?.startActivity(intent)
        }
    }

    override fun onConfirmPressed(itemArea: GoodsManagementAreaListBean.Row?, pos: Int, selectedZone: Zone?) {
        closeDrawer()
        repeat(mFragmentList.size){
            (mFragmentList[it] as GoodsManagementFragment).onDrawerConfirmPressed(itemArea, selectedZone)
        }
    }
}