package com.ybm100.app.crm.presenter.license;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.license.DeliveryAddressBean;
import com.ybm100.app.crm.contract.license.LicenseSelectAreaContract;
import com.ybm100.app.crm.model.license.LicenseSelectAreaModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

import java.util.List;

/**
 * Created by XyyMvpSportTemplate on 01/11/2019 10:46
 */
public class SelectAreaPresenter extends BasePresenter<LicenseSelectAreaContract.ILicenseSelectAreaModel, LicenseSelectAreaContract.ILicenseSelectAreaView> {

    @Override
    protected LicenseSelectAreaContract.ILicenseSelectAreaModel getModel() {
        return new LicenseSelectAreaModel();
    }

    /**
     * 获取收货地址
     *
     * @param areaCode
     */
    public void getAddress(String areaCode) {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.getAddress(areaCode).subscribe(new SimpleSuccessConsumer<RequestBaseBean<List<DeliveryAddressBean>>>(mIView) {
            @Override
            public void onSuccess(RequestBaseBean<List<DeliveryAddressBean>> requestBaseBean) {
                mIView.getAddressSuccess(requestBaseBean);
            }

            @Override
            public void onFailure(int errorCode) {
                mIView.showNetError();
            }
        }, new SimpleErrorConsumer(mIView) {
            @Override
            protected void onError(Throwable throwable, String msg) {
                mIView.showNetError();
                mIView.showToast(msg);
            }
        }));
    }
}
