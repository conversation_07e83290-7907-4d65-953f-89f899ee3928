package com.ybm100.app.crm.goods.presenter

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.helper.RxHelper
import com.ybm100.app.crm.goods.api.GoodsApiService
import com.ybm100.app.crm.goods.bean.CustomTagBean
import com.ybm100.app.crm.goods.bean.TagBean
import com.ybm100.app.crm.goods.ui.AddTagActivity
import com.ybm100.app.crm.net.RetrofitCreateHelper
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer

/**
 * @author: zcj
 * @time:2020/7/28.
 * Description:
 */
class AddTagPresenter : BasePresenter<Any, AddTagActivity>() {
    override fun getModel(): Any {
        return Any()
    }

    fun addTag(label: String, skuId: String) {
        val subscribe = RetrofitCreateHelper.createApi(GoodsApiService::class.java)
                .addTags(label, skuId)
                .compose(RxHelper.rxSchedulerHelper())
                .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<TagBean?>?>(mIView, "正在加载中...") {
                    override fun onSuccess(bean: RequestBaseBean<TagBean?>?) {
                        mIView?.addTagSuccess(bean?.data)
                    }

                    override fun onFailure(errorCode: Int) {
                        mIView?.toastMsg("保存失败")
                    }
                }, object : SimpleErrorConsumer(mIView) {
                })
        mRxManager.register(subscribe)
    }

    fun queryTags() {
        val subscribe = RetrofitCreateHelper.createApi(GoodsApiService::class.java)
                .queryHistoryTags()
                .compose(RxHelper.rxSchedulerHelper())
                .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<CustomTagBean>?>(mIView, "正在加载中...") {
                    override fun onSuccess(bean: RequestBaseBean<CustomTagBean>?) {
                        mIView?.queryTagListSuccess(bean?.data)

                    }

                    override fun onFailure(errorCode: Int) {
                        mIView?.queryTagListFail()
                    }
                }, object : SimpleErrorConsumer(mIView) {
                    override fun onError(throwable: Throwable?, msg: String?) {
                        super.onError(throwable, msg)
                        mIView?.queryTagListFail()
                    }
                })
        mRxManager.register(subscribe)
    }
}