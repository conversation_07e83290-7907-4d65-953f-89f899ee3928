package com.ybm100.app.crm.presenter.contact;

import android.content.Context;
import android.text.TextUtils;

import com.xyy.common.util.ToastUtils;
import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.rxbus.RxBus;
import com.xyy.utilslibrary.utils.ResourcesUtils;
import com.xyy.utilslibrary.utils.StringUtils;
import com.xyy.utilslibrary.utils.TimeUtils;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.contact.ContactBean;
import com.ybm100.app.crm.bean.contact.WrapCallLogBean;
import com.ybm100.app.crm.contract.contact.ContactDetailContract;
import com.ybm100.app.crm.contract.contact.CreateContactContract;
import com.ybm100.app.crm.model.contact.CreateContactModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;
import com.ybm100.app.crm.utils.CallLogUtil;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import io.reactivex.Observable;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;

import static com.ybm100.app.crm.constant.RxBusCode.RX_BUS_UPDATE_CONTACT_BOOK;
import static com.ybm100.app.crm.model.contact.ContactConstants.SPLIT_TAG;
import static com.ybm100.app.crm.utils.InputFilter.EditUtil.CONTACT_TAG_PATTERN;

/**
 * Created by XyyMvpSportTemplate on 12/25/2018 14:02
 */
public class ContactDetailPresenter extends BasePresenter<CreateContactContract.ICreateContactModel, ContactDetailContract.IContactDetailView> {

    private static final int PHONES_TYPE_INDEX = 0;
    private static final int PHONES_DATE_INDEX = 1;
    private static final int PHONES_DURATION_INDEX = 2;

    public static ContactDetailPresenter newInstance() {
        return new ContactDetailPresenter();
    }

    @Override
    protected CreateContactModel getModel() {
        return CreateContactModel.newInstance();
    }

    ContactBean contact = null;

    public ContactBean getContact() {
        return contact;
    }

    public void getContactDetail(ContactBean contact) {
        mIView.renderContactDetail(contact);
    }

    public void addTag(final String trim) {
        String reg = CONTACT_TAG_PATTERN;
        Pattern pattern = Pattern.compile(reg, Pattern.DOTALL);
        Matcher matcher = pattern.matcher(trim);
        if (!matcher.matches()) {
            mIView.showToast("字数不能超过8个，中文，数字，英文字母，相对常用的标点逗号，句号，冒号，括号，叹号");
            return;
        }

        String contactTag = contact.getContactTag();
        if (!TextUtils.isEmpty(contactTag)) {
            String[] split = contactTag.split(SPLIT_TAG);
            if (split.length >= 10) {
                mIView.showToast("标签数量不能超过10个");
                return;
            }
        }
        if (contact != null) {
            mIView.showWaitDialog(ResourcesUtils.getString(R.string.adding_tag));
            mRxManager.register(mIModel.updateContact(contact, false, trim)
                    .subscribe(new SimpleSuccessConsumer<RequestBaseBean<ContactBean>>(mIView) {
                        @Override
                        public void onSuccess(RequestBaseBean<ContactBean> response) {
                            String tags = contact.getContactTag();
                            StringBuilder sb = new StringBuilder(tags != null ? tags : "");
                            if (!TextUtils.isEmpty(tags)) {
                                sb.append(",");
                            }
                            sb.append(trim);
                            contact.setContactTag(sb.toString());
                            mIView.renderContactDetail(contact);
                            RxBus.get().send(RX_BUS_UPDATE_CONTACT_BOOK, response.getData());
                        }
                    }, new SimpleErrorConsumer(mIView)));
        }
    }

    public void delTag(String tag) {
        //更新
        String tags = contact.getContactTag();
        if (TextUtils.isEmpty(tags)) {
            mIView.updateTags(contact);
            return;
        }
        String[] split = tags.split(SPLIT_TAG);

        ArrayList<String> objects = new ArrayList<>();
        for (String s : split) {
            objects.add(s);
        }

        Iterator<String> iterator = objects.iterator();
        while (iterator.hasNext()) {
            String next = iterator.next();
            if (TextUtils.equals(next, tag)) {
                iterator.remove();
            }
        }

        String s = StringUtils.listSplitByChar(objects, SPLIT_TAG.charAt(0));
        contact.setContactTag(s);
        mIView.showWaitDialog(ResourcesUtils.getString(R.string.del_tag));
        mRxManager.register(mIModel.delTag(contact, false)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean>(mIView) {
                    @Override
                    public void onSuccess(RequestBaseBean response) {
                        mIView.updateTags(contact);
                        RxBus.get().send(RX_BUS_UPDATE_CONTACT_BOOK, contact);
                    }
                }, new SimpleErrorConsumer(mIView)));
    }

    public void getCallRecord(final Context activity, String phoneNo) {
        ArrayList<String> objects = new ArrayList<>();
        objects.add(phoneNo);
        Observable<WrapCallLogBean> callLogs = CallLogUtil.getCallLogs(activity, objects, TimeUtils.DATE_FORMAT_YMDHMS);
        Disposable subscribe = callLogs.subscribe(new Consumer<WrapCallLogBean>() {
            @Override
            public void accept(WrapCallLogBean wrapCallLogBean) throws Exception {
                mIView.renderContactCallRecord(wrapCallLogBean);
            }
        },new Consumer<Throwable>() {
            @Override
            public void accept(Throwable throwable) throws Exception {
                if (throwable != null) {
                    throwable.printStackTrace();
                    ToastUtils.showShort("查询通话记录失败");
                }
            }
        });

        mRxManager.register(subscribe);

    }


    public void setData(ContactBean contact) {
        this.contact = contact;
    }
}
