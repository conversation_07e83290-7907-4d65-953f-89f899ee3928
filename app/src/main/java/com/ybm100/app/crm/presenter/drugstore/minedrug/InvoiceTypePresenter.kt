package com.ybm100.app.crm.presenter.drugstore.minedrug

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.bean.drugstore.minedrugstore.InvoiceBean
import com.ybm100.app.crm.contract.drugstore.minedrug.InvoiceTypeContract
import com.ybm100.app.crm.model.drugstore.minedrug.InvoiceTypeModel
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer

/**
 * Created by XyyMvpYkqTemplate on 07/29/2019 11:33
 */
class InvoiceTypePresenter : BasePresenter<InvoiceTypeContract.IInvoiceTypeModel, InvoiceTypeContract.IInvoiceTypeView>() {

    override fun getModel(): InvoiceTypeModel = InvoiceTypeModel()

    fun getInvoiceData(map: HashMap<String, Any>, isShowLoading: Boolean, forceRefresh: Boolean) {
        mRxManager.register(mIModel.reqInvoiceData(map)
                .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<InvoiceBean>>(mIView, isShowLoading) {
                    override fun onSuccess(t: RequestBaseBean<InvoiceBean>?) {
                        //数据请求成功
                        if (t != null) {
                            mIView.reqInvoiceSuccess(t.data, forceRefresh)
                        }
                    }
                }, SimpleErrorConsumer(mIView)))
    }
}
