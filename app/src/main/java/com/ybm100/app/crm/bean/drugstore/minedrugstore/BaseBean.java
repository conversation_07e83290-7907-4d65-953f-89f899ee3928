package com.ybm100.app.crm.bean.drugstore.minedrugstore;

import android.text.TextUtils;

/**
 * author :lx
 * date 2019/1/7.
 * email： <EMAIL>
 */
public class BaseBean {

    public String status;
    public String errorMsg;
    public int errorCode;
    public String msg;
    public boolean isFromCache;
    public CommonDialog dialog;

    public boolean isSuccess(){
        if(TextUtils.isEmpty(status)){
            return false;
        }
        return  status.toLowerCase().equals("success");
    }

    public String getErrMsg(){
        return errorMsg;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setErrMsg(String errMsg) {
        this.errorMsg = errMsg;
    }
}
