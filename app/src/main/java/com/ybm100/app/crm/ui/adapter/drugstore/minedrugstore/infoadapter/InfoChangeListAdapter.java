package com.ybm100.app.crm.ui.adapter.drugstore.minedrugstore.infoadapter;

import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.LogItem;
import com.ybm100.app.crm.utils.FormatUtils;

import java.util.List;

/**
 * author :lx
 * date 2018/12/29.
 * email： <EMAIL>
 */
public class InfoChangeListAdapter extends BaseQuickAdapter<LogItem, BaseViewHolder> {

    public InfoChangeListAdapter(int layoutResId, @Nullable List<LogItem> data) {
        super(layoutResId, data);
    }

    public InfoChangeListAdapter(@Nullable List<LogItem> data) {
        super(data);
    }

    public InfoChangeListAdapter(int layoutResId) {
        super(layoutResId);
    }

    @Override
    protected void convert(BaseViewHolder helper, LogItem item) {
        helper.setText(R.id.tv_info_text_change_oldValue, FormatUtils.textFormat(item.getOldValue()))
                .setText(R.id.tv_info_text_change_newValue, FormatUtils.textFormat(item.getNewValue()))
                .setText(R.id.tv_type, FormatUtils.textFormat(item.getFieldName()));
    }
}
