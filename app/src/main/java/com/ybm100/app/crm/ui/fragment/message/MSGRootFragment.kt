package com.ybm100.app.crm.ui.fragment.message

import android.graphics.Color
import android.graphics.Typeface
import android.os.Build
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import me.yokeyword.fragmentation.SupportFragment

import com.ybm100.app.crm.R
import com.ybm100.app.crm.ui.adapter.messge.MSGRootAdapter

/**
 * 消息
 */
class MSGRootFragment : SupportFragment(), TabLayout.OnTabSelectedListener {

    private lateinit var rootAdapter: MSGRootAdapter
    private lateinit var rootContainer: ViewPager2
    private lateinit var tabLayout: TabLayout

    private val tabTitles = arrayOf("通知消息", "客服消息")
    private val tabDots = arrayOf(View.GONE, View.GONE)

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return inflater.inflate(R.layout.fragment_message_root, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        rootAdapter = MSGRootAdapter(this)
        rootContainer = view.findViewById(R.id.container)
        rootContainer.adapter = rootAdapter

        tabLayout = view.findViewById(R.id.tabs)
        tabLayout.addOnTabSelectedListener(this)

        TabLayoutMediator(tabLayout, rootContainer) { tab, position ->
            tab.setCustomView(R.layout.message_tab_item)
            tab.customView?.findViewById<TextView>(R.id.tab_name)?.text = tabTitles[position]
            tab.customView?.findViewById<ImageView>(R.id.tab_dot)?.visibility = tabDots[position]
        }.attach()
    }

    @RequiresApi(Build.VERSION_CODES.M)
    override fun onTabSelected(tab: TabLayout.Tab?) {
        tab?.position?.also {
            val text = tabTitles[it]
            val tabSS = SpannableString(text)
            tabSS.setSpan(ForegroundColorSpan(Color.parseColor("#292933")), 0, text.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            tabSS.setSpan(StyleSpan(Typeface.BOLD), 0, text.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)

            tab.customView?.findViewById<TextView>(R.id.tab_name)?.text = tabSS
        }
    }

    override fun onTabUnselected(tab: TabLayout.Tab?) {
        val index = tab?.position ?: -1
        if (index >= 0) {
            tab?.customView?.findViewById<TextView>(R.id.tab_name)?.text = tabTitles[index]
        }
    }

    override fun onTabReselected(tab: TabLayout.Tab?) {
        // do nothing
    }

    fun updateRedPoint(notificationNum: Int, sessionNum: Int) {
        if (notificationNum > 0) {
            tabDots[0] = View.VISIBLE
        }

        if (sessionNum > 0) {
            tabDots[1] = View.VISIBLE
        }

        tabLayout.getTabAt(0)?.customView?.findViewById<ImageView>(R.id.tab_dot)?.visibility = tabDots[0]
        tabLayout.getTabAt(1)?.customView?.findViewById<ImageView>(R.id.tab_dot)?.visibility = tabDots[1]
    }

    fun setSelection(index: Int){
        tabLayout.getTabAt(index)?.select()
        rootContainer.setCurrentItem(index, false)
    }
}