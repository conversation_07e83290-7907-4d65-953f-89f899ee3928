package com.ybm100.app.crm.ui.adapter.schedule;

import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.ValueBean;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by dengmingjia on 2019/1/13
 */
public class MultipleSelectListAdapter extends BaseQuickAdapter<ValueBean, BaseViewHolder> {
    public ActionListener mActionListener;
    private final List<ValueBean> valueBeans = new ArrayList<>();
    private final List<ValueBean> removeValueBeans = new ArrayList<>();

    public MultipleSelectListAdapter() {
        super(R.layout.item_multiple_layout);
    }

    public void setActionListener(ActionListener actionListener) {
        this.mActionListener = actionListener;
    }

    @Override
    protected void convert(final BaseViewHolder helper, final ValueBean item) {
        helper.setText(R.id.tv_name, item.getValue());
        if (item.isChecked()) {
            helper.setImageResource(R.id.iv_check, R.drawable.icon_radio_btn_selected);
        } else {
            helper.setImageResource(R.id.iv_check, R.drawable.icon_radio_btn_normal);
        }

        helper.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                boolean isChecked = item.isChecked();
                item.setChecked(!isChecked);
                notifyItemChanged(helper.getAdapterPosition());
                if (item.isChecked()) {
                    valueBeans.add(item);
                    removeValueBeans.remove(item);
                } else {
                    removeValueBeans.add(item);
                    valueBeans.remove(item);
                }
                mActionListener.onItemSelect(valueBeans);
                mActionListener.onItemRemove(removeValueBeans);
            }
        });
    }


    public interface ActionListener {
        void onItemSelect(List<ValueBean> items);

        void onItemRemove(List<ValueBean> items);
    }

}
