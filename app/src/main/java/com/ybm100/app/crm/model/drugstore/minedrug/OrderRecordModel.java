package com.ybm100.app.crm.model.drugstore.minedrug;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiDrugstoreInfo;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.DrugstoreOrderBean;
import com.ybm100.app.crm.contract.drugstore.minedrug.OrderRecordContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:09
 */
public class OrderRecordModel extends BaseModel implements OrderRecordContract.IOrderRecordModel {

    public static OrderRecordModel newInstance() {
        return new OrderRecordModel();
    }

    @Override
    public Observable<RequestBaseBean<DrugstoreOrderBean>> getOrderListData(int limit, String merchantId, int offset) {
        return RetrofitCreateHelper.createApi(ApiDrugstoreInfo.class).getOrderListData(limit, merchantId, offset)
                .compose(RxHelper.rxSchedulerHelper());
    }
}