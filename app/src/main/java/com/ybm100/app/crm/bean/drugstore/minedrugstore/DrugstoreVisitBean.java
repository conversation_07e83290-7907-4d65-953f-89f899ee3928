package com.ybm100.app.crm.bean.drugstore.minedrugstore;

import java.io.Serializable;
import java.util.List;
/**
 * author :lx
 * date 2018/12/27.
 * email： <EMAIL>
 */
public class DrugstoreVisitBean implements Serializable {

    private int limit;        //number	@mock=10
    private int offset;        //number	@mock=0
    private List<VisitItemBean> rows;        //array<object>
    private int total;    //总条数	number	@mock=74
    private boolean lastPage;

    public boolean isLastPage() {
        return lastPage;
    }

    public void setLastPage(boolean lastPage) {
        this.lastPage = lastPage;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public List<VisitItemBean> getRows() {
        return rows;
    }

    public void setRows(List<VisitItemBean> rows) {
        this.rows = rows;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }
}
