package com.ybm100.app.crm.ui.fragment.share;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.xyy.common.navigationbar.AbsNavigationBar;
import com.xyy.common.navigationbar.DefaultNavigationBar;
import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.activity.BaseMVPCompatActivity;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.share.BranchBean;
import com.ybm100.app.crm.contract.share.ShareRootContract;
import com.ybm100.app.crm.order.bean.TagBean;
import com.ybm100.app.crm.presenter.share.ShareRootPresenter;
import com.ybm100.app.crm.ui.activity.share.ShareSearchActivityActivity;
import com.ybm100.app.crm.widget.popwindow.ShareRangePopup;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * <AUTHOR>
 * @date 2019/3/6
 * 发现
 */
@Route(path = "/crm/mine/ShareToWeChatCircle")
public class ShareRootActivity extends BaseMVPCompatActivity<ShareRootPresenter> implements ShareRootContract.IShareRootView {

    @BindView(R.id.rb_select_range)
    RadioButton rbSelectRange;
    @BindView(R.id.rb_share_times)
    TextView rbShareTimes;
    @BindView(R.id.container_share)
    LinearLayout containerShare;
    private ShareRangePopup mRangePopup;
    private List<TagBean> tagBeanList;
    private ShareListFragment listFragment;
    private List<BranchBean> branchBeanList;
    private boolean desc = true;//默认降序
    private BranchBean selectBean = new BranchBean();
    private TagBean selectTagBean;

    @Override
    public int getLayoutId() {
        return R.layout.fragment_share_root;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        ARouter.getInstance().inject(this);

        mPresenter.getTopGroups();
    }

    @Override
    protected AbsNavigationBar initHead() {
        return new DefaultNavigationBar.Builder(this)
                .setTitle("一键发圈")
                .setRightIcon(R.drawable.icon_search)
                .setRightClickListener(v -> {
                    //跳转搜索页
                    startActivity(new Intent(mContext, ShareSearchActivityActivity.class));
                }).builder();
    }

    @OnClick({R.id.rb_select_range, R.id.rb_share_times})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.rb_select_range:
                if (rbSelectRange.isChecked()) {
                    rbSelectRange.setChecked(false);
                }
                initPopWindow(view);
                break;
            case R.id.rb_share_times:
                rbShareTimes.setSelected(desc);
                desc = !desc;
                if (listFragment != null)
                    listFragment.filter(desc ? "1" : "2", selectBean.getBranchCode(), true);
                break;
        }
    }

    private void initPopWindow(View v) {
        if (mRangePopup == null) {
            if (tagBeanList == null) return;
            mRangePopup = new ShareRangePopup(this, tagBeanList);
            mRangePopup.setOnPopItemClickListener(new ShareRangePopup.OnPopItemClickListener() {
                @Override
                public void onItemClick(TagBean tagBean, int position) {
                    boolean isrefresh = false;
                    if (selectTagBean != null && selectTagBean.getTagKey() == tagBean.getTagKey()) {
                        tagBean.setChecked(true);
                        tagBeanList.set(position, tagBean);
                        mRangePopup.refresh(tagBeanList);
                        isrefresh = false;
                    } else {
                        selectTagBean = tagBean;
                        isrefresh = true;
                    }
                    rbSelectRange.setText(tagBean.getTagName());
                    rbSelectRange.setChecked(false);
                    selectBean = branchBeanList.get(tagBean.getTagKey());
                    if (listFragment != null)
                        listFragment.filter(desc ? "1" : "2", selectBean.getBranchCode(), isrefresh);
                }
            });
        }
        mRangePopup.showPopupWindow(v);
    }

    @Override
    public void getTopGroupsSuccess(RequestBaseBean<List<BranchBean>> baseBean) {
        if (baseBean != null && baseBean.getData().size() > 0) {
            branchBeanList = baseBean.getData();
            selectBean = branchBeanList.get(0);
            tagBeanList = new ArrayList<>();
            if (branchBeanList == null) return;
            for (int i = 0; i < branchBeanList.size(); i++) {
                TagBean tagBean = new TagBean(branchBeanList.get(i).getBranchName(), i, i == 0, i == 0);
                tagBeanList.add(tagBean);
            }
        }
        rbSelectRange.setText(selectBean.getBranchName());
        listFragment = ShareListFragment.newInstance(desc ? "1" : "2", selectBean.getBranchCode());
        loadRootFragment(R.id.container_share, listFragment);
    }

    @NonNull
    @Override
    public BasePresenter initPresenter() {
        return ShareRootPresenter.newInstance();
    }

    @Override
    public void showNetError() {
        rbSelectRange.setText(selectBean.getBranchName());
        listFragment = ShareListFragment.newInstance(desc ? "1" : "2", selectBean.getBranchCode());
        loadRootFragment(R.id.container_share, listFragment);
    }
}
