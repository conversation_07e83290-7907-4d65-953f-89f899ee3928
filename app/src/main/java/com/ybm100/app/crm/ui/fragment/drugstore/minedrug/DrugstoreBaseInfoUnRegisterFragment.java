package com.ybm100.app.crm.ui.fragment.drugstore.minedrug;

import android.Manifest;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.alibaba.android.arouter.launcher.ARouter;
import com.tbruyelle.rxpermissions2.Permission;
import com.tbruyelle.rxpermissions2.RxPermissions;
import com.xyy.common.util.ToastUtils;
import com.xyy.common.widget.GridSpacingItemDecoration;
import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.base.fragment.BaseMVPCompatFragment;
import com.xyy.utilslibrary.rxbus.RxBus;
import com.xyy.utilslibrary.rxbus.Subscribe;
import com.xyy.utilslibrary.utils.DisplayUtils;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.contact.ContactBean;
import com.ybm100.app.crm.bean.drugstore.PublicCustomerDetailBean;
import com.ybm100.app.crm.constant.DrugstoreConstants;
import com.ybm100.app.crm.constant.RoleTypeConfig;
import com.ybm100.app.crm.constant.RxBusCode;
import com.ybm100.app.crm.contract.drugstore.PublicCustomerDetailContract;
import com.ybm100.app.crm.order.adapter.PublicDetailPicAdapter;
import com.ybm100.app.crm.permission.PermissionUtil;
import com.ybm100.app.crm.presenter.drugstore.BaseInfoPublicCustomerDetailPresenter;
import com.ybm100.app.crm.ui.adapter.drugstore.ContactBaseInfoAdapter;
import com.ybm100.app.crm.ui.fragment.hycontact.HyContactActivity;
import com.ybm100.app.crm.utils.CallUtil;
import com.ybm100.app.crm.utils.FormatUtils;
import com.ybm100.app.crm.utils.HyRouterPath;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.Group;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;
import butterknife.OnClick;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;

import static com.ybm100.app.crm.ui.activity.message.ContactActivity.EDIT;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:04
 * 基本信息
 */
public class DrugstoreBaseInfoUnRegisterFragment extends BaseMVPCompatFragment<BaseInfoPublicCustomerDetailPresenter> implements PublicCustomerDetailContract.IBaseInfoPublicCustomerDetailView {
    @BindView(R.id.tv_customer_name)
    TextView tvCustomerName;
    @BindView(R.id.tv_customer_type)
    TextView tvCustomerType;
    @BindView(R.id.tv_detail_address)
    TextView tvDetailAddress;
    @BindView(R.id.tv_business_time)
    TextView tvBusinessTime;
    @BindView(R.id.tv_phone)
    TextView tvPhone;
    @BindView(R.id.tv_mobile_phone)
    TextView tvMobilePhone;
    @BindView(R.id.tv_remark)
    TextView tvRemark;
    @BindView(R.id.recycle_view_photo)
    RecyclerView recycleViewPhoto;
    @BindView(R.id.layout_claim)
    LinearLayout layoutClaim;
    @BindView(R.id.tv_bd)
    TextView tvBd;
    @BindView(R.id.bd_group)
    Group bdGroup;
    @BindView(R.id.tv_poi_id)
    TextView tvPoiId;
    //荷叶健康 联系人
    @BindView(R.id.rl_expand)
    RelativeLayout mRlExpand;
    @BindView(R.id.tv_expand)
    TextView mTvExpand;
    @BindView(R.id.iv_expand)
    ImageView mIvExpand;
    @BindView(R.id.recycler_contact)
    RecyclerView mRvContact;
    @BindView(R.id.ll_contact)
    LinearLayout llContact;
    //联系人
    private boolean expand;
    private ContactBaseInfoAdapter mAdapter;
    private List<ContactBean> topList;
    private List<ContactBean> allList;
    private ConstraintLayout layout;
    //客户id
    private String id;
    //poiId
    private String poiId;
    //是否来自荷叶健康页面
    private boolean isFromHY;
    private String customerName;

    public static DrugstoreBaseInfoUnRegisterFragment newInstance(String merchantId) {
        Bundle args = new Bundle();
        DrugstoreBaseInfoUnRegisterFragment fragment = new DrugstoreBaseInfoUnRegisterFragment();
        args.putString(DrugstoreConstants.INTENT_KEY_OPENSEAID, merchantId);
        fragment.setArguments(args);
        return fragment;
    }
    public static DrugstoreBaseInfoUnRegisterFragment newInstance(String merchantId,boolean isFromHY,String poiId) {
        Bundle args = new Bundle();
        DrugstoreBaseInfoUnRegisterFragment fragment = new DrugstoreBaseInfoUnRegisterFragment();
        args.putString(DrugstoreConstants.INTENT_KEY_OPENSEAID, merchantId);
        args.putBoolean(DrugstoreConstants.INTENT_ACTION_FROM_HY,isFromHY);
        args.putString(DrugstoreConstants.INTENT_KEY_POI_ID,poiId);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public int getLayoutId() {
        return R.layout.activity_private_detail_base_info;
    }

    @NonNull
    @Override
    public BasePresenter initPresenter() {
        return BaseInfoPublicCustomerDetailPresenter.newInstance();
    }

    @Override
    public void initUI(View view, @Nullable Bundle savedInstanceState) {
        if (getArguments()!=null){
            id = getArguments().getString(DrugstoreConstants.INTENT_KEY_OPENSEAID);
            poiId = getArguments().getString(DrugstoreConstants.INTENT_KEY_POI_ID);
            isFromHY = getArguments().getBoolean(DrugstoreConstants.INTENT_ACTION_FROM_HY,false);
        }
        layoutClaim.setVisibility(View.GONE);
    }

    @Override
    public void onLazyInitView(@Nullable Bundle savedInstanceState) {
        super.onLazyInitView(savedInstanceState);
        mPresenter.searchOpenSeaDetail(id);
        if (isFromHY){
            llContact.setVisibility(View.VISIBLE);
            mPresenter.getContactList(poiId);
        }
    }

    @Override
    public void searchOpenSeaDetailSuccess(RequestBaseBean<PublicCustomerDetailBean> baseBean) {
        if (baseBean == null || baseBean.getData() == null) return;
        PublicCustomerDetailBean detailBean = baseBean.getData();
        customerName=detailBean.getCustomerName();
        if (2 == detailBean.getRegisterFlag()) {
            //详细地址
            tvDetailAddress.setText(FormatUtils.textFormat(detailBean.getAddress()));
        } else {
            //详细地址
            tvDetailAddress.setText(FormatUtils.textFormat(detailBean.getEcAddress()));
        }
        //门店名称(poi地址)
        tvCustomerName.setText(FormatUtils.textFormat(TextUtils.isEmpty(detailBean.getEcCustomerName()) ? detailBean.getCustomerName() : detailBean.getEcCustomerName()));
        //门店品类
        tvCustomerType.setText(FormatUtils.textFormat(detailBean.getPoiCategory()));
        //营业时间
        tvBusinessTime.setText(FormatUtils.textFormat(detailBean.getBusinessHours()));
        //座机电话
        tvPhone.setText(FormatUtils.textFormat(detailBean.getPoiPhone()));
        //手机号码
        tvMobilePhone.setText(FormatUtils.textFormat(detailBean.getPoiContactMobile()));
        //备注说明
        tvRemark.setText(FormatUtils.textFormat(detailBean.getPoiRemark()));
        // 门店ID
        tvPoiId.setText(FormatUtils.textFormat(detailBean.getPoiId()));
        //BD
        StringBuffer text = new StringBuffer();
        for (int i = 0; detailBean.getBdInfos() != null && i < detailBean.getBdInfos().size(); i++) {
            String bd_name = detailBean.getBdInfos().get(i).getRealName();
            text.append(bd_name);
            if (i < detailBean.getBdInfos().size() - 1) {
                text.append("、");
            }
        }
        tvBd.setText(FormatUtils.textFormat(text.toString()));
        //门头照
        GridLayoutManager linearLayoutManager = new GridLayoutManager(mContext, 3);
        GridSpacingItemDecoration itemDecoration = new GridSpacingItemDecoration(3, getResources().getDimensionPixelOffset(R.dimen.dp10), getResources().getDimensionPixelOffset(R.dimen.dp10), true);
        recycleViewPhoto.addItemDecoration(itemDecoration);
        recycleViewPhoto.setLayoutManager(linearLayoutManager);
        if (!TextUtils.isEmpty(detailBean.getPoiImageUrl())) {
            String[] imageStr = detailBean.getPoiImageUrl().split(",");
            List<String> imageInfo = new ArrayList<>(Arrays.asList(imageStr));
            PublicDetailPicAdapter adapter = new PublicDetailPicAdapter(R.layout.item_image_door_photo, imageInfo, false, linearLayoutManager);
            recycleViewPhoto.setAdapter(adapter);
        }
        //是否被领取 1是 2否 未被认领展示认领按钮
        if (detailBean.getReceiveType() == 2 && !RoleTypeConfig.isGJRGrop()&&!isFromHY) {
            layoutClaim.setVisibility(View.VISIBLE);
        } else {
            layoutClaim.setVisibility(View.GONE);
        }
        //荷叶健康隐藏BD字段
        bdGroup.setVisibility(isFromHY?View.GONE:View.VISIBLE);
    }

    @Override
    public void receiveSuccess(RequestBaseBean baseBean,String merchantId) {
    }

    // 荷叶健康基本信息联系人列表
    @Override
    public void getContactListSuccess(RequestBaseBean<List<ContactBean>> contactList) {
        /********联系人************/
        allList = contactList.getData();
        if(allList==null){
            allList=new ArrayList<>();
        }
//        if (allList != null) {
//            llContact.setVisibility(View.VISIBLE);
            LinearLayoutManager layoutManager = new LinearLayoutManager(mContext);
            mRvContact.setLayoutManager(layoutManager);
            if (allList.size() >= 2) {
                mRlExpand.setVisibility(View.VISIBLE);
                topList = allList.subList(0, 2);
            } else {
                mRlExpand.setVisibility(View.GONE);
                topList = allList;
            }
            mAdapter = new ContactBaseInfoAdapter(R.layout.item_contact_baseinfo, expand ? allList : topList);
            mRvContact.setAdapter(mAdapter);
            View addView = LayoutInflater.from(mActivity).inflate(R.layout.item_contact_add, null);
            layout = addView.findViewById(R.id.layout_add_contact);
            View line = addView.findViewById(R.id.line);
            if (allList.size() == 0) {
                line.setVisibility(View.GONE);
            }
            layout.setLayoutParams(new ConstraintLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, DisplayUtils.dp2px(51)));
            if (!expand && allList.size() >= 2) {
                layout.setVisibility(View.GONE);
            } else {
                layout.setVisibility(View.VISIBLE);
            }
            layout.setOnClickListener(v -> {
                if (contactList != null && !TextUtils.isEmpty(poiId)) {
                    ContactBean contactBean = new ContactBean();
                    contactBean.setPoiId(poiId);
                    contactBean.setPoiName(customerName);
                    ARouter.getInstance()
                            .build(HyRouterPath.HY_CONTACT_BOOK_PAGE)
                            .withInt(HyContactActivity.TYPE, HyContactActivity.CREATE_CONTACT)
                            .withSerializable("bean", contactBean)
                            .withBoolean(EDIT, false)
                            .navigation();
                }
            });
            mAdapter.addFooterView(addView);
            mAdapter.setOnItemChildClickListener((adapter, view, position) -> {
                ContactBean contact = (ContactBean) adapter.getData().get(position);
                switch (view.getId()) {
                    case R.id.iv_edit_contact:
                        RxPermissions rxPermissions = new RxPermissions(getActivity());
                        Disposable subscribe = rxPermissions.requestEach(Manifest.permission.READ_CALL_LOG).subscribe(new Consumer<Permission>() {
                            @Override
                            public void accept(Permission permission) throws Exception {
                                if (permission.granted) {
                                    ARouter.getInstance()
                                            .build(HyRouterPath.HY_CONTACT_BOOK_PAGE)
                                            .withInt(HyContactActivity.TYPE, HyContactActivity.CONTACT_DETAIL)
                                            .withSerializable("bean", contact)
                                            .withBoolean(EDIT, true)
                                            .navigation();
                                } else if (permission.shouldShowRequestPermissionRationale) {
                                    ToastUtils.showShort(getString(R.string.please_open_call_log_permission));
                                } else {
                                    PermissionUtil.showPermissionDialog(mContext, getString(R.string.phone_permission_name), false);
                                }
                            }
                        });
                        addDisposable(subscribe);
                        break;
                    case R.id.iv_call_phone:
                        CallUtil.call(mActivity, contact.getContactMobile(), id, contact.getMerchantName(), "base_info");
                        break;
                    default:
                        break;
                }
            });
//        } else {
//           llContact.setVisibility(View.GONE);
//        }
    }
    @OnClick({R.id.rl_expand})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.rl_expand:
                expand = !expand;
                if (expand) {
                    mTvExpand.setText("点击收起全部联系人");
                    mIvExpand.setImageResource(R.drawable.ic_green_up);
                    mAdapter.setNewData(allList);
                    layout.setVisibility(View.VISIBLE);
                } else {
                    mTvExpand.setText("点击展开全部联系人");
                    mIvExpand.setImageResource(R.drawable.ic_green_down);
                    mAdapter.setNewData(topList);
                    if (layout != null && mAdapter.getData().size() >= 2) {
                        layout.setVisibility(View.GONE);
                    }
                }
                break;
            default:
                break;
        }
    }
    //新建联系人成功后刷新 选择联系人列表
    @Subscribe(code = RxBusCode.RX_BUS_UPDATE_SELECT_CONTACT_LIST)
    public void updateContactList() {
        mPresenter.getContactList(poiId);
    }
    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        RxBus.get().register(this);
    }

    @Override
    public void onDetach() {
        super.onDetach();
        RxBus.get().unRegister(this);
    }
    @Override
    public void showNetError() {

    }
}
