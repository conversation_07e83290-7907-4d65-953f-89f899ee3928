package com.ybm100.app.crm.ui.fragment.find

import android.content.Intent
import android.graphics.Point
import android.net.Uri
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import com.alibaba.android.arouter.launcher.ARouter
import com.baidu.location.BDLocation
import com.just.ynbweb.bean.LocationInfo
import com.xyy.common.util.ConvertUtils
import com.xyy.common.util.ToastUtils
import com.xyy.flutter.container.container.ContainerRuntime
import com.xyy.userbehaviortracking.utils.UserBehaviorTrackingUtils
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.fragment.BaseMVPCompatFragment
import com.xyy.utilslibrary.rxbus.RxBus
import com.ybm100.app.crm.R
import com.ybm100.app.crm.api.ApiUrl
import com.ybm100.app.crm.bean.MineMenuBean
import com.ybm100.app.crm.bean.lzcustomer.LzRoleBean
import com.ybm100.app.crm.bean.user.UserInfoBean
import com.ybm100.app.crm.widget.SpaceBetweenItemDecoration
import com.ybm100.app.crm.constant.RoleTypeConfig
import com.ybm100.app.crm.constant.RxBusCode
import com.ybm100.app.crm.doraemon.ynb.YNBHybridActivity
import com.ybm100.app.crm.permission.PermissionUtil
import com.ybm100.app.crm.presenter.MineAndSettingPresenter
import com.ybm100.app.crm.schedule.service.CallRecordManager
import com.ybm100.app.crm.ui.activity.SettingActivity
import com.ybm100.app.crm.ui.activity.lbs.LocationManager
import com.ybm100.app.crm.ui.adapter.find.CommonToolsAdapter
import com.ybm100.app.crm.ui.fragment.lzcustomer.LzToastUtils
import com.ybm100.app.crm.utils.LzRoleInfoManager
import com.ybm100.app.crm.utils.SharedPrefManager
import com.ybm100.app.crm.utils.SnowGroundUtils
import kotlinx.android.synthetic.main.fragment_mine.*
import java.lang.Exception


class MineFragment : BaseMVPCompatFragment<MineAndSettingPresenter>(), View.OnClickListener, MineAndSettingPresenter.Mine {
    private var mCommonToolsAdapter: CommonToolsAdapter? = null
    private var currPoint = -1
    private var locationListener: LocationManager.LocationListener? = null
    private var locationInfo: LocationInfo? = null

    override fun initPresenter(): BasePresenter<*, *> {
        return MineAndSettingPresenter()
    }

    override fun showNetError() {

    }

    override fun onGetMineMenuSuccess(data: MineMenuBean?) {
        mCommonToolsAdapter?.setNewData(data?.rows)
    }

    override fun onGetMineMenuFail() {

    }

    override fun getRolesSuccess(roleBean: List<LzRoleBean>?) {
        LzRoleInfoManager.INSTANCE.setRoleInfo(roleBean)
        mPresenter.getMineMenu()
    }

    override fun getRolesFail() {
        mPresenter.getMineMenu()
    }

    override fun initUI(view: View?, savedInstanceState: Bundle?) {
        initLocationListener()

        iv_avatar?.setOnClickListener(this)
        tv_real_name?.setOnClickListener(this)
        tv_department_phone?.setOnClickListener(this)

        iv_cs.setOnClickListener(this)
        iv_setting.setOnClickListener(this)

        val userInfoBean: UserInfoBean? = SharedPrefManager.getInstance()?.userInfo
        tv_real_name?.text = userInfoBean?.realName ?: ""
        tv_department_phone?.text = "${userInfoBean?.department ?: ""} ${userInfoBean?.phone ?: ""}"

        initCommonlyUsedTools()

        mPresenter.getRoles()
    }

    private fun initCommonlyUsedTools() {
        mCommonToolsAdapter = CommonToolsAdapter().apply {
            setOnItemClickListener { adapter, view, position ->
                val item = data.getOrNull(position)
                SnowGroundUtils.track("mc_mine_click_" + (item?.typeID?:"empty"))
                when (item?.action?.type) {
                    "1", "3" -> {
                        if (RoleTypeConfig.isLzType()) {
                            LzToastUtils.show()
                            return@setOnItemClickListener
                        }
                        //跳转
                        when (item.androidUrl) {
                            "/crm/mine/OrderManagement" -> {
                                var pageType = 0
                                if (!item.action.parameter.isNullOrEmpty()) {
                                    pageType = item.action.parameter.toInt()
                                }
                                //订单管理、退单管理
                                ContainerRuntime.router.open(context,
                                        "/order_manage_page?pageType=${pageType}")
                            }
                            "/crm/mine/AchievementManagement" -> {
                                ContainerRuntime.getFlutterRouter().open(context, "/custom_statistics")
                            }
                            "/crm/mine/TaskManagement" -> {
                                val roleType = SharedPrefManager.getInstance().userInfo?.roleType
                                roleType?.let {
                                    ContainerRuntime.getFlutterRouter().open(context, "/GoodsManagement?roleType=${it}")
                                }
                            }
                            "/crm/mine/ScheduleManagement" -> {
                                CallRecordManager.handleCallRecordImmediately("mineFragment", 1000, true)
                                val encodeJson = Uri.encode(LzRoleInfoManager.INSTANCE.roleBeansJson)
                                val url = "/visit_manage_page?rolesJSON=$encodeJson"
                                ContainerRuntime.router.open(context, url, null)
                            }
                            "/crm/mine/AptitudeManagement" -> {
                                ContainerRuntime.getFlutterRouter().open(context, "/licence_manager_page")
                            }
                            "/customer_funnel" -> {
                                ContainerRuntime.getFlutterRouter().open(context, "/customer_funnel?oaId=${SharedPrefManager.getInstance().userInfo.sysUserId}")
                            }
                            "/commodity_rank_page" -> {
                                ContainerRuntime.getFlutterRouter().open(context, "/commodity_rank_page")
                            }
                            "/pop_data_statistics" -> {
                                ContainerRuntime.getFlutterRouter().open(context, "/pop_data_statistics")
                            }
                            "/control_manager_page" -> {
                                ContainerRuntime.getFlutterRouter().open(context, "/control_manager_page")
                            }
                            else -> {
                                try {
                                    val build = ARouter.getInstance().build(item.androidUrl ?: "")
                                    if (!item.action.parameter.isNullOrEmpty()) {
                                        build.withInt("type", item.action.parameter.toInt())
                                    }
                                    build.navigation()
                                } catch (ignore: Exception) {
                                    ToastUtils.showShort("暂无该页面配置")
                                }
                            }
                        }
                    }
                    "2" -> {
                        //定位
                        currPoint = item.action.parameter?.toInt() ?: -1
                        if (RoleTypeConfig.isLzType()) {
                            if (currPoint == 0) {
                                showProgressDialog("加载中")
                                checkLocationInfo()

                                UserBehaviorTrackingUtils.track("mc-personal-tools-clue")
                            } else {
                                LzToastUtils.show()
                            }
                        } else if (RoleTypeConfig.isHyType()) {
                            if (currPoint == 0 || currPoint == 2 || currPoint == 3) {
                                showProgressDialog("加载中")
                                checkLocationInfo()
                            } else {
                                LzToastUtils.show()
                            }
                        } else {
                            showProgressDialog("加载中")
                            checkLocationInfo()
                        }

                    }
                    "4" -> {
                        //直接跳转url
                        YNBHybridActivity.jumpYnb(mActivity, item.androidUrl, null)
                    }
                }
            }
        }

        rv_commonly_used_tools.apply {
            layoutManager = GridLayoutManager(context, 4)
            adapter = mCommonToolsAdapter

            val point = Point()
            mActivity.windowManager.defaultDisplay.getSize(point)

            val width = point.x - ConvertUtils.dp2px(SCREEN_MARGIN)

            addItemDecoration(SpaceBetweenItemDecoration((width - ConvertUtils.dp2px(75f) * 4) / 3.0, ConvertUtils.dp2px(24f)))
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_mine
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.iv_avatar, R.id.tv_real_name, R.id.tv_department_phone -> {
                // startNewActivity(UserInfoActivity.class);
                /*val userInfoIntent = FlutterActivity.CachedEngineIntentBuilder(FlutterRunnerActivity::class.java, "engine")
                        .build(mContext)
                userInfoIntent.putExtra("uri_path", "/personal_data")
                startActivity(userInfoIntent)*/

                ContainerRuntime.getFlutterRouter().open(context, "/personal_data")

                UserBehaviorTrackingUtils.track("mc-personal-personalcenter")
            }
            R.id.iv_cs -> {
                //在线客服
                if (RoleTypeConfig.isLzType()) {
                    LzToastUtils.show()
                } else {
                    val bundle = Bundle()
                    bundle.putInt("position", 3)
                    bundle.putInt("index", 1)
                    RxBus.get().send(RxBusCode.RX_BUS_MESSAGE_SELECT, bundle)
                    SnowGroundUtils.track("mc-mypage-contactbutton")
                }
            }
            R.id.iv_setting -> {
                startActivity(Intent(mContext, SettingActivity::class.java))

                UserBehaviorTrackingUtils.track("mc-personal-set")
            }

        }
    }

    override fun onPause() {
        super.onPause()
        LocationManager.getInstance().unRegisterLocationListener(locationListener)
    }

    fun initLocationListener() {
        locationListener = LocationManager.LocationListener { bd: BDLocation ->
            locationInfo = LocationInfo()
            locationInfo?.latitude = bd.latitude.toString()
            locationInfo?.longitude = bd.longitude.toString()
            hideProgressDialog()
            //在线客服
            if (currPoint == 1) {
                // do nothing
            } else if (currPoint == 0) {
                YNBHybridActivity.jumpYnb(mActivity, ApiUrl.getFindRecord(), locationInfo)
            } else if (currPoint == 2) {
                YNBHybridActivity.jumpYnb(mActivity, ApiUrl.getRecommendBaseUrl(), locationInfo)

                UserBehaviorTrackingUtils.track("Event-Find-click")
            } else if (currPoint == 3) {//荷叶健康业绩管理
                YNBHybridActivity.jumpYnb(mActivity, ApiUrl.getHYAchievementManagement(), locationInfo)
            }
            currPoint = -1
        }
    }

    private fun checkLocationInfo() {
        LocationManager.getInstance().locationPermissions(mActivity, locationListener, true, PermissionUtil.OnCancelCallBack { })
    }

    companion object {
        const val SCREEN_MARGIN = 60f

        @JvmStatic
        fun newInstance(bundle: Bundle? = null) =
                MineFragment().apply {
                    arguments = Bundle().apply {
                        if (bundle != null) {
                            this.putAll(bundle)
                        }
                    }
                }
    }
}