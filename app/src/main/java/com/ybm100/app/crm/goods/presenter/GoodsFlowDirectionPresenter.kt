package com.ybm100.app.crm.goods.presenter

import android.annotation.SuppressLint
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.IBaseView
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.helper.RxHelper
import com.ybm100.app.crm.goods.api.GoodsApiService
import com.ybm100.app.crm.goods.bean.GoodsFlow
import com.ybm100.app.crm.net.RetrofitCreateHelper
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer

class GoodsFlowDirectionPresenter(private val isPurchased: Boolean) : BasePresenter<Any, GoodsFlowDirection>() {
    var pageSize = 20
    var pageNo = 0

    override fun getModel(): Any {
        return Any()
    }

    @SuppressLint("CheckResult")
    fun requestGoodFlow(refresh: Boolean, goodsId: Int, isThisMonth: Boolean, sortType: Int) {
        if (refresh) {
            pageNo = 0
            mIView.enableLoadMore(true)
        }
        val result = RetrofitCreateHelper.createApi(GoodsApiService::class.java)
                .getGoodsFlow(
                        if (isThisMonth) 3 else 8,
                        if (isPurchased) 1 else 0,
                        pageSize,
                        pageNo,
                        goodsId,
                        sortType
                )
                .compose(RxHelper.rxSchedulerHelper())
                .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<GoodsFlow?>?>(mIView, "正在加载中...") {
                    override fun onSuccess(bean: RequestBaseBean<GoodsFlow?>?) {
                        if (bean == null || bean.data == null) {
                            mIView.showEmpty()
                            return
                        }
                        if (bean.data?.lastPage != false || bean.data?.rows.isNullOrEmpty()) {
                            mIView.loadMoreComplete() //超出一页没有更多的数据
                        }
                        mIView.requestGoodsFlowSuccess(refresh, bean.data)
                        pageNo++
                    }

                    override fun onFailure(errorCode: Int) {
                        mIView.requestGoodsFlowFail(null)
                    }
                }, object : SimpleErrorConsumer(mIView) {
                    override fun onError(throwable: Throwable?, msg: String?) {
                        mIView.requestGoodsFlowFail(msg)
                    }
                })
        mRxManager.register(result)

    }


}

interface GoodsFlowDirection : IBaseView {
    fun requestGoodsFlowSuccess(refresh: Boolean, data: GoodsFlow?)
    fun requestGoodsFlowFail(msg: String?)
    fun enableLoadMore(enable: Boolean)
    fun loadMoreComplete()
    fun showEmpty()
}
