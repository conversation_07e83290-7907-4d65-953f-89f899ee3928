package com.ybm100.app.crm.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.TextView;

import com.xyy.utilslibrary.utils.MathUtils;
import com.ybm100.app.crm.R;

/**
 * author :lx
 * date 2018/12/27.
 * email： <EMAIL>
 * 药店列表头部
 */
public class OrderListHeaderView extends BaseHeaderView {

    //当月订单数
    private TextView mSameMonthOrderNum;
    //上月订单数
    private TextView mLastMonthOrderNum;
    //当月订单金额
    private TextView mSameMonthOrderMoney;
    //上月订单金额
    private TextView mLastMonthOrderMoney;

    public OrderListHeaderView(Context context) {
        super(context);
    }

    public OrderListHeaderView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public OrderListHeaderView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void init() {
        setOrientation(VERTICAL);
        mSameMonthOrderNum = getView(R.id.tv_same_month_order_num);
        mLastMonthOrderNum = getView(R.id.tv_last_month_order_num);
        mSameMonthOrderMoney = getView(R.id.tv_same_month_order_money);
        mLastMonthOrderMoney = getView(R.id.tv_last_month_order_money);

    }

    @Override
    protected int getLayoutId() {
        return R.layout.order_list_header_layout;
    }

    public void bindData(int sameMonthOrderNum, int lastMonthOrderNum
            , double sameMonthOrderMoney, double lastMonthOrderMoney) {
        mSameMonthOrderNum.setText(sameMonthOrderNum + "");
        mLastMonthOrderNum.setText(lastMonthOrderNum + "");
        mSameMonthOrderMoney.setText(MathUtils.getFormatNum(sameMonthOrderMoney));
        mLastMonthOrderMoney.setText(MathUtils.getFormatNum(lastMonthOrderMoney));
    }
}