package com.ybm100.app.crm.task.activity

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import com.xyy.common.util.FragmentUtils
import com.xyy.utilslibrary.base.activity.BaseCompatActivity
import com.ybm100.app.crm.R
import com.ybm100.app.crm.constant.Constants
import com.ybm100.app.crm.task.bean.TaskGoodsDetailDataBean
import com.ybm100.app.crm.task.fragment.SelectGoodsFragment

/**
 * 选择商品
 */
class SelectGoodsActivity : BaseCompatActivity() {

    override fun getLayoutId(): Int {
        return R.layout.activity_select_goods
    }

    override fun initView(savedInstanceState: Bundle?) {
        FragmentUtils.replaceFragment(supportFragmentManager, SelectGoodsFragment.newInstance(intent?.extras), R.id.fragment_container, false)
    }

    companion object {
        val sGoodsCartList: MutableList<TaskGoodsDetailDataBean.Row> = mutableListOf()

        @JvmStatic
        fun startActivity(activity: Activity?, taskId: String?, searchType: Int, timeFilter: String?) {

            sGoodsCartList.clear()

            val intent = Intent(activity, SelectGoodsActivity::class.java)
            val bundle = Bundle()
            bundle.putString(Constants.Task.ARG_TASK_ID, taskId ?: "")
            bundle.putInt(Constants.Task.ARG_SEARCH_TYPE, searchType)
            bundle.putString(Constants.Task.ARG_TASK_TIME_FILTER, timeFilter)
            intent.putExtras(bundle)
            activity?.startActivity(intent)
        }
    }
}
