package com.ybm100.app.crm.bean.schedule;

import java.util.ArrayList;

/**
 * Author ： <PERSON>N<PERSON><PERSON>heart
 * Date:2019/1/6
 */
public class RemindTimeBean {

    private String timeName;
    private long time;

    public RemindTimeBean(String timeName, long time) {
        this.timeName = timeName;
        this.time = time;
    }


    public String getTimeName() {
        return timeName;
    }

    public void setTimeName(String timeName) {
        this.timeName = timeName;
    }

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }



    public static ArrayList<RemindTimeBean> getAllRemindTime(){
        ArrayList<RemindTimeBean> list = new ArrayList<>();
        list.add(new RemindTimeBean("无需提醒", -1));
        list.add(new RemindTimeBean("15分钟", 15*60*1000));
        list.add(new RemindTimeBean("30分钟", 30*60*1000));
        list.add(new RemindTimeBean("1小时", 60*60*1000));
        list.add(new RemindTimeBean("2小时", 60*60*1000*2));
        list.add(new RemindTimeBean("6小时", 60*60*1000*6));
        list.add(new RemindTimeBean("24小时", 60*60*1000*24));
        return list;
    }



}
