package com.ybm100.app.crm.flutter.channel

import android.util.Log
import androidx.fragment.app.FragmentActivity
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.ybm100.app.crm.schedule.service.CallRecordManager.handleCallRecord

class HandleCallRecordHandler : BaseHandler() {

    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {
        val source = (params["source"] ?: "flutter") as String
        Log.e("guan", "handle_call_record:${source}")
        handleCallRecord(source)
    }


}
