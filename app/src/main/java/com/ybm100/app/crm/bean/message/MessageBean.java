package com.ybm100.app.crm.bean.message;

/**
 * <AUTHOR>
 * @version 1.0
 * @file MessageBean.java
 * @brief
 * @date 2018/12/22
 * Copyright (c) 2018, 北京小药药
 * All rights reserved.
 */
public class MessageBean {
    public MessageBean() {
    }

    // 共用的字段
    public long id;
    public String title;// 共用的title
    public String content;
    public String applicationNumber;
    public String licenseType;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getApplicationNumber() {
        return applicationNumber;
    }

    public void setApplicationNumber(String applicationNumber) {
        this.applicationNumber = applicationNumber;
    }

    public String getLicenseType() {
        return licenseType;
    }

    public void setLicenseType(String licenseType) {
        this.licenseType = licenseType;
    }
//动态消息特有字段

    public String branchCode;//所属分公司code
    public long createTime;//创建时间
    public long sysUserId;//动态创建人ID
    public String sysUserName;//创建人姓名
    public int type;//1:拜访分享,10:客户完成注册,20:系统自动分配客户,上级分配客户:30
    public int taskStatus;//1是 任务开始   2，是任务结束    ，时间取创建时间
    public boolean canRedirect;//是否可跳转
    public int readStatus;//1未读,0已读
    public String typeName;

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public int getTaskStatus() {
        return taskStatus;
    }

    public boolean isCanRedirect() {
        return canRedirect;
    }

    public void setCanRedirect(boolean canRedirect) {
        this.canRedirect = canRedirect;
    }

    public int getReadStatus() {
        return readStatus;
    }

    public void setReadStatus(int readStatus) {
        this.readStatus = readStatus;
    }

    public void setTaskStatus(int taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public long getSysUserId() {
        return sysUserId;
    }

    public void setSysUserId(long sysUserId) {
        this.sysUserId = sysUserId;
    }

    public String getSysUserName() {
        return sysUserName;
    }

    public void setSysUserName(String sysUserName) {
        this.sysUserName = sysUserName;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }


    //分享通知消息特有字段（可能还有用）

    public String creator;//创建人
    public String redirectUrl;//跳转URL
    public String remark;//备注
    public int customerType;//客户类型
    public int state;//状态 1未读,2已读
    public long taskId;//任务ID

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getRedirectUrl() {
        return redirectUrl;
    }

    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public int getCustomerType() {
        return customerType;
    }

    public void setCustomerType(int customerType) {
        this.customerType = customerType;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public boolean isRead() {
        return state != 1;
    }

    public long getTaskId() {
        return taskId;
    }

    public void setTaskId(long taskId) {
        this.taskId = taskId;
    }


    //资质消息特有字段
    public int merchantId;// 药店id //当大于0时跳转到客户详情，否则跳转到私海列表，（）
    public String customerId;//customerId
    public String messageTitle;// 资质消息的title
    public int messageStatus;//状态 1未读,2已读
    public int messageType;// 资质消息类型 1一审 2二录 3资质临期 4资质过期
    public String licenseId;//资质id

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public int getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(int merchantId) {
        this.merchantId = merchantId;
    }

    public String getMessageTitle() {
        return messageTitle;
    }

    public void setMessageTitle(String messageTitle) {
        this.messageTitle = messageTitle;
    }

    public int getMessageStatus() {
        return messageStatus;
    }

    public void setMessageStatus(int messageStatus) {
        this.messageStatus = messageStatus;
    }

    public boolean isReadMsg() {
        return messageStatus != 1;
    }

    public int getMessageType() {
        return messageType;
    }

    public void setMessageType(int messageType) {
        this.messageType = messageType;
    }

    public String getLicenseId() {
        return licenseId;
    }

    public void setLicenseId(String licenseId) {
        this.licenseId = licenseId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MessageBean that = (MessageBean) o;
        return id == that.id;
    }

    @Override
    public int hashCode() {
        return String.valueOf(id).hashCode();
    }

}
