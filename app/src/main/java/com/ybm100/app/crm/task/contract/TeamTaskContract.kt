package com.ybm100.app.crm.task.contract

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.task.bean.TeamTaskBean
import io.reactivex.Observable


class TeamTaskContract {
    interface ITeamTaskView : IBaseActivity {
        fun onGetTeamTaskSuccess(data: RequestBaseBean<TeamTaskBean?>?, isRefresh: Boolean, isLastPage: Boolean)
        fun onGetTeamTaskFail()
    }

    interface ITeamTaskModel : IBaseModel {
        fun getTeamTask(queryMap: Map<String, String>): Observable<RequestBaseBean<TeamTaskBean?>?>
    }
}