package com.ybm100.app.crm.presenter.drugstore.minedrug

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.bean.drugstore.minedrugstore.ApplyInitBean
import com.ybm100.app.crm.contract.drugstore.minedrug.InvoiceApplyContract
import com.ybm100.app.crm.model.drugstore.minedrug.InvoiceApplyModel
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer
import com.ybm100.app.crm.order.bean.AptitudeInitBean

/**
 * Created by XyyMvpYkqTemplate on 07/30/2019 18:32
 */
class InvoiceApplyPresenter : BasePresenter<InvoiceApplyContract.IInvoiceApplyModel, InvoiceApplyContract.IInvoiceApplyView>() {

    override fun getModel(): InvoiceApplyModel = InvoiceApplyModel()

    fun getInvoiceInit(map: HashMap<String, Any>) {
        mRxManager.register(mIModel.reqInvoiceInit(map)
                .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<ApplyInitBean>>(mIView) {
                    override fun onSuccess(t: RequestBaseBean<ApplyInitBean>?) {
                        if (t != null) {
                            mIView.reqInvoiceInitSuccess(t.data)
                        }
                    }
                }, SimpleErrorConsumer(mIView)))
    }

    fun submitInvoiceApply(map: HashMap<String, Any>) {
        mRxManager.register(mIModel.submitInvoiceApply(map)
                .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<*>>(mIView, "提交中...") {
                    override fun onSuccess(t: RequestBaseBean<*>?) {
                        if (t != null) {
                            mIView.submitInvoiceApplySuccess()
                        }
                    }
                }, SimpleErrorConsumer(mIView)))
    }

    fun initLicenseAuditDetail( merchantId: String) {
        if (mIView == null || mIModel == null) return

        mRxManager.register(mIModel.initLicenseAuditDetail(merchantId, "2").subscribe(object : SimpleSuccessConsumer<RequestBaseBean<AptitudeInitBean>>(mIView) {
            override fun onSuccess(requestBaseBean: RequestBaseBean<AptitudeInitBean>) {
                mIView.initLicenseAuditDetailSuccess(requestBaseBean)
            }
        }, object : SimpleErrorConsumer(mIView) {}))
    }
}
