package com.ybm100.app.crm.ui.fragment.drugstore.minedrug;

import android.app.Dialog;
import android.content.DialogInterface;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jakewharton.rxbinding3.view.RxView;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;
import com.xyy.common.widget.DefaultItemDecoration;
import com.xyy.common.widget.statusview.StatusViewLayout;
import com.xyy.flutter.container.container.ContainerRuntime;
import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.base.fragment.BaseMVPCompatFragment;
import com.xyy.utilslibrary.rxbus.RxBus;
import com.xyy.utilslibrary.rxbus.Subscribe;
import com.xyy.utilslibrary.utils.DisplayUtils;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.AptitudesBean;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.LicenceBean;
import com.ybm100.app.crm.constant.Constants;
import com.ybm100.app.crm.constant.RxBusCode;
import com.ybm100.app.crm.contract.drugstore.minedrug.DrugstoreAptitudesContract;
import com.ybm100.app.crm.listener.MineDrugRefreshListener;
import com.ybm100.app.crm.order.bean.AptitudeInitBean;
import com.ybm100.app.crm.order.bean.InvoiceHasBean;
import com.ybm100.app.crm.presenter.drugstore.minedrug.DrugstoreAptitudesPresenter;
import com.ybm100.app.crm.ui.activity.drugstore.MineDrugstoreDetailItemActivity;
import com.ybm100.app.crm.ui.adapter.drugstore.minedrugstore.DrugstoreAptitudesAdapter;
import com.ybm100.app.crm.ui.adapter.drugstore.minedrugstore.DrugstoreAuditListAdapter;
import com.ybm100.app.crm.utils.GsonUtils;
import com.ybm100.app.crm.utils.SnowGroundUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import butterknife.BindView;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:08
 * 药店资质
 */
public class DrugstoreAptitudesFragment extends BaseMVPCompatFragment<DrugstoreAptitudesPresenter> implements DrugstoreAptitudesContract.IDrugstoreAptitudesView,
        MineDrugRefreshListener {

    @BindView(R.id.smart_refresh)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.rv_drugstore_aptitude_required)
    RecyclerView rvDrugstoreAptitudeRequired;
    @BindView(R.id.rv_drugstore_aptitude_not_required)
    RecyclerView rvDrugstoreAptitudeNotRequired;
    @BindView(R.id.svl_aptitude)
    StatusViewLayout svlAptitude;
    @BindView(R.id.scrollView)
    NestedScrollView scrollView;
    @BindView(R.id.rv_drugstore_aptitude_list)
    RecyclerView rvDrugstoreAptitudeList;
    @BindView(R.id.ll_audit)
    LinearLayout llAudit;
    @BindView(R.id.tv_operation)
    TextView tvOperation;
    @BindView(R.id.ll_operation)
    LinearLayout llOperation;
    @BindView(R.id.ll_required)
    LinearLayout llRequired;
    @BindView(R.id.ll_not_required)
    LinearLayout llNotRequired;
    private String merchantId = "";
    //资质状态
    private int licenseStatus = 1;
    //底部操作按钮是否展示  0添加首营2资质变更3不展示按钮（3.8.0 变更为展示按钮弹窗提示）
    private int licenseBtn = -1;
    private String tipMessage;

    public static DrugstoreAptitudesFragment newInstance(String merchantId) {
        Bundle args = new Bundle();
        DrugstoreAptitudesFragment fragment = new DrugstoreAptitudesFragment();
        args.putString("merchantId", merchantId);
        fragment.setArguments(args);
        return fragment;
    }

    @NonNull
    @Override
    public BasePresenter initPresenter() {
        return DrugstoreAptitudesPresenter.newInstance();
    }

    @Override
    public void initData() {
        super.initData();
        Bundle bundle = getArguments();
        if (bundle != null) {
            merchantId = bundle.getString("merchantId");
        }
    }


    @Override
    public int getLayoutId() {
        return R.layout.fragment_drugstore_aptitudes;
    }

    @Override
    public void initUI(View view, @Nullable Bundle savedInstanceState) {
        RxBus.get().register(this);
        rvDrugstoreAptitudeRequired.setLayoutManager(new LinearLayoutManager(mContext));
        rvDrugstoreAptitudeNotRequired.setLayoutManager(new LinearLayoutManager(mContext));
        DefaultItemDecoration itemDecoration = new DefaultItemDecoration(mContext);
        itemDecoration.setLineColorResId(R.color.color_F6F6F6);
        rvDrugstoreAptitudeRequired.addItemDecoration(itemDecoration);
        rvDrugstoreAptitudeNotRequired.addItemDecoration(itemDecoration);
        rvDrugstoreAptitudeList.setLayoutManager(new LinearLayoutManager(mContext));
        svlAptitude.setOnRetryListener(v -> {
            if (!TextUtils.isEmpty(merchantId)) {
                mPresenter.getAptitudeDetailList(merchantId);
            } else {
                showToast("参数错误");
            }
        });
        setRefreshLayout(smartRefreshLayout);
        smartRefreshLayout.setEnableLoadMore(false);
        smartRefreshLayout.setEnableRefresh(true);
        smartRefreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                if (!TextUtils.isEmpty(merchantId)) {
                    mPresenter.getAptitudeDetailList(merchantId);
                } else {
                    showToast("参数错误");
                }
            }
        });
        RxView.clicks(llOperation).throttleFirst(500, TimeUnit.MILLISECONDS).subscribe(unit -> {
            if (licenseBtn == 3) {
                showTipDialog(tipMessage);
            } else {
                mPresenter.isHaveType(merchantId);
            }
        });
    }

    @Override
    public void onLazyInitView(@Nullable Bundle savedInstanceState) {
        super.onLazyInitView(savedInstanceState);
        showWaitDialog(getResources().getString(R.string.loading));
        if (!TextUtils.isEmpty(merchantId) && isVisiable()) {
            mPresenter.getAptitudeDetailList(merchantId);
        } else {
            showToast("参数错误");
        }
    }

    @Override
    public void getAptitudeDetailListSuccess(RequestBaseBean<AptitudesBean> requestBaseBean) {
        if (requestBaseBean.getData() != null) {
            svlAptitude.showContent();
            List<LicenceBean> necessaryLicenceList = requestBaseBean.getData().getNecessaryLicenceList();
            List<LicenceBean> allLicenceList = new ArrayList<>();
            //必填
            if (necessaryLicenceList != null && necessaryLicenceList.size() != 0) {
                allLicenceList.addAll(necessaryLicenceList);
                DrugstoreAptitudesAdapter drugstoreAptitudesRequiredAdapter = new DrugstoreAptitudesAdapter(R.layout.item_drugstore_aptitude, requestBaseBean.getData().getNecessaryLicenceList(), 0);
                rvDrugstoreAptitudeRequired.setAdapter(drugstoreAptitudesRequiredAdapter);
                drugstoreAptitudesRequiredAdapter.setOnItemChildClickListener((adapter, view, position) -> {
                    jumpLicensePhotoViewPage(allLicenceList, position);
//                    LicensePhotoViewActivity.Companion.start(mActivity, (ArrayList<LicenceBean>) allLicenceList, position);
                    SnowGroundUtils.track("Event-MerchantLicense-AttachmentView");
                });
            }
            //非必填
            List<LicenceBean> optionalLicenceList = requestBaseBean.getData().getOptionalLicenceList();
            if (optionalLicenceList != null && optionalLicenceList.size() != 0) {
                allLicenceList.addAll(optionalLicenceList);
                DrugstoreAptitudesAdapter drugstoreAptitudesNotRequiredAdapter = new DrugstoreAptitudesAdapter(R.layout.item_drugstore_aptitude, optionalLicenceList, 1);
                rvDrugstoreAptitudeNotRequired.setAdapter(drugstoreAptitudesNotRequiredAdapter);
                drugstoreAptitudesNotRequiredAdapter.setOnItemChildClickListener((adapter, view, position) -> {
                    jumpLicensePhotoViewPage(allLicenceList, necessaryLicenceList.size() + position);
//                    LicensePhotoViewActivity.Companion.start(mActivity, (ArrayList<LicenceBean>) allLicenceList, necessaryLicenceList.size() + position);
                    SnowGroundUtils.track("Event-MerchantLicense-AttachmentView");
                });
            }
            //资质申请
            List<LicenceBean> auditList = requestBaseBean.getData().getAuditList();
            if (auditList != null && auditList.size() != 0) {//&& !RoleTypeConfig.isGJRGrop()
                llAudit.setVisibility(View.VISIBLE);
                String realName = "";
                if (requestBaseBean.getData().getMerchant() != null) {
                    realName = requestBaseBean.getData().getMerchant().getRealName();
                }
                DrugstoreAuditListAdapter auditListAdapter = new DrugstoreAuditListAdapter(R.layout.item_audit, auditList, realName);
                rvDrugstoreAptitudeList.setAdapter(auditListAdapter);
                auditListAdapter.setOnItemClickListener((adapter, view, position) -> {
                    //资质问题
//                    ARouter.getInstance().build(RouterPath.LICENCE_DETAIL)
//                            .withString("licenseAuditId", "" + auditList.get(position).getCode())
//                            .withString("type", "" + auditList.get(position).getType())
//                            .navigation();
                    String sb = "/license_detail_page?" + "licenseAuditId=" +
                            auditList.get(position).getCode() +
                            "&type=" +
                            auditList.get(position).getType();
                    ContainerRuntime.INSTANCE.getRouter().open(getContext(), sb, null);
                });
            } else {
                llAudit.setVisibility(View.GONE);
            }
            licenseStatus = requestBaseBean.getData().getMerchant().getLicenseStatus();
            licenseBtn = requestBaseBean.getData().getLicenseBtn();
            tipMessage = requestBaseBean.getData().getLicenseToastMsg();
            switch (licenseBtn) {
                case 0:
                    //首营资质
                    tvOperation.setText("添加首营资质");
                    llOperation.setVisibility(View.VISIBLE);
                    break;
                case 2:
                    tvOperation.setText("资质变更");
                    llOperation.setVisibility(View.VISIBLE);
                    break;
                case 3:
                    if (TextUtils.isEmpty(tipMessage)) {
                        llOperation.setVisibility(View.GONE);
                    } else {
                        tvOperation.setText("资质变更");
                        llOperation.setVisibility(View.VISIBLE);
                    }
                    break;
                default:
                    llOperation.setVisibility(View.GONE);
                    break;
            }
            //未注册的客户没有该信息，返回列表均为空
            if ((requestBaseBean.getData().getNecessaryLicenceList() == null
                    || requestBaseBean.getData().getNecessaryLicenceList().size() == 0)
                    && (optionalLicenceList == null
                    || optionalLicenceList.size() == 0)
                    && (auditList == null || auditList.size() == 0)) {
                llNotRequired.setVisibility(View.GONE);
                llRequired.setVisibility(View.GONE);
                llAudit.setVisibility(View.GONE);
                llOperation.setVisibility(View.GONE);
                svlAptitude.showEmpty();
            }
        } else {
            svlAptitude.showError();
        }
    }

    private void jumpLicensePhotoViewPage(List<LicenceBean> allLicenceList, int position) {
        List<String> urlPathList = new ArrayList<>();
        List<String> titleList = new ArrayList<>();
        List<String> subTitleList = new ArrayList<>();
        int targetPosition = 0;
        for (int parentIndex = 0; parentIndex < allLicenceList.size(); parentIndex++) {
            LicenceBean licenceBean = allLicenceList.get(parentIndex);

            List<LicenceBean.ListBean> enclosureList = licenceBean.getEnclosureList();
            if (enclosureList != null && !enclosureList.isEmpty()) {

                for (int childIndex = 0; childIndex < enclosureList.size(); childIndex++) {
                    LicenceBean.ListBean imageBean = enclosureList.get(childIndex);
                    urlPathList.add(imageBean.getUrl());
                    titleList.add(licenceBean.getName());
                    subTitleList.add("(" + (childIndex + 1) + "/" + enclosureList.size() + ")");
                }
                if (parentIndex+1 < allLicenceList.size() && parentIndex < position) {
                    targetPosition += enclosureList.size();
                }
            }

        }
        String url = "/photo_list_view_page" + "?urlPathListJson=" +
                Uri.encode(GsonUtils.toJson(urlPathList)) +
                "&titleListJson=" +
                Uri.encode(GsonUtils.toJson(titleList)) +
                "&subTitleListJson=" +
                Uri.encode(GsonUtils.toJson(subTitleList)) +
                "&isWaterMark=true" +
                "&position=" +
                targetPosition;
        ContainerRuntime.INSTANCE.getRouter().open(getActivity(), url, null);
    }

    @Override
    public void isHaveTypeSuccess(RequestBaseBean<InvoiceHasBean> requestBaseBean) {
        if (requestBaseBean.getData() == null) {
            return;
        }
        if (requestBaseBean.getData().getExit() == 0) {
            showReceiptDialog();
        } else {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    mPresenter.initLicenseAuditDetail(merchantId);
                }
            }, 200);
        }
    }

    @Override
    public void initLicenseAuditDetailSuccess(RequestBaseBean<AptitudeInitBean> data) {
        if (data == null || data.getData() == null) {
            return;
        }
        jumpNext(data.getData());
    }

    private void jumpNext(AptitudeInitBean data) {
        if (data == null || data.necessaryLicenceList == null || data.optionalLicenceList == null) {
            showToast(getString(R.string.text_aptitude_failure));
            return;
        }
        boolean isAddType = licenseBtn == 0;
//        AddLicenseActivity.Companion.startActivity(
//                mContext,
//                data.customer,
//                merchantId,
//                "",
//                isAddType,
//                true,
//                "", null, null);
        ContainerRuntime.router.open(mContext, "/licence_edit_base_page?" +
                        "mIsAddType=" + isAddType +
                        "&merchantId=" + merchantId +
                        "&isDraft=" + "true" +
                        "&from=" +
                        "&licenseInitDataJson=" + Uri.encode(GsonUtils.toJson(data))
                , null);
    }

    /**
     * 显示设置发票类型弹窗
     */
    private void showReceiptDialog() {
        Dialog dialog = new Dialog(mContext, R.style.custom_dialog2);
        View view = View.inflate(mContext, R.layout.dialog_base, null);
        TextView content = view.findViewById(R.id.tv_dialog_content);
        TextView title = view.findViewById(R.id.tv_dialog_title);
        TextView tips = view.findViewById(R.id.tv_dialog_tips);
        TextView cancel = view.findViewById(R.id.btn_cancel);
        TextView confirm = view.findViewById(R.id.btn_ensure);
        dialog.setContentView(view);
        content.setText(getResources().getString(R.string.receipt_content));
        title.setText("设置发票类型");
        tips.setText(getResources().getString(R.string.receipt_tips));
        cancel.setOnClickListener(v -> dialog.dismiss());
        confirm.setOnClickListener(v -> {
            if (getActivity() != null && getActivity() instanceof MineDrugstoreDetailItemActivity) {
                ((MineDrugstoreDetailItemActivity) getActivity()).setTab(Constants.DrugTabType.INVOICETYPE_PAGE);
            }
            dialog.dismiss();
        });
        // back键处理
        dialog.setOnCancelListener(DialogInterface::dismiss);
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(false);
        dialog.show();
        // 设置布局参数(必须在show之后设置)
        Window window = dialog.getWindow();
        if (window == null) {
            return;
        }
        WindowManager.LayoutParams lp = window.getAttributes();
        int screenWidthPixels = DisplayUtils.getScreenWidthPixels(mActivity);
        lp.width = (int) (screenWidthPixels * 0.85);
        lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
        lp.gravity = Gravity.CENTER;
        window.setAttributes(lp);
    }

    /**
     * 显示提示弹窗
     */
    private void showTipDialog(String tipText) {
        Dialog dialog = new Dialog(mContext, R.style.custom_dialog2);
        View view = View.inflate(mContext, R.layout.dialog_man, null);
        TextView title = view.findViewById(R.id.title);
        TextView cancel = view.findViewById(R.id.cancel);
        TextView confirm = view.findViewById(R.id.confirm);
        LinearLayout parent = view.findViewById(R.id.buttonContainer);
        dialog.setContentView(view);
        title.setTextSize(14);
        title.setText(tipText);
        confirm.setText("确认");
        cancel.setVisibility(View.GONE);
        parent.setVisibility(View.VISIBLE);
        confirm.setOnClickListener(v -> {
            dialog.dismiss();
        });
        dialog.setOnCancelListener(DialogInterface::dismiss);
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(false);
        dialog.show();
        // 设置布局参数(必须在show之后设置)
        Window window = dialog.getWindow();
        if (window == null) {
            return;
        }
        WindowManager.LayoutParams lp = window.getAttributes();
        int screenWidthPixels = DisplayUtils.getScreenWidthPixels(mActivity);
        lp.width = (int) (screenWidthPixels * 0.85);
        lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
        lp.gravity = Gravity.CENTER;
        window.setAttributes(lp);
    }

    @Override
    public void onRefreshMine() {
        if (!TextUtils.isEmpty(merchantId)) {
            mPresenter.getAptitudeDetailList(merchantId);
        } else {
            showToast("参数错误");
        }
    }

    @Override
    public boolean canRefresh() {
        return scrollView == null || scrollView.getScrollY() == 0;
    }

    @Override
    public void showNetError() {

    }

    @Subscribe(code = RxBusCode.RX_BUS_UPDATE_LICENCEDTAIL_CHANGE)
    public void update(Bundle bundle) {
        onRefreshMine();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        RxBus.get().unRegister(this);
    }
}
