package com.ybm100.app.crm.contract.contact;

import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.contact.BaseDrugInfoBean;
import com.ybm100.app.crm.bean.contact.ContactBean;
import com.ybm100.app.crm.bean.contact.QueryMerchantsApi;
import com.ybm100.app.crm.schedule.model.ContactJobEnum;

import java.util.List;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/24/2018 19:46
 */
public interface CreateContactContract {

    interface ICreateContactView extends ContactDetailContract.IContactDetailView {
        void updateTags(ContactBean bean);
        void finish();
        void renderMerchants(List<BaseDrugInfoBean> merchants);

        void getContactJobSuccess(List<ContactJobEnum> list);

    }

    interface ICreateContactModel extends IBaseModel {
        Observable<RequestBaseBean> addContactTag(String id, String trim);

        Observable<RequestBaseBean<ContactBean>>  updateContact(ContactBean contact, boolean create, String tag);

        Observable<QueryMerchantsApi>  getDrugInfo(String obj);

        Observable<RequestBaseBean<ContactBean>> delTag(ContactBean contact, boolean b);

        Observable<RequestBaseBean<List<ContactJobEnum>>> getContactJob();
    }

}
