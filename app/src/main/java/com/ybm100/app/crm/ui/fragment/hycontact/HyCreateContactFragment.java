package com.ybm100.app.crm.ui.fragment.hycontact;


import android.annotation.SuppressLint;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.listener.OnTimeSelectListener;
import com.bigkoo.pickerview.view.TimePickerView;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.jakewharton.rxbinding3.widget.RxTextView;
import com.xyy.common.navigationbar.DefaultEditNavigationBar;
import com.xyy.common.util.ToastUtils;
import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.fragment.BaseMVPCompatFragment;
import com.xyy.utilslibrary.utils.ResourcesUtils;
import com.xyy.utilslibrary.utils.TimeUtils;
import com.xyy.utilslibrary.utils.ViewUtils;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.contact.BaseDrugInfoBean;
import com.ybm100.app.crm.bean.contact.ContactBean;
import com.ybm100.app.crm.bean.contact.WrapCallLogBean;
import com.ybm100.app.crm.contract.contact.CreateContactContract;
import com.ybm100.app.crm.model.contact.ContactConstants;
import com.ybm100.app.crm.presenter.hycontact.HyCreateContactPresenter;
import com.ybm100.app.crm.schedule.model.ContactJobEnum;
import com.ybm100.app.crm.ui.adapter.messge.ContactDrugAdapter;
import com.ybm100.app.crm.utils.CheckUtils;
import com.ybm100.app.crm.utils.InputFilter.EditUtil;
import com.ybm100.app.crm.widget.combinationview.ActInputEditView;
import com.ybm100.app.crm.widget.picker.PickerManager;
import com.zhy.view.flowlayout.FlowLayout;
import com.zhy.view.flowlayout.TagAdapter;
import com.zhy.view.flowlayout.TagFlowLayout;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Consumer;
import io.reactivex.functions.Function;
import io.reactivex.functions.Predicate;

import static com.ybm100.app.crm.ui.activity.message.ContactActivity.EDIT;

/**
 * 新建联系人
 * HyCreateContactFragment
 */
public class HyCreateContactFragment extends BaseMVPCompatFragment<HyCreateContactPresenter> implements CreateContactContract.ICreateContactView {

    private TagFlowLayout tagFlowLayout;
    private TextView mAddTag;
    private EditText mTagEdit;
    private ActInputEditView mPosition;
    private ActInputEditView mBirthday;
    private RadioGroup mSexGroup;
    private EditText mStore;
    private EditText mPhone;
    private EditText mName;
    private TimePickerView mTimePicker;
    private RecyclerView mRecyclerView;
    private ContactDrugAdapter contactDrugAdapter;
    private RelativeLayout mCreateContainer;
    private ActInputEditView mSexC;
    private LinearLayout mLinearlayout;
    //每次选择完成药店后丢弃一次搜索，因为选择完成后会重置选择药店名称到搜索框，这时候会导致文字再次改变，又搜索一次
    boolean throwNextSearch;

    public static HyCreateContactFragment newInstance(ContactBean contact, boolean edit) {
        Bundle args = new Bundle();
        if (contact != null && !TextUtils.isEmpty(contact.getContactJob()) && TextUtils.isEmpty(contact.getContactJobName())) {
            String s = ContactConstants.contactJobs.get(contact.getContactJob());
            contact.setContactJobName(s);
        }
        if (contact != null)
            args.putSerializable("contact", contact);
        args.putBoolean(EDIT, edit);
        HyCreateContactFragment fragment = new HyCreateContactFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @NonNull
    @Override
    public BasePresenter initPresenter() {
        return HyCreateContactPresenter.newInstance();
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_create_contact;
    }

    @Override
    public void initUI(View view, @Nullable Bundle savedInstanceState) {
        Serializable args = getArguments().getSerializable("contact");
        boolean editable = getArguments().getBoolean(EDIT, true);
        initHeader(args);

        mTimePicker = initTimePicker();
        mLinearlayout = view.findViewById(R.id.ll_block_ll);
        tagFlowLayout = view.findViewById(R.id.tfl_contact_create_tags);
        mAddTag = view.findViewById(R.id.iv_create_contact_add_tag);
        mTagEdit = view.findViewById(R.id.ret_create_contact_detail);
        mPosition = view.findViewById(R.id.aie_contact_position);
        mBirthday = view.findViewById(R.id.aie_contact_birthday);
        mSexGroup = view.findViewById(R.id.rg_create_contact_sex);
        mStore = view.findViewById(R.id.aie_drug_store_name);
        mSexC = view.findViewById(R.id.aie_sex);
        mPhone = view.findViewById(R.id.aie_contact_phone);
        mRecyclerView = view.findViewById(R.id.rv_contact_drug);
        mCreateContainer = view.findViewById(R.id.rl_create_contact_container);
        initDrugListInvisibleCondition();
        initRecyclerView();

        mName = view.findViewById(R.id.aie_contact_last_name);
        try {
            EditUtil.InputBuilder builder = new EditUtil.InputBuilder();
            InputFilter[] build = builder.setInputLength(20).addIllegalInput().build();
            mName.setFilters(build);
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            mTagEdit.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {

                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {

                }

                @Override
                public void afterTextChanged(Editable s) {
                    String s1 = s.toString();
                    mAddTag.setTextColor(!TextUtils.isEmpty(s1) ? ResourcesUtils.getColor(R.color.text_color_35C561) : Color.parseColor("#8E8E93"));
                }
            });
            EditUtil.InputBuilder builder2 = new EditUtil.InputBuilder();
            //逗号，句号，冒号，括号，叹号 可以
            InputFilter[] build = builder2.setInputLength(8).build();//包含 ' 这个符号
            mTagEdit.setFilters(build);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (editable) { // 可编辑时过滤特殊符号
            try {
                EditUtil.InputBuilder builder = new EditUtil.InputBuilder();
                InputFilter[] build = builder.addIllegalInput().setInputLength(200).build();
                mStore.setFilters(build);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (!editable) {
            mStore.setEnabled(editable);
        }
        mStore.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                if (editable) { // 可编辑时过滤特殊符号
                    try {
                        EditUtil.InputBuilder builder = new EditUtil.InputBuilder();
                        InputFilter[] build = builder.addIllegalInput().setInputLength(200).build();
                        mStore.setFilters(build);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
        mBirthday.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                String birthdayText = mBirthday.getText();
                if (!TextUtils.isEmpty(birthdayText)) {
                    String[] split = birthdayText.split("-");
                    if (split.length == 3) {
                        String year = split[0];
                        String month = split[1];
                        String day = split[2];
                        Calendar instance = Calendar.getInstance();
                        instance.set(Calendar.YEAR, Integer.valueOf(year));
                        instance.set(Calendar.MONTH, Integer.valueOf(month) - 1);
                        instance.set(Calendar.DAY_OF_MONTH, Integer.valueOf(day));
                        mTimePicker.setDate(instance);
                    }
                }
                mTimePicker.show();
            }
        });
        mSexC.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                ArrayList<String> strings = new ArrayList<>();
                strings.add("女");
                strings.add("男");
                String text = mSexC.getText();
                int defaultIndex = 0;
                if (TextUtils.equals("男", text)) {
                    defaultIndex = 1;
                }
                PickerManager.showSingleSelectPicker(getContext(), strings, defaultIndex, new PickerManager.OnOptionsSelectCallback() {
                    @Override
                    public void onOptionsSelect(int index, String content) {
                        if (index == ContactConstants.FEMALE) {
                            mSexGroup.check(R.id.rb_female_contact);
                            mSexC.setDescription(getString(R.string.female));
                        } else if (ContactConstants.MALE == index) {
                            mSexC.setDescription(getString(R.string.male));
                            mSexGroup.check(R.id.rb_male_contact);
                        }
                    }
                });
            }
        });

        mPosition.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
//                String[] stringArray = getResources().getStringArray(R.array.quarters);
                List<String> jobNameList = ContactConstants.getContactJobNameList();
                String text = mPosition.getText();
                int defaultIndex = 0;
                for (int i = 0; i < jobNameList.size(); i++) {
                    String s = jobNameList.get(i);
                    if (TextUtils.equals(s, text)) {
                        defaultIndex = i;
                    }
                }


                PickerManager.showSingleSelectPicker(getContext(), jobNameList, defaultIndex, new PickerManager.OnOptionsSelectCallback() {
                    @Override
                    public void onOptionsSelect(int index, String content) {

                        mPosition.setDescription(content);
                    }
                });
            }
        });
        mAddTag.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Editable text = mTagEdit.getText();
                String trim = text.toString();
                if (!TextUtils.isEmpty(trim)) {
                    mPresenter.addTag(trim);
                    mTagEdit.setText("");
                } else {
                    showToast(getString(R.string.tag_cantbe_null));
                }
            }
        });
        if (args != null) {
            ContactBean contact = (ContactBean) args;
            //包装一个药店
            BaseDrugInfoBean baseDrugInfoBean = new BaseDrugInfoBean();
            baseDrugInfoBean.setRealName(contact.poiName);
            baseDrugInfoBean.setId(contact.getPoiId());
            mStore.setTag(baseDrugInfoBean);
            if (contact.getContactSex() == -1) {
                contact.setContactSex(ContactConstants.MALE);
            }

            mPresenter.setData(contact);
            throwNextSearch = true;
            renderContactDetail(contact);
        } else {
            //新建
            mSexC.setDescription(getString(R.string.male));
            mSexGroup.check(R.id.rb_male_contact);
            ContactBean contactBean = new ContactBean();
            contactBean.setContactSex(ContactConstants.MALE);
            mPresenter.setData(contactBean);
        }
        mPresenter.getContactJobEnum();
    }

    private void initHeader(Serializable args) {
        new DefaultEditNavigationBar.Builder(getContext()).setTitle(args == null || (args != null && TextUtils.isEmpty(((ContactBean) args).getContactName())) ? getString(R.string.create_contact) : getString(R.string.edit_contact))
                .setLeftClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        getBindActivity().finish();
                    }
                })
                .setRightText(getString(R.string.confirm))
                .setRightClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        boolean clickable = ViewUtils.isClickable();
                        if (clickable) {
                            updateUser();
                        }
                    }
                })
                .builder();
    }

    private void updateUser() {
        String name = mName.getText().toString();
        String phone = mPhone.getText().toString();
        Object tag = mStore.getTag();
        int text = mSexGroup.getCheckedRadioButtonId();
        int sex = -1;
        if (text == R.id.rb_male_contact) {
            sex = ContactConstants.MALE;
        } else if (text == R.id.rb_female_contact) {
            sex = ContactConstants.FEMALE;
        }

        String birthdayText = mBirthday.getText();
        String positionText = mPosition.getText();
        if (!TextUtils.isEmpty(phone) && !CheckUtils.isCanCallNum(phone)) {
            ToastUtils.showShort("请输入正确的联系方式");
            return;
        }
        mPresenter.updateContact(name, phone, tag, sex, birthdayText, positionText);
    }

    @SuppressLint("ClickableViewAccessibility")
    private void initDrugListInvisibleCondition() {
        mCreateContainer.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View view, MotionEvent motionEvent) {
                mStore.setFocusable(true);
                mStore.setFocusableInTouchMode(true);
                mStore.requestFocus();
                mStore.requestFocusFromTouch();
                return false;
            }
        });
        mStore.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View view, boolean b) {
                if (!b) {
                    mRecyclerView.setVisibility(View.INVISIBLE);
                }
            }
        });
    }

    @Override
    public void onLazyInitView(@Nullable Bundle savedInstanceState) {
        super.onLazyInitView(savedInstanceState);
        mLinearlayout.setDescendantFocusability(ViewGroup.FOCUS_BEFORE_DESCENDANTS);
    }

    private void initRecyclerView() {
        contactDrugAdapter = new ContactDrugAdapter(R.layout.item_drug_info);
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(getContext());
        linearLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        mRecyclerView.setLayoutManager(linearLayoutManager);
        mRecyclerView.setAdapter(contactDrugAdapter);
        contactDrugAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                throwNextSearch = true;
                BaseDrugInfoBean o = (BaseDrugInfoBean) adapter.getData().get(position);
                /*测试产品脑抽非得选择的支持特殊符号,手输入的不支持特殊符号
                 */
                try {
                    EditUtil.InputBuilder builder = new EditUtil.InputBuilder();
                    InputFilter[] build = builder.setInputLength(200).build();
                    mStore.setFilters(build);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (!TextUtils.isEmpty(o.getId())) {
                    mStore.setText(o.getRealName());
                    mStore.setTag(o);
                } else {
                    mStore.setText("");
                    mStore.setTag(null);
                }
                mRecyclerView.setVisibility(View.INVISIBLE);

                mStore.setFocusable(true);
                mStore.setFocusableInTouchMode(true);
                mStore.requestFocus();
                mStore.requestFocusFromTouch();
                mStore.setSelection(mStore.getText().length());
            }
        });

        addDisposable(RxTextView.textChanges(mStore)
                .debounce(500, TimeUnit.MILLISECONDS)
                .filter(new Predicate<CharSequence>() {
                    @Override
                    public boolean test(CharSequence charSequence) throws Exception {
                        boolean decision = throwNextSearch;
                        throwNextSearch = false;
                        return !decision;
                    }
                })
                .map(new Function<CharSequence, String>() {
                    @Override
                    public String apply(CharSequence charSequence) throws Exception {
                        return charSequence.toString();
                    }
                })
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<String>() {
                    @Override
                    public void accept(String s) throws Exception {
                        if (!TextUtils.isEmpty(s)) {
                            mPresenter.getDrugInfo(s);
                        } else {
                            mRecyclerView.setVisibility(View.INVISIBLE);
                        }
                    }
                }));
    }

    private TimePickerView initTimePicker() {
        TimePickerBuilder timePickerBuilder = new TimePickerBuilder(getContext(), new OnTimeSelectListener() {
            @Override
            public void onTimeSelect(Date date, View v) {
                String s = TimeUtils.date2String(date, TimeUtils.DATE_FORMAT_YMD);
                mBirthday.setDescription(s);
            }
        });
        Calendar start = Calendar.getInstance();
        start.set(Calendar.YEAR, 1920);
        start.set(Calendar.MONTH, 0);
        start.set(Calendar.DATE, 1);
        Calendar end = Calendar.getInstance();
//        end.add(Calendar.DATE, -1);//不能选今天
        timePickerBuilder.setRangDate(start, end);
        timePickerBuilder.setDate(end);
        timePickerBuilder.setType(new boolean[]{true, true, true, false, false, false});
        timePickerBuilder.setCancelText("取消");
        timePickerBuilder.setCancelColor(ContextCompat.getColor(mContext, R.color.text_color_cancel_button));
        timePickerBuilder.setSubmitText("确认");
        timePickerBuilder.setSubmitColor(ContextCompat.getColor(mContext, R.color.text_color_confirm_button));
        return timePickerBuilder.build();
    }


    @Override
    public void renderContactDetail(ContactBean data) {
        String contactName = data.getContactName();
        String phoneNo = data.getContactMobile();
        String storeName = data.getPoiName();
        if (!TextUtils.isEmpty(contactName)) {
            mName.setText(contactName.trim());
        }
        if (!TextUtils.isEmpty(phoneNo)) {
            mPhone.setText(phoneNo);
        }
        if (!TextUtils.isEmpty(storeName)) {
            mStore.setText(storeName.trim());
        }
        if (data.getContactSex() == ContactConstants.MALE) {
            mSexGroup.check(R.id.rb_male_contact);
            mSexC.setDescription(getString(R.string.male));
        } else if (data.getContactSex() == ContactConstants.FEMALE) {
            mSexGroup.check(R.id.rb_female_contact);
            mSexC.setDescription(getString(R.string.female));
        }

        if (!TextUtils.isEmpty(data.getContactBirth())) {

            try {
                String contactBirth = data.getContactBirth();
                long l = Long.parseLong(contactBirth);
                String date = TimeUtils.millis2String(l, TimeUtils.DATE_FORMAT_YMD);
                mBirthday.setDescription(date);
            } catch (Exception e) {
                e.printStackTrace();
                mBirthday.setDescription(data.getContactBirth());
            }
        }

        if (!TextUtils.isEmpty(data.getContactJobName())) {
            mPosition.setDescription(data.getContactJobName());
        }

        updateTags(data);
        ViewGroup parent = (ViewGroup) mTagEdit.getParent();
        parent.setFocusable(true);
        parent.setFocusableInTouchMode(true);
        parent.requestFocus();
        parent.requestFocusFromTouch();
    }

    @Override
    public void updateTags(ContactBean data) {
        String tags = data.getContactTag();
        if (tags != null) {
            String[] split = tags.split("、");
            List<String> strings = Arrays.asList(split);
            if (strings.size() == 1 && TextUtils.isEmpty(strings.get(0))) {
                return;
            }
            TagAdapter<String> tagAdapter = new TagAdapter<String>(strings) {
                @Override
                public View getView(FlowLayout parent, int position, String o) {
                    View inflate = View.inflate(getContext(), R.layout.item_tags, null);
                    TextView viewById = inflate.findViewById(R.id.tv_item_tags_name);
                    View del = inflate.findViewById(R.id.tv_item_tags_del);
                    del.setTag(o);
                    del.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            Object tag1 = view.getTag();
                            if (tag1 != null) {
                                String tag = (String) view.getTag();
                                if (!TextUtils.isEmpty(tag)) {
                                    mPresenter.del(tag);
                                }
                            }
                        }
                    });
                    viewById.setText(o);
                    return inflate;
                }
            };
            tagFlowLayout.setAdapter(tagAdapter);
        } else {
            tagFlowLayout.removeAllViews();
        }
    }

    @Override
    public void finish() {
        getBindActivity().finish();
    }

    @Override
    public void renderMerchants(List<BaseDrugInfoBean> merchants) {
        if (merchants != null && merchants.size() > 0) {
            int bottom = mStore.getBottom();
            RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) mRecyclerView.getLayoutParams();
            layoutParams.topMargin = bottom;
            contactDrugAdapter.getData().clear();
            mRecyclerView.setVisibility(View.VISIBLE);
            contactDrugAdapter.addData(merchants);
        } else {
            mRecyclerView.setVisibility(View.INVISIBLE);
        }
    }

    @Override
    public void getContactJobSuccess(List<ContactJobEnum> list) {
        ContactConstants.setContactJobs(list);
    }

    @Override
    public void renderContactCallRecord(WrapCallLogBean callLogBeans) {

    }

    @Override
    public void showNetError() {

    }
}
