package com.ybm100.app.crm.task.contract

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.task.bean.TaskGoodsListDataBean
import io.reactivex.Observable


class TaskGoodsContract {
    interface ITaskGoodsView : IBaseActivity {
        fun onGetTaskListSuccess(data: RequestBaseBean<TaskGoodsListDataBean?>?, isRefresh: Boolean, isLastPage: Boolean)
        fun onGetTaskListFail()
    }

    interface ITaskGoodsModel : IBaseModel {
        fun getTaskList(queryMap: Map<String, String>): Observable<RequestBaseBean<TaskGoodsListDataBean?>?>
    }
}