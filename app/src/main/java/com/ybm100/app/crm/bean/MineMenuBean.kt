package com.ybm100.app.crm.bean
import androidx.annotation.Keep


@Keep
data class MineMenuBean(
    val rows: List<Row?>? = listOf()
) {
    @Keep
    data class Row(
        val action: Action? = Action(),
        val androidUrl: String? = "", // /crm/mine/AptitudeManagement
        val iOSUrl: String? = "", // /crm/mine/CRMQualificationDocumentsViewController
        val icon: String? = "",
        val name: String? = "", // 资质管理
        val typeID: String? = "", // 1
        val url: String? = ""
    ) {
        @Keep
        data class Action(
            val parameter: String? = "",
            val type: String? = "" // 1
        )
    }
}