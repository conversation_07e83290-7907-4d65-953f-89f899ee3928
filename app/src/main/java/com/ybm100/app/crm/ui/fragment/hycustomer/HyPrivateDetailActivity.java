package com.ybm100.app.crm.ui.fragment.hycustomer;


import android.annotation.SuppressLint;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.Group;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.just.ynbweb.bean.LocationInfo;
import com.xyy.common.navigationbar.AbsNavigationBar;
import com.xyy.common.navigationbar.DefaultNavigationBar;
import com.xyy.common.util.ToastUtils;
import com.xyy.common.widget.RoundTextView;
import com.xyy.flutter.container.container.ContainerRuntime;
import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.activity.BaseMVPCompatActivity;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.rxbus.RxBus;
import com.xyy.utilslibrary.utils.DisplayUtils;
import com.xyy.utilslibrary.utils.ViewUtils;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.api.ApiUrl;
import com.ybm100.app.crm.bean.AddressBean;
import com.ybm100.app.crm.bean.drugstore.DrugstoreBaseBean;
import com.ybm100.app.crm.bean.hycustomer.HyPrivateDetailBean;
import com.ybm100.app.crm.utils.GsonUtils;
import com.ybm100.app.crm.constant.DrugstoreConstants;
import com.ybm100.app.crm.constant.RoleTypeConfig;
import com.ybm100.app.crm.constant.RxBusCode;
import com.ybm100.app.crm.contract.hycustomer.HyPrivateDetailContract;
import com.ybm100.app.crm.doraemon.ynb.YNBHybridActivity;
import com.ybm100.app.crm.listener.MineDrugstoreListener;
import com.ybm100.app.crm.permission.PermissionUtil;
import com.ybm100.app.crm.presenter.hycustomer.HyPrivateDetailPresenter;
import com.ybm100.app.crm.task.bean.TaskAndMerchantBean;
import com.ybm100.app.crm.ui.activity.lbs.LBSMapActivity;
import com.ybm100.app.crm.ui.activity.lbs.LocationManager;
import com.ybm100.app.crm.ui.adapter.drugstore.DrugstoreGridAdapter;
import com.ybm100.app.crm.utils.FormatUtils;
import com.ybm100.app.crm.utils.LzRoleInfoManager;
import com.ybm100.app.crm.utils.SnowGroundUtils;
import com.ybm100.app.crm.widget.drug.ContactDialog;
import com.ybm100.app.crm.widget.drug.CopyTextDialog;
import com.ybm100.app.crm.widget.popwindow.CustomerDetailPopup;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;


/**
 * 荷叶健康私海客户详情
 */
public class HyPrivateDetailActivity extends BaseMVPCompatActivity<HyPrivateDetailPresenter> implements HyPrivateDetailContract.IHyPrivateDetailView,
        MineDrugstoreListener {
    @BindView(R.id.tv_mine_drugstore_name)
    TextView tvMineDrugstoreName;
    @BindView(R.id.tv_mine_drugstore_address)
    TextView tvMineDrugstoreAddress;
    @BindView(R.id.tv_mine_drugstore_serial_hint)
    TextView tvMineDrugstoreSerialHint;
    @BindView(R.id.tv_mine_drugstore_serial_number)
    TextView tvMineDrugstoreSerialNumber;
    @BindView(R.id.operate_layout)
    ConstraintLayout operateLayout;
    @BindView(R.id.bt_add_schedule)
    RoundTextView btAdd_schedule;
    @BindView(R.id.recycler_grid)
    RecyclerView recyclerView;
    @BindView(R.id.tv_group_register)
    Group groupRegister;
    @BindView(R.id.tv_mine_drugstore_register_address)
    TextView tvRegisterAddress;
    @BindView(R.id.tv_mine_drugstore_poi_id)
    TextView tvDrugstorePoiId;//poi id
    @BindView(R.id.tv_mine_drugstore_hy_id)
    TextView tvDrugstoreHyId;//荷叶门店id
    private DefaultNavigationBar bar;
    private String[] titles;
    private int[] drawables;
    /**
     * 药店id
     */
    private String merchantId = "";
    private HyPrivateDetailBean baseInfo;
    AddressBean addressBean = null;
    private boolean change;
    //是否可分配
    private int distributable;
    private static final int REQUEST_EXECUTOR = 1;
    private String poiId;
    private String hyId;//荷叶门店id
    private LocationManager.LocationListener locationListener;
    private LocationInfo locationInfo;
    private boolean distributabled;
    private int registerFlag;

    @NonNull
    @Override
    public BasePresenter initPresenter() {
        return HyPrivateDetailPresenter.newInstance();
    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_hy_private_detail;
    }

    @Override
    protected void initTransferData() {
        super.initTransferData();
        titles = new String[]{getResources().getString(R.string.base_info), getResources().getString(R.string.hy_visit_record),
                getResources().getString(R.string.hy_sell_data), getResources().getString(R.string.hy_bd_follow_up_info),
        };
        drawables = new int[]{R.drawable.drugstore_base_info, R.drawable.drugstore_order_record,
                R.drawable.drugstore_hy_sell_data,
                R.drawable.drugstore_hy_bd_follow_up_info};
//        }

        merchantId = getIntent().getStringExtra(DrugstoreConstants.INTENT_KEY_MERCHANTID);
        hyId = getIntent().getStringExtra(DrugstoreConstants.INTENT_KEY_HY_ID);
        distributable = getIntent().getIntExtra(DrugstoreConstants.INTENT_KEY_DRUGSTORE_DISTRIBUTABLE, 0);
        if (merchantId == null || hyId == null) {
            try {
                Uri data = getIntent().getData();
                merchantId = data.getQueryParameter("shopId");
                hyId = data.getQueryParameter("hyId");
                distributable = Integer.parseInt(data.getQueryParameter("distributable"));
            } catch (Exception ignore) {

            }
        }
    }

    @Override
    protected AbsNavigationBar initHead() {
        bar = new DefaultNavigationBar.Builder(this).setTitle("客户详情").setLeftClickListener(v -> result()).builder();
        return bar;
    }

    private void postError(HashMap<String, String> map) {
        switch (this.baseInfo.getPoiAuditStatus()) {
            case 1:
                ToastUtils.showShort("客户信息审核中，无法提交审核");
                break;
            case 3:
                ToastUtils.showShort("客户信息审核驳回，无法操作");
                break;
            case 2:
            case 0:
                SnowGroundUtils.track("Event-HYPrivateSea-UploadError", map);
                LocationManager.getInstance().locationPermissions(this, locationListener, true, new PermissionUtil.OnCancelCallBack() {
                    @Override
                    public void cancel() {

                    }
                });
                break;
        }
    }

    //定位监听
    public void initLocationListener() {
        locationListener = bd -> {
            locationInfo = new LocationInfo();
            locationInfo.setLatitude(String.valueOf(bd.getLatitude()));
            locationInfo.setLongitude(String.valueOf(bd.getLongitude()));
            String url = ApiUrl.getDataPostError() + "?poiId=" + poiId + "&" + ApiUrl.getSourceType();
            YNBHybridActivity.jumpYnb(HyPrivateDetailActivity.this, url, locationInfo);
        };
    }

    @Override
    protected void onPause() {
        super.onPause();
        LocationManager.getInstance().unRegisterLocationListener(locationListener);
        LocationManager.getInstance().stopLocation();
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        initRxBus();
        showProgressDialog("正在加载中……");
        setContentView();
        getMineDrugData();
        initLocationListener();
    }

    /**
     * 设置tablayou 部分
     */
    public void setContentView() {
        List<DrugstoreBaseBean> list = new ArrayList<>();
        for (int i = 0; i < titles.length; i++) {
            DrugstoreBaseBean baseBean = new DrugstoreBaseBean();
            baseBean.setTitle(titles[i]);
            baseBean.setDrawable(drawables[i]);
            list.add(baseBean);
        }
        GridLayoutManager layoutManager = new GridLayoutManager(this, 4);
        recyclerView.setLayoutManager(layoutManager);
        DrugstoreGridAdapter adapter = new DrugstoreGridAdapter(R.layout.item_drugstore_grid, list);
        recyclerView.setAdapter(adapter);
        adapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                if (!ViewUtils.isClickable()) {
                    return;
                }
                if (baseInfo != null) {
                    Intent intent = new Intent(HyPrivateDetailActivity.this, HyMineDrugstoreDetailItemActivity.class);
                    intent.putExtra(DrugstoreConstants.INTENT_KEY_MERCHANTID, merchantId);
                    intent.putExtra(DrugstoreConstants.INTENT_KEY_DRUGSTORE_NAME, (TextUtils.isEmpty(baseInfo.getEcCustomerName()) ? baseInfo.getCustomerName() : baseInfo.getEcCustomerName()));
                    intent.putExtra("position", position);
                    intent.putExtra("registerFlag", registerFlag);
                    intent.putExtra(DrugstoreConstants.INTENT_KEY_POI_ID, poiId);
                    startActivityForResult(intent, DrugstoreConstants.REQUEST_DETAIL_INFO);
                }

            }
        });
    }

    /**
     * 请求数据
     */
    public void getMineDrugData() {
        if (!TextUtils.isEmpty(merchantId)) {
            mPresenter.getDrugBaseInfo(merchantId);
        }
    }

    @OnClick({R.id.bt_add_schedule, R.id.tv_mine_drugstore_address
            , R.id.tv_mine_drugstore_name, R.id.iv_call})
    public void onViewClicked(View view) {
        if (!ViewUtils.isClickable()) {
            return;
        }
        switch (view.getId()) {
            case R.id.bt_add_schedule:
                mPresenter.toAddVisit(merchantId, "1");
                break;
            case R.id.tv_mine_drugstore_address:
                if (baseInfo != null) {
                    LBSMapActivity.startMapActivityFromPrivate(HyPrivateDetailActivity.this, poiId, baseInfo.getPoiLatitude(),
                            baseInfo.getPoiLongitude(), baseInfo.getCustomerName(), TextUtils.isEmpty(baseInfo.getAddress()) ? baseInfo.getDefaultAddress() : baseInfo.getAddress(), true, false, true);
                }
                break;
            case R.id.tv_mine_drugstore_name:
                CopyTextDialog copyTextDialog = new CopyTextDialog(mContext);
                copyTextDialog.setText(tvMineDrugstoreName.getText().toString().trim());
                copyTextDialog.show();
                break;
            case R.id.iv_call:
                showContactDialog();
                break;
            default:
                break;
        }
    }

    /**
     * 3.5.5版本改动：调出所有联系人的电话列表，不需要单独暂时祖册电话，因为注册电话也已经默认生成了一个联系人；
     * <p>
     * 旧版逻辑：
     * 电话弹窗
     * 如果注册号码与此客户的联系人中某个号码一致，直接拨打
     * <p>
     * 如果注册号码与此客户的联系人没有号码一致的，下方弹出选择弹窗，弹窗中列出：注册号码，排第一位；
     * 所有联系人号码，排序根据创建时间，按照创建时间倒序，最新创建的放在上面
     */
    private void showContactDialog() {
        if (baseInfo == null) {
            return;
        }
        //未注册
//        if (baseInfo.getRegisterFlag() == 2) {
//            if (TextUtils.isEmpty(baseInfo.getPoiMobilePhone())) {
//                ToastUtils.showLong("暂无电话");
//                return;
//            }
//            CallUtil.call(this,
//                    baseInfo.getPoiMobilePhone(),
//                    baseInfo.getId(),
//                    baseInfo.getCustomerName(),
//                    "customer_detail");
//            return;
//        }
        if (baseInfo.getContactList() == null || baseInfo.getContactList().size() == 0) {
            ToastUtils.showShort("联系人为空，请添加联系人");
        } else {
            new ContactDialog(this,
                    baseInfo.getContactList(),
                    baseInfo.getId(),
                    baseInfo.getCustomerName()).show();
        }
    }


    /**
     * 药店信息
     */
    @SuppressLint("CheckResult")
    @Override
    public void getBaseInfo(RequestBaseBean<HyPrivateDetailBean> requestBaseBean) {
        if (requestBaseBean.getData() != null) {
            this.baseInfo = requestBaseBean.getData();
            this.baseInfo.setId(merchantId);
            this.poiId = requestBaseBean.getData().getPoiId();
            this.registerFlag = requestBaseBean.getData().getRegisterFlag();
            renderData(requestBaseBean.getData());
            operateLayout.setVisibility(View.VISIBLE);

            // 设置righit bar
            bar.rightImgView.setImageResource(R.drawable.ic_customer_more);

            bar.rightImgView.setOnClickListener(v -> {
                List<String> dataList = Arrays.asList(getResources().getStringArray(R.array.customer_detail_public_menu_post));
                CustomerDetailPopup detailPopup = new CustomerDetailPopup(HyPrivateDetailActivity.this, dataList);
                detailPopup.setBackgroundDrawable(R.drawable.pop_bg_right);
                detailPopup.setOnPopItemClickListener(position -> {
                    HashMap<String, String> map = new HashMap<>();
                    map.put("PoiId", merchantId);
                    postError(map);
//                    switch (position) {
//                        //释放至公海
//                        case 0:
//                            SnowGroundUtils.track("Event-HYPrivateSeaDetail-Release", map);
//
//                            Intent intent = new Intent(HyPrivateDetailActivity.this, ReleaseReasonActivity.class);
//                            Bundle bundle = new Bundle();
//                            bundle.putString(DrugstoreConstants.INTENT_KEY_MERCHANTID, merchantId);
//                            bundle.putBoolean(DrugstoreConstants.INTENT_ACTION_FROM_HY, true);
//                            intent.putExtras(bundle);
//                            startActivityForResult(intent, DrugstoreConstants.REQUEST_RELEASE_REASON);
//                            break;
//                        //客户错误信息上报
//                        case 1:
//                            postError(map);
//                            break;
//                    }
                });
                int[] location = new int[2];
                v.getLocationOnScreen(location);
                detailPopup.showPopupWindow(location[0] + v.getWidth() + DisplayUtils.dp2px(15f),
                        location[1] + v.getHeight() + DisplayUtils.dp2px(5f));
            });
        } else {
            //按钮隐藏
            operateLayout.setVisibility(View.GONE);
        }

    }

    /**
     * 渲染数据
     */
    public void renderData(HyPrivateDetailBean baseInfo) {
        //门店地址
        if (TextUtils.isEmpty(baseInfo.getAddress())) {
            tvMineDrugstoreAddress.setText(FormatUtils.textFormat(baseInfo.getDefaultAddress()));
        } else {
            tvMineDrugstoreAddress.setText(FormatUtils.textFormat(baseInfo.getAddress()));
        }
        tvDrugstorePoiId.setText(getString(R.string.str_hy_detail_drugstore_poi_id).concat(FormatUtils.textFormat(baseInfo.getPoiId())));
        if (!TextUtils.isEmpty(hyId)) {
            tvDrugstoreHyId.setVisibility(View.VISIBLE);
            tvDrugstoreHyId.setText(getString(R.string.str_hy_detail_drugstore_hy_id).concat(FormatUtils.textFormat(hyId)));
        } else {
            tvDrugstoreHyId.setVisibility(View.GONE);
        }
        //门店名称(poi名称)
        tvMineDrugstoreName.setText(FormatUtils.textFormat(TextUtils.isEmpty(baseInfo.getEcCustomerName()) ? baseInfo.getCustomerName() : baseInfo.getEcCustomerName()));
    }

    @Override
    public void showNetError() {

    }

    /**
     * 回调
     */
    @Override
    public void getBaseInfoData() {
        getMineDrugData();
    }

    @Override
    public void distributeToBDSuccess(RequestBaseBean baseBean) {
        if (baseBean.isSuccess()) {
            ToastUtils.showLongSafe("分配成功");
            distributable = 0;
            getBaseInfoData();
            distributabled = true;
        }
    }

    @Override
    public void toAddVisit(RequestBaseBean<TaskAndMerchantBean> requestBaseBean) {
        String json = Uri.encode(GsonUtils.toJson(requestBaseBean.getData()));
        //BDM跳转陪访，bd和跟进人跳转拜访
        String encodeJson = Uri.encode(LzRoleInfoManager.INSTANCE.getRoleBeansJson());
        String url;
        if (RoleTypeConfig.isBDMOrGJRBDM()) {
            url = "/add_accompany_visit_page?isHeyeVisit=1&rolesJSON=" + encodeJson + "&externalJson=" + json;
        } else {
            url = "/add_visit_page?isHeyeVisit=1&rolesJSON=" + encodeJson + "&externalJson=" + json;
        }
        ContainerRuntime.INSTANCE.getRouter().open(this, url, null);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == -1) {
            if (requestCode == DrugstoreConstants.REQUEST_RELEASE_REASON) {
                RxBus.get().send(RxBusCode.RX_BUS_UPDATE_PRIVATE_LIST);
                finish();
            } else {
                addressBean = (AddressBean) data.getSerializableExtra(DrugstoreConstants.INTENT_KEY_ADDRESS_INFO);
                if (addressBean != null) {
                    String text = tvMineDrugstoreAddress.getText().toString();
                    StringBuilder builder = new StringBuilder();
                    builder.append(addressBean.getAddress());
                    builder.append(TextUtils.isEmpty(addressBean.getDetailAddress()) ? "" : addressBean.getDetailAddress());
                    if (TextUtils.isEmpty(text) || !text.equals(builder.toString())) {
                        change = true;
                    }
                    tvMineDrugstoreAddress.setText(builder.toString());
                }
            }
        } else if (resultCode == -2) {
            //新建拜访success
            getBaseInfoData();
        }
    }

    private void result() {
        if (change) {
            setResult(RESULT_OK);
        }
        if (distributabled) {
            RxBus.get().send(RxBusCode.RX_BUS_UPDATE_PRIVATE_LIST);
        }
        finish();
    }

    @Override
    public void onBackPressedSupport() {
        result();
    }


    private void initRxBus() {
        RxBus.get().register(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        RxBus.get().unRegister(this);
    }


}
