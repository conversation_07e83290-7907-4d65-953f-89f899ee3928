package com.ybm100.app.crm.task.adapter;

import androidx.annotation.NonNull;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.task.bean.ExecutorLevelItem;

/**
 * Created by dengmingjia on 2019/1/14
 */
public class ExecutorGroupAdapter extends BaseQuickAdapter<ExecutorLevelItem, BaseViewHolder> {
    public ExecutorGroupAdapter() {
        super(R.layout.item_executor_group);
    }

    @Override
    protected void convert(BaseViewHolder helper, ExecutorLevelItem item) {
        int position = helper.getAdapterPosition();
        helper.setText(R.id.tv_name, item.getContent());
        if (position == getItemCount() - 1) {
            helper.setTextColor(R.id.tv_name, mContext.getResources().getColor(R.color.text_color_666666));
        } else {
            helper.setTextColor(R.id.tv_name, mContext.getResources().getColor(R.color.text_color_35C561));
        }
    }

    @Override
    public void addData(@NonNull ExecutorLevelItem data) {
        super.addData(data);
        notifyDataSetChanged();
    }

}
