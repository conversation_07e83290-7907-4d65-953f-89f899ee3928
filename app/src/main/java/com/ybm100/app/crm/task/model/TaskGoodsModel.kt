package com.ybm100.app.crm.task.model

import com.xyy.utilslibrary.base.BaseModel
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.helper.RxHelper
import com.ybm100.app.crm.api.ApiService
import com.ybm100.app.crm.net.RetrofitCreateHelper
import com.ybm100.app.crm.task.bean.TaskGoodsListDataBean
import com.ybm100.app.crm.task.contract.TaskGoodsContract
import io.reactivex.Observable
import java.util.*

class TaskGoodsModel : BaseModel(), TaskGoodsContract.ITaskGoodsModel {
    override fun getTaskList(queryMap: Map<String, String>): Observable<RequestBaseBean<TaskGoodsListDataBean?>?> {
        return RetrofitCreateHelper.createApi(ApiService::class.java).getTaskGoodsList(queryMap as HashMap<String, String>?)
                .compose(RxHelper.rxSchedulerHelper<RequestBaseBean<TaskGoodsListDataBean?>?>())
        /*return RetrofitCreateHelper.createApiMock(ApiService::class.java).getTaskGoodsList(queryMap as HashMap<String, String>?)
                .compose(RxHelper.rxSchedulerHelper<RequestBaseBean<TaskGoodsListDataBean?>?>())*/
    }
}