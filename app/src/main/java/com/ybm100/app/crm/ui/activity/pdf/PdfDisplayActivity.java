package com.ybm100.app.crm.ui.activity.pdf;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.github.barteksc.pdfviewer.PDFView;
import com.tbruyelle.rxpermissions2.RxPermissions;
import com.xyy.common.navigationbar.AbsNavigationBar;
import com.xyy.common.navigationbar.DefaultNavigationBar;
import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.activity.BaseMVPCompatActivity;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.contract.pdf.PdfDisplayContract;
import com.ybm100.app.crm.presenter.pdf.PdfDisplayPresenter;
import com.ybm100.app.crm.utils.FileUtil;
import com.ybm100.app.crm.utils.ShareUtil;

import java.io.File;

import butterknife.BindView;
import io.reactivex.functions.Consumer;

/**
 * 展示PDF
 */
public class PdfDisplayActivity extends BaseMVPCompatActivity<PdfDisplayPresenter> implements PdfDisplayContract.IPdfDisplayView {

    @BindView(R.id.tvTip)
    TextView tvTip;
    @BindView(R.id.pdfView)
    PDFView pdfView;

    private String title;
    private String mTargetPath;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_pdf_display;
    }

    @Override
    @SuppressLint("CheckResult")
    protected void initView(Bundle savedInstanceState) {
        title = getIntent().getStringExtra("title");
        String url = getIntent().getStringExtra("fileurl");
        showWaitDialog("正在下载文件");
        new RxPermissions(this).request(
                android.Manifest.permission.READ_EXTERNAL_STORAGE,
                android.Manifest.permission.WRITE_EXTERNAL_STORAGE)
                        .subscribe(new Consumer<Boolean>() {
                            @Override
                            public void accept(Boolean aBoolean) throws Exception {
                                if (aBoolean) {
                                    mPresenter.download(url, FileUtil.getExternalFilePath(getApplication()));
                                }
                            }
                        }, new Consumer<Throwable>() {
                            @Override
                            public void accept(Throwable throwable) throws Exception {

                            }
                        });
    }

    @Override
    protected AbsNavigationBar initHead() {
        return new DefaultNavigationBar
                .Builder(this)
                .setTitle(title)
                .setRightIcon(R.drawable.ic_share)
                .setRightClickListener(v -> ShareUtil.sharePdfFileWechatFriend(PdfDisplayActivity.this, new File(mTargetPath))).builder();
    }

    @Override
    public void downloadSuccess(String path, boolean isPdf) {
        mTargetPath = path;
        hideWaitDialog();
        tvTip.setVisibility(!isPdf? View.VISIBLE: View.GONE);
        pdfView.setVisibility(isPdf? View.VISIBLE: View.GONE);
        if (isPdf) {
            previewPdf(path);
        }
    }

    /**
     * 预览pdf
     * @param targetPath
     */
    private void previewPdf(String targetPath) {
        pdfView.fromFile(new File(targetPath))
                .enableSwipe(true)          // allows to block changing pages using swipe
                .defaultPage(0)
                .onError(t -> tvTip.setVisibility(View.VISIBLE))
                .enableAnnotationRendering(true) // render annotations (such as comments, colors or forms)
                .load();
    }

    @Override
    public void downloadFailure(String errorMsg) {
        hideWaitDialog();
        showToast(errorMsg);
    }

    @NonNull
    @Override
    public BasePresenter initPresenter() {
        return PdfDisplayPresenter.newInstance();
    }

    @Override
    public void showNetError() {

    }
}
