package com.ybm100.app.crm.bean.drugstore.minedrugstore;

import java.io.Serializable;
import java.util.List;

/**
 * author :lx
 * date 2019/1/7.
 * email： <EMAIL>
 */
public class CommonListBean extends BaseBean implements Serializable {

    /**
     * size : 30
     * count : 359
     * productList :
     */

    private int size;
    private int count;
    private int activeTagCount;
    private int activeTagsize;

    private List<CommonProcureProductList> productList;
    private List<CommonProcureProductList> activeTagProductList;

    public List<CommonProcureProductList> getActiveTagProductList() {
        return activeTagProductList;
    }

    public void setActiveTagProductList(List<CommonProcureProductList> activeTagProductList) {
        this.activeTagProductList = activeTagProductList;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getActiveTagCount() {
        return activeTagCount;
    }

    public void setActiveTagCount(int activeTagCount) {
        this.activeTagCount = activeTagCount;
    }

    public int getActiveTagsize() {
        return activeTagsize;
    }

    public void setActiveTagsize(int activeTagsize) {
        this.activeTagsize = activeTagsize;
    }

    public List<CommonProcureProductList> getProductList() {
        return productList;
    }

    public void setProductList(List<CommonProcureProductList> productList) {
        this.productList = productList;
    }
}
