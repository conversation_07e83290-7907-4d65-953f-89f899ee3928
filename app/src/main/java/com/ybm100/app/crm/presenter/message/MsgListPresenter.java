package com.ybm100.app.crm.presenter.message;

import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.rxbus.RxBus;
import com.ybm100.app.crm.bean.message.MessageApiBean;
import com.ybm100.app.crm.bean.message.MessageBean;
import com.ybm100.app.crm.contract.message.MessageContract;
import com.ybm100.app.crm.contract.message.MsgListContract;
import com.ybm100.app.crm.model.message.MessageModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;
import com.ybm100.app.crm.presenter.BaseRecyclerPresenter;

import java.util.List;

import io.reactivex.Observable;

import static com.ybm100.app.crm.constant.RxBusCode.RX_BUS_SHOW_RED_POINT;

/**
 * Created by XyyMvpSportTemplate on 12/22/2018 11:47
 */
public class MsgListPresenter extends BaseRecyclerPresenter<MessageContract.IMessageModel, MsgListContract.IMsgListView, RequestBaseBean<MessageApiBean>, MessageBean> {


    private boolean lastPage;

    public static MsgListPresenter newInstance() {
        MsgListPresenter msgListPresenter = new MsgListPresenter();
        return msgListPresenter;
    }

    @Override
    protected int getDefaultPageNo() {
        return 0;
    }

    @Override
    protected MessageContract.IMessageModel getModel() {
        return MessageModel.newInstance();
    }

    int type = -1;
    private int unread;

    @Override
    protected Observable<RequestBaseBean<MessageApiBean>> getObservable() {
        return mIModel.getMsg(type, pageNo, pageSize);
    }

    @Override
    protected List<MessageBean> processData(RequestBaseBean<MessageApiBean> data) {
        unread = data.getData().getUnReadCount();
        lastPage = data.getData().isLastPage();
        mIView.renderUnreadCount(unread);
        return data.getData().getRows();
    }

    public void getMsg(final boolean refresh, int type) {
        this.type = type;
        getData(refresh);
    }

    @Override
    protected void getData(boolean refresh) {
        if (!lastPage || refresh) {
            super.getData(refresh);
        } else {
            mIView.loadMoreComplete();
        }
    }

    public void read(long id, final int position) {

        mRxManager.register(mIModel.read(type, id).subscribe(new SimpleSuccessConsumer<RequestBaseBean>(mIView) {
            @Override
            public void onSuccess(RequestBaseBean requestBaseBean) {
                RxBus.get().send(RX_BUS_SHOW_RED_POINT);//刷新界面数量
                mIView.updateRead(position, type);
            }
        }, new SimpleErrorConsumer(mIView)));

    }
}
