package com.ybm100.app.crm.ui.fragment.hycontact;

import android.Manifest;
import android.os.Bundle;
import androidx.core.content.ContextCompat;
import android.text.TextUtils;
import android.view.WindowManager;

import com.alibaba.android.arouter.facade.annotation.Autowired;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.gyf.barlibrary.ImmersionBar;
import com.tbruyelle.rxpermissions2.Permission;
import com.tbruyelle.rxpermissions2.RxPermissions;
import com.xyy.utilslibrary.base.activity.BaseCompatActivity;
import com.xyy.utilslibrary.dialog.JYDialog;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.contact.ContactBean;
import com.ybm100.app.crm.permission.PermissionUtil;
import com.ybm100.app.crm.ui.fragment.contact.ContactFragment;
import com.ybm100.app.crm.ui.fragment.contact.ContactSearchFragment;
import com.ybm100.app.crm.utils.HyRouterPath;

import java.io.Serializable;

import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;

/**
 * 新建联系人、联系人详情和搜索联系人
 * HyContactActivity
 */
@Route(path = HyRouterPath.HY_CONTACT_BOOK_PAGE)
public class HyContactActivity extends BaseCompatActivity {

    public static final String TYPE = "type";
    public static final String EDIT = "editable";
    public static final int CONTACT_BOOK = 1;
    public static final int CREATE_CONTACT = 2;
    public static final int CONTACT_DETAIL = 3;
    public static final int CONTACT_SEARCH = 4;

    @Autowired
    int type;
    @Autowired(name = EDIT)
    boolean editable;
    ContactBean bean;
    private JYDialog dialog;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_contact;
    }

    @Override
    protected void initTransferData() {
        super.initTransferData();
        ARouter.getInstance().inject(this);
        Serializable bean = getIntent().getSerializableExtra("bean");
        if (bean instanceof ContactBean) {
            this.bean = (ContactBean) bean;
        }
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        if (type == CONTACT_BOOK && findFragment(ContactFragment.class) == null) {
            loadRootFragment(R.id.fl_contact_activity, ContactFragment.newInstance());
        } else if (type == CREATE_CONTACT && findFragment(HyCreateContactFragment.class) == null) {
            if (bean != null && !TextUtils.isEmpty(bean.getPoiId()) && !TextUtils.isEmpty(bean.getPoiName())) {
                //药店不为空时 药店不能编辑
                loadRootFragment(R.id.fl_contact_activity, HyCreateContactFragment.newInstance(bean, false));
            } else {
                loadRootFragment(R.id.fl_contact_activity, HyCreateContactFragment.newInstance(bean, editable));
            }
        } else if (type == CONTACT_DETAIL && findFragment(HyContactDetailFragment.class) == null) {
            loadRootFragment(R.id.fl_contact_activity, HyContactDetailFragment.newInstance(bean, editable));
        } else if (type == CONTACT_SEARCH && findFragment(ContactSearchFragment.class) == null) {
            loadRootFragment(R.id.fl_contact_activity, ContactSearchFragment.newInstance());
        }
    }

    @Override
    protected void initImmersionBar() {
        // 关闭fitSystemWindow,因为Fragment内已经设置fitSystemWindow = true
        ImmersionBar.with(this).statusBarColor(R.color.white).statusBarDarkFont(true, 0.2f).fitsSystemWindows(false).keyboardEnable(true, WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN).init();
    }

    @Override
    protected void onResume() {
        super.onResume();
        RxPermissions rxPermissions = new RxPermissions(this);
        Disposable subscribe = rxPermissions.requestEach(Manifest.permission.READ_CALL_LOG).subscribe(new Consumer<Permission>() {

            @Override
            public void accept(Permission permission) throws Exception {
                if (permission.granted) {
                    if (dialog != null) {
                        dialog.dismiss();
                    }
                } else if (permission.shouldShowRequestPermissionRationale) {
                    if (dialog != null) {
                        dialog.dismiss();
                    }
                } else {
                    if (dialog == null) {
                        dialog = new JYDialog(mContext, null, false);
                        dialog.setTitleIsVisible(false);
                        dialog.setContent(getString(R.string.please_open_call_log_permission));
                        dialog.setRightButtonTextColor(ContextCompat.getColor(mContext, R.color.color_FF3E0A));
                        dialog.setLeftText("返回", v -> {
                            dialog.dismiss();
                            finish();
                        });
                        dialog.setRightText("去授权", v -> PermissionUtil.openAppSettingDetail(mContext)).show();
                    }
                }
            }
        });
    }
}
