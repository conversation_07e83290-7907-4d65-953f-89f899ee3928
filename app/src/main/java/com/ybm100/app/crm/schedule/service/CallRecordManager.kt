package com.ybm100.app.crm.schedule.service

import android.annotation.SuppressLint
import android.content.Context
import android.content.SharedPreferences
import android.database.Cursor
import android.os.Handler
import android.os.HandlerThread
import android.os.Message
import android.provider.CallLog
import android.telephony.TelephonyManager
import android.text.TextUtils
import android.util.Log
import com.xyy.common.util.Abase
import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.utilslibrary.utils.AppUtils
import com.xyy.utilslibrary.utils.JsonUtils
import com.ybm100.app.crm.net.RetrofitCreateHelper
import com.ybm100.app.crm.net.helper.BaseObserver
import com.ybm100.app.crm.schedule.api.ScheduleApiService
import com.ybm100.app.crm.schedule.model.CallLogModel
import com.ybm100.app.crm.schedule.model.CallRecord
import com.ybm100.app.crm.utils.SharedPrefManager
import com.ybm100.app.crm.utils.SnowGroundUtils
import kotlin.math.abs


object CallRecordManager {

    const val SP_FILE_NAME = "call_record"

    private var sp: SharedPreferences? = null
    private val handlerThread = HandlerThread("handleCallRecordThread")

    private val uploadingIds = mutableListOf<String>()

    private const val MSG_CREATE = 997
    private const val MSG_DELETE = 998
    private const val MSG_HANDLE = 999
    private const val MSG_CLEAR = 1000

    private const val MAX_RETRY_COUNT = 2//sp提交失败的重试次数
    private const val MAX_FAIL_COUNT = 20//app记录的通话记录最大的创建请求失败次数，超过这个次数则认为通话记录失效，兜底用，防止一直存在一些失效的记录
    private const val QUERY_CALL_LOG_COUNT = 10//一次查询通话记录的条数
    private const val MATCH_TIME_BUFFER = 60 * 1000//app记录的电话记录与系统记录的通话记录匹配时，开始时间最大相差值
    private const val CALL_RECORD_DEAD_TIME = 24 * 60 * 60 * 1000//app记录的电话记录失效时间  24小时

    private val handler: Handler by lazy {
        @SuppressLint("HandlerLeak")//这里是个单例，即使单例泄露了也没问题
        object : Handler(handlerThread.looper) {
            override fun handleMessage(msg: Message?) {
                super.handleMessage(msg)
                Log.d("callRecord", "handleMessage:$msg")
                //这里是在子线程执行的
                msg?.let {
                    when (it.what) {
                        MSG_CREATE -> {//创建app通话记录
                            if (it.obj != null && it.obj is CallRecord) {
                                addCallRecordInternal(it.arg1, it.obj as CallRecord)
                            }
                        }
                        MSG_DELETE -> {//删除app记录的通话记录，一般是内部逻辑调用
                            if (it.obj != null && it.obj is String) {
                                deleteCallRecordInternal(it.arg1, it.obj as String)
                            }
                        }
                        MSG_HANDLE -> {//处理app记录的通话记录
                            if (it.obj != null && it.obj is String) {
                                handleCallRecordInternal(it.obj as String)
                            }
                        }
                        MSG_CLEAR -> {//清除本地所有通话记录
                            clearCallRecordInternal()
                        }
                    }
                }

            }
        }
    }

    private fun clearCallRecordInternal() {
        getSP(Abase.getContext())?.edit()?.clear()?.apply()
    }

    private fun deleteCallRecordInternal(retryCount: Int, spKey: String) {
        //重试两次
        if (retryCount < MAX_RETRY_COUNT) {
            Log.d("callRecord", "deleteCallRecordInternal:$spKey")
            val success = getSP(Abase.getContext())?.edit()?.remove(spKey)?.commit() ?: false
            if (!success) {
                //重试，放到消息队列前，重试次数+1
                handler.sendMessageAtFrontOfQueue(handler.obtainMessage(MSG_DELETE, retryCount + 1, 0, spKey))
                track("call_record_delete_internal", spKey)
            }
        } else {
            track("call_record_delete_internal_max", spKey)
        }
    }

    init {
        //这样会导致这个子线程一直存在。按需启动又存在线程创建消耗，可以考虑搞一个handlerThead的线程池
        handlerThread.start()
    }


    fun addCallRecord(phoneNum: String?, customerId: String?, customerName: String?, source: String?) {
        if (checkValid(phoneNum) && !isCalling(Abase.getContext())) {
            val callRecord = createCallRecord(phoneNum, customerId, customerName, source)
            handler.removeMessages(MSG_HANDLE)
            handler.sendMessage(handler.obtainMessage(MSG_CREATE, 0, 0, callRecord))
            track("call_local_record", callRecord)
        }
    }

    fun handleCallRecordImmediately(source: String, delay: Long, refreshEvent: Boolean) {
        if (Abase.getContext() != null
                && AppUtils.isMainProcess(Abase.getContext())
                && SharedPrefManager.getInstance().isLogin
                && !isCalling(Abase.getContext())) {
            if (refreshEvent) {
                handler.removeMessages(MSG_HANDLE)
            }
            if (!handler.hasMessages(MSG_HANDLE)) {
                Log.d("callRecord", "send handle:${source}")
                handler.sendMessageDelayed(handler.obtainMessage(MSG_HANDLE, 0, 0, source), delay)
            }
        }
    }


    fun handleCallRecord(source: String) {
        handleCallRecordImmediately(source, 5000, false)
    }

    /**
     * 一般退出登录需要清空当前账号的电话记录
     */
    fun clearCallRecord() {
        handler.sendEmptyMessage(MSG_CLEAR)
    }

    @SuppressLint("CheckResult")
    private fun handleCallRecordInternal(source: String?) {
        val context = Abase.getContext()
        if (isCalling(context)) {
            Log.d("callRecord", "handleCallRecordInternal isCalling")
            return
        }

        val all = getSP(context)?.all//获取所有app记录的打电话历史
        if (all == null || all.isEmpty()) {
            return
        }
        val consumedList = mutableListOf<CallLogModel>()//本次处理任务中，已被匹配的通话记录列表
        all.map { entry ->
            val spKey = entry.key
            val json = entry.value as String?
            Log.d("callRecord", "all.map:$json")
            json?.run {
                try {
                    JsonUtils.deserialize(this, CallRecord::class.java)
                } catch (ignore: Exception) {
                    null
                }
            }.also { callRecord ->
                var exceptionMsg = "init"
                if (checkValidCallRecord(spKey, callRecord)
                        && callRecord != null
                        && !uploadingIds.contains(getSPKey(callRecord))) {
                    var callLogs: List<CallLogModel> = arrayListOf()
                    try {
                        callLogs = getCallLogs(context, callRecord.phoneNum)//获取最近的系统通话记录
                    } catch (e: Exception) {
                        exceptionMsg = e.message ?: ""
                    }
                    callRecord.callLogList = ""

                    //通过通话记录（callLog）完善本地的通话记录（callRecord）
                    completeCallRecord(callLogs, callRecord, consumedList)

                    //处理已完善的通话记录（callRecord）
                    disposeCallRecord(callRecord, spKey, source, exceptionMsg)
                }
            }
        }
    }

    private fun checkValidCallRecord(spKey: String, callRecord: CallRecord?): Boolean {
        return if (callRecord == null
                || abs(callRecord.startTime - System.currentTimeMillis()) > CALL_RECORD_DEAD_TIME
                || callRecord.failCount > MAX_FAIL_COUNT) {
            Log.d("callRecord", "delete invalid:${callRecord}")
            if (callRecord == null) {
                track("call_record_delete_invalid2", spKey)
            } else {
                track("call_record_delete_invalid1", callRecord)
            }
            /**
             * 出现以下情况则不生成拜访记录，直接删除
             * 1、记录解析失败
             * 2、打电话时间超过24小时
             * 3、上报失败次数超过20次
             */
            handler.sendMessage(handler.obtainMessage(MSG_DELETE, 0, 0, spKey))
            false
        } else {
            true
        }
    }

    private fun disposeCallRecord(callRecord: CallRecord, spKey: String, source: String?, exceptionMsg: String) {
        callRecord.handleSource = source ?: ""
        callRecord.exception = exceptionMsg
        if (callRecord.duration == 0L) {
            Log.d("callRecord", "not connect:${callRecord}")
            if (!callRecord.hasReportNotConnect) {
                track("call_record_notconnected", callRecord)
                callRecord.hasReportNotConnect = true
                Log.d("callRecord", "rewrite:${callRecord}")
                handler.sendMessageAtFrontOfQueue(handler.obtainMessage(MSG_CREATE, 0, 0, callRecord))
            }
            //电话时长为0，无效通话，一般为没有接通,暂时保存这条记录，直到匹配上、或者超过24小时
//            handler.sendMessage(handler.obtainMessage(MSG_DELETE, 0, 0, spKey))
        } else {
            requestCreateSchedule(callRecord, spKey)
        }
    }

    private fun completeCallRecord(callLogs: List<CallLogModel>, callRecord: CallRecord, consumedList: MutableList<CallLogModel>) {
        val matchedCallLogs = arrayListOf<CallLogModel>()
        callRecord.callLogList = ""
        callLogs.forEach { callLog ->
            /**
             *  获取相似的电话记录信息，遍历互相匹配,需要符合所有条件才能算作相似记录：
             *  1、手机号相同
             *  2、app打电话历史存在开始时间
             *  3、通话开始时间相差不超过60s
             *  4、不包含在callRecord生成前的电话记录里
             */
            Log.d("callRecord", "compare:$callLog")
            //添加埋 点记录
            callRecord.callLogList += callLog.convertStr()
            if (callLog.phoneNum == callRecord.phoneNum
                    && callRecord.startTime != -1L
                    && abs(callRecord.startTime - callLog.startTime) <= MATCH_TIME_BUFFER
                    && !consumedList.contains(callLog)) {
                Log.d("callRecord", "match:$callLog")
                matchedCallLogs.add(callLog)
            }
        }
        consumedList.forEach { consumed ->
            Log.d("callRecord", "consumed:$consumed")
        }
        //最接近本地记录时间的系统通话记录
        var bestMatch: CallLogModel? = null //与callRecord开始时间最接近的通话记录

        //匹配最接近本地记录时间的通话记录
        matchedCallLogs.forEach {
            if (bestMatch == null
                    || abs(callRecord.originStartTime - it.startTime) < abs(bestMatch!!.startTime - callRecord.originStartTime)) {
                //当前绝对值小于上次匹配的绝对值
                bestMatch = it
            }
        }
        Log.d("callRecord", "bestMatch:${bestMatch}")


        bestMatch?.let {
            consumedList.add(it)
            //获取真实的电话时间
            callRecord.startTime = it.startTime
            callRecord.duration = it.duration
            Log.d("callRecord", "matched CallRecord:${callRecord}")
        }
    }

    private fun requestCreateSchedule(callRecord: CallRecord, spKey: String) {
        //发送请求
        SharedPrefManager.getInstance().userInfo?.let { userInfo ->
            if (!userInfo.sysUserId.isNullOrEmpty()) {
                val paramsMap = HashMap<String, Any>()
                paramsMap["merchantId"] = callRecord.customerId
                paramsMap["merchantName"] = callRecord.customerName
                paramsMap["mobile"] = callRecord.phoneNum
                paramsMap["talkBeginTime"] = callRecord.startTime
                if (callRecord.duration != -1L) {
                    paramsMap["talkTime"] = callRecord.duration
                } else {
                    paramsMap["talkTime"] = 0
                }
                track("call_record_request", callRecord)
                uploadingIds.add(getSPKey(callRecord))
                RetrofitCreateHelper.createApi(ScheduleApiService::class.java)
                        .createNoCompletedSchedule(paramsMap)
                        .compose(RxHelper.rxSchedulerHelper())
                        .subscribe(object : BaseObserver<String?>() {
                            override fun onFailure(msg: String?) {
                                //如果请求失败了重新将有问题的记录添加回去，下次handle时候再重新试验,失败次数+1
                                Log.d("callRecord", "requestCreateSchedule fail:$callRecord")
                                callRecord.failCount++
                                uploadingIds.remove(getSPKey(callRecord))
                                track("call_record_fail", callRecord)
                                handler.sendMessage(handler.obtainMessage(MSG_CREATE, 0, 0, callRecord))
                            }

                            override fun onSuccess(response: String?) {
                                Log.d("callRecord", "requestCreateSchedule success:$callRecord")
                                //发送创建拜访请求的同时删除本地记录，优先级最高，防止多个handle事件在请求结果回来前触发，从而导致重复创建
                                handler.sendMessageAtFrontOfQueue(handler.obtainMessage(MSG_DELETE, 0, 0, spKey))
                                uploadingIds.remove(getSPKey(callRecord))
                                track("call_record_track2", callRecord)
                            }
                        })
            }
        }
    }

    private fun addCallRecordInternal(retryCount: Int, callRecord: CallRecord) {
        //重试两次
        if (retryCount < MAX_RETRY_COUNT) {
            Log.d("callRecord", "addCallRecordInternal:$callRecord")
            val success = getSP(Abase.getContext())?.edit()?.putString(getSPKey(callRecord), JsonUtils.serialize(callRecord))?.commit()
                    ?: false
            if (!success) {
                //重试，放到消息队列前，重试次数+1
                handler.sendMessageAtFrontOfQueue(handler.obtainMessage(MSG_CREATE, retryCount + 1, 0, callRecord))
                track("call_record_add_internal", callRecord)
            }
        } else {
            track("call_record_add_internal_max", callRecord)
        }
    }


    private fun track(action: String, callRecord: CallRecord) {
        val trackParams = HashMap<String, String>()
        trackParams["ph"] = callRecord.phoneNum
        trackParams["st"] = callRecord.startTime.toString()
        trackParams["dur"] = callRecord.duration.toString()
        trackParams["add"] = callRecord.addSource
        trackParams["handle"] = callRecord.handleSource
        trackParams["ost"] = callRecord.originStartTime.toString()
        trackParams["mId"] = callRecord.customerId
        trackParams["cId"] = getSPKey(callRecord)
        trackParams["cl"] = callRecord.callLogList
        trackParams["ocl"] = callRecord.originCallLogList
        if (callRecord.exception.isNotEmpty()) trackParams["ext"] = callRecord.exception
        if (callRecord.originException.isNotEmpty()) trackParams["oext"] = callRecord.exception
        trackParams["fc"] = callRecord.failCount.toString()
        try {
            trackParams["ic"] = isCalling(Abase.getContext()).toString()
        } catch (ignore: Exception) {
        }
        SnowGroundUtils.track(action, trackParams)
    }

    private fun track(action: String, params: String) {
        val trackParams = HashMap<String, String>()
        trackParams["params"] = params
        SnowGroundUtils.track(action, trackParams)
    }

    private fun getSPKey(callRecord: CallRecord): String {
        return "${callRecord.phoneNum}|${callRecord.originStartTime}"
    }

    fun getSP(context: Context): SharedPreferences? {
        if (sp == null) {
            try {
                val sysUserId = SharedPrefManager.getInstance().userInfo?.sysUserId
                sp = context.getSharedPreferences("${SP_FILE_NAME}_${sysUserId}", Context.MODE_PRIVATE)
            } catch (ignore: Throwable) {
            }
        }
        return sp
    }

    private fun createCallRecord(phoneNum: String?, customerId: String?, customerName: String?, source: String?): CallRecord {
        val phone = phoneNum?.replace("-", "") ?: ""
        var originCallLogStr = ""
        var exceptionMsg = ""
        try {
            getCallLogs(Abase.getContext(), phone).forEach {
                originCallLogStr += it.convertStr()
            }
        } catch (e: Exception) {
            exceptionMsg = e.message ?: ""
        }
        return CallRecord(phone,
                System.currentTimeMillis(),
                System.currentTimeMillis(),
                -1L,
                customerId ?: "",
                customerName ?: "",
                0,
                source ?: "",
                "",
                "",
                "",
                originCallLogStr,
                exceptionMsg,
                false
        )
    }

    private fun checkValid(phoneNum: String?): Boolean {
        if (phoneNum == null || phoneNum.isEmpty()) {
            return false
        }
        return true
    }


    private fun getCurrentPhoneState(context: Context): Int {
        val tm = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
        return tm.callState
    }


    private fun isCalling(context: Context): Boolean {
        return getCurrentPhoneState(context) == TelephonyManager.CALL_STATE_OFFHOOK
    }

    private val CALL_LOGS_PROJECTION = arrayOf(
            CallLog.Calls._ID,
            CallLog.Calls.NUMBER,
            CallLog.Calls.DATE,
            CallLog.Calls.DURATION,
            CallLog.Calls.TYPE)

    private fun getCallLogs(context: Context, phone: String): List<CallLogModel> {
        val callLogs = arrayListOf<CallLogModel>()
        var cursor: Cursor? = null
        try {
            cursor = context.contentResolver.query(CallLog.Calls.CONTENT_URI,
                    CALL_LOGS_PROJECTION, "${CallLog.Calls.NUMBER} =? and ${CallLog.Calls.TYPE} =?",
                    arrayOf(phone, CallLog.Calls.OUTGOING_TYPE.toString()), CallLog.Calls.DATE + " DESC")

            if (cursor != null) {
                while (cursor.moveToNext()) {
                    var phoneNum = cursor.getString(cursor.getColumnIndex(CallLog.Calls.NUMBER))
                    if (TextUtils.isEmpty(phoneNum)) {
                        continue
                    }
                    phoneNum = phoneNum.replace("-", "")
                    val id = cursor.getLong(cursor.getColumnIndex(CallLog.Calls._ID))
                    val duration = cursor.getLong(cursor.getColumnIndex(CallLog.Calls.DURATION))
                    val date = cursor.getLong(cursor.getColumnIndex(CallLog.Calls.DATE))
                    callLogs.add(CallLogModel(id, phoneNum, date, duration))
                    if (callLogs.size > QUERY_CALL_LOG_COUNT) {
                        break
                    }
                }
            }
        } catch (e: Exception) {
            throw Exception("${e::class.java.simpleName}|${e.message}")
        } finally {
            cursor?.close()
        }
        callLogs.forEach {
            Log.d("callRecord", it.toString())
        }
        return callLogs
    }

}