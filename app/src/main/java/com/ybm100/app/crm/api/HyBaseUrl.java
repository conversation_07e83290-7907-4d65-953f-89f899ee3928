package com.ybm100.app.crm.api;

import com.ybm100.app.crm.constant.AppNetConfig;
import com.ybm100.app.crm.platform.RuntimeEnv;

public class HyBaseUrl {
    private static final String HY_CONTACT_HOST_RELEASE= "http://hycrm.ybm100.com/";//荷叶健康联系人正式域名
    private static final String HY_CONTACT_HOST_DEV= "http://hycrm.dev.ybm100.com/";//荷叶健康联系人开发域名
    private static final String HY_CONTACT_HOST_TEST= "http://hycrm.test.ybm100.com/";//荷叶健康联系人测试域名
    public static String getBaseUrl() {
        //String currFlavor = SharedPrefManager.getInstance().getCurrFlavor();
        if (AppNetConfig.FlavorType.PROD.equals(RuntimeEnv.INSTANCE.getEnv())) {
            return HY_CONTACT_HOST_RELEASE;
        } else {
            return HY_CONTACT_HOST_TEST;
//            switch (currFlavor) {
//                case AppNetConfig.FlavorType.DEV:
//                    return HY_CONTACT_HOST_DEV;
//                case AppNetConfig.FlavorType.BETA:
//                    return HY_CONTACT_HOST_TEST;
//                default:
//                    return HY_CONTACT_HOST_RELEASE;
//            }
        }
    }
}
