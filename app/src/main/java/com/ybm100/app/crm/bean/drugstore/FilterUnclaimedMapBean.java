package com.ybm100.app.crm.bean.drugstore;

import com.ybm100.app.crm.order.bean.TagBean;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/8
 */
public class FilterUnclaimedMapBean implements Serializable {
    private List<TagBean> licenseType;
    private List<TagBean> registerSource;
    private HashMap<String, String> map;

    public List<TagBean> getLicenseType() {
        return licenseType;
    }

    public void setLicenseType(List<TagBean> licenseType) {
        this.licenseType = licenseType;
    }

    public List<TagBean> getRegisterSource() {
        return registerSource;
    }

    public void setRegisterSource(List<TagBean> registerSource) {
        this.registerSource = registerSource;
    }

    public HashMap<String, String> getMap() {
        return map;
    }

    public void setMap(HashMap<String, String> map) {
        this.map = map;
    }
}
