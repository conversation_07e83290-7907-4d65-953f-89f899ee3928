package com.ybm100.app.crm.flutter.channel

import android.os.Bundle
import androidx.fragment.app.FragmentActivity
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.xyy.utilslibrary.rxbus.RxBus

class EventBusHandler : BaseHandler() {

    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {
        val code = params["code"]
        val map = params["data"] as Map<*, *>?

        val bundle = Bundle()
        map?.forEach {
            if (it.key is String && it.key != null && it.value != null) {
                when (it.value) {
                    is String -> {
                        bundle.putString(it.key as String, it.value as String)
                    }
                    is Boolean -> {
                        bundle.putBoolean(it.key as String, it.value as Boolean)
                    }
                    is Int -> {
                        bundle.putInt(it.key as String, it.value as Int)
                    }
                    is Double -> {
                        bundle.putDouble(it.key as String, it.value as Double)
                    }
                }

            }
        }
        if (code is Int) {
            RxBus.get().send(code, bundle)
        } else if (code is String) {
            code.toIntOrNull()?.let {
                RxBus.get().send(it, bundle)
            }
        }
    }

}
