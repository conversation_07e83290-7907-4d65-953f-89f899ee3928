package com.ybm100.app.crm.ui.fragment.drugstore.minedrug

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentPagerAdapter
import androidx.core.content.ContextCompat
import androidx.viewpager.widget.ViewPager
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.xyy.common.util.ToastUtils
import com.xyy.utilslibrary.base.activity.BaseCompatActivity
import com.xyy.utilslibrary.utils.AppUtils
import com.ybm100.app.crm.R
import com.ybm100.app.crm.bean.drugstore.minedrugstore.LicenceBean
import com.ybm100.app.crm.utils.SnowGroundUtils
import kotlinx.android.synthetic.main.activity_licence_photo_view.*
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException
import java.lang.StringBuilder

/**
 * @author: zcj
 * @time:2020/7/31.
 * Description:
 */
class LicensePhotoViewActivity : BaseCompatActivity() {
    private val REQUEST_WRITE = 20001//申请权限的请求码
    override fun getLayoutId(): Int = R.layout.activity_licence_photo_view
    private var firstPosition = 0
    private var currPosition = 0
    private var allUrlList: ArrayList<LicenceBean.ListBean> = ArrayList()
    override fun initView(savedInstanceState: Bundle?) {
        val urlList = intent.getParcelableArrayListExtra<LicenceBean>(INTENT_URL_LIST)
        firstPosition = intent.getIntExtra(INTENT_CURR_POSITION, 0)
        urlList?.forEachIndexed { index0, listBean0 ->
            listBean0.enclosureList?.forEachIndexed { index, listBean ->
                val imageBean: LicenceBean.ListBean = LicenceBean.ListBean()
                val sb = StringBuilder()
                sb.append("(").append(index + 1).append("/").append(listBean0.enclosureList.size).append(")")
                imageBean.enclosureName = listBean0.name + sb.toString()
                imageBean.url = listBean.url
                allUrlList.add(imageBean)
            }
            if (index0 < firstPosition) {
                currPosition += listBean0.enclosureList?.size ?: 0
            }
        }
        val index = allUrlList[0].enclosureName?.indexOf("(") ?: 0
        val num = allUrlList[0].enclosureName?.substring(index, allUrlList[0].enclosureName.length)
        tv_title.text = allUrlList[0].enclosureName?.substring(0, index)
        tv_title_num.text = num
        viewPage.adapter = object : androidx.fragment.app.FragmentPagerAdapter(supportFragmentManager) {
            override fun getCount(): Int {
                return allUrlList.size
            }

            override fun getItem(i: Int): androidx.fragment.app.Fragment {
                return LicensePhotoViewFragment.newInstance(allUrlList[i])
            }
        }
        viewPage.addOnPageChangeListener(object : androidx.viewpager.widget.ViewPager.OnPageChangeListener {
            override fun onPageScrollStateChanged(p0: Int) {

            }

            override fun onPageScrolled(p0: Int, p1: Float, p2: Int) {
            }

            override fun onPageSelected(i: Int) {
                currPosition = i
                val index = allUrlList[i].enclosureName?.indexOf("(") ?: 0
                val num = allUrlList[i].enclosureName?.substring(index, allUrlList[i].enclosureName.length)
                tv_title.text = allUrlList[i].enclosureName?.substring(0, index)
                tv_title_num.text = num
            }

        })
        viewPage.currentItem = currPosition
        onClick()
    }

    private fun onClick() {
        iv_close.setOnClickListener {
            finish()
        }
        layout_save.setOnClickListener {
            checkPermission(allUrlList[currPosition].url)
        }
    }


    /**
     * 检查权限
     */
    private fun checkPermission(url: String) {
        //判断是否6.0以上的手机   不是就不用
        if (Build.VERSION.SDK_INT >= 23) {
            //判断是否有这个权限
            if (ContextCompat.checkSelfPermission(this,
                            Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                //2、申请权限: 参数二：权限的数组；参数三：请求码
                requestPermissions(arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE), REQUEST_WRITE)
            } else {
                saveImageToGallery(this, url)
            }
        } else {
            saveImageToGallery(this, url)
        }
    }

    private fun saveImageToGallery(context: Context?, url: String) {
        Glide.with(AppUtils.getContext())
                .asBitmap()   //强制转换Bitmap
                .load(url)
                .into(object : SimpleTarget<Bitmap>() {
                    override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                        val SAVE_PIC_PATH = if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED, ignoreCase = true)) {
                            Environment.getExternalStorageDirectory().absolutePath
                        } else
                            "/mnt/sdcard"//保存到SD卡
                        // 首先保存图片
                        val appDir = File("$SAVE_PIC_PATH/豆芽/")
                        if (!appDir.exists()) {
                            appDir.mkdir()
                        }
                        val nowSystemTime = System.currentTimeMillis()
                        val fileName = "$nowSystemTime.png"
                        val file = File(appDir, fileName)
                        val map = HashMap<String, String>()
                        map["url"] = url
                        map["localPath"] = file.absolutePath
                        try {
                            if (!file.exists()) {
                                file.createNewFile()
                            }
                            val fos = FileOutputStream(file)
                            resource.compress(Bitmap.CompressFormat.JPEG, 100, fos)
                            fos.flush()
                            fos.close()
                        } catch (e: FileNotFoundException) {
                            map["error"] = e.toString()
                            SnowGroundUtils.track("Event-LicensePhotoView-Error", map)
                            e.printStackTrace()
                        } catch (e: IOException) {
                            map["error"] = e.toString()
                            SnowGroundUtils.track("Event-LicensePhotoView-Error", map)
                            e.printStackTrace()
                        }
                        //保存图片后发送广播通知更新数据库
                        val uri = Uri.fromFile(file)
                        context!!.sendBroadcast(Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, uri))
                        ToastUtils.showShort("图片保存成功")
                        SnowGroundUtils.track("Event-LicensePhotoView-Path", map)
                    }
                })

    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putParcelable("android:support:fragments", null)
    }

    companion object {
        const val INTENT_URL_LIST = "intent_url_list"
        const val INTENT_CURR_POSITION = "intent_curr_position"
        fun start(context: Activity?, urlList: ArrayList<LicenceBean>, currPosition: Int) {
            if (context != null && !urlList.isNullOrEmpty()) {
                val intent = Intent(context, LicensePhotoViewActivity::class.java).also {
                    it.putParcelableArrayListExtra(INTENT_URL_LIST, urlList)
                    it.putExtra(INTENT_CURR_POSITION, currPosition)
                }
                context.startActivity(intent)
            }
        }
    }
}