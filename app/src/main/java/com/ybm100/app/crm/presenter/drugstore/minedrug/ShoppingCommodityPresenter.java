package com.ybm100.app.crm.presenter.drugstore.minedrug;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.CartBean;
import com.ybm100.app.crm.contract.drugstore.minedrug.ShoppingCommodityContract;
import com.ybm100.app.crm.goodsmanagement.bean.EstimatedPriceListBean;
import com.ybm100.app.crm.model.drugstore.minedrug.ShoppingCommodityModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

import java.util.HashMap;

import io.reactivex.functions.Consumer;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:20
 */
public class ShoppingCommodityPresenter extends BasePresenter<ShoppingCommodityContract.IShoppingCommodityModel, ShoppingCommodityContract.IShoppingCommodityView> {

    public static ShoppingCommodityPresenter newInstance() {
        return new ShoppingCommodityPresenter();
    }

    @Override
    protected ShoppingCommodityModel getModel() {
        return ShoppingCommodityModel.newInstance();
    }


    public void getCartListRequest(String shopId) {
        if (mIView == null || mIModel == null) return;

        mRxManager.register(mIModel.getCartListRequest(shopId).subscribe(new SimpleSuccessConsumer<RequestBaseBean<CartBean>>(mIView,"") {
            @Override
            public void onSuccess(RequestBaseBean<CartBean> requestBaseBean) {

                mIView.getCartListRequestSuccess(requestBaseBean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }

    public void getEstimatedPrices(HashMap<String, String> queryMap){

        mRxManager.register(mIModel.getEstimatedPrices(queryMap).subscribe(new SimpleSuccessConsumer<RequestBaseBean<EstimatedPriceListBean>>(mIView) {
            @Override
            public void onSuccess(RequestBaseBean<EstimatedPriceListBean> estimatedPriceListBeanRequestBaseBean) {
                mIView.onGetEstimatedPricesSuccess(estimatedPriceListBeanRequestBaseBean);
            }

            @Override
            public void onFailure(int errorCode) {
//                super.onFailure(errorCode);
            }
        }, new Consumer<Throwable>() {
            @Override
            public void accept(Throwable throwable) throws Exception {

            }
        }));
    }
}
