package com.ybm100.app.crm.presenter.share;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.share.ShareListBean;
import com.ybm100.app.crm.contract.share.ShareListContract;
import com.ybm100.app.crm.model.share.ShareListModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by XyyMvpSportTemplate on 03/05/2019 19:07
 */
public class ShareListPresenter extends BasePresenter<ShareListContract.IShareListModel, ShareListContract.IShareListView> {

    private int offset = 0;
    private final int limit = 10;

    public static ShareListPresenter newInstance() {
        return new ShareListPresenter();
    }

    @Override
    protected ShareListModel getModel() {
        return ShareListModel.newInstance();
    }

    /**
     * 获取列表
     */
    public void getListData(final boolean refresh, Map<String, String> params) {
        if (mIModel == null || mIView == null) {
            return;
        }
        if (params == null) {
            params = new HashMap<>();
        }
        if (refresh) {
            offset = 0;
            mIView.enableLoadMore(true);
        }
        params.put("offset", offset + "");
        params.put("limit", limit + "");
        mRxManager.register(mIModel.getListData(params).subscribe(new SimpleSuccessConsumer<RequestBaseBean<ShareListBean>>(mIView) {
            @Override
            public void onSuccess(RequestBaseBean<ShareListBean> baseBean) {
                if (mIView == null) return;
                if (baseBean != null && baseBean.getData() != null) {
                    if (baseBean.getData().isLastPage()) {
                        mIView.loadMoreComplete();
                    }
                    mIView.getListDataSuccess(refresh, baseBean);
                }
                offset++;
            }
        }, new SimpleErrorConsumer(mIView)));
    }

    /**
     * 分享统计
     */
    public void shareTimesAddOne(String sourceId) {
        if (mIModel == null || mIView == null) {
            return;
        }
        mRxManager.register(mIModel.shareTimesAddOne(sourceId).subscribe(new SimpleSuccessConsumer<RequestBaseBean>(mIView) {
            @Override
            public void onSuccess(RequestBaseBean baseBean) {
                if (mIView == null) return;
                mIView.shareTimesAddOneSuccess(baseBean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }
}
