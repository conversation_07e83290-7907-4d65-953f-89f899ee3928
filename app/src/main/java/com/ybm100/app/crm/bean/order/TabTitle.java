package com.ybm100.app.crm.bean.order;

import android.text.TextUtils;

import com.flyco.tablayout.listener.CustomTabEntity;

/**
 * author :lx
 * date 2019/1/11.
 * email： <EMAIL>
 */
public class TabTitle implements CustomTabEntity {

    private final String title;

    public TabTitle(String title) {
        this.title = title;
    }

    @Override
    public String getTabTitle() {
        return TextUtils.isEmpty(title) ? "" : title;
    }

    @Override
    public int getTabSelectedIcon() {
        return 0;
    }

    @Override
    public int getTabUnselectedIcon() {
        return 0;
    }
}
