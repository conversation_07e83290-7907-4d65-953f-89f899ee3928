package com.ybm100.app.crm.task.model

import com.xyy.utilslibrary.base.BaseModel
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.helper.RxHelper
import com.ybm100.app.crm.api.ApiService
import com.ybm100.app.crm.net.RetrofitCreateHelper
import com.ybm100.app.crm.task.bean.ConfirmRecommendationBean
import com.ybm100.app.crm.task.bean.SelectCustomersBean
import com.ybm100.app.crm.task.contract.SelectCustomersContract
import io.reactivex.Observable
import java.util.*

class SelectCustomersModel : BaseModel(), SelectCustomersContract.ISelectCustomersModel {
    override fun getCustomerList(queryMap: Map<String, String>): Observable<RequestBaseBean<SelectCustomersBean?>?> {
        return RetrofitCreateHelper.createApi(ApiService::class.java).getCustomerList(queryMap as HashMap<String, String>?)
                .compose(RxHelper.rxSchedulerHelper<RequestBaseBean<SelectCustomersBean?>?>())
        /*return RetrofitCreateHelper.createApiMock(ApiService::class.java).getCustomerList(queryMap as HashMap<String, String>?)
                .compose(RxHelper.rxSchedulerHelper<RequestBaseBean<SelectCustomersBean?>?>())*/
    }

    override fun confirmRecommendation(queryMap: Map<String, String>): Observable<RequestBaseBean<ConfirmRecommendationBean?>?> {
        return RetrofitCreateHelper.createApi(ApiService::class.java).confirmRecommendation(queryMap as HashMap<String, String>?)
                .compose(RxHelper.rxSchedulerHelper<RequestBaseBean<ConfirmRecommendationBean?>?>())
    }
}