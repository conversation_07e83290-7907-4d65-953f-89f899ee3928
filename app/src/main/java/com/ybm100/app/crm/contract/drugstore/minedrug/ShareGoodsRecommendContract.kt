package com.ybm100.app.crm.contract.drugstore.minedrug

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.bean.goods.GoodsCategoryBean
import com.ybm100.app.crm.bean.schedule.ConditionFiltrateBean
import io.reactivex.Observable

/**
 * @author: zcj
 * @time:2019/11/5.
 *
 * Description:
 */
class ShareGoodsRecommendContract {
    interface IShareGoodsRecommendModel : IBaseModel {
        // 获取分类
        fun getCategoryName(categoryName: String?): Observable<RequestBaseBean<ArrayList<GoodsCategoryBean>>>

        //获取筛选数据
        fun getConditionEnums(enumType: Int): Observable<RequestBaseBean<ConditionFiltrateBean>>
    }

    interface IShareGoodsRecommendView : IBaseActivity {
        fun getCategoryNameSuccess(first: Boolean, baseBean: RequestBaseBean<ArrayList<GoodsCategoryBean>>)

        fun getConditionEnumsSuccess(baseBean: RequestBaseBean<ConditionFiltrateBean>)
    }
}