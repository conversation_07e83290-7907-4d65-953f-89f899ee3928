package com.ybm100.app.crm.bean.drugstore;

import java.io.Serializable;

/**
 * author :lx
 * date 2018/12/25.
 * email： <EMAIL>
 *  药店联系人bean
 */
public class MerchantContact implements Serializable {
    private String id;
    private String contactJob; //	岗位	string
    private String contactJobName; //	岗位	string
    private String contactMobile;    //联系人电话	string
    private String contactName;    //联系人姓名	string
    private String mobile;    //注册手机号	string	@mock=18627920908
    private String nickname;    //联系人	string
    private String realName;    //药店名	string	@mock=天津大药房
    private String regName;    //注册名	string	@mock=张三
    private int status;    //注册状态	number	@mock=1
    private String sysJobNumber;    //关联销售工号	string
    private String sysRealName;    //关联销售名称	string

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getContactJob() {
        return contactJob;
    }

    public String getContactJobName() {
        return contactJobName;
    }

    public void setContactJobName(String contactJobName) {
        this.contactJobName = contactJobName;
    }

    public void setContactJob(String contactJob) {
        this.contactJob = contactJob;
    }

    public String getContactMobile() {
        return contactMobile;
    }

    public void setContactMobile(String contactMobile) {
        this.contactMobile = contactMobile;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getRegName() {
        return regName;
    }

    public void setRegName(String regName) {
        this.regName = regName;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getSysJobNumber() {
        return sysJobNumber;
    }

    public void setSysJobNumber(String sysJobNumber) {
        this.sysJobNumber = sysJobNumber;
    }

    public String getSysRealName() {
        return sysRealName;
    }

    public void setSysRealName(String sysRealName) {
        this.sysRealName = sysRealName;
    }

}
