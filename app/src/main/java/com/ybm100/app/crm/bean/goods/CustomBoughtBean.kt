package com.ybm100.app.crm.bean.goods

/**
 * @author: zcj
 * @time:2019/12/30.
 *
 * Description:已购客户
 */
class CustomBoughtBean {
    var lastPage: Boolean = false
    var currentPage: Int = 0
    var total: Int = 0
    var rows: List<RowBean>? = null

    class RowBean {
        /**
         *     merchantName	客户名称	string	@mock=张三
         *     totalAmount	已购金额	number	@mock=200
         *     totalAmountRate	环比	string
         *     totalcount	已购数量	number	@mock=30
         */
        var merchantName: String = ""
        var totalAmount: String = ""
        var totalAmountRate: String = ""
        var totalcount: String = ""
        var merchantId: String = ""
        var customerId: String = ""
    }

}