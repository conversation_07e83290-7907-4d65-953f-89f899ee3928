package com.ybm100.app.crm.ui.adapter.popwindow;

import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.ybm100.app.crm.R;

import java.util.List;

/**
 * @author: zcj
 * @time:2020/3/29. Description:
 */
public class CustomerPopDateAdapter extends BaseQuickAdapter<String, BaseViewHolder> {

    private final List<String> mList;

    public CustomerPopDateAdapter(int layoutResId, @Nullable List<String> data) {
        super(layoutResId, data);
        this.mList = data;
    }

    @Override
    protected void convert(BaseViewHolder helper, String item) {
        helper.setText(R.id.tv_content, item);
    }
}
