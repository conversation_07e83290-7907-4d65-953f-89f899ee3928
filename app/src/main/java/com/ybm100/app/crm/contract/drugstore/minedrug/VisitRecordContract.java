package com.ybm100.app.crm.contract.drugstore.minedrug;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.DrugstoreVisitBean;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:10
 *
 * <AUTHOR>
 */
public interface VisitRecordContract {

    interface IVisitRecordModel extends IBaseModel {
        /**
         * 跟进记录
         *
         * @param limit      每页条数
         * @param merchantId 客户ID
         * @param offset     页码
         * @return 数据
         */
        Observable<RequestBaseBean<DrugstoreVisitBean>> followInRecords(int limit,
                                                                        String merchantId,
                                                                        int offset);
    }

    interface IVisitRecordView extends IBaseActivity {
        void getVisitListDataSuccess(RequestBaseBean<DrugstoreVisitBean> requestBaseBean);
    }

}
