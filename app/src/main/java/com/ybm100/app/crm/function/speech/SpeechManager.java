package com.ybm100.app.crm.function.speech;

import android.content.Context;
import android.os.Bundle;

import com.iflytek.cloud.RecognizerListener;
import com.iflytek.cloud.RecognizerResult;
import com.iflytek.cloud.SpeechError;
import com.iflytek.cloud.SpeechEvent;
import com.xyy.common.util.ToastUtils;
import com.xyy.utilslibrary.utils.LogUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.LinkedHashMap;

/**
 * Author ： LoveNewsweetheart
 * Date:2018/12/28
 */
public class SpeechManager {

    private XunFeiSpeech speech = null;
    private final OnSpeechCallback callback;

    public SpeechManager(Context mContext, OnSpeechCallback callback) {
        this.callback = callback;
        speech = XunFeiSpeech.getXunFeiSpeech(mContext);
    }

    /**
     * 建议调用此方法前，请先授予权限
     */
    public void startSpeech() {
        speech.startRecoder(mRecognizerListener);
    }

    public void stopSpeech() {
        speech.stopRecoder();
        if (callback != null) {
            callback.onEndOfSpeech();
        }
    }

    public interface OnSpeechCallback{
        void onSpeechConvertResult(String content, boolean isLast);
        /**
         * @param volume 音量大小
         */
        void onVolumeChanged(int volume);
        void onBeginOfSpeech();
        void onError(int code, String hintMsg);
        void onEndOfSpeech();
    }

    /**
     * 听写监听器。
     */
    private final RecognizerListener mRecognizerListener = new RecognizerListener() {

        @Override
        public void onBeginOfSpeech() {
            // 此回调表示：sdk内部录音机已经准备好了，用户可以开始语音输入
            if (callback != null) {
                callback.onBeginOfSpeech();
            }
        }

        @Override
        public void onError(SpeechError error) {
            LogUtils.e("讯飞语音  错误码为：" + error.getErrorCode());
            if (callback == null) {
                return;
            }
            switch (error.getErrorCode()) {
                case 23003:
                case 23106:
                    callback.onError(23106, "本机内存不足");
                    return;
                case 20006:
                    callback.onError(20006, "没有录音权限");
                    return;
                case 20010:
                    callback.onError(20010, "没有读写外部存储权限");
                    return;
                case 20001:
                case 20002:
                case 20003:
                    callback.onError(20001, "网络异常");
                    return;
                case 28675:
                    callback.onError(28675, "不正常的语音");
                    return;
                case 10118:
                    callback.onError(10118, "您没有说话");
                    return;
                case 14001:
                case 28673:
                    callback.onError(14001, "没有声音或者声音太小");
                    return;
                case 11603:
                    callback.onError(11603, "声音太嘈杂");
                    return;
                case 10131:
                    callback.onError(10131, "您已取消");
                    return;
                case 23008:
                    callback.onError(23008, "离线识别，输入语音总长度不可超过20s");
                    return;
                default:
                    ToastUtils.showShort(error.getPlainDescription(true));
                    break;
            }
            callback.onError(-1, "语音失败");
            LogUtils.e("错误码为：" + error.getErrorCode());

        }

        @Override
        public void onEndOfSpeech() {
            LogUtils.e("讯飞语音  -----onEndOfSpeech---");
            // 此回调表示：检测到了语音的尾端点，已经进入识别过程，不再接受语音输入
            if (callback != null) {
                callback.onEndOfSpeech();
            }
        }

        @Override
        public void onResult(RecognizerResult results, boolean isLast) {
            //LogUtils.e(results.getResultString());
            parserResult(results,isLast);
        }


        @Override
        public void onVolumeChanged(int volume, byte[] data) {
            //LogUtils.e("当前正在说话，音量大小：" + volume);
            //LogUtils.e("返回音频数据：" + data.length);
            if (callback != null) {
                callback.onVolumeChanged(volume);
            }
        }

        @Override
        public void onEvent(int eventType, int arg1, int arg2, Bundle obj) {

            // 以下代码用于获取与云端的会话id，当业务出错时将会话id提供给技术支持人员，可用于查询会话日志，定位出错原因
            // 若使用本地能力，会话id为null
            if (SpeechEvent.EVENT_SESSION_ID == eventType) {
                String sid = obj.getString(SpeechEvent.KEY_EVENT_SESSION_ID);
                LogUtils.e("session id =" + sid);
            }
        }
    };


    // 用HashMap存储听写结果
    private final HashMap<String, String> mIatResults = new LinkedHashMap<String, String>();
    private void parserResult(RecognizerResult results,boolean isLast) {
        String text = JsonParser.parseIatResult(results.getResultString());

        String sn = null;
        // 读取json结果中的sn字段
        try {
            JSONObject resultJson = new JSONObject(results.getResultString());
            sn = resultJson.optString("sn");
        } catch (JSONException e) {
            e.printStackTrace();
        }

        mIatResults.put(sn, text);

        StringBuffer resultBuffer = new StringBuffer();
        for (String key : mIatResults.keySet()) {
            resultBuffer.append(mIatResults.get(key));
        }

        if (callback != null) {
            //callback.onSpeechConvertResult(resultBuffer.toString(),isLast);
            callback.onSpeechConvertResult(text, isLast);
        }
    }


}
