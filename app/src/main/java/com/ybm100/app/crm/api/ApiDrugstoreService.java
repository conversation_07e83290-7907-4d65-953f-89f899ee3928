package com.ybm100.app.crm.api;


import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.coupon.VoucherBean;
import com.ybm100.app.crm.bean.drugstore.AreaBean;
import com.ybm100.app.crm.bean.drugstore.BasicInfo;
import com.ybm100.app.crm.bean.drugstore.CustomerPublicBean;
import com.ybm100.app.crm.bean.drugstore.PrivateListBean;
import com.ybm100.app.crm.bean.drugstore.PrivateListFilterBean;
import com.ybm100.app.crm.bean.drugstore.PublicCustomerDetailBean;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.FilterPrivateBean;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.RegisterParams;
import com.ybm100.app.crm.task.bean.TaskAndMerchantBean;

import java.util.HashMap;
import java.util.List;

import io.reactivex.Observable;
import retrofit2.http.Field;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;
import retrofit2.http.QueryMap;

/**
 * Created by zcj on 2018/12/21 14
 */
public interface ApiDrugstoreService {


    /**
     * 私海客户列表
     */
    @POST("customer/private/page/list")
    @FormUrlEncoded
    Observable<RequestBaseBean<PrivateListBean>> getPrivateListData(@FieldMap HashMap<String, String> map);

    /**
     * 线索编辑
     */
    @GET("openSea/openSeaEdit")
    Observable<RequestBaseBean> openSeaEdit(@QueryMap HashMap<String, String> map);

    /**
     * 公海列表
     */
    @POST("openSea4POI/searchOpenSea")
    @FormUrlEncoded
    Observable<RequestBaseBean<CustomerPublicBean>> searchOpenSea(@FieldMap HashMap<String, String> map);

    /**
     * 区域列表
     */
    @POST("customer/private/area/search")
    @FormUrlEncoded
    Observable<RequestBaseBean<List<AreaBean>>> searchArea(@Field("areaCode") String areaCode);

    /**
     * 新版区域列表，4.6.5版本新增
     */
    @POST("customer/private/crmArea/search")
    @FormUrlEncoded
    Observable<RequestBaseBean<List<AreaBean>>> searchAreaV2(@Field("areaCode") String areaCode);

    /**
     * 公海认领
     */
    @POST("openSea4POI/receive")
    @FormUrlEncoded
    Observable<RequestBaseBean> receive(@Field("id") String id,@Field("skuCollectCodes") String skuCollectCode);


    /**
     * 修改上传药店地图
     */
    @POST("merchantMap/addMerchantMap")
    @FormUrlEncoded
    Observable<RequestBaseBean> addMerchantMap(@FieldMap HashMap<String, String> map);


    /**
     * 公海详情
     */
    @GET("openSea4POI/searchOpenSeaDetail")
    Observable<RequestBaseBean<PublicCustomerDetailBean>> searchOpenSeaDetail(@Query("id") String id);


    /**
     * 优惠券接口
     */
    @POST("merchantperspective/getMerchantVoucherData")
    @FormUrlEncoded
    Observable<RequestBaseBean<VoucherBean>> getVoucherData(@FieldMap HashMap<String, String> map);

    /**
     * 私海客户详情基本信息
     */
    @POST("customer/private/basic")
    @FormUrlEncoded
    Observable<RequestBaseBean<BasicInfo>> getAuditInfo(@Field("merchantId") String merchantId);

    @GET("task/v290/toAddVisit")
    Observable<RequestBaseBean<TaskAndMerchantBean>> toAddVisit(@Query("customerId") String merchantId, @Query("customerType") String customerType);

    /**
     * 分配至BD
     */
    @POST("customer/private/bind/user")
    @FormUrlEncoded
    Observable<RequestBaseBean> distributeToBD(@Field("bindUserId") String bindUserId, @Field("customerId") String customerId, @Field("skuCollectCodes") String skuCollectCodes);


    /**
     * 释放至公海
     */
    @GET("customer/private/unBindCustomer")
    Observable<RequestBaseBean> releaseToPublic(@Query("id") String customerId, @Query("skuCollectCodes") String skuCollectCodes);


    /**
     * 私海客户获取筛选条件
     */
    @POST("customer/private/list/condition")
    Observable<RequestBaseBean<PrivateListFilterBean>> getFilterItems();

    /**
     * 接口名称 私海客户列表其他查询条件
     */
    @GET("customer/private/other/condition")
    Observable<RequestBaseBean<FilterPrivateBean>> getOtherFilterItems();

    /**
     * 获取注册状态字段
     */
    @GET("openSea4POI/getCustomerStatus")
    Observable<RequestBaseBean<List<RegisterParams>>> getRegisterParams();
}
