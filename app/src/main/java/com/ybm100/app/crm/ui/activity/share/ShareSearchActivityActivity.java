package com.ybm100.app.crm.ui.activity.share;

import android.os.Bundle;

import com.xyy.utilslibrary.base.activity.BaseCompatActivity;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.ui.fragment.share.ShareListFragment;
import com.ybm100.app.crm.utils.InputFilter.EditUtil;
import com.ybm100.app.crm.widget.searchview.ICallBack;
import com.ybm100.app.crm.widget.searchview.SearchView;

import butterknife.BindView;

/**
 * Created by XyyMvpSportTemplate on 03/06/2019 14:34
 * 一键发圈搜索
 */
public class ShareSearchActivityActivity extends BaseCompatActivity {

    @BindView(R.id.sv_share_search)
    SearchView mSearchView;
    ShareListFragment shareListFragment;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_share_search;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        shareListFragment = ShareListFragment.newInstance("0", null);
        loadRootFragment(R.id.container_share_search, shareListFragment);
        initSearchView();
    }

    //初始化搜索
    private void initSearchView() {
        //搜索控件隐藏右侧按钮
        mSearchView.setTextHint("搜索");
        mSearchView.setOnClickSearch(new ICallBack() {
            @Override
            public void SearchAction(String key) {
                hiddenKeyboard();
                shareListFragment.search(key);
            }
        });
        //限制非法字符
        EditUtil.setEditTextInhibitInputIllegaCharacter(mSearchView.getEt_search(), EditUtil.DEFAULT_PATTERN);
        mSearchView.setOnClickBack(new SearchView.bCallBack() {
            @Override
            public void backAction() {
                finish();
            }
        });
    }
}
