package com.ybm100.app.crm.ui.activity.drugstore;

import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.xyy.common.navigationbar.AbsNavigationBar;
import com.xyy.common.navigationbar.DefaultNavigationBar;
import com.xyy.common.util.ToastUtils;
import com.xyy.common.widget.RoundTextView;
import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.activity.BaseMVPCompatActivity;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.drugstore.AreaBean;
import com.ybm100.app.crm.contract.drugstore.AreaSelectContract;
import com.ybm100.app.crm.presenter.drugstore.AreaSelectPresenter;
import com.ybm100.app.crm.ui.adapter.drugstore.AreaSelectListAdapter;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;

/**
 * @author: zcj
 * @time:2020/3/29. Description:
 */
public class AreaSelectActivity extends BaseMVPCompatActivity<AreaSelectPresenter> implements AreaSelectContract.IAreaSelectView {
    @BindView(R.id.rcy_province)
    RecyclerView rcyProvince;
    @BindView(R.id.rcy_city)
    RecyclerView rcyCity;
    @BindView(R.id.rcy_area)
    RecyclerView rcyArea;
    @BindView(R.id.rt_confirm)
    RoundTextView rtConfirm;
    @BindView(R.id.rt_reset)
    RoundTextView rtReset;
    private int provincePoint = 0;
    private int cityPoint = 0;
    private int currLevel = 0;
    private AreaSelectListAdapter provinceSelectAdapter;
    private AreaSelectListAdapter citySelectAdapter;
    private AreaSelectListAdapter areaSelectAdapter;
    private ArrayList<AreaBean> provinceList = new ArrayList<>();
    private ArrayList<AreaBean> cityList = new ArrayList<>();
    private ArrayList<AreaBean> areaList = new ArrayList<>();
    private ArrayList<AreaBean> selectAreaList = new ArrayList<>();
    private AreaSelectBean areaSelectBean;

    @NonNull
    @Override
    public BasePresenter initPresenter() {
        return AreaSelectPresenter.newInstance();
    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_area_select;
    }

    @Override
    protected AbsNavigationBar initHead() {
        return new DefaultNavigationBar.Builder(this).setTitle("区域选择")
                .setRightIcon(R.drawable.tab_close).setRightClickListener(v -> {
                    finish();
                }).builder();
    }

    @Override
    public void showNetError() {

    }


    @Override
    protected void initView(Bundle savedInstanceState) {
        rcyProvince.setLayoutManager(new LinearLayoutManager(this));
        rcyCity.setLayoutManager(new LinearLayoutManager(this));
        rcyArea.setLayoutManager(new LinearLayoutManager(this));
        areaSelectBean = (AreaSelectBean) getIntent().getSerializableExtra("mapBean");
        if (areaSelectBean != null) {
            provinceList = areaSelectBean.getProvinceList();
            cityList = areaSelectBean.getCityList();
            areaList = areaSelectBean.getAreaList();
            selectAreaList = areaSelectBean.getSelectAreaList();
            provincePoint = areaSelectBean.getProvincePoint();
            cityPoint = areaSelectBean.getCityPoint();
        }
        if (selectAreaList == null) {
            selectAreaList = new ArrayList<>();
        }
        currLevel = 0;
        if (provinceList == null || provinceList.isEmpty()) {
            mPresenter.searchArea(currLevel, "");
        } else {
            initProvince(provinceList, false);
            initCity(cityList, false);
            initArea(areaList, false);
        }
        rtReset.setOnClickListener(v -> {
            setResult(RESULT_OK);
            finish();
        });
        rtConfirm.setOnClickListener(v -> {
            if (provinceList.size() == 0 || cityList.size() == 0) {
                ToastUtils.showShort("数据异常");
                return;
            }
            Bundle postBundle = new Bundle();
            AreaSelectBean selectBean = new AreaSelectBean();
            selectBean.setProvinceBean(provinceList.get(provincePoint));
            selectBean.setCityBean(cityList.get(cityPoint));
            selectBean.setProvincePoint(provincePoint);
            selectBean.setCityPoint(cityPoint);
            selectBean.setSelectAreaList(selectAreaList);
            selectBean.setProvinceList(provinceList);
            selectBean.setCityList(cityList);
            selectBean.setAreaList(areaList);
            postBundle.putSerializable("mapBean", selectBean);
            setResult(RESULT_OK, new Intent().putExtras(postBundle));
            finish();
        });
    }

    public static String getCode(List<AreaBean> selectAreaList) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < selectAreaList.size(); i++) {
            sb.append(selectAreaList.get(i).getAreaCode());
            if (i != selectAreaList.size() - 1) {
                sb.append(",");
            }
        }
        return sb.toString();
    }

    public static String getName(List<AreaBean> selectAreaList) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < selectAreaList.size(); i++) {
            sb.append(selectAreaList.get(i).getAreaName());
            if (i != selectAreaList.size() - 1) {
                sb.append(",");
            }
        }
        return sb.toString();
    }

    public static String getLevel(List<AreaBean> selectAreaList) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < selectAreaList.size(); i++) {
            sb.append(selectAreaList.get(i).getAreaLevel());
            if (i != selectAreaList.size() - 1) {
                sb.append(",");
            }
        }
        return sb.toString();
    }

    @Override
    public void searchAreaSuccess(int currLevel, RequestBaseBean<List<AreaBean>> baseBean) {
        switch (currLevel) {
            case 0:
                initProvince(baseBean.getData(), true);
                break;
            case 1:
                initCity(baseBean.getData(), true);
                break;
            case 2:
                initArea(baseBean.getData(), true);
                break;
        }

    }


    private void initProvince(List<AreaBean> baseBean, boolean reset) {
        if (reset) {
            provinceList = resetList(baseBean, 0);
            currLevel = 1;
            provincePoint = 0;
            mPresenter.searchArea(currLevel, provinceList.get(provincePoint).getAreaCode());
        }
        if (provinceSelectAdapter == null) {
            provinceSelectAdapter = new AreaSelectListAdapter(R.layout.item_area_selcte, provinceList);
        } else {
            provinceSelectAdapter.setNewData(provinceList);
        }
        rcyProvince.setAdapter(provinceSelectAdapter);
        provinceSelectAdapter.setOnItemClickListener((adapter, view, position) -> {
            currLevel = 1;
            provincePoint = position;
            provinceSelectAdapter.setNewData(resetList(provinceList, position));
            mPresenter.searchArea(currLevel, provinceList.get(provincePoint).getAreaCode());
        });

    }


    private void initCity(List<AreaBean> baseBean, boolean reset) {
        if (reset) {
            List<AreaBean> areaBeans = new ArrayList<>();
            AreaBean areaBean = new AreaBean();
            areaBean.setAreaCode(provinceList.get(provincePoint).getAreaCode());
            areaBean.setAreaLevel(provinceList.get(provincePoint).getAreaLevel());
            areaBean.setHasNext(1);
            areaBean.setAreaName("全" + provinceList.get(provincePoint).getAreaName());
            areaBeans.add(areaBean);
            areaBeans.addAll(baseBean);
            cityList = resetList(areaBeans, 0);
            areaList = null;
            currLevel = 3;
            cityPoint = 0;
            setAreaTopData();
        }
        if (citySelectAdapter == null) {
            citySelectAdapter = new AreaSelectListAdapter(R.layout.item_area_selcte, cityList);
        } else {
            citySelectAdapter.setNewData(cityList);
        }
        rcyCity.setAdapter(citySelectAdapter);
        citySelectAdapter.setOnItemClickListener((adapter, view, position) -> {
            currLevel = 2;
            cityPoint = position;
            citySelectAdapter.setNewData(resetList(cityList, position));
            setAreaTopData();
        });
    }

    /**
     * 填充省份数据
     */
    private void setAreaTopData() {
        if (cityPoint > 0) {
            mPresenter.searchArea(currLevel, cityList.get(cityPoint).getAreaCode());
        } else {
            List<AreaBean> areaBeanList = new ArrayList<>();
            AreaBean areaInfo = new AreaBean();
            areaInfo.setAreaCode(provinceList.get(provincePoint).getAreaName());
            areaInfo.setAreaLevel("-1");
            areaInfo.setHasNext(0);
            areaInfo.setSelect(true);
            areaInfo.setAreaName("全" + provinceList.get(provincePoint).getAreaName());
            areaBeanList.add(areaInfo);
            if (areaSelectAdapter == null) {
                areaSelectAdapter = new AreaSelectListAdapter(R.layout.item_area_selcte, areaBeanList);
            } else {
                areaSelectAdapter.setNewData(areaBeanList);
            }
            selectAreaList.clear();
            rcyArea.setAdapter(areaSelectAdapter);
        }
    }

    private void initArea(List<AreaBean> baseBean, boolean reset) {
        if (!reset && cityPoint == 0) {
            currLevel = 3;
            areaList = null;
            setAreaTopData();
            return;
        }
        if (reset) {
            List<AreaBean> areaBeanList = new ArrayList<>();
            AreaBean areaInfo = new AreaBean();
            areaInfo.setAreaCode(cityList.get(cityPoint).getAreaName());
            areaInfo.setAreaLevel("-1");
            areaInfo.setHasNext(0);
            areaInfo.setAreaName("全" + cityList.get(cityPoint).getAreaName());
            areaBeanList.add(areaInfo);
            areaBeanList.addAll(baseBean);
            areaList = resetList(areaBeanList, 0);
            if (selectAreaList != null) {
                selectAreaList.clear();
            }
        }
        if (areaSelectAdapter == null) {
            areaSelectAdapter = new AreaSelectListAdapter(R.layout.item_area_selcte, areaList);
        } else {
            areaSelectAdapter.setNewData(areaList);
        }
        areaSelectAdapter.setOnItemClickListener((adapter, view, position) -> {
            areaSelectAdapter.setNewData(selectList(areaList, position));
        });
        rcyArea.setAdapter(areaSelectAdapter);
    }

    private ArrayList<AreaBean> resetList(List<AreaBean> baseBean, int select) {
        ArrayList<AreaBean> newBaseBean = new ArrayList<>();
        for (int i = 0; i < baseBean.size(); i++) {
            newBaseBean.add(baseBean.get(i));
            newBaseBean.get(i).setSelect(i == select);
        }
        return newBaseBean;
    }

    private ArrayList<AreaBean> selectList(ArrayList<AreaBean> baseBean, int select) {
        if (baseBean == null) return null;
        if (select == 0 && !baseBean.get(select).isSelect()) {
            baseBean = resetList(baseBean, 0);
            selectAreaList.clear();
            return baseBean;
        }
        for (int i = 0; i < baseBean.size(); i++) {
            baseBean.get(0).setSelect(false);
            if (i == select) {
                if (baseBean.get(i).isSelect()) {
                    baseBean.get(i).setSelect(false);
                    AreaBean tempBean = null;
                    for (AreaBean bean : selectAreaList) {
                        if (bean.getAreaCode().equals(baseBean.get(i).getAreaCode())) {
                            tempBean = bean;
                            break;
                        }
                    }
                    if (tempBean != null) {
                        selectAreaList.remove(tempBean);
                    }
                } else {
                    if (i == 0) {
                        baseBean.get(i).setSelect(true);
                        selectAreaList.clear();
                        break;
                    }
                    baseBean.get(i).setSelect(true);
                    AreaBean tempBean = null;
                    for (AreaBean bean : selectAreaList) {
                        if (bean.getAreaCode().equals(baseBean.get(i).getAreaCode())) {
                            tempBean = bean;
                            break;
                        }
                    }
                    if (tempBean == null) {
                        selectAreaList.add(baseBean.get(i));
                    }
                }
            }
        }
        if (selectAreaList.size() == 0) {
            baseBean.get(0).setSelect(true);
        }
        return baseBean;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        areaSelectAdapter = null;
        citySelectAdapter = null;
        provinceSelectAdapter = null;
    }
}
