package com.ybm100.app.crm.task.bean

data class TeamTaskBean(
        val achieveGoal: String? = "", // 10540
        val departStr: String? = "", // 测试内容j3b2
        val goal: String? = "", // 61543
        val rows: List<Row?>? = listOf(),
        val total: String? = "", // 33075
        val lastPage: Boolean? = false, // false
        val rate: Double? = 0.00, // false
        val typeStr: String? = ""
) {
    data class Row(
            val achieveGoal: String? = "", // 1
            val goal: String? = "", // 1
            val name: String? = "", // 测试内容126m
            val rate: Double? = 0.00
    )
}