package com.ybm100.app.crm.order.activity

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.rxbus.RxBus
import com.xyy.utilslibrary.utils.DisplayUtils
import com.ybm100.app.crm.R
import com.ybm100.app.crm.constant.Constants
import com.ybm100.app.crm.constant.ExtraConstants
import com.ybm100.app.crm.constant.RxBusCode
import com.ybm100.app.crm.contract.BaseExecutorContract
import com.ybm100.app.crm.order.presenter.OrderExecutorPresenter
import com.ybm100.app.crm.task.bean.ExecutorLevelItem
import com.ybm100.app.crm.ui.activity.BaseExecutorActivity
import kotlinx.android.synthetic.main.activity_select_executor.*

/**
 * Created by dengmingjia on 2019/1/4
 * 订单执行人
 */
class OrderExecutorActivity : BaseExecutorActivity<OrderExecutorPresenter>(), BaseExecutorContract.IView {
    private var mTaskID: String = ""
    private var canSelectP = true
    private var needSetResult = "false"
    private var checkIsM = "false"
    private var isPop = false
    override fun needPersonBottomLayout(): Boolean {
        return false
    }

    private var mSelected: ExecutorLevelItem? = null

    companion object {
        var REQ_SEARCH = 1

        /**
         * @param activity
         * @param taskID 任务 ID
         */
        @JvmStatic
        fun startActivity(activity: Activity?, taskID: String?) {
            val intent = Intent(activity, OrderExecutorActivity::class.java)
            val bundle = Bundle()
            bundle.putString(Constants.Task.ARG_TASK_ID, taskID ?: "")
            intent.putExtras(bundle)
            activity?.startActivity(intent)
        }
    }

    override fun onSubmit() {
        if (mSelected != null) {
            val bundle = Bundle()
            bundle.putString(ExtraConstants.NAME, mSelected!!.content)
            bundle.putString(ExtraConstants.ID, mSelected!!.id)
            bundle.putBoolean(ExtraConstants.IS_GROUP, !mSelected!!.isPerson)
            if (needSetResult == "true") {
                if (checkIsM == "true") {
                    mPresenter.getUserLevel(mSelected!!.id)
                    return
                }
            }
            if (mTaskID.isNotEmpty()) {
                RxBus.get().send(RxBusCode.RX_BUS_UPDATE_TEAM_TASK, bundle)
            } else {
                RxBus.get().send(RxBusCode.RX_BUS_UPDATE_EXECUTOR, bundle)
                if (needSetResult == "true") {
                    // 选择全部时id为null，这时候通过channel传递到flutter时，flutter会过滤掉这个值
                    val resultBundle = Bundle().also { it.putAll(bundle) }
                    if (mSelected?.id == null) {
                        resultBundle.putString(ExtraConstants.ID, "")
                    }
                    setResult(1024, Intent().also {
                        it.putExtras(resultBundle)
                        it.putExtra(ExtraConstants.IS_GROUP, (!mSelected!!.isPerson).toString())
                    })
                }
            }

        }
        finish()
    }

    override fun initTitleString(): String {
        return getString(R.string.title_select_range)
    }

    override fun initPresenter(): BasePresenter<*, *> {
        val queryParameter = intent?.data?.getQueryParameter("isPop")
        Log.e("guan", "queryParameter1:${queryParameter}")
        try {
            isPop = (intent?.data?.getQueryParameter("isPop") ?: "false").let {
                try {
                    it.toBoolean()
                } catch (e: Exception) {
                    false
                }
            }
            mTaskID = intent?.extras?.getString(Constants.Task.ARG_TASK_ID, "") ?: ""
            needSetResult = (intent?.data?.getQueryParameter("needSetResult") ?: "false")
            checkIsM = (intent?.data?.getQueryParameter("checkIsM") ?: "false")
        } catch (e: Exception) {

        }
        Log.e("guan", "queryParameter2:${isPop}")
        return if (mTaskID.isNotEmpty()) {
            OrderExecutorPresenter(true, mTaskID, isPop)
        } else {
            OrderExecutorPresenter(false, "", isPop)
        }

    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        val canSelectPStr = intent?.data?.getQueryParameter("canChooseDepartment")
        if (canSelectPStr.isNullOrEmpty()) {
            canSelectP = intent.getBooleanExtra(ExtraConstants.IS_CAN_SELECT_P, true)
        } else {
            canSelectP = try {
                canSelectPStr.toBoolean()
            } catch (e: Exception) {
                true
            }
        }
        if (mTaskID.isNotEmpty()) {
            tv_search.visibility = View.GONE
        }
    }

    override fun onOptionalCheck(item: ExecutorLevelItem) {
//        if (item == mData || SharedPrefManager.getInstance().userInfo.roleType == RoleTypeConfig.) {
//            ToastUtils.showShort("不支持多选")
//            return
//        }

//        mSelected = item
//        onSubmit()

        var parentChildCount: Int
        if (item == mSelected) {
            check(item, false, false)
            clearCheckPersonCount(item)
            mSelected = null
        } else {
            check(mSelected, false, false)
            clearCheckPersonCount(mSelected)
            check(item, true, false)
            mSelected = item
            parentChildCount = if (item.isPerson) {
                1
            } else {
                setCheckPersonCount(item)
            }

            var temp = item
            while (temp.parent != null) {
                temp.parent.checkedPersonCount = parentChildCount
                temp = temp.parent
            }
        }
        refreshAllList()
    }

    override fun onSearchClicked() {
        val bundle = Bundle()
        bundle.putString(Constants.Task.ARG_TASK_ID, mTaskID)
        bundle.putBoolean("isPop", isPop)
        bundle.putBoolean("canPickPerson", mCanPickPerson)
        startActivityForResult(OrderExecutorSearchActivity::class.java, bundle, REQ_SEARCH)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                REQ_SEARCH -> {
                    val bundle = Bundle()
                    val isGroup = data?.getBooleanExtra(ExtraConstants.IS_GROUP, false)
                            ?: false
                    bundle.putString(ExtraConstants.NAME, data?.getStringExtra(ExtraConstants.NAME))
                    bundle.putString(ExtraConstants.ID, data?.getStringExtra(ExtraConstants.ID)!!)
                    bundle.putBoolean(ExtraConstants.IS_GROUP, isGroup)

                    if (needSetResult == "true") {
                        if (checkIsM == "true" && isGroup == false) {
                            mSelected = ExecutorLevelItem(data?.getStringExtra(ExtraConstants.ID), data?.getStringExtra(ExtraConstants.NAME), !isGroup, null);
                            mPresenter.getUserLevel(mSelected!!.id)
                            return
                        }
                    }
                    RxBus.get().send(RxBusCode.RX_BUS_UPDATE_EXECUTOR, bundle)
                    if (needSetResult == "true") {
                        // 选择全部时id为null，这时候通过channel传递到flutter时，flutter会过滤掉这个值
                        val resultBundle = Bundle().also { it.putAll(bundle) }
                        if (data.getStringExtra(ExtraConstants.ID) == null) {
                            resultBundle.putString(ExtraConstants.ID, "")
                        }
                        setResult(1024, Intent().also {
                            it.putExtras(resultBundle)
                            it.putExtra(ExtraConstants.IS_GROUP, isGroup.toString())
                        })
                    }
                    finish()
                }
            }
        }
    }

    override fun showNetError() {

    }

    override fun onGetUserLevelSuccess(bean: Boolean) {
        if (mSelected != null) {
            if (bean) {
                //M级
                showReceiptDialog(mSelected!!.content) {
                    setRequestResult()
                }
            } else {
                setRequestResult()
            }
        }
    }

    private fun setRequestResult() {
        val bundle = Bundle()
        bundle.putString(ExtraConstants.NAME, mSelected!!.content)
        bundle.putString(ExtraConstants.ID, mSelected!!.id)
        bundle.putBoolean(ExtraConstants.IS_GROUP, !mSelected!!.isPerson)
        if (mTaskID.isNotEmpty()) {
            RxBus.get().send(RxBusCode.RX_BUS_UPDATE_TEAM_TASK, bundle)
        } else {
            RxBus.get().send(RxBusCode.RX_BUS_UPDATE_EXECUTOR, bundle)
            if (needSetResult == "true") {
                // 选择全部时id为null，这时候通过channel传递到flutter时，flutter会过滤掉这个值
                val resultBundle = Bundle().also { it.putAll(bundle) }
                if (mSelected?.id == null) {
                    resultBundle.putString(ExtraConstants.ID, "")
                }
                setResult(1024, Intent().also {
                    it.putExtras(resultBundle)
                    it.putExtra(ExtraConstants.IS_GROUP, (!mSelected!!.isPerson).toString())
                })
            }
        }
        finish()
    }

    /**
     * 显示设置发票类型弹窗
     */
    @SuppressLint("SetTextI18n")
    private fun showReceiptDialog(peopleName: String, callback: () -> Unit) {
        val dialog = Dialog(mContext, R.style.custom_dialog2)
        val view = View.inflate(mContext, R.layout.dialog_base, null)
        val content = view.findViewById<TextView>(R.id.tv_dialog_content)
        val title = view.findViewById<TextView>(R.id.tv_dialog_title)
        val tips = view.findViewById<TextView>(R.id.tv_dialog_tips)
        val cancel = view.findViewById<TextView>(R.id.btn_cancel)
        val confirm = view.findViewById<TextView>(R.id.btn_ensure)
        dialog.setContentView(view)
        content.text = resources.getString(R.string.receipt_content)
        title.text = "提示"
        content.text = "您选择的被陪访人${peopleName}是M级账号，根据当前业务规则，陪访M级将判定为无效陪访。"
        tips.visibility = View.GONE
        cancel.setOnClickListener {
            callback()
            dialog.dismiss()
        }
        confirm.setOnClickListener {
            dialog.dismiss()
        }
        confirm.text = "更换被陪访人"
        cancel.text = "确认陪访M级"
        // back键处理
        dialog.setOnCancelListener { obj: DialogInterface -> obj.dismiss() }
        dialog.setCanceledOnTouchOutside(false)
        dialog.setCancelable(false)
        dialog.show()
        // 设置布局参数(必须在show之后设置)
        val window = dialog.window ?: return
        val lp = window.attributes
        val screenWidthPixels = DisplayUtils.getScreenWidthPixels(this)
        lp.width = (screenWidthPixels * 0.7).toInt()
        lp.height = WindowManager.LayoutParams.WRAP_CONTENT
        lp.gravity = Gravity.CENTER
        window.attributes = lp
    }

    override fun onGetUserLevelFail(msg: String?) {
    }

    override fun canSelectP(): Boolean = canSelectP
}