package com.ybm100.app.crm.bean.message;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @file MessageApiBean.java
 * @brief
 * @date 2018/12/22
 * Copyright (c) 2018, 北京小药药
 * All rights reserved.
 */
public class MessageApiBean {

    private int unReadCount;
    private boolean lastPage;
    private List<MessageBean> rows;

    public List<MessageBean> getRows() {
        return rows;
    }

    public void setRows(List<MessageBean> rows) {
        this.rows = rows;
    }

    public boolean isLastPage() {
        return lastPage;
    }

    public void setLastPage(boolean lastPage) {
        this.lastPage = lastPage;
    }

    public int getUnReadCount() {
        return unReadCount;
    }

    public void setUnReadCount(int unReadCount) {
        this.unReadCount = unReadCount;
    }
}
