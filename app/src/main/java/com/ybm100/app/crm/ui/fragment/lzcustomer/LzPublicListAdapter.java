package com.ybm100.app.crm.ui.fragment.lzcustomer;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.xyy.common.widget.RoundConstraintLayout;
import com.xyy.utilslibrary.utils.DisplayUtils;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.lzcustomer.LzPublicListBean;
import com.ybm100.app.crm.constant.RoleTypeConfig;
import com.ybm100.app.crm.utils.FormatUtils;

import java.util.List;

/**
 * 公海客户
 *
 * <AUTHOR>
 * @date 2018/12/29
 * @time 4:56 PM
 */
public class LzPublicListAdapter extends BaseQuickAdapter<LzPublicListBean.RowBean, BaseViewHolder> {
    private boolean hasLocation;

    public LzPublicListAdapter(int layoutResId, @Nullable List<LzPublicListBean.RowBean> data, boolean hasLocation) {
        super(layoutResId, data);
        this.hasLocation = hasLocation;
    }

    public LzPublicListAdapter(@Nullable List<LzPublicListBean.RowBean> data) {
        super(data);
    }

    public LzPublicListAdapter(int layoutResId) {
        super(layoutResId);
    }

    @Override
    protected void convert(BaseViewHolder helper, final LzPublicListBean.RowBean item) {
        helper.setText(R.id.tv_binder_user, "绑定销售:" + FormatUtils.textFormat(item.getSysUserName()));
        if ("2".equals(item.getRegisterFlag())) {
            helper.setText(R.id.tv_drugstore_address, FormatUtils.textFormat(item.getAddress()));
        } else if ("1".equals(item.getRegisterFlag())) {
            helper.setText(R.id.tv_drugstore_address, FormatUtils.textFormat(item.getEcAddress()));
        } else {
            helper.setText(R.id.tv_drugstore_address, FormatUtils.textFormat(""));
        }
        helper.setText(R.id.tv_drugstore_name, FormatUtils.textFormat(TextUtils.isEmpty(item.getEcCustomerName())
                ? item.getCustomerName() : item.getEcCustomerName()));
        //被认领的客户不展示操作按钮 ,跟进人不展示认领
        if ("1".equals(item.getReceiveType()) || RoleTypeConfig.isGJRGrop() || TextUtils.isEmpty(item.getReceiveType())) {
            helper.getView(R.id.group).setVisibility(View.GONE);
        } else {
            helper.getView(R.id.group).setVisibility(View.VISIBLE);
            helper.addOnClickListener(R.id.tv_operation_claim);
        }
        //被认领展示绑定销售
        if ("1".equals(item.getReceiveType())) {
            helper.setGone(R.id.tv_binder_user, true);
            helper.setText(R.id.tv_status, "已认领");
            helper.setTextColor(R.id.tv_status, ContextCompat.getColor(mContext, R.color.text_color_35C561));
        } else {
            helper.setGone(R.id.tv_binder_user, false);
            helper.setText(R.id.tv_status, "未认领");
            helper.setTextColor(R.id.tv_status, ContextCompat.getColor(mContext, R.color.color_red_FC132F));
        }
        if (hasLocation) {
            helper.setText(R.id.tv_distance, "距您" + FormatUtils.textFormat(item.getDistance()));
        } else {
            helper.setText(R.id.tv_distance, "获取不到当前位置");
        }
        RoundConstraintLayout linearLayout = helper.getView(R.id.layout_parent_no_claim);
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        lp.leftMargin = DisplayUtils.dip2px(mContext, 10);
        lp.rightMargin = DisplayUtils.dip2px(mContext, 10);
        if (helper.getAdapterPosition() == 0) {
            lp.topMargin = DisplayUtils.dip2px(mContext, 10);
        }
        linearLayout.setLayoutParams(lp);
    }
}