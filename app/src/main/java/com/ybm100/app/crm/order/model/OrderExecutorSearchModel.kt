package com.ybm100.app.crm.order.model

import com.xyy.utilslibrary.base.BaseModel
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.helper.RxHelper
import com.ybm100.app.crm.api.ApiService
import com.ybm100.app.crm.task.bean.PagingApiBean
import com.ybm100.app.crm.contract.BaseSearchContract
import com.ybm100.app.crm.net.RetrofitCreateHelper
import com.ybm100.app.crm.order.bean.OrderExecutorSearchBean
import io.reactivex.Observable
import java.util.*

/**
 * Created by dengmingjia on 2019/1/8
 */
class OrderExecutorSearchModel(private val isPop: Boolean) : BaseModel(), BaseSearchContract.IModel<OrderExecutorSearchBean> {
    override fun search(apiVersion: String, keyword: String, limit: Int, offset: Int): Observable<RequestBaseBean<PagingApiBean<OrderExecutorSearchBean>>> {
        return RetrofitCreateHelper.createApi(ApiService::class.java).let {
            if (isPop) {
                it.searchPopOrderExecutor(keyword)
            } else {
                it.searchOrderExecutor(keyword)
            }
        }.flatMap { orderExecutorSearchResultBeanRequestBaseBean ->
            val result = RequestBaseBean<PagingApiBean<OrderExecutorSearchBean>>()
            result.errorCode = orderExecutorSearchResultBeanRequestBaseBean.errorCode
            result.errorMsg = orderExecutorSearchResultBeanRequestBaseBean.errorMsg
            result.msg = orderExecutorSearchResultBeanRequestBaseBean.msg
            result.status = orderExecutorSearchResultBeanRequestBaseBean.status
            val pagingApiBean = PagingApiBean<OrderExecutorSearchBean>()
            pagingApiBean.limit = 20
            pagingApiBean.offset = 1
            pagingApiBean.pageCount = 1
            pagingApiBean.total = 1
            val list = ArrayList<OrderExecutorSearchBean>()
            if (orderExecutorSearchResultBeanRequestBaseBean.data != null) {
                val orderExecutorBeans = orderExecutorSearchResultBeanRequestBaseBean.data.groupList
                if (orderExecutorBeans != null && orderExecutorBeans.size > 0) {
                    for ((_, _, id, name) in orderExecutorBeans) {
                        list.add(OrderExecutorSearchBean(name, false, id))
                    }
                }

                val userBeans = orderExecutorSearchResultBeanRequestBaseBean.data.userList
                if (userBeans != null && userBeans.size > 0) {
                    for ((_, id, _, realName) in userBeans) {
                        list.add(OrderExecutorSearchBean(realName, true, id))
                    }
                }
                pagingApiBean.rows = list
            }
            result.data = pagingApiBean
            Observable.just(result)
        }.compose(RxHelper.rxSchedulerHelper())
    }
}
