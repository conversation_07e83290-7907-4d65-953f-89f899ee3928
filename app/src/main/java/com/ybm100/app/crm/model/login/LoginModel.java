package com.ybm100.app.crm.model.login;

import androidx.annotation.NonNull;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiService;
import com.ybm100.app.crm.bean.CaptchaBean;
import com.ybm100.app.crm.bean.user.UserInfoBean;
import com.ybm100.app.crm.contract.login.LoginContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import io.reactivex.Observable;

public class LoginModel extends BaseModel implements LoginContract.ILoginModel {

    @NonNull
    public static LoginModel newInstance() {
        return new LoginModel();
    }

    @Override
    public Observable<UserInfoBean> loginRequest(String mobile, String pwd, String deviceId, String vcode, String captchaId) {
        return RetrofitCreateHelper.createApi(ApiService.class).login(mobile, pwd, deviceId, vcode, captchaId)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<CaptchaBean> getVerifyCode() {
        return RetrofitCreateHelper.createApi(ApiService.class).getVerifyCode()
                .compose(RxHelper.rxSchedulerHelper());
    }
}
