package com.ybm100.app.crm.goodsmanagement.contract

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.goodsmanagement.bean.AvailableZone
import com.ybm100.app.crm.goodsmanagement.bean.EstimatedPriceListBean
import com.ybm100.app.crm.goodsmanagement.bean.GoodsManagementListBean
import com.ybm100.app.crm.goodsmanagement.bean.VarietyListBean
import com.ybm100.app.crm.task.bean.ShareConfirm
import io.reactivex.Observable


class GoodsFilterContract {
    interface IGoodsFilterView : IBaseActivity {
        fun onGetAvailableZoneSuccess(data: RequestBaseBean<AvailableZone?>?)
        fun onGetAvailableZoneFail()
    }

    interface IGoodsFilterModel : IBaseModel {
        fun getAvailableZone(queryMap: Map<String, String>): Observable<RequestBaseBean<AvailableZone?>?>
    }
}