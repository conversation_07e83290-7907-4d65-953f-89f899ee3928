package com.ybm100.app.crm.doraemon.ynb;

import android.os.Bundle;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import com.just.ynbweb.bean.LocationInfo;
import com.xyy.common.navigationbar.AbsNavigationBar;
import com.xyy.common.navigationbar.DefaultNavigationBar;
import com.xyy.utilslibrary.base.activity.BaseCompatActivity;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.permission.PermissionUtil;
import com.ybm100.app.crm.ui.activity.lbs.LocationManager;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: zcj
 * @time:2020/3/23. Description:
 */
public class YNBEditHostActivity extends BaseCompatActivity {
    private EditText et_host;
    private Button bt_confirm;
    private TextView tv_history;
    private StringBuilder sb;
    private final List<String> hostList = new ArrayList<>();
    private String url;
    private LocationManager.LocationListener locationListener;
    private LocationInfo locationInfo;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_edit_hybrid;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        et_host = findViewById(R.id.et_host);
        bt_confirm = findViewById(R.id.btn_confirm);
        tv_history = findViewById(R.id.tv_history);
        url = et_host.getText().toString();
        initLocationListener();
        bt_confirm.setOnClickListener(v -> LocationManager.getInstance().locationPermissions(YNBEditHostActivity.this, locationListener, true, new PermissionUtil.OnCancelCallBack() {
            @Override
            public void cancel() {

            }
        }));
        setUrlText();
    }

    @Override
    public void onPause() {
        super.onPause();
        LocationManager.getInstance().unRegisterLocationListener(locationListener);
    }

    //定位监听
    public void initLocationListener() {
        locationListener = bd -> {
            locationInfo = new LocationInfo();
            locationInfo.setLatitude(String.valueOf(bd.getLatitude()));
            locationInfo.setLongitude(String.valueOf(bd.getLongitude()));
            url = et_host.getText().toString();
            YNBHybridActivity.jumpYnb(YNBEditHostActivity.this, url, locationInfo);
        };
    }

    private void setUrlText() {
        if (!hostList.contains(url)) {
            hostList.add(url);
        }
        sb = new StringBuilder();
        sb.append("https://recruiter.test.ybm100.com/api/toRecruitPage");
        for (String host : hostList) {
            sb.append("\n");
            sb.append(host);
        }
        tv_history.setText(sb);
    }

    @Override
    protected AbsNavigationBar initHead() {
        return new DefaultNavigationBar.Builder(this).setTitle("配置HOST").builder();
    }
}
