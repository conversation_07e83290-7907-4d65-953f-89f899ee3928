package com.ybm100.app.crm.ui.fragment.personal;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.xyy.utilslibrary.base.fragment.BaseCompatFragment;
import com.ybm100.app.crm.R;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * 我的
 */

public class ShareFragment extends BaseCompatFragment {

    @BindView(R.id.ivQr)
    ImageView ivQr;
    @BindView(R.id.ivQr2)
    ImageView ivQr2;
    @BindView(R.id.ivQr3)
    ImageView ivQr3;
    @BindView(R.id.tvQr)
    TextView tvQr;
    public static final String TYPE = "type";


    @Override
    public int getLayoutId() {
        return R.layout.fragment_share;
    }

    @Override
    public void initUI(View view, @Nullable Bundle savedInstanceState) {
        int type = getArguments().getInt(TYPE);
        switch (type) {
            case 1:
                ivQr.setImageResource(R.drawable.qr_ybm);
                break;
            case 2:
                ivQr.setImageResource(R.drawable.qr_crm);
                break;
            case 3:
                ivQr.setImageResource(R.drawable.qr_crm_ios);
                break;

        }
    }


    public static ShareFragment getInstance(int type) {
        ShareFragment shareFragment = new ShareFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(TYPE, type);
        shareFragment.setArguments(bundle);


        return shareFragment;
    }

    @OnClick({R.id.ll})
    public void onClick(View view) {
        startBrowser("http://www.ybm100.com");
    }

    public void startBrowser(String url) {
        Intent intent = new Intent();
        intent.setAction("android.intent.action.VIEW");
        Uri content_url = Uri.parse(url);
        intent.setData(content_url);
        getContext().startActivity(intent);

    }
}
