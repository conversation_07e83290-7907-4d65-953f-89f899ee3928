package com.ybm100.app.crm.presenter.hycustomer;


import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.hycustomer.HyPrivateDetailBean;
import com.ybm100.app.crm.contract.hycustomer.HyPrivateDetailContract;
import com.ybm100.app.crm.model.hycustomer.HyPrivateDetailModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;
import com.ybm100.app.crm.task.bean.TaskAndMerchantBean;

/**
 * 荷叶健康私海客户详情presenter
 */
public class HyPrivateDetailPresenter extends BasePresenter<HyPrivateDetailContract.IHyPrivateDetailModel, HyPrivateDetailContract.IHyPrivateDetailView> {

    public static HyPrivateDetailPresenter newInstance() {
        return new HyPrivateDetailPresenter();
    }

    @Override
    protected HyPrivateDetailModel getModel() {
        return HyPrivateDetailModel.newInstance();
    }

    public void getDrugBaseInfo(String merchantId) {
        if (mIView == null || mIModel == null) return;

        mRxManager.register(mIModel.getBaseInfo(merchantId).subscribe(new SimpleSuccessConsumer<RequestBaseBean<HyPrivateDetailBean>>(mIView) {
            @Override
            public void onSuccess(RequestBaseBean<HyPrivateDetailBean> drugstoreInfoBean) {

                mIView.getBaseInfo(drugstoreInfoBean);
            }
        }, new SimpleErrorConsumer(mIView)));

    }

    /**
     * 新建拜访（药店入口）
     *
     * @param merchantId
     * @param customerType
     */
    public void toAddVisit(String merchantId, String customerType) {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.toAddVisit(merchantId, customerType)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean<TaskAndMerchantBean>>(mIView) {
                    @Override
                    public void onSuccess(RequestBaseBean<TaskAndMerchantBean> listRequestBaseBean) {
                        if (mIView == null) return;
                        mIView.toAddVisit(listRequestBaseBean);
                    }

                    @Override
                    public void onFailure(int errorCode) {
                        super.onFailure(errorCode);
                    }
                }, new SimpleErrorConsumer(mIView)));
    }

    public void distributeToBD(String bindUserId, String customerId) {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.distributeToBD(bindUserId, customerId)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean>(mIView) {
                    @Override
                    public void onSuccess(RequestBaseBean listRequestBaseBean) {
                        if (mIView == null) return;
                        mIView.distributeToBDSuccess(listRequestBaseBean);
                    }

                    @Override
                    public void onFailure(int errorCode) {

                    }
                }, new SimpleErrorConsumer(mIView) {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        mIView.showToast(throwable.getMessage());
                    }
                }));
    }

}
