package com.ybm100.app.crm.goodsmanagement.activity

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.GravityCompat
import androidx.drawerlayout.widget.DrawerLayout
import androidx.fragment.app.Fragment
import com.ybm100.app.crm.R
import com.ybm100.app.crm.constant.Constants
import com.ybm100.app.crm.goodsmanagement.bean.Zone
import com.ybm100.app.crm.goodsmanagement.fragment.CustomerGoodsManagementFilterFragment
import com.ybm100.app.crm.goodsmanagement.fragment.GoodsManagementFragment
import kotlinx.android.synthetic.main.activity_base_drawer.*

/**
 * 用于flutter中展示全部商品的fragment，整合了drawer的逻辑
 */
class GoodsManagementContainerFragment : Fragment(),
    CustomerGoodsManagementFilterFragment.CustomerDrawerListener {

    companion object {
        fun newInstance(merchantId: String): GoodsManagementContainerFragment {
            val args = Bundle()
            args.putString("merchantId", merchantId)
            val fragment = GoodsManagementContainerFragment()
            fragment.arguments = args
            return fragment
        }
    }

    private var mDrawerFragments: List<Fragment> = listOf()
    private lateinit var mCustomerDrawerFragment: Fragment
    private var merchantId: String? = null
    private lateinit var fragment: GoodsManagementFragment

    fun getLayoutId(): Int {
        return R.layout.activity_base_drawer
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(getLayoutId(), container, false);
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initUI(view, savedInstanceState)
    }

    fun initUI(view: View?, savedInstanceState: Bundle?) {
        /**
         * 测滑菜单初始化
         */
        initDrawLayout()
        /**
         * 加载主界面
         */
        merchantId = arguments?.getString("merchantId");
        fragment = GoodsManagementFragment.newInstance(Bundle().apply {
            putInt(
                Constants.GoodsManagement.ARG_FRAGMENT_TYPE,
                Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS
            )
            putString(Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_MERCHANT_ID, merchantId)
        })
        childFragmentManager.beginTransaction().replace(R.id.fl_content_main, fragment).commitAllowingStateLoss()
    }

    private fun initDrawLayout() {
        /**
         * 初始化测滑菜单 Fragments
         */
        mDrawerFragments = getDrawerFragments()

        drawer_layout.apply {
            setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED)
            //TODO 菜单关闭的时候 可以优化
            addDrawerListener(object : DrawerLayout.SimpleDrawerListener() {
                override fun onDrawerOpened(drawerView: View) {
                    super.onDrawerOpened(drawerView)
                    setDrawerLockMode(DrawerLayout.LOCK_MODE_UNLOCKED)
                }

                override fun onDrawerClosed(drawerView: View) {
                    super.onDrawerClosed(drawerView)
                    setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED)
                }
            })
        }
    }

    fun openDrawer(position: Int = 0) {
        if (mDrawerFragments.isEmpty()) {
            return
        }

        val transaction = fragmentManager?.beginTransaction()

        val fragmentListSize = mDrawerFragments.size

        transaction?.hide(mDrawerFragments[fragmentListSize - 1 - position])

        if (mDrawerFragments[position].isAdded) {
            transaction?.show(mDrawerFragments[position])?.commitNowAllowingStateLoss()
        } else {
            transaction?.add(R.id.drawer_fragment_container, mDrawerFragments[position])
                ?.show(mDrawerFragments[position])?.commitNowAllowingStateLoss()
        }

        drawer_layout.openDrawer(GravityCompat.END)
    }

    fun closeDrawer() {
        drawer_layout.closeDrawer(GravityCompat.END)
    }

    fun syncCustomerDrawerFilterData(YBMFilterPos: Int) {
        if (::mCustomerDrawerFragment.isInitialized) {
            (mCustomerDrawerFragment as CustomerGoodsManagementFilterFragment).syncCustomerDrawerFilterData(YBMFilterPos)
        }
    }


    fun getDrawerFragments(): List<Fragment> {
        mCustomerDrawerFragment =
            CustomerGoodsManagementFilterFragment.newInstance(merchantId).apply {
                setCustomerDrawerListener(this@GoodsManagementContainerFragment)
            }
        return listOf(mCustomerDrawerFragment)
    }

    override fun onCustomerConfirmPressed(
        YBMFilterPos: Int,
        collectionStatusFilterPos: Int,
        selectedZone: Zone?
    ) {
        closeDrawer()
        fragment.onCustomerDrawerConfirmPressed(
            YBMFilterPos,
            collectionStatusFilterPos,
            selectedZone
        )
    }
}

