package com.ybm100.app.crm.ui.adapter.share;

import android.app.Activity;
import android.os.Build;
import androidx.annotation.Nullable;
import android.text.Html;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.xyy.utilslibrary.utils.GlideLoadUtils;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.api.ApiUrl;
import com.ybm100.app.crm.bean.share.ShareListBean;
import com.ybm100.app.crm.ui.activity.PhotoViewActivity;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2019/3/6
 * 活动列表 adapter
 */
public class ShareListAdapter extends BaseQuickAdapter<ShareListBean.RowBean, BaseViewHolder> {

    private OnShareCallBack callBack;
    private final int contextMaxLength = 200;

    public ShareListAdapter(int layoutResId, @Nullable List<ShareListBean.RowBean> data, OnShareCallBack callBack) {
        super(layoutResId, data);
        this.callBack = callBack;
    }

    public ShareListAdapter(int layoutResId) {
        super(layoutResId);
    }

    @Override
    protected void convert(final BaseViewHolder helper, final ShareListBean.RowBean item) {
        helper.setText(R.id.tv_item_title, item.getSourceName());
        helper.setText(R.id.tv_item_share_num, mContext.getResources().getString(R.string.share_time, item.getShareTime() + ""));
        if (!TextUtils.isEmpty(item.getSourceTypeName())) {
            helper.setText(R.id.tv_item_type, item.getSourceTypeName());
        } else {
            helper.getView(R.id.tv_item_type).setVisibility(View.GONE);
        }
        helper.setText(R.id.tv_item_share_branch_name, item.getBranchName());
        TextView textview = helper.getView(R.id.tv_item_share_context);
        //支持标签转化
        // flags
        // FROM_HTML_MODE_COMPACT：html块元素之间使用一个换行符分隔
        // FROM_HTML_MODE_LEGACY：html块元素之间使用两个换行符分隔
        String text = item.getSourceDetail();
        Spanned spanned;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            spanned = Html.fromHtml(text, Html.FROM_HTML_MODE_COMPACT);
        } else {
            spanned = Html.fromHtml(text);
        }
        textview.setText(spanned);
        textview.setMovementMethod(LinkMovementMethod.getInstance());
        final ImageView imageView = helper.getView(R.id.iv_item_icon);
        GlideLoadUtils.loadImgWithUrl(mContext, getUrl(item.getSourceImg()), imageView, R.drawable.icon_empty);
        //分享回调
        helper.getView(R.id.tv_share).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (callBack != null) {
                    callBack.onShare(item, helper.getAdapterPosition());
                }
            }
        });
        imageView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                PhotoViewActivity.launch((Activity) mContext, getUrl(item.getSourceImg()), imageView);
            }
        });

    }

    /**
     * 检查地址，防止地址异常
     */
    private String getUrl(String url) {
        if (url.startsWith("/")) {
            return ApiUrl.CDN_URL.concat(url);
        } else {
            return ApiUrl.CDN_URL.concat("/").concat(url);
        }
    }


    public interface OnShareCallBack {
        void onShare(ShareListBean.RowBean item, int position);
    }
}