package com.ybm100.app.crm.task.activity

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import com.xyy.common.navigationbar.AbsNavigationBar
import com.xyy.common.navigationbar.DefaultNavigationBar
import com.xyy.common.util.FragmentUtils
import com.xyy.userbehaviortracking.utils.UserBehaviorTrackingUtils
import com.xyy.utilslibrary.base.activity.BaseCompatActivity
import com.ybm100.app.crm.R
import com.ybm100.app.crm.constant.Constants
import com.ybm100.app.crm.constant.Constants.GoodsManagement.ACTIVITY_REQUEST_CODE_SELECT_CUSTOMER
import com.ybm100.app.crm.task.bean.SelectCustomersBean
import com.ybm100.app.crm.task.fragment.SelectCustomersFragment

/**
 * 选择客户
 */
class SelectCustomersActivity : BaseCompatActivity() {
    private lateinit var mSelectCustomersFragment: SelectCustomersFragment

    override fun initHead(): AbsNavigationBar<*> {
        return DefaultNavigationBar.Builder(this).setTitle("选择客户")
            .setRightIcon(R.drawable.icon_search)
            .setRightClickListener {
                BaseTaskSearchActivity.startActivity(
                    this,
                    Constants.Task.CONSTANT_SEARCH_TYPE_CUSTOMERS,
                    intent?.extras?.getString(Constants.Task.ARG_TASK_ID, ""),
                    from = intent?.extras?.getInt(Constants.Task.ARG_FROM, -1),
                    goodsStr = mSelectCustomersFragment.mGoods,
                    branchCode = intent?.extras?.getString(Constants.Task.ARG_BRANCH_CODE, "") ?: ""
                )

                UserBehaviorTrackingUtils.track(Constants.Task.MC_TASK_PRODUCT_RECOMMEND_WITH_CLIENT_SEARCH)
            }
            .builder()
    }

    override fun getLayoutId(): Int {
        return R.layout.activity_select_customers
    }

    override fun initView(savedInstanceState: Bundle?) {
        mSelectCustomersFragment = SelectCustomersFragment.newInstance(intent?.extras)
        FragmentUtils.replaceFragment(
            supportFragmentManager,
            mSelectCustomersFragment,
            R.id.fragment_container,
            false
        )
    }

    companion object {
        val sCartList: MutableList<SelectCustomersBean.Row> = mutableListOf()

        /**
         * @param activity
         * @param taskId 任务 id，商品管理可以传null
         * @param goods 商品 id，多个以「,」分割
         * @param branchCode 只有商品详情跳转过来的会传branchCode
         */
        @JvmStatic
        fun startActivity(
            activity: Activity?,
            taskId: String?,
            goods: String?,
            from: Int?,
            isCustomerSearch: Boolean = false,
            branchCode: String? = ""
        ) {

            sCartList.clear()

            val intent = Intent(activity, SelectCustomersActivity::class.java)
            val bundle = Bundle()
            bundle.putString(Constants.Task.ARG_TASK_ID, taskId ?: "")
            bundle.putString(Constants.Task.ARG_GOODS_STR, goods ?: "")
            bundle.putInt(Constants.Task.ARG_FROM, from ?: -1)
            bundle.putBoolean(Constants.Task.ARG_IS_CUSTOMER_SEARCH, isCustomerSearch)
            bundle.putString(Constants.Task.ARG_BRANCH_CODE, branchCode)
            intent.putExtras(bundle)
            activity?.startActivityForResult(intent, ACTIVITY_REQUEST_CODE_SELECT_CUSTOMER)
        }
    }
}
