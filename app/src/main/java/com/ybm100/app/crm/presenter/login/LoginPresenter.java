package com.ybm100.app.crm.presenter.login;

import androidx.annotation.NonNull;

import com.xyy.common.util.ToastUtils;
import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.utils.LogUtils;
import com.ybm100.app.crm.bean.CaptchaBean;
import com.ybm100.app.crm.bean.user.UserInfoBean;
import com.ybm100.app.crm.contract.login.LoginContract;
import com.ybm100.app.crm.model.login.LoginModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

public class LoginPresenter extends BasePresenter<LoginContract.ILoginModel, LoginContract.ILoginView> {

    @NonNull
    public static LoginPresenter newInstance() {
        return new LoginPresenter();
    }

    @Override
    protected LoginContract.ILoginModel getModel() {
        return LoginModel.newInstance();
    }

    public void login(final String mobile, final String pwd, String deviceId, String vcode, String captchaId) {
        if (mIView == null || mIModel == null)
            return;
        mRxManager.register(mIModel.loginRequest(mobile, pwd, deviceId, vcode, captchaId).subscribe(
                new SimpleSuccessConsumer<UserInfoBean>(mIView, "登录中...") {
                    @Override
                    public void onSuccess(UserInfoBean bean) {
                        ToastUtils.showShortSafe("登录成功");
                        mIView.loginSuccess(bean);
                    }

                    @Override
                    public void onFailure(UserInfoBean baseBean) {
                        mIView.loginFailure(baseBean.getErrorMsg());
                    }
                }, new SimpleErrorConsumer(mIView) {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        super.accept(throwable);
                        mIView.loginFailure(throwable.toString());
                    }
                }));
    }

    public void getVerifyCode() {
        if (mIView == null || mIModel == null)
            return;
        mRxManager.register(mIModel.getVerifyCode().subscribe(
                new SimpleSuccessConsumer<CaptchaBean>(mIView) {
                    @Override
                    public void onSuccess(CaptchaBean captchaBean) {
                        LogUtils.e("code", "onSuccess");
                        mIView.getVerifyCodeSuccess(captchaBean);
                    }

                    @Override
                    public void onFailure(int errorCode) {
                        mIView.getVerifyCodeFailure();
                    }
                }, new SimpleErrorConsumer(mIView) {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        super.accept(throwable);
                        mIView.getVerifyCodeFailure();
                    }
                }));
    }
}
