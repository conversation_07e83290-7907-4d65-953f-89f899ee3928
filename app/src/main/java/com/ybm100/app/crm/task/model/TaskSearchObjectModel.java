package com.ybm100.app.crm.task.model;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiService;
import com.ybm100.app.crm.api.HyApiService;
import com.ybm100.app.crm.task.bean.MerchantBean;
import com.ybm100.app.crm.task.bean.PagingApiBean;
import com.ybm100.app.crm.contract.BaseSearchContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import io.reactivex.Observable;

/**
 * Created by dengmingjia on 2018/12/25
 * 任务选择对象
 */
public class TaskSearchObjectModel extends BaseModel implements BaseSearchContract.IModel<MerchantBean> {
    int authType;
    int ifVip;
    double latitude;
    double longitude;
    boolean isFromHY;

    public TaskSearchObjectModel(int authType, int ifVip, double latitude, double longitude,boolean isFromHY) {
        this.authType = authType;
        this.ifVip = ifVip;
        this.latitude = latitude;
        this.longitude = longitude;
        this.isFromHY=isFromHY;
    }

    @Override
    public Observable<RequestBaseBean<PagingApiBean<MerchantBean>>> search(String apiVersion, String keyword, int limit, int offset) {
        return  isFromHY?RetrofitCreateHelper.createApi(HyApiService.class).searchTaskObjectsByKey(apiVersion, keyword, "", limit, offset, authType, ifVip, latitude, longitude)
                .compose(RxHelper.rxSchedulerHelper())
        :RetrofitCreateHelper.createApi(ApiService.class).searchTaskObjectsByKey(apiVersion, keyword, "", limit, offset, authType, ifVip, latitude, longitude)
                .compose(RxHelper.rxSchedulerHelper());
    }
}
