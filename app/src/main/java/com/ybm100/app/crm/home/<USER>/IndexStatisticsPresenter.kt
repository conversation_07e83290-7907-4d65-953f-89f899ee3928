package com.ybm100.app.crm.home.presenter

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.bean.lzcustomer.LzRoleBean
import com.ybm100.app.crm.home.bean.SalesStatisticsV2
import com.ybm100.app.crm.home.contract.IndexStatisticsContract
import com.ybm100.app.crm.home.model.IndexStatisticsModel
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer
import com.ybm100.app.crm.utils.module.presenter.BaseModulePresenter
import io.reactivex.disposables.Disposable
import java.util.*

class IndexStatisticsPresenter
    : BaseModulePresenter<IndexStatisticsContract.IIndexStatisticsView>() {

    var type: IndexStatisticsModel.StatisticsType? = null

    fun requestStatistics(fieldMap: HashMap<String, String?>) {
        mRxManager.register(model.getStatistics(fieldMap).subscribe(
                object : SimpleSuccessConsumer<RequestBaseBean<SalesStatisticsV2?>?>(mIView) {
                    override fun onSuccess(bean: RequestBaseBean<SalesStatisticsV2?>?) {
                        mIView.getStatisticsSuccess(bean?.data)
                    }

                    override fun onFailure(errorCode: Int) {
                        mIView.getStatisticsFail()
                    }
                }, object : SimpleErrorConsumer(mIView) {
            override fun onError(throwable: Throwable, msg: String) {
                mIView.getStatisticsFail()
            }
        }))
    }

    override fun getModel(): IndexStatisticsContract.IIndexStatisticsModel {
        return IndexStatisticsModel.newInstance(type ?: IndexStatisticsModel.StatisticsType.All)
    }

}
