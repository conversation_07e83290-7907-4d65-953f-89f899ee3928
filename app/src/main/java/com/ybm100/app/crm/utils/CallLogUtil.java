package com.ybm100.app.crm.utils;

import android.content.ContentResolver;
import android.content.Context;
import android.database.Cursor;
import android.provider.CallLog;

import com.xyy.utilslibrary.utils.TimeUtils;
import com.ybm100.app.crm.bean.contact.CallLogBean;
import com.ybm100.app.crm.bean.contact.WrapCallLogBean;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Function;
import io.reactivex.schedulers.Schedulers;

/**
 * <AUTHOR>
 * @version 1.0
 * @file CallLogUtils.java
 * @brief
 * @date 2018/12/25
 * Copyright (c) 2018, 北京小药药
 * All rights reserved.
 */
public class CallLogUtil {


    private static final String[] CALLLOGS_PROJECTION = new String[]{
            CallLog.Calls.TYPE, CallLog.Calls.DATE,
            CallLog.Calls.DURATION, CallLog.Calls.NUMBER};


    public static Observable<WrapCallLogBean> getCallLogs(final Context activity, List<String> s, final String lastTimeFormat) {
        Observable<WrapCallLogBean> callLogs = Observable.fromIterable(s)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .concatMap(new Function<String, Observable<WrapCallLogBean>>() {
                    @Override
                    public Observable<WrapCallLogBean> apply(String s) {
                        WrapCallLogBean bean = new WrapCallLogBean();
                        ArrayList<CallLogBean> mCallLogBeans = new ArrayList<>();
                        bean.setList(mCallLogBeans);
                        ContentResolver resolver = activity.getContentResolver();
                        // 获取手机联系人
                        Cursor phoneCursor = resolver.query(CallLog.Calls.CONTENT_URI,
                                CALLLOGS_PROJECTION, CallLog.Calls.NUMBER + "=?", new String[]{s}, "date DESC");

                        long totalTime = 0;
                        if (phoneCursor != null) {
                            while (phoneCursor.moveToNext()) {

                                CallLogBean callLogBean = new CallLogBean();
                                long date = phoneCursor.getLong(phoneCursor.getColumnIndex(CallLog.Calls.DATE));

                                Date lastDate = TimeUtils.millis2Date(date);
                                Date today = new Date();
                                int i = differentDays(lastDate, today);
                                if (i == 0) {
                                    //今日
                                    String dateStr = TimeUtils.millis2String(date, TimeUtils.DATE_FORMAT_HM);
                                    callLogBean.setDateStr(dateStr);
                                } else {
                                    String dateStr = TimeUtils.millis2String(date, TimeUtils.DATE_FORMAT_YMD);
                                    callLogBean.setDateStr(dateStr);
                                }


                                callLogBean.setDate(date);

                                String duration = phoneCursor.getString(phoneCursor.getColumnIndex(CallLog.Calls.DURATION));
                                String number = phoneCursor.getString(phoneCursor.getColumnIndex(CallLog.Calls.NUMBER));

                                long time = Long.parseLong(duration);
                                totalTime += time;
                                String longs = secondNum2Time(time);
                                callLogBean.setDuration(longs);
                                int type = phoneCursor.getInt(phoneCursor.getColumnIndex(CallLog.Calls.TYPE));
                                String typeStr = getTypeStr(type);
                                callLogBean.setType(type);
                                callLogBean.setTypeStr(typeStr);


                                bean.setNumber(number);

                                mCallLogBeans.add(callLogBean);
                            }

                            String s1 = secondNum2Time(totalTime);
                            bean.setTotalTime(s1);
                            phoneCursor.close();
                        }

                        return Observable.just(bean);
                    }
                });


        return callLogs;
    }

    /**
     * date2比date1多的天数
     *
     * @param date1
     * @param date2
     * @return
     */
    public static int differentDays(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        int day1 = cal1.get(Calendar.DAY_OF_YEAR);
        int day2 = cal2.get(Calendar.DAY_OF_YEAR);

        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);
        if (year1 != year2)   //同一年
        {
            int timeDistance = 0;
            for (int i = year1; i < year2; i++) {
                if (i % 4 == 0 && i % 100 != 0 || i % 400 == 0)    //闰年
                {
                    timeDistance += 366;
                } else    //不是闰年
                {
                    timeDistance += 365;
                }
            }

            return timeDistance + (day2 - day1);
        } else    //不同年
        {
            System.out.println("判断day2 - day1 : " + (day2 - day1));
            return day2 - day1;
        }
    }

    public static String secondNum2Time(long time) {
        long hour = time / 3600;
        long minute = time / 60 % 60;
        long second = time % 60;

        StringBuilder sb = new StringBuilder();

        if (hour > 0) {
            sb.append(hour);
            sb.append("小时");
        }
        if (minute > 0) {
            sb.append(minute);
            sb.append("分钟");
        }
        if (second >= 0) {
            sb.append(second);
            sb.append("秒");
        }
        return sb.toString();
    }

    private static String getTypeStr(int type) {
        if (CallLog.Calls.INCOMING_TYPE == type) {
            return "来电";
        } else if (CallLog.Calls.OUTGOING_TYPE == type) {
            return "去电";
        } else if (CallLog.Calls.MISSED_TYPE == type) {
            return "未接";
        } else if (CallLog.Calls.VOICEMAIL_TYPE == type) {
            return "语音邮件";
        } else if (CallLog.Calls.REJECTED_TYPE == type) {
            return "拒绝";
        } else if (CallLog.Calls.BLOCKED_TYPE == type) {
            return "阻止";
        } else {
            return "未知";
        }
    }
}
