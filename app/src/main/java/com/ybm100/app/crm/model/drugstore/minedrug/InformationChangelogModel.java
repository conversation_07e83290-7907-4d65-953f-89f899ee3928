package com.ybm100.app.crm.model.drugstore.minedrug;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiDrugstoreInfo;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.DrugstoreUpdateLogBean;
import com.ybm100.app.crm.contract.drugstore.minedrug.InformationChangelogContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:15
 */
public class InformationChangelogModel extends BaseModel implements InformationChangelogContract.IInformationChangelogModel {

    public static InformationChangelogModel newInstance() {
        return new InformationChangelogModel();
    }

    @Override
    public Observable<RequestBaseBean<DrugstoreUpdateLogBean>> getInfoChangeLogData(int limit, String merchantId, int offset) {
        return RetrofitCreateHelper.createApi(ApiDrugstoreInfo.class).getInfoChangeLogData(limit, merchantId, offset)
                .compose(RxHelper.rxSchedulerHelper());
    }
}