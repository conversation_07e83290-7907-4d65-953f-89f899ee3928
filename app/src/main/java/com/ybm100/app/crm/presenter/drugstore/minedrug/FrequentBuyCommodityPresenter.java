package com.ybm100.app.crm.presenter.drugstore.minedrug;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.CommonListBean;
import com.ybm100.app.crm.contract.drugstore.minedrug.FrequentBuyCommodityContract;
import com.ybm100.app.crm.goodsmanagement.bean.EstimatedPriceListBean;
import com.ybm100.app.crm.model.drugstore.minedrug.FrequentBuyCommodityModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

import java.util.HashMap;

import io.reactivex.functions.Consumer;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:19
 */
public class FrequentBuyCommodityPresenter extends BasePresenter<FrequentBuyCommodityContract.IFrequentBuyCommodityModel, FrequentBuyCommodityContract.IFrequentBuyCommodityView> {

    public static FrequentBuyCommodityPresenter newInstance() {
        return new FrequentBuyCommodityPresenter();
    }

    @Override
    protected FrequentBuyCommodityModel getModel() {
        return FrequentBuyCommodityModel.newInstance();
    }

    public void shopCommonProcureRequest(String shopId) {
        if (mIView == null || mIModel == null) return;

        mRxManager.register(mIModel.shopCommonProcureRequest(shopId).subscribe(new SimpleSuccessConsumer<RequestBaseBean<CommonListBean>>(mIView) {
            @Override
            public void onSuccess(RequestBaseBean<CommonListBean> requestBaseBean) {

                mIView.shopCommonProcureRequestSuccess(requestBaseBean);
            }
        }, new SimpleErrorConsumer(mIView)));

    }

    public void getEstimatedPrices(HashMap<String, String> queryMap, boolean isHighGrossProfit){

        mRxManager.register(mIModel.getEstimatedPrices(queryMap).subscribe(new SimpleSuccessConsumer<RequestBaseBean<EstimatedPriceListBean>>(mIView) {
            @Override
            public void onSuccess(RequestBaseBean<EstimatedPriceListBean> estimatedPriceListBeanRequestBaseBean) {
                mIView.onGetEstimatedPricesSuccess(estimatedPriceListBeanRequestBaseBean, isHighGrossProfit);
            }

            @Override
            public void onFailure(int errorCode) {
//                super.onFailure(errorCode);
            }
        }, new Consumer<Throwable>() {
            @Override
            public void accept(Throwable throwable) throws Exception {

            }
        }));
    }

}
