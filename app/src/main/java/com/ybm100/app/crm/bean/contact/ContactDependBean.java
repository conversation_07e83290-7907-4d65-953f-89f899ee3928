package com.ybm100.app.crm.bean.contact;

import com.chad.library.adapter.base.entity.MultiItemEntity;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @file ContactDependBean.java
 * @brief
 * @date 2018/12/24
 * Copyright (c) 2018, 北京小药药
 * All rights reserved.
 */
public class ContactDependBean implements MultiItemEntity , Serializable {
    public int itemType;

    public String phoneNum;
    public String id;
    //通话时长
    public String timeDistance;
    public String getPhoneNum() {
        return phoneNum;
    }

    public void setPhoneNum(String phoneNum) {
        this.phoneNum = phoneNum;
    }

    public void setItemType(int itemType) {
        this.itemType = itemType;
    }

    @Override
    public int getItemType() {
        return itemType;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public String getTimeDistance() {
        return timeDistance;
    }

    public void setTimeDistance(String timeDistance) {
        this.timeDistance = timeDistance;
    }
}
