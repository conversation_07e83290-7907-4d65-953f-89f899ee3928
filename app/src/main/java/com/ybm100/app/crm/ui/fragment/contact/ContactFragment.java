package com.ybm100.app.crm.ui.fragment.contact;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.View;
import android.widget.TextView;

import com.alibaba.android.arouter.launcher.ARouter;
import com.xyy.common.navigationbar.DefaultNavigationBar;
import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.fragment.BaseMVPCompatFragment;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.contract.contact.ContactContract;
import com.ybm100.app.crm.presenter.contact.ContactPresenter;
import com.ybm100.app.crm.ui.activity.message.ContactActivity;
import com.ybm100.app.crm.utils.router.RouterPath;

import static com.ybm100.app.crm.ui.activity.message.ContactActivity.EDIT;

/**
 * Created by XyyMvpSportTemplate on 12/24/2018 10:04
 */
public class ContactFragment extends BaseMVPCompatFragment<ContactPresenter> implements ContactContract.IContactView {

    private ContactListFragment contactListFragment;
    private TextView editText;

    public static ContactFragment newInstance() {
        Bundle args = new Bundle();
        ContactFragment fragment = new ContactFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @NonNull
    @Override
    public BasePresenter initPresenter() {
        return ContactPresenter.newInstance();
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_contact;
    }

    @Override
    public void initUI(View view, @Nullable Bundle savedInstanceState) {
        new DefaultNavigationBar.Builder(getContext()).setTitle(getString(R.string.contact_book))
                .builder();
        editText = view.findViewById(R.id.et_search_contact);
        editText.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                ARouter.getInstance()
                        .build(RouterPath.CONTACT_BOOK_PAGE)
                        .withInt(ContactActivity.TYPE, ContactActivity.CONTACT_SEARCH)
                        .navigation();
            }
        });
        contactListFragment = ContactListFragment.newInstance(null);
        contactListFragment.getArguments().putBoolean(EDIT, true);
        loadRootFragment(R.id.fl_contact, contactListFragment);
    }

    @Override
    public void onLazyInitView(@Nullable Bundle savedInstanceState) {
        super.onLazyInitView(savedInstanceState);

    }

    @Override
    public void showNetError() {

    }
}
