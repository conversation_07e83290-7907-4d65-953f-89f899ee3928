package com.ybm100.app.crm.bean;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019-06-24
 */
public class ValueBean implements Serializable, ISearch {
    private String value;
    private int id;
    private boolean checked;

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public boolean isChecked() {
        return checked;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }

    @Override
    public String getDisplay() {
        return value;
    }

    @Override
    public String toString() {
        return "ValueBean{" +
                "value='" + value + '\'' +
                ", id=" + id +
                ", checked=" + checked +
                '}';
    }
}
