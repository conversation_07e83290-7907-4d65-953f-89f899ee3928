package com.ybm100.app.crm.task.bean;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Created by den<PERSON><PERSON>jia on 2018/12/25
 */
public class TaskListBean implements Parcelable {

    /**
     * code : 测试内容7443
     * createTime : {}
     * creatorId : 34147
     * creatorName : 测试内容l1cb
     * customerId : 76682
     * customerName : 测试内容w6ap
     * customerType : 83773
     * endTime : {}
     * id : 25328
     * imageUrl : 测试内容uno3
     * lastVisitTime : {}
     * parentId : 54306
     * remark : 测试内容vv57
     * status : 34457
     * sysUserId : 26814
     * sysUserName : 测试内容c8su
     * theme : 测试内容yy4u
     * type : 52840
     * vidioUrl : 测试内容ox4b
     */

    private String code;
    private long createTime;
    private int creatorId;
    private String creatorName;
    private long customerId;
    private String customerName;
    private int customerType;
    private long endTime;
    private int id;
    private String imageUrl;
    private long lastVisitTime;
    private long parentId;
    private String remark;
    private int status;
    private int sysUserId;
    private String sysUserName;
    private String theme;
    private int type;
    private String vidioUrl;
    private long finishTime;


    protected TaskListBean(Parcel in) {
        code = in.readString();
        createTime = in.readLong();
        creatorId = in.readInt();
        creatorName = in.readString();
        customerId = in.readLong();
        customerName = in.readString();
        customerType = in.readInt();
        endTime = in.readLong();
        id = in.readInt();
        imageUrl = in.readString();
        lastVisitTime = in.readLong();
        parentId = in.readLong();
        remark = in.readString();
        status = in.readInt();
        sysUserId = in.readInt();
        sysUserName = in.readString();
        theme = in.readString();
        type = in.readInt();
        vidioUrl = in.readString();
        finishTime = in.readLong();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(code);
        dest.writeLong(createTime);
        dest.writeInt(creatorId);
        dest.writeString(creatorName);
        dest.writeLong(customerId);
        dest.writeString(customerName);
        dest.writeInt(customerType);
        dest.writeLong(endTime);
        dest.writeInt(id);
        dest.writeString(imageUrl);
        dest.writeLong(lastVisitTime);
        dest.writeLong(parentId);
        dest.writeString(remark);
        dest.writeInt(status);
        dest.writeInt(sysUserId);
        dest.writeString(sysUserName);
        dest.writeString(theme);
        dest.writeInt(type);
        dest.writeString(vidioUrl);
        dest.writeLong(finishTime);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<TaskListBean> CREATOR = new Creator<TaskListBean>() {
        @Override
        public TaskListBean createFromParcel(Parcel in) {
            return new TaskListBean(in);
        }

        @Override
        public TaskListBean[] newArray(int size) {
            return new TaskListBean[size];
        }
    };

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public long getLastVisitTime() {
        return lastVisitTime;
    }

    public void setLastVisitTime(long lastVisitTime) {
        this.lastVisitTime = lastVisitTime;
    }

    public int getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(int creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(long customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public int getCustomerType() {
        return customerType;
    }

    public void setCustomerType(int customerType) {
        this.customerType = customerType;
    }


    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }


    public long getParentId() {
        return parentId;
    }

    public void setParentId(long parentId) {
        this.parentId = parentId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getSysUserId() {
        return sysUserId;
    }

    public void setSysUserId(int sysUserId) {
        this.sysUserId = sysUserId;
    }

    public String getSysUserName() {
        return sysUserName;
    }

    public void setSysUserName(String sysUserName) {
        this.sysUserName = sysUserName;
    }

    public String getTheme() {
        return theme;
    }

    public void setTheme(String theme) {
        this.theme = theme;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getVidioUrl() {
        return vidioUrl;
    }

    public void setVidioUrl(String vidioUrl) {
        this.vidioUrl = vidioUrl;
    }

    public long getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(long finishTime) {
        this.finishTime = finishTime;
    }
}
