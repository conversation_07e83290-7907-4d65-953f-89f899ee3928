package com.ybm100.app.crm.bean.drugstore.minedrugstore;

import com.chad.library.adapter.base.entity.MultiItemEntity;
import com.ybm100.app.crm.goodsmanagement.bean.GoodsManagementListBean;

import java.io.Serializable;
import java.util.List;

/**
 * author :lx
 * date 2019/1/7.
 * email： <EMAIL>
 */
public class CommonProcureProductList implements MultiItemEntity, Serializable {

    public static final int content0 = 0;//内容样式1
    public static final int content1 = 1;//内容样式2

    public static final int header0 = 10;//头部样式1

    /*-----------药店透视-常用采购-浏览过的商品------------*/
    public int id;
    public String imageUrl;//药品图片
    public String manufacturer;//药品厂家
    public String commonName;//药品名称
    public double fob;//药品价格
    public String spec;//药品规格
    public int payAmount;//购买数量
    public int status;//2是已售罄//4是已下架
    public String statusName;//状态文案
    public String isBoughtText;//购买过，未购买
    public int isBought;//购买过1，未购买0
    public double retailPrice;//对比价
    public String discountPrice;


    /*----------------药店透视-购物车--------------------*/
    public int amount;
    public String name;
    public double price;
    public double subtotal;
    public int productStatus;
    public int buyRewardId;
    public boolean isChoosed;
    public boolean hasBuyReward;
    public boolean hasNew;
    public boolean hasProm;
    public String stockTitle;
    public double downPrice;
    public String promotionTag;

    //标签
    public List<GoodsManagementListBean.Row.Tag> tagList;



    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        CommonProcureProductList that = (CommonProcureProductList) o;

        return id == that.id;

    }

    @Override
    public int hashCode() {
        return id;
    }

    @Override
    public int getItemType() {
        return 0;
    }
}