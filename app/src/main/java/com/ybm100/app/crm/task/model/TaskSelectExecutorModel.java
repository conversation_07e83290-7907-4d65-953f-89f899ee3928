package com.ybm100.app.crm.task.model;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.api.ApiService;
import com.ybm100.app.crm.task.bean.ExecutorTreeBean;
import com.ybm100.app.crm.contract.BaseExecutorContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 01/02/2019 20:35
 */
public class TaskSelectExecutorModel extends BaseModel implements BaseExecutorContract.IModel<ExecutorTreeBean>{

    public static TaskSelectExecutorModel newInstance() {
        return new TaskSelectExecutorModel();
    }

    @Override
    public Observable<RequestBaseBean<ExecutorTreeBean>> getData() {
        return RetrofitCreateHelper.createApi(ApiService.class).getTaskExecutorList();
    }

    @Override
    public Observable<RequestBaseBean<Boolean>> getUserLevel(String sysUserId) {
        return null;
    }
}