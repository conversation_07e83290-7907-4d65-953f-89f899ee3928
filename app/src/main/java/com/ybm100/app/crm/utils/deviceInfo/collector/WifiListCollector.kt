package com.ybm100.app.crm.utils.deviceInfo.collector

import android.content.Context
import android.net.wifi.WifiManager
import java.lang.reflect.Field


class WifiListCollector : BaseCollector() {

    override fun internalCollect(context: Context): String? {
        val wifiManager = context.getSystemService(Context.WIFI_SERVICE) as WifiManager
        val sb = StringBuilder()
        wifiManager.scanResults.forEach {
            sb.append(it.SSID).append("|").append(it.level).append(",")
        }
        return sb.toString()

    }


}
