package com.ybm100.app.crm.presenter.drugstore.minedrug;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.PharmacyPerspective;
import com.ybm100.app.crm.contract.drugstore.minedrug.DrugstoreFluoroscopyContract;
import com.ybm100.app.crm.model.drugstore.minedrug.DrugstoreFluoroscopyModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:17
 */
public class DrugstoreFluoroscopyPresenter extends BasePresenter<DrugstoreFluoroscopyContract.IDrugstoreFluoroscopyModel, DrugstoreFluoroscopyContract.IDrugstoreFluoroscopyView> {

    public static DrugstoreFluoroscopyPresenter newInstance() {
        return new DrugstoreFluoroscopyPresenter();
    }

    @Override
    protected DrugstoreFluoroscopyModel getModel() {
        return DrugstoreFluoroscopyModel.newInstance();
    }

    /**
     * 透视数据
     *
     * @param shopId
     */
    public void getPerspectiveData(String shopId) {
        if (mIView == null || mIModel == null) return;

        mRxManager.register(mIModel.getPerspectiveData(shopId).subscribe(new SimpleSuccessConsumer<RequestBaseBean<PharmacyPerspective>>(mIView) {
            @Override
            public void accept(RequestBaseBean<PharmacyPerspective> baseBean) throws Exception {
                super.accept(baseBean);
                if (baseBean.isSuccess()) {
                    onSuccess(baseBean);
                } else {
                    mIView.getPerspectiveDataFail(baseBean.getErrorMsg());
                }
            }

            @Override
            public void onSuccess(RequestBaseBean<PharmacyPerspective> pharmacyPerspectiveRequestBaseBean) {
                mIView.getPerspectiveDataSuccess(pharmacyPerspectiveRequestBaseBean);
            }
        }, new SimpleErrorConsumer(mIView) {
            @Override
            protected void onError(Throwable throwable, String msg) {
                super.onError(throwable, msg);
                if (mIView == null)
                    return;
                mIView.showNetError();
            }
        }));
    }

}
