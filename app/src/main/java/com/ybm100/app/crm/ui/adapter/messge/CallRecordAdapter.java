package com.ybm100.app.crm.ui.adapter.messge;

import android.text.TextUtils;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.contact.CallLogBean;

/**
 * <AUTHOR>
 * @version 1.0
 * @file CallRecordAdapter.java
 * @brief 通话记录
 * @date 2018/12/22
 * Copyright (c) 2018, 北京小药药
 * All rights reserved.
 */
public class CallRecordAdapter extends BaseQuickAdapter<CallLogBean, BaseViewHolder> {


    public CallRecordAdapter(int layoutResId) {
        super(layoutResId);
    }

    @Override
    protected void convert(BaseViewHolder helper, CallLogBean entity) {
        if (!TextUtils.isEmpty(entity.getDateStr())) {
            helper.setText(R.id.tv_contact_call_time, entity.getDateStr());
        }

        if (!TextUtils.isEmpty(entity.getDuration())) {
            helper.setText(R.id.tv_contact_call_distance, entity.getDuration());
        }
    }
}
