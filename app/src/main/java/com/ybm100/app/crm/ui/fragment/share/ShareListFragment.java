package com.ybm100.app.crm.ui.fragment.share;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener;
import com.xyy.common.widget.DefaultItemDecoration;
import com.xyy.common.widget.statusview.StatusViewLayout;
import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.base.fragment.BaseMVPCompatFragment;
import com.xyy.utilslibrary.utils.DisplayUtils;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.api.ApiUrl;
import com.ybm100.app.crm.bean.share.ShareListBean;
import com.ybm100.app.crm.contract.share.ShareListContract;
import com.ybm100.app.crm.order.activity.ShareImageActivity;
import com.ybm100.app.crm.presenter.share.ShareListPresenter;
import com.ybm100.app.crm.ui.adapter.share.ShareListAdapter;
import com.ybm100.app.crm.utils.ShareHelper;

import java.util.HashMap;

import butterknife.BindView;

/**
 * Created by XyyMvpSportTemplate on 03/05/2019 19:07
 * 复用的活动列表
 */
public class ShareListFragment extends BaseMVPCompatFragment<ShareListPresenter> implements ShareListContract.IShareListView, OnRefreshLoadMoreListener {
    @BindView(R.id.rv_share_list)
    RecyclerView rvShareList;
    @BindView(R.id.smart_refresh_share)
    SmartRefreshLayout smartRefreshShare;
    @BindView(R.id.status_view_share)
    StatusViewLayout statusViewShare;

    private ShareListAdapter shareListAdapter;
    private final HashMap<String, String> map = new HashMap<>();
    private int currShareItemPosition = 0;//记录分享的item position
    private String ifDesc = "0";
    private String branchCode;

    public static ShareListFragment newInstance(String ifDesc, String branchCode) {
        Bundle args = new Bundle();
        args.putString("ifDesc", ifDesc);
        args.putString("branchCode", branchCode);
        ShareListFragment fragment = new ShareListFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_share_list;
    }

    @NonNull
    @Override
    public BasePresenter initPresenter() {
        return ShareListPresenter.newInstance();
    }


    @Override
    public void onLazyInitView(@Nullable Bundle savedInstanceState) {
        super.onLazyInitView(savedInstanceState);
        getData(true);
    }

    /**
     * 搜索
     */
    public void search(String keyword) {
        smartRefreshShare.setNoMoreData(false);
        map.clear();
        map.put("keyword", keyword);
        map.put("ifDesc", ifDesc);
        mPresenter.getListData(true, map);
    }

    public void filter(String ifDesc, String branchCode, boolean refresh) {
        if (refresh) {
            smartRefreshShare.setNoMoreData(false);
        }
        this.ifDesc = ifDesc;
        map.clear();
        map.put("ifDesc", ifDesc);
        if (!TextUtils.isEmpty(branchCode)) {
            map.put("branchCode", branchCode);
            this.branchCode = branchCode;
        }
        mPresenter.getListData(true, map);
    }

    @Override
    public void initUI(View view, @Nullable Bundle savedInstanceState) {
        if (getArguments() != null) {
            ifDesc = getArguments().getString("ifDesc");
            branchCode = getArguments().getString("branchCode");
        }
        setRefreshLayout(smartRefreshShare);
        initRecyclerView();
        smartRefreshShare.setOnRefreshLoadMoreListener(this);
        statusViewShare.setOnRetryListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getData(true);
            }
        });
    }

    /**
     * 初始化列表
     */
    private void initRecyclerView() {
        rvShareList.setLayoutManager(new LinearLayoutManager(mContext));
        shareListAdapter = new ShareListAdapter(R.layout.item_share_list);
        DefaultItemDecoration itemDecoration = new DefaultItemDecoration(mContext, DisplayUtils.dp2px(10));
        itemDecoration.setLineColorResId(R.color.color_gray_EFEFF4);
        itemDecoration.setHasHeaderAndFooter(true);
        rvShareList.addItemDecoration(itemDecoration);
        rvShareList.setAdapter(shareListAdapter);
    }

    private void getData(boolean refresh) {
        if (refresh) {
            smartRefreshShare.setNoMoreData(false);
        }
        if (!"0".equals(ifDesc)) {
            map.clear();
            map.put("ifDesc", ifDesc);
            if (!TextUtils.isEmpty(branchCode)) {
                map.put("branchCode", branchCode);
            }
        }
        mPresenter.getListData(refresh, map);
    }

    @Override
    public void getListDataSuccess(boolean refresh, RequestBaseBean<ShareListBean> baseBean) {
        if (baseBean == null || baseBean.getData() == null
                || baseBean.getData().getRows() == null || baseBean.getData().getRows().size() == 0) {
            statusViewShare.showEmpty();
            return;
        }
        if (refresh && shareListAdapter != null) {
            shareListAdapter.getData().clear();
        }
        if (refresh || shareListAdapter == null) {
            shareListAdapter = new ShareListAdapter(R.layout.item_share_list, baseBean.getData().getRows(), new ShareListAdapter.OnShareCallBack() {
                @Override
                public void onShare(ShareListBean.RowBean item, int position) {
                    //获取当前点击item位置
                    currShareItemPosition = position;
                    share(item);
                }
            });
            rvShareList.setAdapter(shareListAdapter);
        } else {
            shareListAdapter.addData(baseBean.getData().getRows());
        }
        smartRefreshShare.setEnableLoadMore(true);
        smartRefreshShare.setEnableRefresh(true);
        statusViewShare.showContent();
    }

    /**
     * 分享弹窗
     */
    private void share(final ShareListBean.RowBean item) {
        ShareImageActivity.Companion.startActivity(mActivity, ApiUrl.CDN_URL.concat(item.getShareImgUrl()));
        ShareImageActivity.Companion.setShareCallback(new ShareHelper.ShareCallback() {
            @Override
            public void onCompleted(@org.jetbrains.annotations.Nullable String platform) {
//                ToastUtils.showLong("分享成功");
                mPresenter.shareTimesAddOne(item.getId());
            }

            @Override
            public void onError(@org.jetbrains.annotations.Nullable String platform, @org.jetbrains.annotations.Nullable String errMsg) {
//                ToastUtils.showLong("分享失败");
            }

            @Override
            public void onCancel(@org.jetbrains.annotations.Nullable String platform) {

            }

            @Override
            public void onStart(@org.jetbrains.annotations.Nullable String platform) {

            }
        });
    }

    /**
     * 更新分享次数 只更改单条
     */
    @Override
    public void shareTimesAddOneSuccess(RequestBaseBean baseBean) {
        if (shareListAdapter != null) {
            if (currShareItemPosition >= shareListAdapter.getData().size()) {
                if (shareListAdapter.getData().size() == 0) {
                    statusViewShare.showEmpty();
                }
                return;
            }
            ShareListBean.RowBean rowBean = shareListAdapter.getData().get(currShareItemPosition);
            int times = rowBean.getShareTime() + 1;
            rowBean.setShareTime(times);
            shareListAdapter.setData(currShareItemPosition, rowBean);
        }
    }

    @Override
    public void showNetError() {

    }

    @Override
    public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
        getData(false);
    }

    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
        getData(true);
    }


    @Override
    public void enableLoadMore(boolean b) {
        smartRefreshShare.setEnableLoadMore(b);
    }

    @Override
    public void loadMoreComplete() {
        smartRefreshShare.finishLoadMoreWithNoMoreData();
    }

}
