package com.ybm100.app.crm.widget.marquee;

import android.content.Context;
import android.content.res.TypedArray;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.View;
import android.view.ViewTreeObserver;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.TextView;
import android.widget.ViewFlipper;

import com.xyy.utilslibrary.utils.DisplayUtils;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.marquee.MarqueeBean;

import java.util.ArrayList;
import java.util.List;

/**
 * 滚动通知
 */
public class MarqueeView extends ViewFlipper {

    private Context mContext;
    private List<MarqueeBean> notices;// 通知集合
    private int interval = 3000;// 默认间隔
    private boolean isSetAnimDuration = false;// 是否能设置动画时长
    private int animDuration = 300;// 默认动画时长
    private int textSize = 14;// 默认字号
    private int textColor = 0xffffffff;// 默认文字颜色
    private boolean singleLine = false;
    private int gravity = Gravity.START | Gravity.CENTER_VERTICAL;
    private static final int TEXT_GRAVITY_LEFT = 0, TEXT_GRAVITY_CENTER = 1, TEXT_GRAVITY_RIGHT = 2;
    private OnItemClickListener onItemClickListener;

    public MarqueeView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs, 0);
    }

    private void init(Context context, AttributeSet attrs, int defStyleAttr) {
        this.mContext = context;
        if (notices == null) {
            notices = new ArrayList<>();
        }
        TypedArray typedArray = getContext().obtainStyledAttributes(attrs, R.styleable.MarqueeViewStyle, defStyleAttr, 0);
        interval = typedArray.getInteger(R.styleable.MarqueeViewStyle_mvInterval, interval);
        isSetAnimDuration = typedArray.hasValue(R.styleable.MarqueeViewStyle_mvAnimDuration);
        singleLine = typedArray.getBoolean(R.styleable.MarqueeViewStyle_mvSingleLine, false);
        animDuration = typedArray.getInteger(R.styleable.MarqueeViewStyle_mvAnimDuration, animDuration);
        if (typedArray.hasValue(R.styleable.MarqueeViewStyle_mvTextSize)) {
            textSize = (int) typedArray.getDimension(R.styleable.MarqueeViewStyle_mvTextSize, textSize);
            textSize = DisplayUtils.px2sp(textSize);
        }
        textColor = typedArray.getColor(R.styleable.MarqueeViewStyle_mvTextColor, textColor);
        int gravityType = typedArray.getInt(R.styleable.MarqueeViewStyle_mvGravity, TEXT_GRAVITY_LEFT);
        switch (gravityType) {
            case TEXT_GRAVITY_CENTER:
                gravity = Gravity.CENTER;
                break;
            case TEXT_GRAVITY_RIGHT:
                gravity = Gravity.END | Gravity.CENTER_VERTICAL;
                break;
            default:
                break;
        }

        typedArray.recycle();
        setFlipInterval(interval);
    }

    /**
     * 根据公告字符串列表  启动轮播1
     *
     * @param notices
     */
    public void startWithList(List<MarqueeBean> notices) {
        stopFlip();
        setNotices(notices);
        start();
    }

    /**
     * 根据公告字符串    启动轮播2
     *
     * @param notice
     */
    public void startWithText(final String notice) {
        if (TextUtils.isEmpty(notice)) {
            return;
        }
        getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                getViewTreeObserver().removeGlobalOnLayoutListener(this);
                startWithFixedWidth(notice, getWidth());
            }
        });
    }

    /**
     * 根据宽度和公告字符串启动轮播
     *
     * @param notice
     * @param width
     */
    private void startWithFixedWidth(String notice, int width) {
        int noticeLength = notice.length();
        int dpW = DisplayUtils.px2dp(width);
        int limit = dpW / textSize;
        if (dpW == 0) {
            throw new RuntimeException("Please set MarqueeView width !");
        }
        ArrayList<String> list = new ArrayList<>();
        if (noticeLength <= limit) {
            list.add(notice);
        } else {
            int size = noticeLength / limit + (noticeLength % limit != 0 ? 1 : 0);
            for (int i = 0; i < size; i++) {
                int startIndex = i * limit;
                int endIndex = ((i + 1) * limit >= noticeLength ? noticeLength : (i + 1) * limit);
                list.add(notice.substring(startIndex, endIndex));
            }
        }
        for (int i = 0; i < list.size(); i++) {
            MarqueeBean bean = new MarqueeBean();
            bean.setMessage(list.get(i));
            notices.add(bean);
        }
        start();
    }

    /**
     * 启动轮播
     *
     * @return
     */
    public boolean start() {
        if (notices == null || notices.size() == 0) {// 通知内容为空
            removeAllViews();
            resetAnimation();
            View view = createView(new MarqueeBean(), 0);
            addView(view);
            return false;
        } else {
            removeAllViews();
            resetAnimation();
            for (int i = 0; i < notices.size(); i++) {
                MarqueeBean bean = notices.get(i);
                final View view = createView(bean, i);
                final int position = i;
                view.setOnClickListener(v -> {
                    if (onItemClickListener != null) {
                        onItemClickListener.onItemClick(position, view, bean);
                    }
                });
                addView(view);
            }
            // 判断数量是否翻转
            if (notices.size() > 1) {
                startFlipping();
            } else {
                stopFlipping();
            }
            return true;
        }
    }

    /**
     * 重置动画
     */
    private void resetAnimation() {
        clearAnimation();
        // 进场动画
        Animation animIn = AnimationUtils.loadAnimation(mContext, R.anim.anim_marquee_in);
        if (isSetAnimDuration) {
            animIn.setDuration(animDuration);
        }
        setInAnimation(animIn);
        // 出场动画
        Animation animOut = AnimationUtils.loadAnimation(mContext, R.anim.anim_marquee_out);
        if (isSetAnimDuration) {
            animOut.setDuration(animDuration);
        }
        setOutAnimation(animOut);
        // 动画监听
        getInAnimation().setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {

            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
    }

    /**
     * 创建ViewFlipper下的TextView
     *
     * @param bean
     * @param position
     * @return
     */
    private View createView(MarqueeBean bean, int position) {
        if (bean == null) {
            return new View(mContext);
        }
        View view = View.inflate(mContext, R.layout.layout_marquee_item, null);
        TextView tv = view.findViewById(R.id.marquee_tv);
        tv.setText(bean.getMessage());
        view.setTag(position);
        return view;
    }

    public int getPosition() {
        return (int) getCurrentView().getTag();
    }

    public MarqueeBean getMarquee(int pos) {
        if (notices == null || notices.size() < pos) return null;
        return notices.get(pos);
    }

    public List<MarqueeBean> getNotices() {
        return notices;
    }

    /**
     * 设置通知内容集合
     *
     * @param notices
     */
    public void setNotices(List<MarqueeBean> notices) {
        this.notices = notices;
    }

    public interface OnItemClickListener {
        void onItemClick(int position, View textView, MarqueeBean bean);
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    /**
     * 开始翻转
     */
    public void startFlip() {
        if (notices.size() > 1) {
            startFlipping();
        }
    }

    /**
     * 停止翻转
     */
    public void stopFlip() {
        stopFlipping();
    }

}

