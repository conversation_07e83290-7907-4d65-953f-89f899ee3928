package com.ybm100.app.crm.ui.fragment.hycustomer;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.google.android.material.appbar.AppBarLayout;
import com.ybm100.app.crm.utils.DisplayUtil;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener;
import com.tbruyelle.rxpermissions2.RxPermissions;
import com.xyy.common.util.ToastUtils;
import com.xyy.common.widget.DefaultItemDecoration;
import com.xyy.common.widget.statusview.StatusViewLayout;
import com.xyy.flutter.container.container.ContainerRuntime;
import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.base.fragment.BaseMVPCompatFragment;
import com.xyy.utilslibrary.rxbus.RxBus;
import com.xyy.utilslibrary.rxbus.Subscribe;
import com.xyy.utilslibrary.utils.DisplayUtils;
import com.xyy.utilslibrary.utils.ImageUtils;
import com.xyy.utilslibrary.utils.ResourcesUtils;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.drugstore.FilterMapBean;
import com.ybm100.app.crm.bean.drugstore.PrivateListFilterBean;
import com.ybm100.app.crm.bean.goods.PopListBean;
import com.ybm100.app.crm.bean.hycustomer.HyPrivateListBean;
import com.ybm100.app.crm.utils.GsonUtils;
import com.ybm100.app.crm.constant.Constants;
import com.ybm100.app.crm.constant.DrugstoreConstants;
import com.ybm100.app.crm.constant.ExtraConstants;
import com.ybm100.app.crm.constant.RoleTypeConfig;
import com.ybm100.app.crm.constant.RxBusCode;
import com.ybm100.app.crm.contract.hycustomer.HyPrivateListContract;
import com.ybm100.app.crm.presenter.hycustomer.HyPrivateListPresenter;
import com.ybm100.app.crm.task.activity.SingleSelectExecutorActivity;
import com.ybm100.app.crm.task.bean.TaskAndMerchantBean;
import com.ybm100.app.crm.task.bean.TaskExecutorBean;
import com.ybm100.app.crm.ui.activity.drugstore.FilterPrivateCustomerActivity;
import com.ybm100.app.crm.ui.activity.lbs.LocationManager;
import com.ybm100.app.crm.utils.LzRoleInfoManager;
import com.ybm100.app.crm.utils.SnowGroundUtils;
import com.ybm100.app.crm.widget.drug.ContactDialog;
import com.ybm100.app.crm.widget.popwindow.CustomerSelectPopup;
import com.ybm100.app.crm.widget.popwindow.GoodsRecommendPopup;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import io.reactivex.Observable;
import io.reactivex.functions.Consumer;
import razerdp.basepopup.BasePopupWindow;

/**
 * 荷叶健康私海客户列表fragment
 */
public class HyPrivateListFragment extends BaseMVPCompatFragment<HyPrivateListPresenter> implements HyPrivateListContract.IHyPrivateListView, OnRefreshLoadMoreListener {
    @BindView(R.id.rv_customer_list)
    RecyclerView rvCustomerList;
    @BindView(R.id.refresh_layout_customer)
    SmartRefreshLayout refreshLayoutCustomer;
    @BindView(R.id.svl_contact_view)
    StatusViewLayout mStatusView;
    @BindView(R.id.appbar_layout)
    AppBarLayout appBarLayout;
    @BindView(R.id.tv_sort)
    TextView tvSort;
    @BindView(R.id.tv_customer_type)
    TextView tvCustomerType;
    @BindView(R.id.tv_customer_level)
    TextView tvCustomerLevel;
    @BindView(R.id.tv_customer_aptitude)
    TextView tvCustomerAptitude;
    private ArrayList<PopListBean> mSortList = new ArrayList();
    private CustomerSelectPopup mSortPopup = null;
    private ArrayList<PopListBean> mTypeList = new ArrayList();
    private GoodsRecommendPopup mTypePopup = null;
    private ArrayList<PopListBean> mLevelList = new ArrayList();
    private GoodsRecommendPopup mLevelPopup = null;
    private ArrayList<PopListBean> mStatusList = new ArrayList<>();
    private CustomerSelectPopup mStatusPopup = null;
    private HyPrivateListAdapter drugstoreMineListAdapter;
    private final HashMap<String, String> map = new HashMap<>();
    private FilterMapBean mapBean;
    private String name;
    private String areaName;
    private boolean isGroup;
    private boolean searchType;
    private String id;
    private String searchKey;
    private boolean isHyInfoSearch;
    private RxPermissions rxPermissions;
    private String locationLatParams;
    private String locationLngParams;
    private LocationManager.LocationListener locationListener;
    private static final int REQUEST_EXECUTOR = 1;
    private String distributeId = "";
    private int queryType;
    private int sortPopStatus = -1;
    private int typePopStatus = -1;
    private int levelPopStatus = -1;
    private int aptitudePopStatus = -1;

    /**
     * 当公海列表认领后需要刷新私海列表，百度地图在Fragment在后台时无法获取定位回调，导致了一系列数据不刷新的问题
     * 这里设置了个flag标志位，当收到认领事件后设置为true，当触发onHiddenChanged时间后通过判断该标识位刷新数据
     */
    private boolean refreshFlag = false;

    public static HyPrivateListFragment newInstance(boolean searchType) {
        Bundle args = new Bundle();
        HyPrivateListFragment fragment = new HyPrivateListFragment();
        args.putBoolean("searchType", searchType);
        fragment.setArguments(args);
        return fragment;
    }

    public static HyPrivateListFragment newInstance(boolean searchType, int queryType) {
        Bundle args = new Bundle();
        HyPrivateListFragment fragment = new HyPrivateListFragment();
        args.putBoolean("searchType", searchType);
        args.putInt("queryType", queryType);
        fragment.setArguments(args);
        return fragment;
    }


    @NonNull
    @Override
    public BasePresenter initPresenter() {
        return HyPrivateListPresenter.newInstance();
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_custom_private_hy;
    }

    @Override
    public void onLazyInitView(@Nullable Bundle savedInstanceState) {
        super.onLazyInitView(savedInstanceState);
        setRefreshLayout(refreshLayoutCustomer);
        initPopData();
        initLocationListener();
        initLactionAndData();
    }

    private void initLactionAndData() {
        //当前Fragment不在栈顶时不展示loading
        if (mActivity == null || mActivity.isDestroyed() || mActivity.isFinishing()) return;
        showWaitDialog("数据加载中");
        LocationManager.getInstance().locationPermissions(mActivity, locationListener, true, () -> {
            hideWaitDialog();
            if (!isAdded() || isDetached() || mActivity.isDestroyed()) {
                return;
            }
            initDatas();
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        distributeId = null;
    }


    //定位监听
    public void initLocationListener() {
        locationListener = bd -> {
            if (!TextUtils.isEmpty(bd.getAddrStr())) {
                locationLatParams = bd.getLatitude() + "";
                locationLngParams = bd.getLongitude() + "";
                map.put("poiLatitude", locationLatParams);
                map.put("poiLongitude", locationLngParams);
            } else {
                map.remove("poiLatitude");
                map.remove("poiLongitude");
                locationLatParams = "";
                locationLngParams = "";
            }
            if (mSortList != null) {
                for (PopListBean bean : mSortList) {
                    if (bean.isSelected()) {
                        map.put("sortType", String.valueOf(bean.getCode()));
                        if (tvSort != null) {
                            tvSort.setText(bean.getContent());
                        }
                        break;
                    }
                }
                initDatas();
            }
        };
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        RxBus.get().unRegister(this);
        SnowGroundUtils.flush();
        dismissAllPopupWindow();
        LocationManager.getInstance().unRegisterLocationListener(locationListener);
    }


    @Override
    public void onPause() {
        super.onPause();
        LocationManager.getInstance().unRegisterLocationListener(locationListener);
    }

    /**
     * 退出时销毁popupwindow，防止内存泄漏
     */
    private void dismissAllPopupWindow() {
        if (mStatusPopup != null) {
            mStatusPopup.dismissWithOutAnimate();
        }
        if (mLevelPopup != null) {
            mLevelPopup.dismissWithOutAnimate();
        }
        if (mSortPopup != null) {
            mSortPopup.dismissWithOutAnimate();
        }
        if (mTypePopup != null) {
            mTypePopup.dismissWithOutAnimate();
        }
    }

    private void checkAndRefresh() {
        if (refreshFlag) {
            initLactionAndData();
            refreshFlag = false;
        }
    }


    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        checkAndRefresh();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        checkAndRefresh();
    }

    /**
     * 请求数据
     */
    private void initDatas() {
        if (refreshLayoutCustomer == null) {
            return;
        }
        try {
            if (!TextUtils.isEmpty(searchKey)) {
                map.put(isHyInfoSearch ? "hyId" : "keyWord", searchKey);
            } else {
                map.remove(isHyInfoSearch ? "hyId" : "keyWord");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        refreshLayoutCustomer.setNoMoreData(false);
        mPresenter.getListData(true, map);
    }

    public void refresh() {
        initLactionAndData();
    }

    public void search(String key, boolean isHyInfoSearch) {
        map.clear();
        searchKey = key;
        this.isHyInfoSearch = isHyInfoSearch;
        initLactionAndData();
    }

    public void clearSearch() {
        searchKey = "";
        isHyInfoSearch = false;
    }

    @Override
    public void initUI(View view, @Nullable Bundle savedInstanceState) {
        RxBus.get().register(this);
        searchType = getArguments().getBoolean("searchType");
        queryType = getArguments().getInt("queryType", 0);
        initDisplayOpinion();
        //初始化列表
        drugstoreMineListAdapter = new HyPrivateListAdapter(R.layout.item_customer_private_hy);
        rvCustomerList.setLayoutManager(new LinearLayoutManager(mActivity));
        DefaultItemDecoration itemDecoration = new DefaultItemDecoration(mActivity, DisplayUtils.dp2px(10));
        itemDecoration.setLineColorResId(R.color.color_gray_EFEFF4);
        itemDecoration.setHasHeaderAndFooter(true);
        rvCustomerList.addItemDecoration(itemDecoration);
        rvCustomerList.setAdapter(drugstoreMineListAdapter);
        //初始化上拉加载下拉刷新
        refreshLayoutCustomer.setEnableLoadMore(true);
        refreshLayoutCustomer.setEnableRefresh(true);
        refreshLayoutCustomer.setOnRefreshLoadMoreListener(this);
        //设置刷新点击事件
        mStatusView.setOnRetryListener(v -> {
            initLactionAndData();
        });
        initOnclick();
    }

    /**
     * 初始化综合排序和客户状态
     */
    private void initPopData() {
        String[] sort = ResourcesUtils.getStringArray(R.array.hy_private_sort);
        String[] sign = ResourcesUtils.getStringArray(R.array.hy_private_sign);
        mSortList.clear();
        for (int i = 0; i < sort.length; i++) {
            PopListBean popListBean = new PopListBean();
            popListBean.setContent(sort[i]);
            popListBean.setCode(i + 1);
            popListBean.setSelected(i == 0);
            mSortList.add(popListBean);
        }
        mStatusList.clear();
        for (int i = 0; i < sign.length; i++) {
            PopListBean popListBean = new PopListBean();
            popListBean.setContent(sign[i]);
            popListBean.setSelected(i == 0);
            popListBean.setCode(i);
            mStatusList.add(popListBean);
        }
        mPresenter.getFilterItems();
    }

    /**
     * 初始化综合排序
     */
    private void initSortPop() {
        int height = ((mSortList.size() + 1) * DisplayUtil.dip2px(mContext, 44f) > DisplayUtil.screenhightPx * 0.5) ?
                (int) (DisplayUtil.screenhightPx * 0.5) : (mSortList.size() + 1) * DisplayUtil.dip2px(mContext, 44f);
        mSortPopup = new CustomerSelectPopup(mContext, mSortList, height);
        mSortPopup.setOnPopItemClickListener((selectList, selectText) -> {
            ImageUtils.setRightDrawable(mContext, R.drawable.ic_green_down, tvSort);
            mSortPopup.setPopList(selectList);
            for (PopListBean bean : mSortList) {
                if (bean.isSelected()) {
                    map.put("sortType", String.valueOf(bean.getCode()));
                    tvSort.setText(bean.getContent());
                    break;
                }
            }
            mSortList = selectList;
            sortPopStatus = 1;
            if (mContext != null) {
                showWaitDialog(mContext.getResources().getString(R.string.loading));
            }
            initDatas();
        });
        mSortPopup.setOnDismissListener(new BasePopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                dismissPop();
            }
        });
        if (map.get("poiLatitude") != null) {
            for (PopListBean bean : mSortList) {
                if (bean.isSelected()) {
                    map.put("sortType", String.valueOf(bean.getCode()));
                    tvSort.setText(bean.getContent());
                    break;
                }
            }
            if (mContext != null) {
                showWaitDialog(mContext.getResources().getString(R.string.loading));
            }
            initDatas();
        }
    }

    /**
     * 初始化客户类型
     */
    private void initTypePop() {
        int height = ((mTypeList.size() + 1) * DisplayUtil.dip2px(mContext, 44f) > DisplayUtil.screenhightPx * 0.5) ?
                (int) (DisplayUtil.screenhightPx * 0.5) : (mTypeList.size() + 1) * DisplayUtil.dip2px(mContext, 44f);
        mTypePopup = new GoodsRecommendPopup(mContext, mTypeList, Constants.GoodsRecommendCheck.FLOW_TAG_CHECKED_MULTI, true, height);
        mTypePopup.setOnPopItemClickListener((selectList, selectAll, selectText) -> {
            if (TextUtils.isEmpty(selectText)) {
                ImageUtils.setRightDrawable(mContext, R.drawable.ic_gray_down, tvCustomerType);
                tvCustomerType.setTextColor(ContextCompat.getColor(mContext, R.color.text_color_666666));
                mTypeList = GoodsRecommendPopup.reset(-1, mTypeList);
                mTypePopup.setPopList(mTypeList);
                map.remove("customerTypeList");
                typePopStatus = -1;
            } else {
                ImageUtils.setRightDrawable(mContext, R.drawable.ic_green_down, tvCustomerType);
                mTypePopup.setPopList(selectList);
                map.put("customerTypeList", selectText);
                mTypeList = selectList;
                typePopStatus = 1;
            }
            if (mContext != null) {
                showWaitDialog(mContext.getResources().getString(R.string.loading));
            }
            initDatas();
        });
        mTypePopup.setOnDismissListener(new BasePopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                dismissPop();
            }
        });
    }

    /**
     * 初始化客户级别
     */
    private void initLevelPop() {
        int height = ((mLevelList.size() + 1) * DisplayUtil.dip2px(mContext, 44f) > DisplayUtil.screenhightPx * 0.5) ?
                (int) (DisplayUtil.screenhightPx * 0.5) : (mLevelList.size() + 1) * DisplayUtil.dip2px(mContext, 44f);
        mLevelPopup = new GoodsRecommendPopup(mContext, mLevelList, Constants.GoodsRecommendCheck.FLOW_TAG_CHECKED_MULTI, true, height);
        mLevelPopup.setOnPopItemClickListener((selectList, selectAll, selectText) -> {
            if (TextUtils.isEmpty(selectText)) {
                ImageUtils.setRightDrawable(mContext, R.drawable.ic_gray_down, tvCustomerLevel);
                tvCustomerLevel.setTextColor(ContextCompat.getColor(mContext, R.color.text_color_666666));
                mLevelList = GoodsRecommendPopup.reset(-1, mLevelList);
                mLevelPopup.setPopList(mLevelList);
                map.remove("customerLevelCode");
                levelPopStatus = -1;
            } else {
                ImageUtils.setRightDrawable(mContext, R.drawable.ic_green_down, tvCustomerLevel);
                mLevelPopup.setPopList(selectList);
                map.put("customerLevelCode", selectText);
                mLevelList = selectList;
                levelPopStatus = 1;
            }
            if (mContext != null) {
                showWaitDialog(mContext.getResources().getString(R.string.loading));
            }
            initDatas();
        });
        mLevelPopup.setOnDismissListener(new BasePopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                dismissPop();
            }
        });
    }

    /**
     * 签约状态
     */
    private void initStatusPop() {
        int height = ((mStatusList.size() + 1) * DisplayUtil.dip2px(mContext, 44f) > DisplayUtil.screenhightPx * 0.5) ?
                (int) (DisplayUtil.screenhightPx * 0.5) : (mStatusList.size() + 1) * DisplayUtil.dip2px(mContext, 44f);
        mStatusPopup = new CustomerSelectPopup(mContext, mStatusList, height);
        mStatusPopup.setOnPopItemClickListener((selectList, selectText) -> {
            if (selectText == 0) {
                ImageUtils.setRightDrawable(mContext, R.drawable.ic_gray_down, tvCustomerAptitude);
                tvCustomerAptitude.setTextColor(ContextCompat.getColor(mContext, R.color.text_color_666666));
                mStatusList = GoodsRecommendPopup.reset(-1, mStatusList);
                mStatusPopup.setPopList(mStatusList);
                map.remove("customerStatus");
                aptitudePopStatus = -1;
            } else {
                ImageUtils.setRightDrawable(mContext, R.drawable.ic_green_down, tvCustomerAptitude);
                mStatusPopup.setPopList(selectList);
                map.put("customerStatus", String.valueOf(selectText));
                mStatusList = selectList;
                aptitudePopStatus = 1;
            }
            if (mContext != null) {
                showWaitDialog(mContext.getResources().getString(R.string.loading));
            }
            initDatas();
        });
        mStatusPopup.setOnDismissListener(new BasePopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                dismissPop();
            }
        });
    }

    private void dismissPop() {
        if (aptitudePopStatus == -1) {
            ImageUtils.setRightDrawable(mContext, R.drawable.ic_gray_down, tvCustomerAptitude);
            tvCustomerAptitude.setTextColor(ContextCompat.getColor(mContext, R.color.text_color_666666));
        } else {
            ImageUtils.setRightDrawable(mContext, R.drawable.ic_green_down, tvCustomerAptitude);
        }
        if (sortPopStatus == -1) {
            ImageUtils.setRightDrawable(mContext, R.drawable.ic_green_down, tvSort);
        } else {
            ImageUtils.setRightDrawable(mContext, R.drawable.ic_green_down, tvSort);
        }
        if (levelPopStatus == -1) {
            ImageUtils.setRightDrawable(mContext, R.drawable.ic_gray_down, tvCustomerLevel);
            tvCustomerLevel.setTextColor(ContextCompat.getColor(mContext, R.color.text_color_666666));
        } else {
            ImageUtils.setRightDrawable(mContext, R.drawable.ic_green_down, tvCustomerLevel);
        }
        if (typePopStatus == -1) {
            ImageUtils.setRightDrawable(mContext, R.drawable.ic_gray_down, tvCustomerType);
            tvCustomerType.setTextColor(ContextCompat.getColor(mContext, R.color.text_color_666666));
        } else {
            ImageUtils.setRightDrawable(mContext, R.drawable.ic_green_down, tvCustomerType);
        }
    }

    private void initOnclick() {
        tvSort.setOnClickListener(v -> {
            if (mSortPopup != null) {
                mSortPopup.showPopupWindow(R.id.tv_sort);
            }
            ImageUtils.setRightDrawable(mContext, R.drawable.ic_green_up, tvSort);
            tvSort.setTextColor(ContextCompat.getColor(mContext, R.color.text_color_35C561));

            SnowGroundUtils.track("Event-HYPrivateSea-Sort");
        });
        tvCustomerType.setOnClickListener(v -> {
            if (mTypePopup != null) {
                mTypePopup.showPopupWindow(R.id.tv_customer_type);
            }
            ImageUtils.setRightDrawable(mContext, R.drawable.ic_green_up, tvCustomerType);
            tvCustomerType.setTextColor(ContextCompat.getColor(mContext, R.color.text_color_35C561));

            SnowGroundUtils.track("Event-HYPrivateSea-CustomerType");
        });
        tvCustomerLevel.setOnClickListener(v -> {
            if (mLevelPopup != null) {
                mLevelPopup.showPopupWindow(R.id.tv_customer_level);
            }
            ImageUtils.setRightDrawable(mContext, R.drawable.ic_green_up, tvCustomerLevel);
            tvCustomerLevel.setTextColor(ContextCompat.getColor(mContext, R.color.text_color_35C561));

            SnowGroundUtils.track("Event-HYPrivateSea-CustomerLevel");
        });
        tvCustomerAptitude.setOnClickListener(v -> {
            if (mStatusPopup != null) {
                mStatusPopup.showPopupWindow(R.id.tv_customer_aptitude);
            }
            ImageUtils.setRightDrawable(mContext, R.drawable.ic_green_up, tvCustomerAptitude);
            tvCustomerAptitude.setTextColor(ContextCompat.getColor(mContext, R.color.text_color_35C561));

            SnowGroundUtils.track("Event-HYPrivateSea-CustomerSign");
        });
    }

    @Override
    public void getListDataSuccess(boolean refresh, RequestBaseBean<HyPrivateListBean> baseBean) {
        hideWaitDialog();
        stopLocation();
        if (baseBean == null || baseBean.getData() == null) return;
        List<HyPrivateListBean.PageDataBean.RowsBean> rowBeans = baseBean.getData().getPageData().getRows();
        if (rowBeans == null || rowBeans.size() == 0) {
            if (refresh) {
                mStatusView.showEmpty();
            }
            return;
        }
        if (refresh && drugstoreMineListAdapter != null) {
            drugstoreMineListAdapter.getData().clear();
        }
        if (refresh || drugstoreMineListAdapter == null) {
            rvCustomerList.setVisibility(View.VISIBLE);
            boolean hasLocation;
            hasLocation = !TextUtils.isEmpty(locationLngParams) && !TextUtils.isEmpty(locationLngParams);
            drugstoreMineListAdapter = new HyPrivateListAdapter(R.layout.item_customer_private_hy, rowBeans, hasLocation);
            rvCustomerList.setAdapter(drugstoreMineListAdapter);
        } else {
            drugstoreMineListAdapter.addData(rowBeans);
        }
        drugstoreMineListAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
            @Override
            public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                HyPrivateListBean.PageDataBean.RowsBean rowBean = (HyPrivateListBean.PageDataBean.RowsBean) adapter.getData().get(position);
                if (rowBean == null) return;
                switch (view.getId()) {
                    //添加拜访
                    case R.id.tv_operation_add_schedule:
                        mPresenter.toAddVisit(rowBean.getId(), "1");
                        break;
                    //分配
                    case R.id.tv_operation_assigned:
                        distributeId = rowBean.getId();
                        Observable.just(1).throttleFirst(2, TimeUnit.SECONDS)
                                .subscribe(new Consumer<Integer>() {
                                    @Override
                                    public void accept(Integer integer) throws Exception {
                                        SingleSelectExecutorActivity.jumpTo(mActivity, REQUEST_EXECUTOR, SingleSelectExecutorActivity.TYPE_DISTRIBUTION, true);
                                    }
                                });
                        break;
                    //电话
                    case R.id.tv_operation_phone:
//                        if (TextUtils.isEmpty(rowBean.getMobile())) {
//                            ToastUtils.showLong("暂无电话");
//                            return;
//                        }
                        callPhone(rowBean);
                        break;
                    default:
                        break;
                }
            }
        });
        //跳转详情
        drugstoreMineListAdapter.setOnItemClickListener((adapter, view, position) -> {
            HyPrivateListBean.PageDataBean.RowsBean rowBean = (HyPrivateListBean.PageDataBean.RowsBean) adapter.getData().get(position);
            if (rowBean == null || mContext == null) {
                return;
            }
            distributeId = rowBean.getId();
            Intent intentDrug = new Intent();
            intentDrug.putExtra(DrugstoreConstants.INTENT_KEY_MERCHANTID, rowBean.getId());
            intentDrug.putExtra(DrugstoreConstants.INTENT_KEY_HY_ID, rowBean.getHyId());
            intentDrug.putExtra(DrugstoreConstants.INTENT_KEY_DRUGSTORE_DISTRIBUTABLE, rowBean.getDistributable());
            intentDrug.putExtra(DrugstoreConstants.INTENT_KEY_DRUGSTORE_REGISTER, rowBean.getRegisterFlag());
            intentDrug.setClass(mContext, HyPrivateDetailActivity.class);
            startActivityForResult(intentDrug, DrugstoreConstants.REQUEST_DETAIL_INFO);
            zhuGe(rowBean);
        });
        mStatusView.showContent();
        //搜索入口进入不展示头部数据
        if (!searchType) {
            appBarLayout.setVisibility(View.VISIBLE);
        }
    }

    private void initDisplayOpinion() {
        DisplayMetrics dm = mContext.getResources().getDisplayMetrics();
        DisplayUtil.density = dm.density;
        DisplayUtil.densityDPI = dm.densityDpi;
        DisplayUtil.screenWidthPx = dm.widthPixels;
        DisplayUtil.screenhightPx = dm.heightPixels;
        DisplayUtil.screenWidthDip = DisplayUtil.px2dip(mContext, dm.widthPixels);
        DisplayUtil.screenHightDip = DisplayUtil.px2dip(mContext, dm.heightPixels);
    }

    @Override
    public void toAddVisit(RequestBaseBean<TaskAndMerchantBean> requestBaseBean) {
        String json = Uri.encode(GsonUtils.toJson(requestBaseBean.getData()));
        //BDM跳转陪访，bd和跟进人跳转拜访
        String encodeJson = Uri.encode(LzRoleInfoManager.INSTANCE.getRoleBeansJson());
        String url;
        if (RoleTypeConfig.isBDMOrGJRBDM()) {
            url = "/add_accompany_visit_page?isHeyeVisit=1&rolesJSON=" + encodeJson + "&externalJson=" + json;
        } else {
            url = "/add_visit_page?isHeyeVisit=1&rolesJSON=" + encodeJson + "&externalJson=" + json;
        }
        ContainerRuntime.INSTANCE.getRouter().open(getContext(), url, null);
    }

    @Override
    public void distributeToBDSuccess(RequestBaseBean baseBean) {
        if (baseBean.isSuccess()) {
            ToastUtils.showLongSafe("分配成功");
            if (mContext != null) {
                showWaitDialog(mContext.getResources().getString(R.string.loading));
            }
            distributeId = null;
            initDatas();
        }
    }

    @Override
    public void getFilterItemsSuccess(RequestBaseBean<PrivateListFilterBean> baseBean) {
        mLevelList = baseBean.getData().getLevelType();
        mTypeList = baseBean.getData().getCustomerType();
        initSortPop();
        //initStatusPop();//接口不支持客户状态筛选 暂时注释
        initLevelPop();
        initTypePop();
    }

    //自定义筛选
    public void filter(Activity activity) {
        try {
            Intent filterIntent = new Intent();
            filterIntent.putExtra("map", mapBean);
            filterIntent.putExtra("name", name);
            filterIntent.putExtra("id", id);
            filterIntent.putExtra("isGroup", isGroup);
            filterIntent.putExtra("areaName", areaName);
            filterIntent.setClass(activity, FilterPrivateCustomerActivity.class);
            startActivityForResult(filterIntent, DrugstoreConstants.REQUEST_FILTER);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void zhuGe(HyPrivateListBean.PageDataBean.RowsBean rowBean) {
        HashMap<String, String> map = new HashMap<>();
        String snowGroundName = "";
        /**
         * 已注册 merchantId
         * 未注册 id
         * registerFlag 1是，2否
         */
        if (rowBean.getRegisterFlag() == 1) {
            map.put("merchantName", rowBean.getCustomerName());
            map.put("merchantId", rowBean.getId());
            snowGroundName = "选择药店";
        } else if (rowBean.getRegisterFlag() == 2) {
            map.put("PoiId", rowBean.getId());
            snowGroundName = "Event-HYPrivateSea-NotRegisteredCustomer";
        }
        SnowGroundUtils.track(snowGroundName, map);
    }

    @Override
    public void enableLoadMore(boolean b) {
        refreshLayoutCustomer.setEnableLoadMore(b);
    }

    @Override
    public void loadMoreComplete() {
        refreshLayoutCustomer.finishLoadMoreWithNoMoreData();
    }


    @Override
    public void showEmpty() {
        mStatusView.showEmpty();
        hideWaitDialog();
        stopLocation();
    }

    private void stopLocation() {
        LocationManager.getInstance().unRegisterLocationListener(locationListener);
        LocationManager.getInstance().stopLocation();
    }


    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        //筛选
        if (requestCode == DrugstoreConstants.REQUEST_FILTER && resultCode == RESULT_OK) {
            if (data != null) {
                mapBean = (FilterMapBean) data.getSerializableExtra("map");
                name = data.getStringExtra("name");
                areaName = data.getStringExtra("areaName");
                id = data.getStringExtra("id");
                isGroup = data.getBooleanExtra("isGroup", false);
                if (mapBean.getMap() != null) {
                    String licenseCode = "";
                    if (map.get("customerStatus") != null) {
                        licenseCode = map.get("customerStatus");
                    }
                    map.clear();
                    if (!TextUtils.isEmpty(licenseCode)) {
                        map.put("customerStatus", licenseCode);
                    }
                    map.put("poiLatitude", locationLatParams);
                    map.put("poiLongitude", locationLngParams);
                    if (mSortList != null) {
                        for (PopListBean bean : mSortList) {
                            if (bean.isSelected()) {
                                map.put("sortType", String.valueOf(bean.getCode()));
                                break;
                            }
                        }
                    }
                    map.putAll(mapBean.getMap());
                } else {
                    map.clear();
                }
            }
            if (mContext != null) {
                showWaitDialog(mContext.getResources().getString(R.string.loading));
            }
            initDatas();
            //详情
        } else if (requestCode == DrugstoreConstants.REQUEST_DETAIL_INFO && resultCode == RESULT_OK) {
            if (mContext != null) {
                showWaitDialog(mContext.getResources().getString(R.string.loading));
            }
            initDatas();
        }
    }


    @Override
    public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
        mPresenter.getListData(false, map);
    }

    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
        initLactionAndData();
    }

    @Override
    public void showNetError() {
        mStatusView.showNetWorkException();
        hideWaitDialog();
        stopLocation();
    }

    /**
     * 更新药店列表
     */
    @Subscribe(code = RxBusCode.RX_BUS_UPDATE_PRIVATE_LIST)
    public void refreshList() {
//        initLactionAndData();
        refreshFlag = true;
    }

    /**
     * 更新药店列表
     */
    @Subscribe(code = RxBusCode.RX_BUS_HY_DISTRIBUTE)
    public void distribute(Bundle bundle) {
        ArrayList<TaskExecutorBean> list = bundle.getParcelableArrayList(ExtraConstants.DATA);
        if (list != null && !TextUtils.isEmpty(distributeId)) {
            mPresenter.distributeToBD(list.get(0).getId(), distributeId);
            distributeId = null;
        }

    }

    /**
     * 分配
     */
    @Subscribe(code = RxBusCode.RX_BUS_HY_UPDATE_EXECUTOR_SINGLE)
    public void rxBusEvent(Bundle bundle) {
        String bindUserId = bundle.getString(ExtraConstants.ID);
        if (!TextUtils.isEmpty(distributeId)) {
            mPresenter.distributeToBD(bindUserId, distributeId);
            distributeId = null;
        }
    }

    @SuppressLint("CheckResult")
    public void callPhone(final HyPrivateListBean.PageDataBean.RowsBean rowsBean) {
        if (getActivity() == null) {
            return;
        }
        //未注册
//        if (rowsBean.getRegisterFlag() == 2) {
//            if (TextUtils.isEmpty(rowsBean.getMobile())) {
//                ToastUtils.showLong("暂无电话");
//                return;
//            }
//            CallUtil.call(getActivity(),
//                    rowsBean.getMobile(),
//                    rowsBean.getId(),
//                    rowsBean.getCustomerName(),
//                    "private_list");
//            return;
//        }
        if (rowsBean.getContactList() == null || rowsBean.getContactList().size() == 0) {
            ToastUtils.showShort("联系人为空，请添加联系人");
        } else {
            new ContactDialog(getActivity(),
                    rowsBean.getContactList(),
                    rowsBean.getId(),
                    rowsBean.getCustomerName()).show();
        }
    }
}
