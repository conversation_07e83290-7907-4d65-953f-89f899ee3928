package com.ybm100.app.crm.utils.deviceInfo.collector

import android.content.Context
import com.ybm100.app.crm.utils.deviceInfo.util.CommandUtil
import java.io.File


class RootCollector : BaseCollector() {
    override fun internalCollect(context: Context): String? {
        return isRoot().toString()
    }

    /**
     * 检查root权限
     *
     * @return
     */
    private fun isRoot(): Boolean {
        val secureProp: Int = getroSecureProp()
        return if (secureProp == 0) //eng/userdebug版本，自带root权限
            true else isSUExist() //user版本，继续查su文件
    }

    private fun getroSecureProp(): Int {
        val secureProp: Int
        val roSecureObj = CommandUtil.getProperty("ro.secure")
        secureProp = if (roSecureObj == null) 1 else {
            if ("0" == roSecureObj) 0 else 1
        }
        return secureProp
    }

    private fun isSUExist(): Boolean {
        var file: File? = null
        val paths = arrayOf("/sbin/su",
                "/system/bin/su",
                "/system/xbin/su",
                "/data/local/xbin/su",
                "/data/local/bin/su",
                "/system/sd/xbin/su",
                "/system/bin/failsafe/su",
                "/data/local/su")
        for (path in paths) {
            file = File(path)
            if (file.exists()) return true
        }
        return false
    }


}
