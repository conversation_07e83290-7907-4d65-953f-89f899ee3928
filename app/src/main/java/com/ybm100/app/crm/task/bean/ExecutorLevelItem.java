package com.ybm100.app.crm.task.bean;

import java.util.List;

/**
 * Created by deng<PERSON><PERSON><PERSON> on 2019/1/3
 */
public class ExecutorLevelItem {
    private boolean isPerson;
    private String id;
    private String content;
    private boolean isChecked;
    private ExecutorLevelItem parent;
    private List<ExecutorLevelItem> child;
    private int checkedPersonCount;
    public ParentUser parentUser;
    private int totalPersonCount;
    private boolean isPost;

    public ExecutorLevelItem(String id, String content, boolean isPerson, ExecutorLevelItem parent) {
        this.isPerson = isPerson;
        this.id = id;
        this.content = content;
        this.parent = parent;
    }

    public int getCheckedPersonCount() {
        return checkedPersonCount;
    }

    public void setCheckedPersonCount(int checkedPersonCount) {
        this.checkedPersonCount = checkedPersonCount;
    }

    public int getTotalPersonCount() {
        return totalPersonCount;
    }

    public void setTotalPersonCount(int totalPersonCount) {
        this.totalPersonCount = totalPersonCount;
    }

    public boolean isPerson() {
        return isPerson;
    }

    public void setPerson(boolean person) {
        isPerson = person;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public boolean isChecked() {
        return isChecked;
    }

    public void setChecked(boolean checked) {
        isChecked = checked;
    }

    public ExecutorLevelItem getParent() {
        return parent;
    }

    public ParentUser getParentUser() {
        return parentUser;
    }

    public void setParentUser(ParentUser parentUser) {
        this.parentUser = parentUser;
    }

    public boolean isPost() {
        return isPost;
    }

    public void setPost(boolean isPost) {
        this.isPost = isPost;
    }

    public class ParentUser {
        String oaId;
        String realname;

        public String getOaId() {
            return oaId;
        }

        public void setOaId(String oaId) {
            this.oaId = oaId;
        }

        public String getRealname() {
            return realname;
        }

        public void setRealname(String realname) {
            this.realname = realname;
        }
    }

    public void setParent(ExecutorLevelItem parent) {
        this.parent = parent;
    }

    public List<ExecutorLevelItem> getChild() {
        return child;
    }

    public void setChild(List<ExecutorLevelItem> child) {
        this.child = child;
    }
}
