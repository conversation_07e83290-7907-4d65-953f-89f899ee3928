package com.ybm100.app.crm.home.sales.presenter

import android.annotation.SuppressLint
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.helper.RxHelper
import com.ybm100.app.crm.home.api.HomeApiService
import com.ybm100.app.crm.home.sales.bean.CustomSaleData
import com.ybm100.app.crm.home.sales.ui.SalesDataConfigActivity
import com.ybm100.app.crm.net.RetrofitCreateHelper
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer

class SalesDataConfigPresenter : BasePresenter<Any, SalesDataConfigActivity>() {
    override fun getModel(): Any {
        return Any()
    }


    @SuppressLint("CheckResult")
    fun requestCustomIndex() {
        RetrofitCreateHelper.createApi(HomeApiService::class.java)
                .getCustomSaleIndex()
                .compose(RxHelper.rxSchedulerHelper())
                .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<CustomSaleData?>>(mIView, "正在加载中...") {
                    override fun onSuccess(bean: RequestBaseBean<CustomSaleData?>?) {
                        if (bean == null || bean.data == null) {
                            mIView?.requestCustomIndexFail(null)
                        } else {
                            mIView?.requestCustomIndexSuccess(bean.data)
                        }
                    }

                    override fun onFailure(errorCode: Int) {
                        mIView?.requestCustomIndexFail(null)
                    }
                }, object : SimpleErrorConsumer(mIView) {
                    override fun onError(throwable: Throwable?, msg: String?) {
                        super.onError(throwable, msg)
                        mIView?.requestCustomIndexFail(msg)
                    }
                })
    }

    @SuppressLint("CheckResult")
    fun updateCustomIndex(customIndex: String, id: String, oaId: String, surplusIndex: String) {
        RetrofitCreateHelper.createApi(HomeApiService::class.java)
                .updateCustomSaleIndex(customIndex, id, oaId, surplusIndex)
                .compose(RxHelper.rxSchedulerHelper())
                .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<String?>>(mIView, "正在加载中...") {
                    override fun onSuccess(bean: RequestBaseBean<String?>?) {
                        if (bean == null || !bean.isSuccess) {
                            mIView?.updateCustomIndexFail(null)
                        } else {
                            mIView?.updateCustomIndexSuccess()
                        }
                    }

                    override fun onFailure(errorCode: Int) {
                        mIView?.updateCustomIndexFail(null)
                    }
                }, object : SimpleErrorConsumer(mIView) {
                    override fun onError(throwable: Throwable?, msg: String?) {
                        super.onError(throwable, msg)
                        mIView?.updateCustomIndexFail(msg)
                    }
                })
    }


}