package com.ybm100.app.crm.task.presenter

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer
import com.ybm100.app.crm.task.bean.ConfirmRecommendationBean
import com.ybm100.app.crm.task.bean.SelectCustomersBean
import com.ybm100.app.crm.task.contract.SelectCustomersContract
import com.ybm100.app.crm.task.model.SelectCustomersModel

class SelectCustomersPresenter(private val branchCode: String? = "") : BasePresenter<SelectCustomersContract.ISelectCustomersModel, SelectCustomersContract.ISelectCustomersView>() {
    var mQueryMap: MutableMap<String, String> = mutableMapOf()
    private var mOffset: Int = 0

    init {
        initQueryMap()
    }

    private fun initQueryMap() {
        mOffset = 0
        mQueryMap["branchCode"] = "$branchCode"
        mQueryMap["offset"] = "$mOffset"
        mQueryMap["limit"] = "20"
    }

    override fun getModel(): SelectCustomersContract.ISelectCustomersModel {
        return SelectCustomersModel()
    }

    fun getCustomerList(isRefresh: Boolean) {
        if (isRefresh) {
            mOffset = 0
            mQueryMap["offset"] = "$mOffset"
        }
        mRxManager.register(mIModel.getCustomerList(mQueryMap)
                .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<SelectCustomersBean?>?>(mIView, "") {
                    override fun onSuccess(requestBaseBean: RequestBaseBean<SelectCustomersBean?>?) {
                        mQueryMap["offset"] = "${++mOffset}"
                        mIView.onGetCustomerListSuccess(requestBaseBean, isRefresh, requestBaseBean?.data?.lastPage
                                ?: false)
                    }

                }, SimpleErrorConsumer(mIView)))
    }

    fun confirmRecommendation(queryMap: Map<String, String>) {
        mRxManager.register(mIModel.confirmRecommendation(queryMap)
                .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<ConfirmRecommendationBean?>?>(mIView, "") {
                    override fun onSuccess(requestBaseBean: RequestBaseBean<ConfirmRecommendationBean?>?) {
                        mIView.onConfirmRecommendationSuccess(requestBaseBean)
                    }

                }, SimpleErrorConsumer(mIView)))
    }
}