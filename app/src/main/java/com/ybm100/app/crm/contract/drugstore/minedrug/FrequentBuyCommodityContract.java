package com.ybm100.app.crm.contract.drugstore.minedrug;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.CommonListBean;
import com.ybm100.app.crm.goodsmanagement.bean.EstimatedPriceListBean;

import java.util.HashMap;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:19
 */
public interface FrequentBuyCommodityContract {

    interface IFrequentBuyCommodityModel extends IBaseModel {
        Observable<RequestBaseBean<CommonListBean>> shopCommonProcureRequest(String shopId);
        Observable<RequestBaseBean<EstimatedPriceListBean>> getEstimatedPrices(HashMap<String, String> queryMap);
    }

    interface IFrequentBuyCommodityView extends IBaseActivity {
        void shopCommonProcureRequestSuccess(RequestBaseBean<CommonListBean> requestBaseBean);
        void onGetEstimatedPricesSuccess(RequestBaseBean<EstimatedPriceListBean> data, boolean isHighGrossProfit);
    }

}
