package com.ybm100.app.crm.ui.activity.lbs;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.baidu.mapapi.map.BaiduMap;
import com.baidu.mapapi.map.MapStatus;
import com.baidu.mapapi.map.MapStatusUpdate;
import com.baidu.mapapi.map.MapStatusUpdateFactory;
import com.baidu.mapapi.map.MapView;
import com.baidu.mapapi.model.LatLng;
import com.baidu.mapapi.search.core.PoiInfo;
import com.baidu.mapapi.search.core.SearchResult;
import com.baidu.mapapi.search.geocode.GeoCodeResult;
import com.baidu.mapapi.search.geocode.OnGetGeoCoderResultListener;
import com.baidu.mapapi.search.geocode.ReverseGeoCodeResult;
import com.xyy.common.navigationbar.AbsNavigationBar;
import com.xyy.common.navigationbar.DefaultNavigationBar;
import com.xyy.common.util.ToastUtils;
import com.xyy.utilslibrary.base.activity.BaseCompatActivity;
import com.xyy.utilslibrary.utils.DialogUtils;
import com.xyy.utilslibrary.utils.LogUtils;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.AddressBean;
import com.ybm100.app.crm.constant.DrugstoreConstants;

import butterknife.BindView;

/**
 * <AUTHOR>
 * @date 2019/1/3
 * 重新标记
 */
public class LBSReMarksActivity extends BaseCompatActivity {
    @BindView(R.id.map)
    MapView mMapView;
    @BindView(R.id.iv_location)
    ImageView ivLocation;
    @BindView(R.id.tv_address)
    TextView tvAddress;
    @BindView(R.id.layout_address)
    LinearLayout layoutAddress;

    private BaiduMap baiduMap;
    private LatLng point;//我的当前位置
    private PoiInfo lbsInfo;
    private String address;//详细地址
    private AddressBean addressBean;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_lbs;
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    protected void initTransferData() {
        super.initTransferData();
        addressBean = (AddressBean) getIntent().getSerializableExtra(DrugstoreConstants.INTENT_KEY_ADDRESS_INFO);
        if (addressBean == null) {
            ToastUtils.showShort("参数异常");
            finish();
            return;
        }
    }

    @Override
    protected AbsNavigationBar initHead() {
        return new DefaultNavigationBar.Builder(this).setTitle("修改地图").setRightText("确定").setRightTextColor(getResources().getColor(R.color.text_color_confirm_button)).setRightClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DialogUtils.showCommonDialog(LBSReMarksActivity.this, "确认修改？", "确定", "取消", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        result();
                    }
                });
            }
        }).builder();
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        layoutAddress.setVisibility(View.VISIBLE);
        ivLocation.setVisibility(View.VISIBLE);
        //获取给定的位置
        try {
            double lat = addressBean.getLat();
            double lng = addressBean.getLng();
            address = addressBean.getAddress();
            if (lat > 0 && lng > 0) {
                point = new LatLng(lat, lng);
                LogUtils.d("lat=" + lat + ",lng=" + lng);
                lbsInfo = new PoiInfo();
                lbsInfo.location = point;
                lbsInfo.address = address;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        String str = address + (TextUtils.isEmpty(addressBean.getDetailAddress()) || "null".equals(addressBean.getDetailAddress().toLowerCase()) ? "" : addressBean.getDetailAddress());
        tvAddress.setText(str);
        //初始化地图
        mMapView.showZoomControls(false);
        mMapView.showScaleControl(false);
        baiduMap = mMapView.getMap();
        //将对应坐标放置中心位置
        MapStatus mapStatus = new MapStatus.Builder().target(point).zoom(14.0f).build();
        MapStatusUpdate mMapStatusUpdate = MapStatusUpdateFactory.newMapStatus(mapStatus);
        baiduMap.setMapStatus(mMapStatusUpdate);

        baiduMap.setOnMapStatusChangeListener(new BaiduMap.OnMapStatusChangeListener() {
            @Override
            public void onMapStatusChangeStart(MapStatus mapStatus) {
                baiduMap.clear();
                ivLocation.setVisibility(View.VISIBLE);
            }

            @Override
            public void onMapStatusChangeStart(MapStatus mapStatus, int i) {

            }

            @Override
            public void onMapStatusChange(MapStatus mapStatus) {

            }

            @Override
            public void onMapStatusChangeFinish(MapStatus mapStatus) {
                point = new LatLng(mapStatus.target.latitude, mapStatus.target.longitude);
                reverseGeo();
                if (lbsInfo != null) {
                    lbsInfo.location = point;
                }
            }
        });

    }

    //重置药店位置
    private void reverseGeo() {
        LocationManager.getInstance().reverseGeo(point, new OnGetGeoCoderResultListener() {
            @Override
            public void onGetGeoCodeResult(GeoCodeResult geoCodeResult) {

            }

            @Override
            public void onGetReverseGeoCodeResult(ReverseGeoCodeResult result) {
                if (result != null && result.error == SearchResult.ERRORNO.NO_ERROR) {
                    if (tvAddress == null) {
                        return;
                    }
                    if (lbsInfo == null) {
                        lbsInfo = new PoiInfo();
                        lbsInfo.location = result.getLocation();
                    }
                    lbsInfo.address = result.getAddress();
                    lbsInfo.city = result.getAddressDetail().city;
                    lbsInfo.province = result.getAddressDetail().province;
                    lbsInfo.area = result.getAddressDetail().district;
                    tvAddress.setText(lbsInfo.address);

                }
            }
        });
    }

    @Override
    public void onBackPressedSupport() {
        result();
    }

    private void result() {
        Intent intent = new Intent();
        intent.putExtra(DrugstoreConstants.INTENT_KEY_POI_INFO, lbsInfo);
        setResult(RESULT_OK, intent);
        finish();
    }
}
