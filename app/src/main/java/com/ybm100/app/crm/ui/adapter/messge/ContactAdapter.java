package com.ybm100.app.crm.ui.adapter.messge;

import android.text.TextUtils;
import android.view.View;
import android.widget.SectionIndexer;

import com.chad.library.adapter.base.BaseMultiItemQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.entity.MultiItemEntity;
import com.xyy.utilslibrary.utils.ResourcesUtils;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.contact.ContactBean;
import com.ybm100.app.crm.bean.contact.ContactDependBean;
import com.ybm100.app.crm.bean.user.UserInfoBean;
import com.ybm100.app.crm.constant.RoleTypeConfig;
import com.ybm100.app.crm.utils.SharedPrefManager;
import com.ybm100.app.crm.widget.swipemenu.EasySwipeMenuLayout;

import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR>
 * @version 1.0
 * @file MsgAdapter.java
 * @brief
 * @date 2018/12/22
 * Copyright (c) 2018, 北京小药药
 * All rights reserved.
 */
public class ContactAdapter extends BaseMultiItemQuickAdapter<MultiItemEntity, BaseViewHolder> implements SectionIndexer {

    public static final int CONTACT_BASE = 0;
    public static final int CONTACT_DEPEND = 1;

    public ContactAdapter(List<MultiItemEntity> list) {
        super(list);
        addItemType(CONTACT_BASE, R.layout.item_contact_base);
        addItemType(CONTACT_DEPEND, R.layout.item_contact_depend);
    }

    @Override
    protected void convert(BaseViewHolder helper, MultiItemEntity entity) {
        if (entity.getItemType() == CONTACT_BASE) {
            int adapterPosition = helper.getAdapterPosition();
            ContactBean item = (ContactBean) entity;
            handleBaseData(helper, item, adapterPosition);
        } else {
            ContactDependBean item = (ContactDependBean) entity;
            handleDependData(helper, item);
        }
    }

    private void handleBaseData(BaseViewHolder helper, ContactBean item, final int adapterPosition) {
        final EasySwipeMenuLayout sw = helper.getView(R.id.esml_contact);

        //根据position获取分类的首字母的Char ascii值
        int section = getSectionForPosition(adapterPosition);
        //如果当前位置等于该分类首字母的Char的位置 ，则认为是第一次出现
        if (adapterPosition == getPositionForSection(section)) {
            helper.getView(R.id.tv_contact_first_letter).setVisibility(View.VISIBLE);
            helper.setText(R.id.tv_contact_first_letter, item.getSortLetters());
        } else {
            helper.getView(R.id.tv_contact_first_letter).setVisibility(View.GONE);
        }

        if (!TextUtils.isEmpty(item.getContactName())) {
            if (TextUtils.isEmpty(item.getContactJobName())) {
                helper.setText(R.id.tv_contact_name, item.getContactName());
            } else {
                helper.setText(R.id.tv_contact_name, item.getContactName() + "（" + item.getContactJobName() + "）");
            }
            String contactName = item.getContactName();
            if (contactName.length() > 1) {
                String substring = contactName.substring(contactName.length() - 2);
                helper.setText(R.id.tv_contact_base_icon, substring);
            } else {
                helper.setText(R.id.tv_contact_base_icon, contactName);
            }
        }
        if (!TextUtils.isEmpty(item.getMerchantName())) {
            helper.setText(R.id.tv_contact_address, item.getMerchantName());
        }

        if (!TextUtils.isEmpty(item.getLastCallTime())) {
            helper.setText(R.id.tv_contact_last_call_time, item.getLastCallTime());
        } else {
            helper.setText(R.id.tv_contact_last_call_time, "暂无通话记录");
        }

        helper.setImageResource(R.id.iv_expand_collapse, item.isExpanded() ? R.drawable.jiantou_up : R.drawable.jiantou_down);

        sw.setCanRightSwipe(!item.isExpanded() && canRightSwipe());

        helper.setTag(R.id.ll_contact_content, item);
        helper.getView(R.id.ll_contact_content).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Object tag = view.getTag();
                if (tag != null && tag instanceof ContactBean) {
                    int parentPosition = getParentPosition((MultiItemEntity) tag);
                    boolean expanded = ((ContactBean) tag).isExpanded();

                    if (expanded) {
                        collapse(parentPosition);
                    } else {
                        expand(parentPosition);
                    }
                    sw.setCanRightSwipe(expanded && canRightSwipe());
                }
            }
        });
        helper.setTag(R.id.tv_edit_contact, item);
        helper.getView(R.id.tv_edit_contact).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Object tag = view.getTag();
                if (tag != null && tag instanceof ContactBean) {
                    if (action != null) {
                        action.editContact((ContactBean) tag);
                    }
                }
            }
        });
        helper.setTag(R.id.tv_del_contact, item);
        helper.getView(R.id.tv_del_contact).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                //请求网络删除联系人
                Object tag = view.getTag();
                if (tag != null && tag instanceof ContactBean) {
                    int parentPosition = getParentPosition((MultiItemEntity) tag);
                    if (action != null) {
                        action.delContact((ContactBean) tag, parentPosition);
                    }
                }
            }
        });
    }

    private void handleDependData(BaseViewHolder helper, ContactDependBean item) {
        helper.setTag(R.id.iv_contact_phoneno, item);
        if (!TextUtils.isEmpty(item.getTimeDistance())) {
            helper.setText(R.id.tv_contact_total_time, ResourcesUtils.getString(R.string.total_call_timex, item.getTimeDistance()));
        } else {
            helper.setText(R.id.tv_contact_total_time, ResourcesUtils.getString(R.string.total_call_timex, "0秒"));
        }
        helper.getView(R.id.iv_contact_phoneno).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                ContactDependBean tag = (ContactDependBean) view.getTag();
                int parentPosition = getParentPosition(tag);
                MultiItemEntity entity = getData().get(parentPosition);
                if (action != null && entity != null && entity instanceof ContactBean) {
                    ContactBean contactBean = (ContactBean) entity;
                    action.call(contactBean.getContactMobile(), contactBean.poiId, contactBean.merchantName);
                }
            }
        });
        helper.setTag(R.id.iv_contact_detail, item);
        helper.getView(R.id.iv_contact_detail).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                ContactDependBean tag = (ContactDependBean) view.getTag();
                int parentPosition = getParentPosition(tag);
                MultiItemEntity entity = getData().get(parentPosition);
                if (action != null && entity != null && entity instanceof ContactBean) {
                    action.go2Detail((ContactBean) entity);
                }
            }
        });
    }

    private ContactMoreAction action;

    public void setContactMoreAction(ContactMoreAction action) {
        this.action = action;
    }

    @Override
    public Object[] getSections() {
        return null;
    }

    /**
     * 根据ListView的当前位置获取分类的首字母的Char ascii值
     */
    public int getSectionForPosition(int position) {
        ContactBean contactBean = (ContactBean) mData.get(position);
        String sortLetters = contactBean.sortLetters;
        char c = sortLetters.charAt(0);
        return c;
    }

    /**
     * 根据分类的首字母的Char ascii值获取其第一次出现该首字母的位置
     */
    public int getPositionForSection(int section) {
        for (int i = 0; i < mData.size(); i++) {
            MultiItemEntity entity = mData.get(i);
            if (!(entity instanceof ContactBean)) {
                continue;
            }
            String sortStr = ((ContactBean) mData.get(i)).getSortLetters();
            char firstChar = sortStr.toUpperCase(Locale.CHINESE).charAt(0);
            if (firstChar == section) {
                return i;
            }
        }

        return -1;
    }

    public interface ContactMoreAction {
        void call(String phoneNo, String merchantId, String merchantName);

        void delContact(ContactBean phoneNo, int parentPosition);

        void editContact(ContactBean bean);

        void go2Detail(ContactBean bean);
    }

    private boolean canRightSwipe() {
        UserInfoBean infoBean = SharedPrefManager.getInstance().getUserInfo();
        if (infoBean == null) return false;
        return infoBean.getRoleType() == RoleTypeConfig.TYPE_BD;
    }
}
