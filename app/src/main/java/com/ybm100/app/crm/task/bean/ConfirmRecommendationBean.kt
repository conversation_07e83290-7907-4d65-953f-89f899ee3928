package com.ybm100.app.crm.task.bean

data class ConfirmRecommendationBean(
    val appLogo: String? = "", // http://upload.test.ybm100.com/ybm/shop/logo/default/app/8f1945b8-d38d-43ee-b74a-f30f6b9268e2.png
    val appName: String? = "", // 药帮忙
    val content: String? = "", // 刚刚在药帮忙看到一个不错的店铺，分享给你
    val shareUrl: String? = "", // http://new-app.test.ybm100.com/static/xyyvue/dist/?from=singlemessage&amp;isappinstalled=0#/browserOpen?jumpurl=http://new-app.test.ybm100.com/static/xyyvue/dist/#/shop?shopCode=DP1007
    val toast: String? = "" // 已成功推荐至客户药帮忙App-我的-专属推荐~
)