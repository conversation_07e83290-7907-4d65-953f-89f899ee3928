package com.ybm100.app.crm.home.api

import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.home.bean.GoalsProgressBean
import com.ybm100.app.crm.home.bean.ModuleConfig
import com.ybm100.app.crm.home.bean.SalesStatisticsV2
import com.ybm100.app.crm.home.sales.bean.CustomSaleData
import com.ybm100.app.crm.home.sales.bean.SalesDataDict
import io.reactivex.Observable
import retrofit2.http.*
import java.util.*

interface HomeApiService {

    /**
     * 首页数据字典文案展示
     */
    @GET("index/getCrmSaleIndex")
    fun getCrmSaleIndex(): Observable<RequestBaseBean<List<SalesDataDict?>?>?>


    /**
     * 更新自定义数据字典
     */
    @FormUrlEncoded
    @POST("index/updateCustomSaleIndex")
    fun updateCustomSaleIndex(@Field("customIndex") customIndex: String,
                              @Field("id") id: String,
                              @Field("oaId") oaId: String,
                              @Field("surplusIndex") surplusIndex: String
    ): Observable<RequestBaseBean<String?>?>


    /**
     * 查询BD自定义数据字典
     */
    @GET("index/getCustomSaleIndex")
    fun getCustomSaleIndex(): Observable<RequestBaseBean<CustomSaleData?>?>



    /**
     * 首页指标统计-总览
     */
    @GET("index/indexAllStatistics")
    fun getIndexAllStatistics(@QueryMap map: HashMap<String, String?>?): Observable<RequestBaseBean<SalesStatisticsV2?>?>


    /**
     * 首页指标统计-自营数据
     */
    @GET("index/indexSelfStatistics")
    fun getIndexSelfStatistics(@QueryMap map: HashMap<String, String?>?): Observable<RequestBaseBean<SalesStatisticsV2?>?>

    /**
     * 首页指标统计-POP数据
     */
    @GET("index/indexPopStatistics")
    fun getIndexPopStatistics(@QueryMap map: HashMap<String, String?>?): Observable<RequestBaseBean<SalesStatisticsV2?>?>

    /**
     * 首页指标统计-控销数据
     */
    @GET("index/indexControlStatistics")
    fun getIndexControlStatistics(@QueryMap map: HashMap<String, String?>?): Observable<RequestBaseBean<SalesStatisticsV2?>?>

    /**
     * 首页指标统计-高毛数据
     */
    @GET("index/indexHighGrossStatistics")
    fun getIndexHighGrossStatistics(@QueryMap map: HashMap<String, String?>?): Observable<RequestBaseBean<SalesStatisticsV2?>?>

    /**
     * 首页模块卡片
     */
    @GET("index/getIndexModules")
    fun getModulesConfig(): Observable<RequestBaseBean<List<ModuleConfig?>?>?>

    /**
     * 首页 - 目标进度
     */
    @GET("index/getSchedule")
    fun getHomeGoalsProgress(@QueryMap map: MutableMap<String?, String?>?): Observable<RequestBaseBean<GoalsProgressBean?>?>
}