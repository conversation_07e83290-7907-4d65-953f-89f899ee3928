package com.ybm100.app.crm.presenter.schedule;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.schedule.ScheduleDetailBean;
import com.ybm100.app.crm.contract.schedule.ScheduleDetailContract;
import com.ybm100.app.crm.model.schedule.ScheduleDetailModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

/**
 * Created by XyyMvpSportTemplate on 12/22/2018 19:17
 */
public class ScheduleDetailPresenter extends BasePresenter<ScheduleDetailContract.IScheduleDetailModel, ScheduleDetailContract.IScheduleDetailView> {

    public static ScheduleDetailPresenter newInstance() {
        return new ScheduleDetailPresenter();
    }

    @Override
    protected ScheduleDetailModel getModel() {
        return ScheduleDetailModel.newInstance();
    }

    public void getScheduleDetail(String scheduleId) {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.getScheduleDetail(scheduleId).subscribe(new SimpleSuccessConsumer<RequestBaseBean<ScheduleDetailBean>>(mIView, "") {
            @Override
            public void onSuccess(RequestBaseBean<ScheduleDetailBean> baseBean) {
                if (mIView == null) return;
                mIView.getScheduleDetailSuccess(baseBean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }
}
