package com.ybm100.app.crm.goods.presenter

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.helper.RxHelper
import com.ybm100.app.crm.api.ApiService
import com.ybm100.app.crm.goods.api.GoodsApiService
import com.ybm100.app.crm.goods.bean.*
import com.ybm100.app.crm.goods.ui.GoodsDetailActivity
import com.ybm100.app.crm.goodsmanagement.bean.EstimatedPriceListBean
import com.ybm100.app.crm.net.RetrofitCreateHelper
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer

class GoodsDetailPresenter : BasePresenter<Any, GoodsDetailActivity>() {
    override fun getModel(): Any {
        return Any()
    }

    fun requestGoodsDetail(goodsId: Int, merchantId: Long?, branchCode: String?) {
        val params = HashMap<String, String>().also {
            it["skuId"] = goodsId.toString()
            if (!branchCode.isNullOrEmpty()) {
                it["branchCode"] = branchCode
            }
            if (merchantId != null) {
                it["merchantId"] = merchantId.toString()
            }
        }
        val result = RetrofitCreateHelper.createApi(GoodsApiService::class.java)
                .getGoodsDetail(params)
                .compose(RxHelper.rxSchedulerHelper())
                .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<GoodsDetail?>?>(mIView, "正在加载中...") {
                    override fun onSuccess(bean: RequestBaseBean<GoodsDetail?>?) {
                        if (bean == null || bean.data == null) {
                            mIView?.requestGoodDetailFail(null)
                        } else {
                            mIView?.requestGoodDetailSuccess(bean.data)
                        }
                    }

                    override fun onFailure(errorCode: Int) {
                        mIView?.requestGoodDetailFail(null)
                    }
                }, object : SimpleErrorConsumer(mIView) {
                    override fun onError(throwable: Throwable?, msg: String?) {
                        super.onError(throwable, msg)
                        mIView?.requestGoodDetailFail(msg)
                    }
                })
        mRxManager.register(result)
    }

    fun requestShareConfirm(goodsId: Int, taskId: String) {
        val params = HashMap<String, String>().also {
            it["skuIds"] = goodsId.toString()
            it["taskId"] = taskId
        }

        val result = RetrofitCreateHelper.createApi(GoodsApiService::class.java)
                .shareConfirm(params)
                .compose(RxHelper.rxSchedulerHelper())
                .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<ShareConfirm?>?>(mIView, "正在加载中...") {
                    override fun onSuccess(bean: RequestBaseBean<ShareConfirm?>?) {
                        if (bean == null || bean.data == null) {
                            mIView?.requestShareConfirmFail(null)
                        } else {
                            mIView?.requestShareConfirmSuccess(bean.data!!)
                        }
                    }

                    override fun onFailure(errorCode: Int) {
                        mIView?.requestShareConfirmFail(null)
                    }
                }, object : SimpleErrorConsumer(mIView) {
                    override fun onError(throwable: Throwable?, msg: String?) {
                        super.onError(throwable, msg)
                        if (msg == null) {
                            mIView?.requestShareConfirmFail(null)
                        } else {
                            mIView?.requestShareConfirmFail(msg)
                        }
                    }
                })
        mRxManager.register(result)
    }

    /**
     * 收藏、取消收藏
     */
    fun collectGood(skuId: String, branchCode: String, type: Int, productName: String) {
        val params = HashMap<String, String>().also {
            it["branchCode"] = branchCode
            it["skuId"] = skuId
            it["type"] = type.toString()
            it["productName"] = productName
        }
        val result = RetrofitCreateHelper.createApi(GoodsApiService::class.java)
                .skuCollection(params)
                .compose(RxHelper.rxSchedulerHelper())
                .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<Any?>?>(mIView, "正在加载中...") {
                    override fun onSuccess(bean: RequestBaseBean<Any?>?) {
                        mIView?.requestCollectGoodSuccess()
                    }

                    override fun onFailure(errorCode: Int) {
                    }
                }, object : SimpleErrorConsumer(mIView) {
                })
        mRxManager.register(result)
    }

    /**
     * 获取预估到手价
     */
    fun getDiscountPrice(merchantId: String, branchCode: String, skuId: String) {
        val params = HashMap<String, String>().also {
            it["branchCode"] = branchCode
            it["idList"] = skuId
            it["merchantId"] = merchantId
        }
        val result = RetrofitCreateHelper.createApi(ApiService::class.java)
                .getEstimatedPrices(params)
                .compose(RxHelper.rxSchedulerHelper())
                .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<EstimatedPriceListBean?>?>(mIView) {
                    override fun onSuccess(bean: RequestBaseBean<EstimatedPriceListBean?>?) {
                        var discountPrice: String? = null
                        if (!bean?.data?.rows.isNullOrEmpty()) {
                            discountPrice = bean?.data?.rows?.get(0)?.discountPrice
                        }
                        mIView?.requestDiscountPriceSuccess(discountPrice)
                    }

                    override fun onFailure(errorCode: Int) {
                    }
                }, object : SimpleErrorConsumer(mIView) {
                })
        mRxManager.register(result)
    }


    fun getRecentPurchaseRecords(merchantId: String, skuId: String) {
        val result = RetrofitCreateHelper.createApi(GoodsApiService::class.java)
                .getLastThreePurchaseRecords(skuId, merchantId)
                .compose(RxHelper.rxSchedulerHelper())
                .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<List<PurchaseRecord?>?>>(mIView, true) {
                    override fun onSuccess(bean: RequestBaseBean<List<PurchaseRecord?>?>?) {
                        if (bean?.data?.isNotEmpty() == true) {
                            mIView.requestRecentPurchaseRecordsSuccess(bean.data!!)
                        } else {
                            mIView.requestRecentPurchaseRecordsFailed()
                        }
                    }

                    override fun onFailure(errorCode: Int) {
                        mIView.requestRecentPurchaseRecordsFailed()
                    }
                }, object : SimpleErrorConsumer(mIView) {
                    override fun onError(throwable: Throwable?, msg: String?) {
                        super.onError(throwable, msg)
                        mIView.requestRecentPurchaseRecordsFailed()
                    }
                })
        mRxManager.register(result)
    }

    fun getGoodsFlow(isThisMonth: Boolean, skuId: String) {

        val result = RetrofitCreateHelper.createApi(GoodsApiService::class.java)
                .getGoodsFlowStatistics(skuId, if (isThisMonth) "3" else "8")
                .compose(RxHelper.rxSchedulerHelper())
                .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<GoodsFlowStatistics?>>(mIView, true) {
                    override fun onSuccess(bean: RequestBaseBean<GoodsFlowStatistics?>?) {
                        mIView.requestGoodsFlowStatisticsResult(isThisMonth, bean?.data)
                    }

                    override fun onFailure(errorCode: Int) {
                        mIView.requestGoodsFlowStatisticsResult(isThisMonth, null)
                    }
                }, object : SimpleErrorConsumer(mIView) {
                    override fun onError(throwable: Throwable?, msg: String?) {
                        super.onError(throwable, msg)
                        mIView.requestGoodsFlowStatisticsResult(isThisMonth, null)
                    }
                })
        mRxManager.register(result)
    }

    fun getAVGPrice(skuId: String, branchCode: String, merchantId: Long?) {
        val result = RetrofitCreateHelper.createApi(GoodsApiService::class.java)
                .getAVGPriceMonthly(skuId, branchCode, merchantId?.toString())
                .compose(RxHelper.rxSchedulerHelper())
                .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<AVGPriceMonth?>>(mIView, true) {
                    override fun onSuccess(bean: RequestBaseBean<AVGPriceMonth?>?) {
                        mIView.requestAVGPriceMonthSuccess(bean?.data)
                    }

                    override fun onFailure(errorCode: Int) {
                        mIView.requestAVGPriceMonthFailed()
                    }
                }, object : SimpleErrorConsumer(mIView) {
                    override fun onError(throwable: Throwable?, msg: String?) {
                        super.onError(throwable, msg)
                        mIView.requestAVGPriceMonthFailed()
                    }
                })
        mRxManager.register(result)
    }

}
