package com.ybm100.app.crm.ui.activity.drugstore;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.xyy.common.navigationbar.AbsNavigationBar;
import com.xyy.common.navigationbar.DefaultNavigationBar;
import com.xyy.common.widget.flowtag.FlowTagLayout;
import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.activity.BaseMVPCompatActivity;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.rxbus.RxBus;
import com.xyy.utilslibrary.rxbus.Subscribe;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.drugstore.AreaBean;
import com.ybm100.app.crm.bean.drugstore.FilterMapBean;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.FilterPrivateBean;
import com.ybm100.app.crm.bean.goods.PrivateOtherFilterBean;
import com.ybm100.app.crm.constant.DrugstoreConstants;
import com.ybm100.app.crm.constant.ExtraConstants;
import com.ybm100.app.crm.constant.RoleTypeConfig;
import com.ybm100.app.crm.contract.drugstore.FilterPrivateContract;
import com.ybm100.app.crm.order.activity.OrderExecutorActivity;
import com.ybm100.app.crm.presenter.drugstore.FilterPrivatePresenter;
import com.ybm100.app.crm.ui.adapter.drugstore.PrivateFilterAdapter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

import static com.ybm100.app.crm.constant.RxBusCode.RX_BUS_UPDATE_EXECUTOR;

/**
 * Created by XyyMvpSportTemplate on 12/22/2018 11:31
 * 私海客户筛选
 */
public class FilterPrivateCustomerActivity extends BaseMVPCompatActivity<FilterPrivatePresenter> implements FilterPrivateContract.IFilterPrivateView {
    @BindView(R.id.tv_range_value)
    TextView tvRangeValue;
    @BindView(R.id.tv_area_value)
    TextView tvAreaValue;
    @BindView(R.id.layout_range_value)
    LinearLayout layoutRangeValue;
    @BindView(R.id.line_range)
    View lineRange;
    @BindView(R.id.flow_tag_layout_status)
    FlowTagLayout flowTagLayoutStatus;
    @BindView(R.id.flow_tag_layout_filter)
    FlowTagLayout flowTagLayoutFilter;
    @BindView(R.id.flow_drugstore_type)
    FlowTagLayout flowDrugstoreType;

    @BindView(R.id.flow_tag_order_status)
    FlowTagLayout flowOrder;


    private String stats = "";//药店状态
    private String licenseStatus = "";//资质状态
    private String order = "";//下单情况
    private String storeType = "";//客户类型
    private FilterMapBean mapBean;
    private HashMap<String, String> map;
    private List<PrivateOtherFilterBean> orderList;//下单状况
    private List<PrivateOtherFilterBean> statusList;//客户状态
    private List<PrivateOtherFilterBean> storeTypeList;//客户类型
    private List<PrivateOtherFilterBean> licensePrepare;//资质筛选
    private String name;
    private String id;
    private boolean isGroup;
    private String areaCode;
    private String areaCodeLevel;
    private String areaName;
    private AreaSelectBean selectBean;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_filter_private;
    }

    @Override
    protected void initTransferData() {
        super.initTransferData();
        if (getIntent() != null) {
            Intent intent = getIntent();
            mapBean = (FilterMapBean) intent.getSerializableExtra("map");
            name = intent.getStringExtra("name");
            isGroup = intent.getBooleanExtra("isGroup", false);
            id = intent.getStringExtra("id");
            areaName = intent.getStringExtra("areaName");
            if (mapBean != null) {
                map = mapBean.getMap();
                selectBean = mapBean.getAreaSelectBean();
                areaCode = map.get("areaCode");
                areaCodeLevel = map.get("areaCodeLevel");
            }
        }

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        RxBus.get().unRegister(this);
    }


    @Override
    protected AbsNavigationBar initHead() {
        DefaultNavigationBar bar = new DefaultNavigationBar.Builder(this)
                .setLeftIcon(R.color.transparent)
                .setShowLine(false)
                .setLeftClickListener(null)
                .builder();
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.MATCH_PARENT);
        if (bar != null && bar.rightImgView != null) {
            bar.rightImgView.setPadding(40, 40, 0, 40);
            bar.rightImgView.setImageResource(R.drawable.ddsx_close);
            bar.rightImgView.setLayoutParams(params);
            bar.rightImgView.setOnClickListener(v -> {
                setResult(RESULT_OK);
                finish();
            });
        }
        return bar;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        RxBus.get().register(this);
        mPresenter.getOtherFilterItems();
        initDrugstoreRange();

        initArea();
        setHistory();
    }

    /**
     * 药店范围
     */
    private void initDrugstoreRange() {
        if (!RoleTypeConfig.isBDMOrGJRBDM()) {
            layoutRangeValue.setVisibility(View.GONE);
            lineRange.setVisibility(View.GONE);
        } else {
            layoutRangeValue.setVisibility(View.VISIBLE);
            lineRange.setVisibility(View.VISIBLE);
        }
        if (TextUtils.isEmpty(name)) {
            tvRangeValue.setText("全部");
        } else {
            tvRangeValue.setText(name);
        }
    }

    /**
     * 药店范围
     */
    private void initArea() {
        if (TextUtils.isEmpty(areaName)) {
            tvAreaValue.setText("全部");
        } else {
            tvAreaValue.setText(areaName);
        }
    }

    @OnClick({R.id.tv_reset, R.id.tv_confirm, R.id.tv_range_value, R.id.tv_area_value})
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tv_reset:
                reset();
                break;
            case R.id.tv_confirm:
                confirm();
                break;
            case R.id.tv_range_value:
                startActivity(OrderExecutorActivity.class);
                break;
            case R.id.tv_area_value:
                Intent intent = new Intent(this, AreaSelectActivityV2.class);
                if (selectBean != null) {
                    intent.putExtra("mapBean", selectBean);
                }
                intent.putExtra("publicType", false);
                startActivityForResult(intent, DrugstoreConstants.REQUEST_SELECT_CITY);
                break;
        }
    }


    /**
     * 药店状态
     */
    private void initDrugstoreStatus() {
        if (mapBean != null) {
            statusList = mapBean.getStatusList();
        } else {
            if (statusList != null && statusList.size() > 0) {
                statusList.get(0).setMutual(true);
            }
        }
        flowTagLayoutStatus.setSpanCount(2);
        flowTagLayoutStatus.setTagCheckedMode(FlowTagLayout.FLOW_TAG_CHECKED_MULTI);
        flowTagLayoutStatus.setTagShowMode(FlowTagLayout.FLOW_TAG_SHOW_FREE);
        PrivateFilterAdapter adapter = new PrivateFilterAdapter(R.layout.item_tag5, statusList);
        flowTagLayoutStatus.setAdapter(adapter);
        flowTagLayoutStatus.setOnTagSelectListener(new FlowTagLayout.OnTagSelectListener() {
            @Override
            public void onItemSelect(FlowTagLayout parent, List selected) {
                StringBuffer selectStr = new StringBuffer();
                for (int i = 0; i < selected.size(); i++) {
                    PrivateOtherFilterBean bean = (PrivateOtherFilterBean) selected.get(i);
                    if (!selectStr.toString().contains(String.valueOf(bean.getText()))) {
                        selectStr.append(bean.getCode());
                        selectStr.append(",");
                    }

                }
                String str = selectStr.toString();
                if (str.endsWith(",")) {
                    str = selectStr.substring(0, selectStr.length() - 1);
                }
                stats = str;
            }
        });

    }


    /**
     * 下单情况
     */
    private void initDrugstoreOrder() {
        if (mapBean != null) {
            orderList = mapBean.getOrderStatusList();
        } else {
            if (orderList != null && orderList.size() > 0) {
                orderList.get(0).setMutual(true);
            }
        }
        flowOrder.setTagCheckedMode(FlowTagLayout.FLOW_TAG_CHECKED_MULTI);
        flowOrder.setTagShowMode(FlowTagLayout.FLOW_TAG_SHOW_FREE);
        flowOrder.setTagCancelable(true);
        PrivateFilterAdapter adapter = new PrivateFilterAdapter(R.layout.item_tag5, orderList);
        flowOrder.setAdapter(adapter);
        flowOrder.setOnTagSelectListener(new FlowTagLayout.OnTagSelectListener() {
            @Override
            public void onItemSelect(FlowTagLayout parent, List selected) {
                StringBuffer selectStr = new StringBuffer();
                for (int i = 0; i < selected.size(); i++) {
                    PrivateOtherFilterBean bean = (PrivateOtherFilterBean) selected.get(i);
                    if (!selectStr.toString().contains(String.valueOf(bean.getCode()))) {
                        selectStr.append(bean.getCode());
                        selectStr.append(",");
                    }

                }
                String str = selectStr.toString();
                if (str.endsWith(",")) {
                    str = selectStr.substring(0, selectStr.length() - 1);
                }
                order = str;
            }
        });
    }


    /**
     * 药店类型
     */
    private void initDrugstoreType() {
        if (mapBean != null) {
            storeTypeList = mapBean.getStoreTypeList();
        } else {
            if (storeTypeList != null && storeTypeList.size() > 0) {
                storeTypeList.get(0).setMutual(true);
            }
        }
        flowDrugstoreType.setSpanCount(2);
        flowDrugstoreType.setTagCheckedMode(FlowTagLayout.FLOW_TAG_CHECKED_MULTI);
        flowDrugstoreType.setTagShowMode(FlowTagLayout.FLOW_TAG_SHOW_FREE);
        PrivateFilterAdapter adapter = new PrivateFilterAdapter(R.layout.item_tag5, storeTypeList);
        flowDrugstoreType.setAdapter(adapter);
        flowDrugstoreType.setOnTagSelectListener(new FlowTagLayout.OnTagSelectListener() {
            @Override
            public void onItemSelect(FlowTagLayout parent, List selected) {
                StringBuffer selectStr = new StringBuffer();
                for (int i = 0; i < selected.size(); i++) {
                    PrivateOtherFilterBean bean = (PrivateOtherFilterBean) selected.get(i);
                    if (!selectStr.toString().contains(String.valueOf(bean.getText()))) {
                        selectStr.append(bean.getCode());
                        selectStr.append(",");
                    }

                }
                String str = selectStr.toString();
                if (str.endsWith(",")) {
                    str = selectStr.substring(0, selectStr.length() - 1);
                }
                storeType = str;
            }
        });

    }

    /**
     * 资质筛选
     */
    private void initDrugstoreFilter() {
        if (mapBean != null) {
            licensePrepare = mapBean.getLicensePrepare();
        } else {
            if (licensePrepare != null && licensePrepare.size() > 0) {
                licensePrepare.get(0).setMutual(true);
            }
        }
        flowTagLayoutFilter.setSpanCount(2);
        flowTagLayoutFilter.setTagCheckedMode(FlowTagLayout.FLOW_TAG_CHECKED_MULTI);
        flowTagLayoutFilter.setTagShowMode(FlowTagLayout.FLOW_TAG_SHOW_FREE);
        PrivateFilterAdapter adapter = new PrivateFilterAdapter(R.layout.item_tag5, licensePrepare);
        flowTagLayoutFilter.setAdapter(adapter);
        flowTagLayoutFilter.setOnTagSelectListener(new FlowTagLayout.OnTagSelectListener() {
            @Override
            public void onItemSelect(FlowTagLayout parent, List selected) {
                StringBuffer selectSb = new StringBuffer();
                for (int i = 0; i < selected.size(); i++) {
                    PrivateOtherFilterBean bean = (PrivateOtherFilterBean) selected.get(i);
                    if (!selectSb.toString().contains(String.valueOf(bean.getCode()))) {
                        selectSb.append(bean.getCode());
                        selectSb.append(",");
                    }
                }
                String selectStr = selectSb.toString();
                if (selectStr.endsWith(",")) {
                    selectStr = selectStr.substring(0, selectStr.length() - 1);
                }
                licenseStatus = selectStr;
            }
        });
    }


    /**
     * 重置
     */
    private void reset() {
        mapBean = null;
        //药店状态
        stats = "";
        //资质筛选
        initDrugstoreFilter();
        licenseStatus = "";
//        isNearValidate = "";
        //下单情况
        flowOrder.resetStatus();
        //药店类型
        flowDrugstoreType.resetStatus();
        flowOrder.resetStatus();
        areaCode = "";
        areaCodeLevel = "";
        areaName = "";
        selectBean = null;
        tvAreaValue.setText("全部");
        order = "";
        storeType = "";
        name = null;
        id = "";
        isGroup = false;
        initDrugstoreRange();
        mPresenter.getOtherFilterItems();
    }

    /**
     * 填充历史数据
     */
    private void setHistory() {
        if (mapBean == null || map == null) return;
        stats = map.get("customerStatusCode");
        licenseStatus = map.get("licenseCode");
        order = map.get("orderConditionCode");
        storeType = map.get("customerTypeCode");//客户类型
    }

    /**
     * 确定
     */
    private void confirm() {
        Intent intent = new Intent();
        FilterMapBean mapBean = new FilterMapBean();
        HashMap<String, String> map = new HashMap<>();
        if (!"-1".equals(stats) && !TextUtils.isEmpty(stats)) {
            map.put("customerStatusCode", stats);
        }
        if (!"-1".equals(licenseStatus) && !TextUtils.isEmpty(licenseStatus)) {
            map.put("licenseStatuses", licenseStatus);
        }

        if (!"-1".equals(order) && !TextUtils.isEmpty(order)) {
            map.put("orderConditionCode", order);
        }
        if (!"-1".equals(storeType) && !TextUtils.isEmpty(storeType)) {
            map.put("customerTypeCode", storeType);
        }
        if (!"-1".equals(areaCodeLevel) && !TextUtils.isEmpty(areaCodeLevel)) {
            map.put("areaCodeLevel", areaCodeLevel);
        }
        if (!"-1".equals(areaCode) && !TextUtils.isEmpty(areaCode)) {
            map.put("areaCode", areaCode);
        }
        if (isGroup && !TextUtils.isEmpty(id)) {
            map.put("groupId", id);
        } else if (!isGroup && !TextUtils.isEmpty(id)) {
            map.put("searchUserId", id);
        }
        mapBean.setMap(first() ? null : map);
        mapBean.setStatusList(statusList);
        mapBean.setLicensePrepare(licensePrepare);
        mapBean.setOrderStatusList(orderList);
        mapBean.setStoreTypeList(storeTypeList);
        mapBean.setAreaSelectBean(selectBean);
        intent.putExtra("name", name);
        intent.putExtra("isGroup", isGroup);
        intent.putExtra("id", id);
        intent.putExtra("map", mapBean);
        intent.putExtra("areaName", areaName);
        setResult(RESULT_OK, intent);
        finish();
    }

    private boolean first() {
        return !isGroup && TextUtils.isEmpty(id)
                && ("-1".equals(stats) || TextUtils.isEmpty(stats))
                && ("-1".equals(licenseStatus) || TextUtils.isEmpty(licenseStatus))
                && ("-1".equals(order) || TextUtils.isEmpty(order))
                && ("-1".equals(areaCode) || TextUtils.isEmpty(areaCode))
                && ("-1".equals(areaCodeLevel) || TextUtils.isEmpty(areaCodeLevel))
                && ("-1".equals(storeType) || TextUtils.isEmpty(storeType));
    }

    /**
     * 更新药店范围
     */
    @Subscribe(code = RX_BUS_UPDATE_EXECUTOR)
    public void rxBusEvent(Bundle bundle) {
        name = bundle.getString(ExtraConstants.NAME);
        id = bundle.getString(ExtraConstants.ID);
        isGroup = bundle.getBoolean(ExtraConstants.IS_GROUP);
        if (!TextUtils.isEmpty(name)) {
            tvRangeValue.setText(name);
        }

    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            if (data != null) {
                selectBean = (AreaSelectBean) data.getSerializableExtra("mapBean");
                if (selectBean == null) return;
                AreaBean provinceBean = selectBean.getProvinceBean();
                AreaBean cityBean = selectBean.getCityBean();
                ArrayList<AreaBean> selectAreaList = selectBean.getSelectAreaList();
                if (selectAreaList.size() == 0) {
                    if (provinceBean.getAreaCode().equals(cityBean.getAreaCode())) {
                        areaCode = provinceBean.getAreaCode();
                        areaCodeLevel = provinceBean.getAreaLevel();
                        areaName = provinceBean.getAreaName();
                    } else {
                        areaCode = cityBean.getAreaCode();
                        areaCodeLevel = cityBean.getAreaLevel();
                        areaName = cityBean.getAreaName();
                    }
                } else {
                    areaCode = AreaSelectActivityV2.getCode(selectAreaList);
                    areaCodeLevel = AreaSelectActivityV2.getLevel(selectAreaList);
                    String selectNames = AreaSelectActivityV2.getName(selectAreaList);
                    String[] str = selectNames.split(",");
                    if (str.length > 1) {
                        areaName = str[0] + "...";
                    } else {
                        areaName = str[0];
                    }
                }
                tvAreaValue.setText(areaName);
            } else {
                areaCode = "";
                areaCodeLevel = "";
                areaName = "";
                selectBean = null;
                if (mapBean != null) {
                    mapBean.setAreaSelectBean(null);
                }
                tvAreaValue.setText("全部");
            }
        }
    }

    @Override
    public void getOtherFilterItemsSuccess(RequestBaseBean<FilterPrivateBean> baseBean) {
        orderList = baseBean.getData().getOrderCondition();
        statusList = baseBean.getData().getCustomerStatus();
        storeTypeList = baseBean.getData().getCustomerType();
        licensePrepare = baseBean.getData().getLicensePrepare();
        initDrugstoreOrder();
        initDrugstoreStatus();
        initDrugstoreType();
        initDrugstoreFilter();
    }

    @NonNull
    @Override
    public BasePresenter initPresenter() {
        return FilterPrivatePresenter.newInstance();
    }

    @Override
    public void showNetError() {

    }
}
