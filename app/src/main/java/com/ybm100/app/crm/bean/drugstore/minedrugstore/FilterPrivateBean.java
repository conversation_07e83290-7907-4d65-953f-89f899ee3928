package com.ybm100.app.crm.bean.drugstore.minedrugstore;

import com.ybm100.app.crm.bean.goods.PrivateOtherFilterBean;

import java.util.ArrayList;

/**
 * @author: zcj
 * @time:2020/4/2. Description:
 */
public class FilterPrivateBean {
    private ArrayList<PrivateOtherFilterBean> customerStatus;
    private ArrayList<PrivateOtherFilterBean> customerType;
    private ArrayList<PrivateOtherFilterBean> lifeCycle;
    private ArrayList<PrivateOtherFilterBean> orderCondition;
    private ArrayList<PrivateOtherFilterBean> licensePrepare;

    public ArrayList<PrivateOtherFilterBean> getCustomerStatus() {
        return customerStatus;
    }

    public ArrayList<PrivateOtherFilterBean> getLicensePrepare() {
        return licensePrepare;
    }

    public void setLicensePrepare(ArrayList<PrivateOtherFilterBean> licensePrepare) {
        this.licensePrepare = licensePrepare;
    }

    public void setCustomerStatus(ArrayList<PrivateOtherFilterBean> customerStatus) {
        this.customerStatus = customerStatus;
    }

    public ArrayList<PrivateOtherFilterBean> getCustomerType() {
        return customerType;
    }

    public void setCustomerType(ArrayList<PrivateOtherFilterBean> customerType) {
        this.customerType = customerType;
    }

    public ArrayList<PrivateOtherFilterBean> getLifeCycle() {
        return lifeCycle;
    }

    public void setLifeCycle(ArrayList<PrivateOtherFilterBean> lifeCycle) {
        this.lifeCycle = lifeCycle;
    }

    public ArrayList<PrivateOtherFilterBean> getOrderCondition() {
        return orderCondition;
    }

    public void setOrderCondition(ArrayList<PrivateOtherFilterBean> orderCondition) {
        this.orderCondition = orderCondition;
    }
}
