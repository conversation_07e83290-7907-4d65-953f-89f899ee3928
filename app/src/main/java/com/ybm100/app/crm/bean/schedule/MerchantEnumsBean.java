package com.ybm100.app.crm.bean.schedule;

import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.ValueBean;

import java.util.List;

/**
 * 对象经营状况 bean
 *
 * <AUTHOR>
 * @date 2019-06-21
 */
public class MerchantEnumsBean  extends RequestBaseBean {

    private List<ValueBean> aroundEnvList;
    private List<ValueBean> needPullSalesList;
    private List<ValueBean> mailyConsuleMedTypesList;
    private List<ValueBean> medicalInsuranceList;
    private List<ValueBean> merchantTypeList;
    private List<ValueBean> needMerchantDiagnoseList;
    private List<ValueBean> buyersAmountList;
    private List<ValueBean> buySkusList;
    private List<ValueBean> buyersTypeList;
    private List<ValueBean> needClerkTrainsList;

    public List<ValueBean> getAroundEnvList() {
        return aroundEnvList;
    }

    public void setAroundEnvList(List<ValueBean> aroundEnvList) {
        this.aroundEnvList = aroundEnvList;
    }

    public List<ValueBean> getNeedPullSalesList() {
        return needPullSalesList;
    }

    public void setNeedPullSalesList(List<ValueBean> needPullSalesList) {
        this.needPullSalesList = needPullSalesList;
    }

    public List<ValueBean> getMailyConsuleMedTypesList() {
        return mailyConsuleMedTypesList;
    }

    public void setMailyConsuleMedTypesList(List<ValueBean> mailyConsuleMedTypesList) {
        this.mailyConsuleMedTypesList = mailyConsuleMedTypesList;
    }

    public List<ValueBean> getMedicalInsuranceList() {
        return medicalInsuranceList;
    }

    public void setMedicalInsuranceList(List<ValueBean> medicalInsuranceList) {
        this.medicalInsuranceList = medicalInsuranceList;
    }

    public List<ValueBean> getMerchantTypeList() {
        return merchantTypeList;
    }

    public void setMerchantTypeList(List<ValueBean> merchantTypeList) {
        this.merchantTypeList = merchantTypeList;
    }

    public List<ValueBean> getNeedMerchantDiagnoseList() {
        return needMerchantDiagnoseList;
    }

    public void setNeedMerchantDiagnoseList(List<ValueBean> needMerchantDiagnoseList) {
        this.needMerchantDiagnoseList = needMerchantDiagnoseList;
    }

    public List<ValueBean> getBuyersAmountList() {
        return buyersAmountList;
    }

    public void setBuyersAmountList(List<ValueBean> buyersAmountList) {
        this.buyersAmountList = buyersAmountList;
    }

    public List<ValueBean> getBuySkusList() {
        return buySkusList;
    }

    public void setBuySkusList(List<ValueBean> buySkusList) {
        this.buySkusList = buySkusList;
    }

    public List<ValueBean> getBuyersTypeList() {
        return buyersTypeList;
    }

    public void setBuyersTypeList(List<ValueBean> buyersTypeList) {
        this.buyersTypeList = buyersTypeList;
    }

    public List<ValueBean> getNeedClerkTrainsList() {
        return needClerkTrainsList;
    }

    public void setNeedClerkTrainsList(List<ValueBean> needClerkTrainsList) {
        this.needClerkTrainsList = needClerkTrainsList;
    }


}
