package com.ybm100.app.crm.bean.drugstore;

/**
 * <AUTHOR>
 * @date 2019-06-24
 */
public class CrmMerchantBasicInfoBean {
    private String areaSize;//药店面积	number
    private String aroundEnv;//周边环境	string
    private String aroundEnvName;//周边环境	string
    private String buySkus;//药店需求SKU数	number
    private String buySkusName;//药店需求SKU数	string
    private String buyerAmount;//药店人流量	string
    private String buyersAmountText;//药店人流量	string
    private String buyersType;//主要消费人群	number
    private String buyersTypeName;//主要消费人群	string
    private String clerkNum;//药店人数	number
    private String currentLevel;//当前等级	number
    private String currentLevelName;//当前等级	string
    private String mainlyConsumeMedTypes;// 主要消费结构	string
    private String mainlyConsumeMedTypesName;//	主要消费结构名称	string
    private int medicalInsurance;// 是否可用医保	number	1 是 0否
    private String merchantDemand;// 商家需求	string
    private String merchantType;// 客户类型	number	客户类型（1单体；2连锁；3诊所；-1未填写）
    private String merchantTypeName;// 客户类型名称	string
    private int needClerkTrains;// 是否需要店员培训	number	1 是 0 否
    private int needMerchantDiagnose;// 是否需要门店诊断	number	1 是 0 否
    private int needPullSales;// 是否需要动销	number	1 是 0 否
    private String purchaseWay;// 核心供应商	string
    private String shortOfTypes;// 缺失品类	string

    public String getAreaSize() {
        return areaSize;
    }

    public void setAreaSize(String areaSize) {
        this.areaSize = areaSize;
    }

    public String getAroundEnv() {
        return aroundEnv;
    }

    public void setAroundEnv(String aroundEnv) {
        this.aroundEnv = aroundEnv;
    }

    public String getAroundEnvName() {
        return aroundEnvName;
    }

    public void setAroundEnvName(String aroundEnvName) {
        this.aroundEnvName = aroundEnvName;
    }

    public String getBuySkus() {
        return buySkus;
    }

    public void setBuySkus(String buySkus) {
        this.buySkus = buySkus;
    }

    public String getBuySkusName() {
        return buySkusName;
    }

    public void setBuySkusName(String buySkusName) {
        this.buySkusName = buySkusName;
    }

    public String getBuyerAmount() {
        return buyerAmount;
    }

    public String getBuyersAmountText() {
        return buyersAmountText;
    }

    public void setBuyersAmountText(String buyersAmountText) {
        this.buyersAmountText = buyersAmountText;
    }

    public void setBuyerAmount(String buyerAmount) {
        this.buyerAmount = buyerAmount;
    }

    public String getBuyersType() {
        return buyersType;
    }

    public void setBuyersType(String buyersType) {
        this.buyersType = buyersType;
    }

    public String getBuyersTypeName() {
        return buyersTypeName;
    }

    public void setBuyersTypeName(String buyersTypeName) {
        this.buyersTypeName = buyersTypeName;
    }

    public String getClerkNum() {
        return clerkNum;
    }

    public void setClerkNum(String clerkNum) {
        this.clerkNum = clerkNum;
    }

    public String getCurrentLevel() {
        return currentLevel;
    }

    public void setCurrentLevel(String currentLevel) {
        this.currentLevel = currentLevel;
    }

    public String getCurrentLevelName() {
        return currentLevelName;
    }

    public void setCurrentLevelName(String currentLevelName) {
        this.currentLevelName = currentLevelName;
    }

    public String getMainlyConsumeMedTypes() {
        return mainlyConsumeMedTypes;
    }

    public void setMainlyConsumeMedTypes(String mainlyConsumeMedTypes) {
        this.mainlyConsumeMedTypes = mainlyConsumeMedTypes;
    }

    public String getMainlyConsumeMedTypesName() {
        return mainlyConsumeMedTypesName;
    }

    public void setMainlyConsumeMedTypesName(String mainlyConsumeMedTypesName) {
        this.mainlyConsumeMedTypesName = mainlyConsumeMedTypesName;
    }

    public int getMedicalInsurance() {
        return medicalInsurance;
    }

    public void setMedicalInsurance(int medicalInsurance) {
        this.medicalInsurance = medicalInsurance;
    }

    public String getMerchantDemand() {
        return merchantDemand;
    }

    public void setMerchantDemand(String merchantDemand) {
        this.merchantDemand = merchantDemand;
    }

    public String getMerchantType() {
        return merchantType;
    }

    public void setMerchantType(String merchantType) {
        this.merchantType = merchantType;
    }

    public String getMerchantTypeName() {
        return merchantTypeName;
    }

    public void setMerchantTypeName(String merchantTypeName) {
        this.merchantTypeName = merchantTypeName;
    }

    public int getNeedClerkTrains() {
        return needClerkTrains;
    }

    public void setNeedClerkTrains(int needClerkTrains) {
        this.needClerkTrains = needClerkTrains;
    }

    public int getNeedMerchantDiagnose() {
        return needMerchantDiagnose;
    }

    public void setNeedMerchantDiagnose(int needMerchantDiagnose) {
        this.needMerchantDiagnose = needMerchantDiagnose;
    }

    public int getNeedPullSales() {
        return needPullSales;
    }

    public void setNeedPullSales(int needPullSales) {
        this.needPullSales = needPullSales;
    }

    public String getPurchaseWay() {
        return purchaseWay;
    }

    public void setPurchaseWay(String purchaseWay) {
        this.purchaseWay = purchaseWay;
    }

    public String getShortOfTypes() {
        return shortOfTypes;
    }

    public void setShortOfTypes(String shortOfTypes) {
        this.shortOfTypes = shortOfTypes;
    }
}
