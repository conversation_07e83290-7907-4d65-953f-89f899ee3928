package com.ybm100.app.crm.ui.adapter.schedule;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.xyy.utilslibrary.utils.GlideLoadUtils;
import com.ybm100.app.crm.R;

import java.util.ArrayList;
import java.util.List;

/**
 * Author ： LoveNewsweetheart
 * Date:2018/12/24
 */
public class SelectImgAdapter extends RecyclerView.Adapter<SelectImgAdapter.MyViewHolder> implements View.OnClickListener {

    private List<String> imgUrlList;
    private final Context mContext;
    private final OnActionCallback actionCallback;
    private int maxShowImgNum = 0;
    private final boolean isEditMode;

    public SelectImgAdapter(List<String> imgUrlList, Context mContext, boolean isEditMode, OnActionCallback actionCallback) {
        this.imgUrlList = imgUrlList;
        this.mContext = mContext;
        this.actionCallback = actionCallback;
        this.isEditMode = isEditMode;
        if (this.imgUrlList == null) {
            this.imgUrlList = new ArrayList<>();
        }
    }

    public void setMaxShowImgNum(int maxShowImgNum) {
        this.maxShowImgNum = maxShowImgNum;
    }

    @NonNull
    @Override
    public MyViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        View view = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.item_schdule_select_img, viewGroup, false);
        return new MyViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull MyViewHolder myViewHolder, final int position) {
        final ImageView iv_picture = myViewHolder.iv_picture;
        ImageView iv_delete = myViewHolder.iv_delete;
        if (isEditMode) {
            if (position == imgUrlList.size()) {
                GlideLoadUtils.loadImg(mContext, iv_picture, R.drawable.add_pic);
                iv_delete.setVisibility(View.GONE);
            } else {
                GlideLoadUtils.loadImg(mContext, iv_picture, imgUrlList.get(position));
                iv_delete.setVisibility(View.VISIBLE);
            }

            iv_delete.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    //点击图片
                    if (actionCallback != null) {
                        actionCallback.onDeleteImgClick(position);
                    }
                }
            });
        } else {
            GlideLoadUtils.loadImg(mContext, iv_picture, imgUrlList.get(position));
            iv_delete.setVisibility(View.GONE);
        }

        iv_picture.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (position == imgUrlList.size() && isEditMode) {
                    //加号
                    if (actionCallback != null) {
                        actionCallback.onAddImgClick();
                    }
                } else {
                    //查看大图
                    if (actionCallback != null) {
                        actionCallback.onClickPicture(iv_picture, imgUrlList.get(position), position);
                    }
                }
            }
        });
    }


    @Override
    public int getItemCount() {
        if (imgUrlList == null || imgUrlList.size() == 0) {
            return isEditMode ? 1 : 0;
        }
        if (maxShowImgNum > 0 && imgUrlList.size() + 1 > maxShowImgNum) {
            return maxShowImgNum;
        } else {
            return isEditMode ? imgUrlList.size() + 1 : imgUrlList.size();
        }
    }

    @Override
    public void onClick(View v) {

    }

    public interface OnActionCallback {
        void onAddImgClick();

        void onDeleteImgClick(int position);

        void onClickPicture(View view, String url, int position);
    }

    public static class MyViewHolder extends RecyclerView.ViewHolder {

        private final ImageView iv_picture;
        private final ImageView iv_delete;

        public MyViewHolder(@NonNull View itemView) {
            super(itemView);
            iv_picture = itemView.findViewById(R.id.iv_picture);
            iv_delete = itemView.findViewById(R.id.iv_delete);
        }
    }
}
