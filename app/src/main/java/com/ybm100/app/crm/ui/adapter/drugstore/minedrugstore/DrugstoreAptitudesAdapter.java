package com.ybm100.app.crm.ui.adapter.drugstore.minedrugstore;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.xyy.utilslibrary.utils.DisplayUtils;
import com.xyy.utilslibrary.utils.TimeUtils;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.LicenceBean;
import com.ybm100.app.crm.utils.FormatUtils;

import java.util.List;

/**
 * author :lx
 * date 2019/1/4.
 * email： <EMAIL>
 * 资质adapter
 */
public class DrugstoreAptitudesAdapter extends BaseQuickAdapter<LicenceBean, BaseViewHolder> {

    private int must = 1;// 1 非必须 0 必须

    public DrugstoreAptitudesAdapter(int layoutResId, @Nullable List<LicenceBean> data, int must) {
        super(layoutResId, data);
        this.must = must;
    }

    public DrugstoreAptitudesAdapter(@Nullable List<LicenceBean> data) {
        super(data);
    }

    public DrugstoreAptitudesAdapter(int layoutResId) {
        super(layoutResId);
    }

    @Override
    protected void convert(BaseViewHolder helper, LicenceBean item) {

        TextView redStar = helper.getView(R.id.tv_red_star);
        TextView text = helper.getView(R.id.tv_drug_aptitude);
        LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) text.getLayoutParams();
        switch (must) {
            case 0:
                redStar.setVisibility(View.VISIBLE);
                redStar.setText("* ");
                lp.leftMargin = 0;
                text.setLayoutParams(lp);
                break;
            case 1:
                redStar.setVisibility(View.GONE);
                redStar.setText("");
                lp.leftMargin = DisplayUtils.dip2px(mContext, 10);
                text.setLayoutParams(lp);
                break;
        }
        long validateUntil = item.getValidUntil();
        String validateTime = "";
        if (validateUntil <= 0) {
            validateTime = "";
        } else {
            validateTime = TimeUtils.millis2String(validateUntil, "yyyy.MM.dd");
        }
        helper.setText(R.id.tv_drug_aptitude, item.getName())
                .setText(R.id.tv_drug_status, FormatUtils.textFormat(item.getEnclosureStatusName()))
                .setText(R.id.tv_drug_validity_date, FormatUtils.textFormat(validateTime));
        if (item.getEnclosureStatus() == 1) {
            helper.setTextColor(R.id.tv_drug_status, ContextCompat.getColor(mContext, R.color.color_00B377));
            helper.getView(R.id.tv_drug_status).setEnabled(true);
        } else {
            helper.setTextColor(R.id.tv_drug_status, ContextCompat.getColor(mContext, R.color.color_292933));
            helper.getView(R.id.tv_drug_status).setEnabled(false);
        }
        if (item.getExpireStatus() == 1 || item.getExpireStatus() == 2) {
            helper.setTextColor(R.id.tv_drug_aptitude, ContextCompat.getColor(mContext, R.color.color_FE3D3D))
                    .setTextColor(R.id.tv_drug_validity_date, ContextCompat.getColor(mContext, R.color.color_FE3D3D));
        } else {
            helper.setTextColor(R.id.tv_drug_aptitude, ContextCompat.getColor(mContext, R.color.color_292933))
                    .setTextColor(R.id.tv_drug_validity_date, ContextCompat.getColor(mContext, R.color.color_292933));
        }
        helper.addOnClickListener(R.id.tv_drug_status);
    }
}
