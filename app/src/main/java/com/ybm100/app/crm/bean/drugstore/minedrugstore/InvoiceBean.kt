package com.ybm100.app.crm.bean.drugstore.minedrugstore

/**
 * 发票类型
 */
class InvoiceBean {

    var invoiceType: String? = null // 发票类型
    var explain: String? = null // 解释
    var isHaveWaitApply: Int = 0 // 是否存在待审核的申请 0不存在，1存在
    var list: ListBean? = null // 发票类型修改记录列表
    var registerFlag: Int = 0

    class ListBean {
        var lastPage: Boolean = false
        var limit: Int = 0
        var offset: Int = 0
        var rows: List<ApplyListBean>? = null // 发票类型申请列表
    }

    class ApplyListBean {
        var auditStatus: Int = 0 // 审核状态 1待审核 2审核通过 3审核不通过
        var auditStatusStr: String? = null // 审核状态
        var invoiceApplyType: Int = 0 // 申请发票类型(1:普通电子发票 2:增值税专用发票 3 纸质发票)
        var invoiceApplyTypeStr: String? = null // 申请发票类型
        var invoiceOriginalType: Int = 0 // 原发票类型(1:普通电子发票 2:增值税专用发票 3 纸质发票)
        var invoiceOriginalTypeStr: String? = null // 原发票类型
        var id: String? = null // 申请id
        var merchantId: String? = null // 药店id
    }
}