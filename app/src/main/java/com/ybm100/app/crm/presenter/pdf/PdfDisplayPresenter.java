package com.ybm100.app.crm.presenter.pdf;

import android.util.Log;

import androidx.annotation.NonNull;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.contract.pdf.PdfDisplayContract;
import com.ybm100.app.crm.model.pdf.PdfDisplayModel;
import com.ybm100.app.crm.utils.FileUtil;

import java.io.File;

import io.reactivex.functions.Function;
import okhttp3.ResponseBody;

public class PdfDisplayPresenter extends BasePresenter<PdfDisplayContract.IPdfDisplayModel, PdfDisplayContract.IPdfDisplayView> {

    @NonNull
    public static PdfDisplayPresenter newInstance() {
        return new PdfDisplayPresenter();
    }

    @Override
    protected PdfDisplayContract.IPdfDisplayModel getModel() {
        return PdfDisplayModel.newInstance();
    }

    public void download(String url, String path) {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.downloadPdfFile(url)
                .map(body -> {
                    try {
                        String targetPath = path + File.separator + FileUtil.getFileNameFromUrl(url);
                        String resultPath = FileUtil.writeFileToSDCard(body, targetPath);
                        Log.i("download", resultPath);
                        return resultPath;
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    return null;
                }).compose(RxHelper.rxSchedulerHelper())
                .subscribe(
                        resultPath -> {
                            if (resultPath == null) {
                                mIView.downloadFailure("下载失败");
                            } else {
                                mIView.downloadSuccess(resultPath, url.endsWith(".pdf"));
                            }
                        }, throwable -> {
                            Log.i("download", throwable.getMessage());
                            mIView.downloadFailure("下载失败");
                        }
                ));
    }
}
