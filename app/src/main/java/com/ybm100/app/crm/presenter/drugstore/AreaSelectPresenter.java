package com.ybm100.app.crm.presenter.drugstore;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.AreaBean;
import com.ybm100.app.crm.contract.drugstore.AreaSelectContract;
import com.ybm100.app.crm.model.drugstore.AreaSelectModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

import java.util.List;

/**
 * @author: zcj
 * @time:2020/3/29. Description:
 */
public class AreaSelectPresenter extends BasePresenter<AreaSelectContract.IAreaSelectModel, AreaSelectContract.IAreaSelectView> {
    @Override
    protected AreaSelectContract.IAreaSelectModel getModel() {
        return AreaSelectModel.newInstance();
    }

    public static AreaSelectPresenter newInstance() {
        return new AreaSelectPresenter();
    }

    //区域选择接口
    public void searchArea(int currLevel, String areaCode) {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.searchArea(areaCode)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean<List<AreaBean>>>(mIView) {
                    @Override
                    public void onSuccess(RequestBaseBean<List<AreaBean>> listRequestBaseBean) {
                        if (mIView == null) return;
                        mIView.searchAreaSuccess(currLevel, listRequestBaseBean);
                    }
                }, new SimpleErrorConsumer(mIView)));
    }
}
