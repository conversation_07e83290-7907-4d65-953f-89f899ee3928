package com.ybm100.app.crm.ui.adapter.drugstore.minedrugstore;

import android.view.View;
import android.view.ViewGroup;

import com.chad.library.adapter.base.BaseViewHolder;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.ui.adapter.MyBaseQuickAdapter;
import com.ybm100.app.crm.ui.adapter.drugstore.minedrugstore.viewholder.YBMBaseHolder;

import java.util.ArrayList;
import java.util.List;
/**
 * author :lx
 * date 2019/1/7.
 * email： <EMAIL>
 */
public abstract class YBMMyBaseAdapter<T> extends MyBaseQuickAdapter {
    private View mContentView;
    private View noData;

    public YBMMyBaseAdapter(List<T> data) {
        this(0, data);
    }

    public YBMMyBaseAdapter(int layoutResId, List<T> data) {
        super(layoutResId, data);
    }

    public YBMMyBaseAdapter(View contentView, List<T> data) {
        this(0, data);
        this.mContentView = contentView;
    }

    public void setlayoutResId(int layoutResId) {
        this.mLayoutResId = layoutResId;
    }

    protected BaseViewHolder createBaseViewHolder(ViewGroup parent, int layoutResId) {
        return this.mContentView == null ? new YBMBaseHolder(getItemView(layoutResId, parent)) : new YBMBaseHolder(mContentView);
    }

    public void notifyDataChangedAfterLoadMore(boolean isNextLoad) {
        if (!isNextLoad) {
            if (this.mData != null && !this.mData.isEmpty()) {
                if (this.noData == null && this.mLayoutInflater != null) {
                    this.noData = this.mLayoutInflater.inflate(R.layout.not_loading, null);
                    this.addFooterView(this.noData);
                }
            } else {
                this.removeFooterView(this.noData);
                this.noData = null;
            }

            try {
                super.notifyDataChangedAfterLoadMore(isNextLoad);
            } catch (Throwable var3) {
                var3.printStackTrace();
            }
        } else {
            this.removeFooterView(this.noData);
            this.noData = null;
            super.notifyDataChangedAfterLoadMore(isNextLoad);
        }

        super.notifyDataChangedAfterLoadMore(isNextLoad);
    }
    protected void convert(BaseViewHolder baseViewHolder, Object bean) {
        this.bindItemView((YBMBaseHolder) baseViewHolder, (T)bean);
    }

    protected abstract void bindItemView(YBMBaseHolder var1, T var2);

    public void setNewData(List data) {
        if (data == null) {
            data = new ArrayList();
        }
        try {
            super.setNewData(data);
        } catch (Throwable var3) {
//            BugUtil.sendBug(var3);
        }

    }
}