package com.ybm100.app.crm.goods.ui.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.AxisBase
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.formatter.ValueFormatter
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet
import com.ybm100.app.crm.R
import com.ybm100.app.crm.goods.bean.AVGPriceMonth
import java.util.*

class AVGPriceMonthView : ConstraintLayout {

    private var chartView: LineChart? = null
    private var avgPriceView: TextView? = null

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        initContentView(context)
    }

    private fun initContentView(context: Context) {
        LayoutInflater.from(context).inflate(R.layout.layout_goods_avg_price, this, true)
        avgPriceView = findViewById(R.id.tv_avg_price_value)
        chartView = findViewById(R.id.lc_price_chart)
        chartView?.let {
            it.setBackgroundColor(Color.WHITE)
            it.description.isEnabled = false
            it.setTouchEnabled(true)
            it.setDrawGridBackground(false)
            val customXAxisRenderer = CustomXAxisRenderer(it.viewPortHandler,
                    it.xAxis, it.getTransformer(YAxis.AxisDependency.LEFT))
            it.setOnChartValueSelectedListener(customXAxisRenderer)
            it.setDrawGridBackground(false)

            val mv = MyMarkerView(getContext())

            mv.chartView = it
            it.marker = mv
            it.setXAxisRenderer(customXAxisRenderer)
            it.rendererLeftYAxis = CustomerYAxisRenderer(it.viewPortHandler,
                    it.axisLeft, it.getTransformer(YAxis.AxisDependency.LEFT))
            it.isDragEnabled = true
            it.setScaleEnabled(false)
            it.setPinchZoom(false)
            it.legend.isEnabled = false
            it.axisRight.isEnabled = false

            it.xAxis.let { xAxis ->
                xAxis.setDrawGridLines(false)
                xAxis.position = XAxis.XAxisPosition.BOTTOM
                xAxis.setLabelCount(2, true)
                xAxis.textSize = 11f
                xAxis.textColor = Color.parseColor("#9494A6")
                xAxis.axisLineColor = Color.parseColor("#DDDDDD")
                xAxis.setDrawAxisLine(false)
                xAxis.valueFormatter = object : ValueFormatter() {
                    override fun getAxisLabel(value: Float, axis: AxisBase?): String {
                        return it.data.getDataSetByIndex(0).getEntryForIndex(value.toInt()).data.toString()
                    }
                }
            }

            it.axisLeft.let { yAxis ->
                yAxis.setCenterAxisLabels(false)
                yAxis.setLabelCount(6, true)
                yAxis.setDrawLabels(false)
                yAxis.setDrawAxisLine(false)
                yAxis.gridColor = Color.parseColor("#f2f2f2")
                yAxis.axisMinimum = 0f
            }

        }

    }


    @SuppressLint("SetTextI18n")
    fun setData(purchaseRecordList: AVGPriceMonth?) {
        purchaseRecordList?.let {
            avgPriceView?.text = "30天平均到手价：¥${it.averPrice ?: "--"}"
            setChartData(it.averPriceList?.mapIndexed { index, item ->
                Entry(index.toFloat(), item.price, item.date)
            } ?: getEmptyChartData())
        }
    }

    private fun setChartData(values: List<Entry>) {
        chartView?.let { chart ->
            if (chart.data != null &&
                    (chart.data?.dataSetCount ?: 0) > 0) {
                (chart.data?.getDataSetByIndex(0) as LineDataSet).let {
                    it.values = values
                    it.notifyDataSetChanged()
                }
                chart.data?.notifyDataChanged()
                chart.notifyDataSetChanged()
            } else {
                val mainColor = Color.parseColor("#00B377")
                val set1 = LineDataSet(values, "DataSet 1")
                set1.setDrawIcons(false)

                set1.setDrawHorizontalHighlightIndicator(false)
                set1.highLightColor = mainColor
                set1.color = mainColor

                set1.lineWidth = 1f
                set1.circleRadius = 3f

                set1.setDrawCircleHole(false)
                val colors: MutableList<Int> = ArrayList()
                for (i in values.indices) {
                    if (i == 0 || i == values.size - 1) {
                        colors.add(mainColor)
                    } else {
                        colors.add(Color.TRANSPARENT)
                    }
                }
                set1.circleColors = colors

                set1.setDrawFilled(false)
                set1.setDrawValues(false)
                val dataSets = ArrayList<ILineDataSet>()
                dataSets.add(set1)

                val data = LineData(dataSets)

                chart.data = data
            }
            chart.highlightValue(values.last().x,0)
        }
    }

    private fun getEmptyChartData(): List<Entry> {
        val values = ArrayList<Entry>()

        for (i in 0 until 30) {
            values.add(Entry(i.toFloat(), 0f))
        }

        return values
    }

}