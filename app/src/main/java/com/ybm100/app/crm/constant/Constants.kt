package com.ybm100.app.crm.constant

/**
 * @author: yuh<PERSON><PERSON>
 * @time: 2019/3/28 下午5:09.
 * projectName: XyyBeanSprouts.
 * Description:
 */
class Constants {
    companion object {
        const val DATE_TYPE = "DATE_TYPE"
        const val MERCHANT_ID = "MERCHANT_ID"  // 药店id
        const val KEYWORD = "keyword"
        const val PERIOD = "period"
        const val INVOICE_EXAMPLE = "开具<font color='#FE3D3D'>增值税专用发票，</font>请完整、准确提供以下信息并<font color='#FE3D3D'>加盖公章：名称、纳税人识别号、地址、电话、开户行、账号。</font>"
    }

    /*ChannelType 0:首页  1:我的*/
    interface ChannelType {
        companion object {
            const val HOME_PAGE = 0 // 首 页     进订单列表
            const val MINE_PAGE = 1 // 订单管理   进订单列表
            const val RANK_PAGE = 2
            const val REFUND_ORDER_PAGE = 3 // 退单管理   进订单列表
        }
    }

    /**
     * 我的客户 tab
     */
    interface DrugTabType {
        companion object {

            /**
             * 客户资质
             */
            const val APTITUDES_PAGE = 3

            /**
             * 发票类型
             */
            const val INVOICETYPE_PAGE = 4

        }
    }

    interface ElectronicType {
        companion object {
            /**
             * 1:未生效
             */
            const val TYPE_UN_FORCE = 1

            /**
             * 2:生效中
             */
            const val TYPE_IN_FORCE = 2

            /**
             * 3:已过期
             */
            const val TYPE_OVERDUE = 3

            /**
             * 4:已冻结
             */
            const val TYPE_BLOCK = 4

            /**
             *  5:待签约
             */
            const val TYPE_SIGNING = 5

            /**
             * 6:审核中
             */
            const val TYPE_IN_REVIEW = 6

            /**
             * 7:待申请
             */
            const val TYPE_APPLICATION = 7
        }
    }

    interface GoodsRecommendCheck {
        companion object {
            /**
             * 1:单选
             */
            const val FLOW_TAG_CHECKED_SINGLE = 1

            /**
             * 2:多选
             */
            const val FLOW_TAG_CHECKED_MULTI = 2

            /**
             * 3：多选有默认值
             */
            const val FLOW_TAG_CHECKED_MULTI_DEFAULT = 3

            /**
             * 分类1
             */
            const val TYPE_CATEGORY_1 = 1

            /**
             * 分类2
             */
            const val TYPE_CATEGORY_2 = 2
        }
    }

    object Task {
        const val ARG_TASK_ID = "ARG_TASK_ID_TASK"
        const val ARG_TASK_TIME_FILTER = "ARG_TASK_TIME_FILTER_TASK"
        const val ARG_FROM = "ARG_FROM_TASK"
        const val ARG_BRANCH_CODE = "ARG_BRANCH_CODE"

        const val ARG_GOODS_STR = "ARG_GOODS_STR_TASK"
        const val ARG_CUSTOMERS_STR = "ARG_CUSTOMERS_STR_TASK"

        const val ARG_SEARCH_TYPE = "ARG_SEARCH_TYPE_TASK"
        const val ARG_GOODS_LIST = "ARG_GOODS_LIST_TASK"
        const val ARG_CUSTOMERS_LIST = "ARG_CUSTOMERS_LIST_TASK"
        const val ARG_KEY_WORDS = "ARG_KEY_WORDS_TASK"
        const val ARG_SEARCH_GOODS_LIST = "ARG_SEARCH_GOODS_LIST_TASK"
        const val ARG_SEARCH_CUSTOMERS_LIST = "ARG_SEARCH_CUSTOMERS_LIST_TASK"

        const val ARG_IS_CUSTOMER_SEARCH = "ARG_IS_CUSTOMER_SEARCH_TASK"


        const val MC_TASK_PRODUCT_RECOMMEND = "mc-taskproduct-recommend"
        const val MC_TASK_PRODUCT_RECOMMEND_WITH_PRODUCT = "mc-taskproduct-recommendwithproduct"
        const val MC_TASK_PRODUCT_RECOMMEND_TYPE1 = "mc-taskproduct-recommendtype1"
        const val MC_TASK_PRODUCT_RECOMMEND_WITH_CLIENT = "mc-taskproduct-recommendwithclient"
        const val MC_TASK_PRODUCT_RECOMMEND_TYPE2 = "mc-taskproduct-recommendtype2"
        const val MC_TASK_PRODUCT_RECOMMEND_TYPE3 = "mc-taskproduct-recommendtype3"
        const val MC_PRODUCT_DETAIL_RECOMMEND = "mc-productdetail-recommend"
        const val MC_PRODUCT_DETAIL_PROMOTION = "mc-productdetail-promotion"
        const val MC_TASK_PRODUCT_SEARCH = "mc-taskproduct-search"
        const val MC_TASK_PRODUCT_RECOMMEND_WITH_CLIENT_SEARCH = "mc-taskproduct-recommendwithclient-search"

        /**
         * 任务商品详情
         */
        const val CONSTANT_SEARCH_TYPE_GOODS = 1

        /**
         * 任务详情 - 推荐
         */
        const val CONSTANT_TYPE_GOODS_RECOMMENDATION = 2

        /**
         * 任务商品推荐 - 搜索任务商品界面
         */
        const val CONSTANT_SEARCH_TYPE_GOODS_RECOMMENDATION = 3

        /**
         * 搜索 - 客户
         */
        const val CONSTANT_SEARCH_TYPE_CUSTOMERS = 4

        /**
         * 从商品详情进入
         */
        const val CONSTANT_FROM_GOODS_DETAIL = 5

        /**
         * 从任务商品推荐进入
         */
        const val CONSTANT_FROM_TASK_RECOMMENDATION = 6
    }

    object GoodsManagement {
        /**
         * Key, 发现 - 商品管理 TAB
         */
        const val ARG_GOODS_MANAGEMENT_SELECTED_TAB = "ARG_GOODS_MANAGEMENT_SELECTED_TAB_GOODS_MANAGEMENT"

        /**
         * ARG_GOODS_MANAGEMENT_SELECTED_TAB value
         * 商品管理 - 我的收藏, 数值固定不能变更
         */
        const val CONSTANT_GOODS_MANAGEMENT_TAB_MY_COLLECTION = 0

        /**
         * ARG_GOODS_MANAGEMENT_SELECTED_TAB value
         * 商品管理 - 全部商品, 数值固定不能变更
         */
        const val CONSTANT_GOODS_MANAGEMENT_TAB_ALL_GOODS = 1

        /**
         * Key, 客户详情 - 商品管理 TAB
         */
        const val ARG_CUSTOMER_GOODS_MANAGEMENT_SELECTED_TAB = "ARG_CUSTOMER_GOODS_MANAGEMENT_SELECTED_TAB_CUSTOMER_GOODS_MANAGEMENT"

        /**
         * ARG_CUSTOMER_GOODS_MANAGEMENT_SELECTED_TAB value
         * 客户详情 - 商品管理 - 全部商品，数值固定不能变更
         */
        const val CONSTANT_CUSTOMER_GOODS_MANAGEMENT_TAB_ALL_GOODS = 0

        /**
         * ARG_CUSTOMER_GOODS_MANAGEMENT_SELECTED_TAB value
         * 客户详情 - 商品管理 - 常购商品，数值固定不能变更
         */
        const val CONSTANT_CUSTOMER_GOODS_MANAGEMENT_TAB_FREQUENTLY_PURCHASED_GOODS = 1

        /**
         * ARG_CUSTOMER_GOODS_MANAGEMENT_SELECTED_TAB value
         * 客户详情 - 商品管理 - 购物车商品，数值固定不能变更
         */
        const val CONSTANT_CUSTOMER_GOODS_MANAGEMENT_TAB_SHOPPING_CART_GOODS = 2

        /**
         * Key, GoodsManagement merchantID
         */
        const val ARG_GOODS_MANAGEMENT_MERCHANT_ID = "ARG_MERCHANT_ID_GOODS_MANAGEMENT"

        const val ARG_SEARCH_KEYWORD = "ARG_SEARCH_KEYWORD_GOODS_MANAGEMENT"

        /**
         * Key, GoodsManagementFragment type
         */
        const val ARG_FRAGMENT_TYPE = "ARG_FRAGMENT_TYPE_GOODS_MANAGEMENT_FRAGMENT"

        /**
         * Key, GoodsManagement areaCode
         */
        const val ARG_GOODS_MANAGEMENT_AREA_CODE = "ARG_AREA_CODE_GOODS_MANAGEMENT"

        /**
         * ARG_FRAGMENT_TYPE value 发现-商品管理-我的收藏
         * 是否展示筛选               是
         * 是否展示收藏               否
         * 是否侧滑删除               是
         * 是否展示复选框             否
         * 是否展示客户商品标签        否
         * 是否可点击查看详情          是
         * 是否回传数据               否
         * 是否展示推荐               是
         * 是否展示购物车             否
         */
        const val CONSTANT_FRAGMENT_TYPE_MY_COLLECTION = 0

        /**
         * ARG_FRAGMENT_TYPE value 发现-商品管理-我的收藏-搜索
         * 是否展示筛选               是
         * 是否展示收藏               否
         * 是否侧滑删除               是
         * 是否展示复选框              否
         * 是否展示客户商品标签        否
         * 是否可点击查看详情          是
         * 是否回传数据               是
         * 是否展示推荐               否
         * 是否展示购物车             否
         */
        const val CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_SEARCH = 1

        /**
         * ARG_FRAGMENT_TYPE value 发现-商品管理-我的收藏-推荐
         * 是否展示筛选               是
         * 是否展示收藏               否
         * 是否侧滑删除               否
         * 是否展示复选框              是
         * 是否展示客户商品标签        否
         * 是否可点击查看详情          否
         * 是否回传数据               否
         * 是否展示推荐               否
         * 是否展示购物车             是
         */
        const val CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_RECOMMENDATION = 2

        /**
         * ARG_FRAGMENT_TYPE value 发现-商品管理-我的收藏-推荐-搜索
         * 是否展示筛选               是
         * 是否展示收藏               否
         * 是否侧滑删除               否
         * 是否展示复选框              是
         * 是否展示客户商品标签        否
         * 是否可点击查看详情          否
         * 是否回传数据               是
         * 是否展示推荐               否
         * 是否展示购物车             是
         */
        const val CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_RECOMMENDATION_SEARCH = 3

        /**
         * ARG_FRAGMENT_TYPE value 发现-商品管理-全部商品
         * 是否展示筛选               是
         * 是否展示收藏               是
         * 是否侧滑删除               否
         * 是否展示复选框              否
         * 是否展示客户商品标签        否
         * 是否可点击查看详情          是
         * 是否回传数据               否
         * 是否展示推荐               否
         * 是否展示购物车             否
         */
        const val CONSTANT_FRAGMENT_TYPE_ALL_GOODS = 4

        /**
         * ARG_FRAGMENT_TYPE value 发现-商品管理-全部商品-搜索
         * 是否展示筛选               是
         * 是否展示收藏               是
         * 是否侧滑删除               否
         * 是否展示复选框              否
         * 是否展示客户商品标签        否
         * 是否可点击查看详情          是
         * 是否回传数据               是
         * 是否展示推荐               否
         * 是否展示购物车             否
         */
        const val CONSTANT_FRAGMENT_TYPE_ALL_GOODS_SEARCH = 5

        /**
         * ARG_FRAGMENT_TYPE value 客户-商品管理-全部商品
         * 是否展示筛选               是
         * 是否展示收藏               是
         * 是否侧滑删除               否
         * 是否展示复选框             否
         * 是否展示客户商品标签        是
         * 是否可点击查看详情          是
         * 是否回传数据               否
         * 是否展示推荐               是
         * 是否展示购物车             否
         */
        const val CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS = 6

        /**
         * ARG_FRAGMENT_TYPE value 客户-商品管理-全部商品-搜索
         * 是否展示筛选               是
         * 是否展示收藏               是
         * 是否侧滑删除               否
         * 是否展示复选框             否
         * 是否展示客户商品标签        是
         * 是否可点击查看详情          是
         * 是否回传数据               是
         * 是否展示推荐               是
         * 是否展示购物车             否
         */
        const val CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_SEARCH = 7

        /**
         * ARG_FRAGMENT_TYPE value 客户-商品管理-全部商品-推荐
         * 是否展示筛选               是
         * 是否展示收藏               否
         * 是否侧滑删除               否
         * 是否展示复选框             是
         * 是否展示客户商品标签        是
         * 是否可点击查看详情          否
         * 是否回传数据               否
         * 是否展示推荐               否
         * 是否展示购物车             是
         */
        const val CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_RECOMMENDATION = 8

        /**
         * ARG_FRAGMENT_TYPE value 客户-商品管理-全部商品-推荐-搜索
         * 是否展示筛选               是
         * 是否展示收藏               否
         * 是否侧滑删除               否
         * 是否展示复选框             是
         * 是否展示客户商品标签        是
         * 是否可点击查看详情          否
         * 是否回传数据               是
         * 是否展示推荐               否
         * 是否展示购物车             是
         */
        const val CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_RECOMMENDATION_SEARCH = 9

        const val CONSTANT_ADAPTER_CART = 10

        const val KEY_SHOULD_SHOW_GUIDE_VIEW = "KEY_SHOULD_SHOW_GUIDE_VIEW_GOODS_MANAGEMENT"


        const val ACTIVITY_REQUEST_CODE_SELECT_CUSTOMER = 10243
    }
}
