import 'package:flutter/material.dart';
import 'dart:ui';

import 'package:XyyBeanSproutsFlutter/utils/color_utils.dart';
import 'package:XyyBeanSproutsFlutter/visit/bean/detail/merchant_basic_info.dart';

class VisitTypeCode {
  ///培训会议兼容历史数据
  static String getImageByType(int type) {
    String image = "";
    switch (type) {
      case 1: //电话拜访
        image = "assets/images/visit/icon_schedule_type1.png";
        break;
      case 9: //陪访
        image = "assets/images/visit/icon_schedule_type3.png";
        break;
      case 2: //上门拜访
        image = "assets/images/visit/icon_schedule_type2.png";
        break;
      case 3: //私海拜访
        image = "assets/images/visit/icon_schedule_type3.png";
        break;
      case 4: //陌生拜访
        image = "assets/images/visit/icon_schedule_type4.png";
        break;
      case 5: //培训
        image = "assets/images/visit/icon_schedule_type5.png";
        break;
      case 6: //会议
        image = "assets/images/visit/icon_schedule_type6.png";
        break;
    }
    return image;
  }

  static String? getNameByType(int type) {
    String? name;
    switch (type) {
      case 1: //电话拜访
        name = "电话拜访";
        break;
      case 9: //陪访
        name = "陪访";
        break;
      case 2: //上门拜访
        name = "上门拜访";
        break;
      case 3: //私海拜访
        name = "私海拜访";
        break;
      case 4: //陌生拜访
        name = "陌生拜访";
        break;
      case 5: //培训
        name = "培训";
        break;
      case 6: //会议
        name = "会议";
        break;
    }
    return name;
  }

  ///是否展示有效
  static bool showEffective(int type) {
    switch (type) {
      case 1: //电话拜访
      case 9: //陪访
      case 2: //上门拜访
      case 4: //陌生拜访
        return true;
    }
    return false;
  }

  ///是否展示有效拜访
  static bool isEffective(int type) {
    return type == 1;
  }

    ///是否展示去完善按钮
  static bool showPerfectBtn(int type) {
    return type == 2 ? false : true;
  }

  ///系统拨打电话时长
  static bool showCallLogTime(int type) {
    switch (type) {
      case 1: //电话拜访
        return true;
    }
    return false;
  }

  ///陪访
  static bool showAccompanyPeople(String? type) {
    switch (type) {
      case "陪访": //陪访
        return true;
    }
    return false;
  }

  ///是否展示提醒时间
  static bool showRemindTime(int type) {
    switch (type) {
      case 5: //培训
        return true;
      case 6: //会议
        return true;
    }
    return false;
  }

  ///是否展示联系人
  static bool showContact(int type) {
    switch (type) {
      case 5: //培训
      case 6: //会议
        return false;
    }
    return true;
  }

  ///是否展示联系电话
  static bool showContactPhone(int type) {
    switch (type) {
      case 3: //私海拜访
      case 4: //陌生拜访
        return true;
    }
    return false;
  }

  ///是否kp
  static bool showKP(int type) {
    switch (type) {
      case 5: //培训
      case 6: //会议
        return false;
    }
    return true;
  }

  ///是否展示拜访事由
  static bool showVisitReason(int type) {
    switch (type) {
      case 5: //培训
      case 6: //会议
        return false;
    }
    return true;
  }

  ///是否展示地址
  static bool showAddress(int type) {
    switch (type) {
      case 5: //培训
      case 6: //会议
        return false;
    }
    return true;
  }



  ///是否展示经营状况
  static bool showOperatingState(MerchantBasicInfo? obj, String? areaSizeStr, String? monthlySalesStr) {
    var obkeys = ["medicalInsuranceText", "clerkNum", "aroundEnvName", "buyersTypeName", "buyersAmountText", "buySkusName", "mainlyConsumeMedTypesName", "needPullSalesText", "needMerchantDiagnoseText", "needClerkTrainsText", "shortOfTypes", "purchaseWay", "merchantDemand"];
    Map<String, dynamic> objMap = obj!.toJson();
    bool nonull = false;
    for (var i = 0; i < obkeys.length; i++) {
      String key = obkeys[i];
      if(objMap[key] != null && objMap[key] != ""){
        nonull = true;
      }
    }
    if(areaSizeStr != "—" || monthlySalesStr != "—"){
      nonull = true;
    }
    return nonull;
  }

  static Color getDividerColorByType(int type) {
    Color color = ColorUtils().color_292933;
    switch (type) {
      case 1: //电话拜访
        color = ColorUtils().color_FF7052;
        break;
      case 9: //陪访
      case 4: //陌生拜访
        color = ColorUtils().color_35c561;
        break;
      case 2: //上门拜访
        color = ColorUtils().color_007AFF;
        break;
      case 3: //私海拜访
        color = ColorUtils().color_3FB6DC;
        break;
      case 5: //培训
        color = ColorUtils().color_F9E900;
        break;
      case 6: //会议
        color = ColorUtils().color_7540EE;
        break;
    }
    return color;
  }
}
