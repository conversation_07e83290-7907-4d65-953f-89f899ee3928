// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'visit_plan_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PlanListModel _$PlanListModelFromJson(Map<String, dynamic> json) {
  return PlanListModel()
    ..id = json['id']
    ..customerId = json['customerId']
    ..customerName = json['customerName']
    ..customerType = json['customerType']
    ..distance = json['distance']
    ..address = json['address']
    ..level = json['level']
    ..merchantStatus = json['merchantStatus']
    ..merchantStatusText = json['merchantStatusText']
    ..latestOrderTime = json['latestOrderTime']
    ..latestVisitTime = json['latestVisitTime']
    ..planTagText = json['planTagText']
    ..planTag = json['planTag']
    ..proposeReason = json['proposeReason']
    ..licenseValidateMust = json['licenseValidateMust']
    ..licenseValidateIssue = json['licenseValidateIssue']
    ..poiLat = json['poiLat']
    ..poiLng = json['poiLng']
    ..selected = false;
}

Map<String, dynamic> _$PlanListModelToJson(PlanListModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'customerId': instance.customerId,
      'customerName': instance.customerName,
      'customerType': instance.customerType,
      'distance': instance.distance,
      'address': instance.address,
      'level': instance.level,
      'merchantStatus': instance.merchantStatus,
      'merchantStatusText': instance.merchantStatusText,
      'latestOrderTime': instance.latestOrderTime,
      'latestVisitTime': instance.latestVisitTime,
      'planTagText': instance.planTagText,
      'planTag': instance.planTag,
      'proposeReason': instance.proposeReason,
      'licenseValidateMust': instance.licenseValidateMust,
      'licenseValidateIssue': instance.licenseValidateIssue,
      'poiLat': instance.poiLat,
      'poiLng': instance.poiLng,
      'selected' : instance.selected,
    };

VisitPlanDataModel _$VisitPlanDataModelFromJson(Map<String, dynamic> json) {
  return VisitPlanDataModel()
    ..id = json['id']
    ..isSelected = json['isSelected'] as bool?
    ..planNums = json['planNums']
    ..planMax = json['planMax']
    ..planList = (json['planList'] as List<dynamic>?)
        ?.map((e) => PlanListModel.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$VisitPlanDataModelToJson(VisitPlanDataModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'isSelected': instance.isSelected,
      'planNums': instance.planNums,
      'planMax': instance.planMax,
      'planList': instance.planList,
    };
