import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'row_bean.g.dart';

@JsonSerializable()
class RowBean extends BaseModel<RowBean> {
  int? id; // 日程id
  String? scheduleTheme; // 日程主题
  String? contactor; //联系人姓名
  int? type; // 日程类型  1电话拜访，2上门拜访，5培训，6会议，3私海拜访，4陌生拜访
  int? visitType; // 新接口日程类型  1电话拜访，2上门拜访，5培训，6会议，3私海拜访，4陌生拜访
  String? creatorName; // 销售姓名
  String? mobile; // 销售电话
  bool? effective; // 是否有效日程
  int? createTime; //创建时间
  String? timeFormat; //拜访时间
  String? typeText; //拜访方式
  String? visitReasonText; //拜访目的
  String? remark; //拜访总结
  int? perfect; //完善状态
  String? merchantName; //拜访对象
  int? merchantId; //拜访客户id
  int? customerId; //客户编码
  String? talkTimeText; //通话时长 格式化后
  int? talkTime; //通话时长
  int? isEffective; //是否kp
  int? poiId;
  dynamic registerFlag; // 是否注册
  String? visitTypeText;//拜访类型文本
  String? visitDemo;//总结

  RowBean();

  @override
  RowBean fromJsonMap(Map<String, dynamic> json) {
    return RowBean.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$RowBeanToJson(this);
  }

  factory RowBean.fromJson(Map<String, dynamic> json) =>
      _$RowBeanFromJson(json);
}
