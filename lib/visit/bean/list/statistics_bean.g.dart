// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'statistics_bean.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StatisticsBean _$StatisticsBeanFromJson(Map<String, dynamic> json) {
  return StatisticsBean()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : StatisticsBean.fromJson(json['data'] as Map<String, dynamic>)
    ..cnt = json['cnt'] as int
    ..effectiveCnt = json['effectiveCnt'] as int
    ..doorCnt = json['doorCnt'] as int
    ..effDoorCnt = json['effDoorCnt'] as int
    ..effPhoneCnt = json['effPhoneCnt'] as int
    ..phoneCnt = json['phoneCnt'] as int;
}

Map<String, dynamic> _$StatisticsBeanToJson(StatisticsBean instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'cnt': instance.cnt,
      'effectiveCnt': instance.effectiveCnt,
      'doorCnt': instance.doorCnt,
      'effDoorCnt': instance.effDoorCnt,
      'effPhoneCnt': instance.effPhoneCnt,
      'phoneCnt': instance.phoneCnt,
    };
