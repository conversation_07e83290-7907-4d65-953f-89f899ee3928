import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:XyyBeanSproutsFlutter/visit/bean/visit_tag_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'visit_status_tag_data.g.dart';

@JsonSerializable()
class VisitStatusTagData extends BaseModelV2<VisitStatusTagData> {
  List<VisitTagData>? dataTimeList;
  List<VisitTagData>? ifEffectiveList;
  List<VisitTagData>? scheduleTypeList;
  List<VisitTagData>? visitReasonList;
  List<VisitTagData>? perfectList;

  VisitStatusTagData();

  @override
  VisitStatusTagData fromJsonMap(Map<String, dynamic> json) {
    return VisitStatusTagData.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$VisitStatusTagDataToJson(this);
  }

  factory VisitStatusTagData.fromJson(Map<String, dynamic> json) =>
      _$VisitStatusTagDataFromJson(json);
}
