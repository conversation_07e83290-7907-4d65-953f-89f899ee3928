// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'merchant_basic_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MerchantBasicInfo _$MerchantBasicInfoFromJson(Map<String, dynamic> json) {
  return MerchantBasicInfo()
    ..areaSize = json['areaSize']
    ..aroundEnvName = json['aroundEnvName'] as String?
    ..buySkus = json['buySkus']
    ..buySkusName = json['buySkusName'] as String?
    ..buyersAmountText = json['buyersAmountText'] as String?
    ..buyersTypeName = json['buyersTypeName'] as String?
    ..clerkNum = json['clerkNum'] as String?
    ..frameVarieties = json['frameVarieties'] as String?
    ..mainlyConsumeMedTypesName = json['mainlyConsumeMedTypesName'] as String?
    ..medicalInsuranceText = json['medicalInsuranceText'] as String?
    ..merchantDemand = json['merchantDemand'] as String?
    ..merchantTypeName = json['merchantTypeName'] as String?
    ..monthBuyAmt = json['monthBuyAmt'] as String?
    ..monthlySales = json['monthlySales']
    ..needClerkTrainsText = json['needClerkTrainsText'] as String?
    ..needMerchantDiagnoseText = json['needMerchantDiagnoseText'] as String?
    ..needPullSalesText = json['needPullSalesText'] as String?
    ..purchaseWay = json['purchaseWay'] as String?
    ..remark = json['remark'] as String?
    ..shortOfTypes = json['shortOfTypes'] as String?;
}

Map<String, dynamic> _$MerchantBasicInfoToJson(MerchantBasicInfo instance) =>
    <String, dynamic>{
      'areaSize': instance.areaSize,
      'aroundEnvName': instance.aroundEnvName,
      'buySkus': instance.buySkus,
      'buySkusName': instance.buySkusName,
      'buyersAmountText': instance.buyersAmountText,
      'buyersTypeName': instance.buyersTypeName,
      'clerkNum': instance.clerkNum,
      'frameVarieties': instance.frameVarieties,
      'mainlyConsumeMedTypesName': instance.mainlyConsumeMedTypesName,
      'medicalInsuranceText': instance.medicalInsuranceText,
      'merchantDemand': instance.merchantDemand,
      'merchantTypeName': instance.merchantTypeName,
      'monthBuyAmt': instance.monthBuyAmt,
      'monthlySales': instance.monthlySales,
      'needClerkTrainsText': instance.needClerkTrainsText,
      'needMerchantDiagnoseText': instance.needMerchantDiagnoseText,
      'needPullSalesText': instance.needPullSalesText,
      'purchaseWay': instance.purchaseWay,
      'remark': instance.remark,
      'shortOfTypes': instance.shortOfTypes,
    };
