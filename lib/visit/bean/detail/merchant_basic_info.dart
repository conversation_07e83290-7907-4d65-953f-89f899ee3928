import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'merchant_basic_info.g.dart';

@JsonSerializable()
class MerchantBasicInfo extends BaseModelV2<MerchantBasicInfo> {
  dynamic areaSize;
  String? aroundEnvName;
  dynamic buySkus;
  String? buySkusName;
  String? buyersAmountText;
  String? buyersTypeName;
  String? clerkNum;
  String? frameVarieties;
  String? mainlyConsumeMedTypesName;
  String? medicalInsuranceText;
  String? merchantDemand;
  String? merchantTypeName;
  String? monthBuyAmt;
  dynamic monthlySales;
  String? needClerkTrainsText;
  String? needMerchantDiagnoseText;
  String? needPullSalesText;
  String? purchaseWay;
  String? remark;
  String? shortOfTypes;

  MerchantBasicInfo();

  @override
  MerchantBasicInfo fromJsonMap(Map<String, dynamic> json) {
    return MerchantBasicInfo.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$MerchantBasicInfoToJson(this);
  }

  factory MerchantBasicInfo.fromJson(Map<String, dynamic> json) =>
      _$MerchantBasicInfoFromJson(json);
}
