// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'visit_detail_bean.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VisitDetailBean _$VisitDetailBeanFromJson(Map<String, dynamic> json) {
  return VisitDetailBean()
    ..address = json['address'] as String?
    ..domainPath = json['domainPath'] as String?
    ..merchantBasicInfo = json['merchantBasicInfo'] == null
        ? null
        : MerchantBasicInfo.fromJson(
            json['merchantBasicInfo'] as Map<String, dynamic>)
    ..personalSchedule = json['personalSchedule'] == null
        ? null
        : ScheduleBean.fromJson(
            json['personalSchedule'] as Map<String, dynamic>)
    ..merchantVisit = json['merchantVisit'] == null
        ? null
        : MerchantVisitBean.fromJson(
            json['merchantVisit'] as Map<String, dynamic>)
    ..task = json['task'] == null
        ? null
        : TaskBean.fromJson(json['task'] as Map<String, dynamic>)
    ..isEffective = json['isEffective'] as int?
    ..invalidReason = json['invalidReason'] as String?
    ..kpFlag = json["kpFlag"] == null ? null : json["kpFlag"];
}

Map<String, dynamic> _$VisitDetailBeanToJson(VisitDetailBean instance) =>
    <String, dynamic>{
      'address': instance.address,
      'domainPath': instance.domainPath,
      'merchantBasicInfo': instance.merchantBasicInfo,
      'personalSchedule': instance.personalSchedule,
      'merchantVisit': instance.merchantVisit,
      'task': instance.task,
      'isEffective': instance.isEffective,
      'invalidReason': instance.invalidReason,
      'kpFlag': instance.kpFlag,
    };
