import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

import 'merchant_basic_info.dart';
import 'merchant_visit_bean.dart';
import 'schedule_bean.dart';
import 'task_bean.dart';

part 'visit_detail_bean.g.dart';

@JsonSerializable()
class VisitDetailBean extends BaseModelV2<VisitDetailBean> {
  String? address;
  String? domainPath;
  
  MerchantBasicInfo? merchantBasicInfo;
  ScheduleBean? personalSchedule;
  MerchantVisitBean? merchantVisit;
  TaskBean? task;
  int? isEffective;
  String? invalidReason;
  bool? kpFlag;

  VisitDetailBean();

  @override
  VisitDetailBean fromJsonMap(Map<String, dynamic> json) {
    return VisitDetailBean.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$VisitDetailBeanToJson(this);
  }

  factory VisitDetailBean.fromJson(Map<String, dynamic> json) =>
      _$VisitDetailBeanFromJson(json);
}
