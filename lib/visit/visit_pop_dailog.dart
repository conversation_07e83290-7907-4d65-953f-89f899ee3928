import 'package:XyyBeanSproutsFlutter/licence/widget/triangle_up_widget.dart';
import 'package:XyyBeanSproutsFlutter/utils/color_utils.dart';
import 'package:flutter/material.dart';

///自定义选择弹窗
class PopDialog {
  static void showPop({required BuildContext context, required double topP}) {
    _buildMenusView(double topP) {
      TextStyle titleStyle = TextStyle(
          fontSize: 14,
          color: ColorUtils().color_292933,
          fontWeight: FontWeight.bold);
      TextStyle tipStyle =
          TextStyle(fontSize: 12, color: ColorUtils().color_666666);
      return Positioned(
        left: 0,
        right: 0,
        top: topP,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Container(
              alignment: Alignment.bottomRight,
              padding: EdgeInsets.only(right: 100),
              child: TriangleUpWidget(height: 10, width: 14),
            ),
            Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(2),
                    color: Colors.white),
                margin: EdgeInsets.only(left: 10, right: 10),
                padding: EdgeInsets.fromLTRB(10, 10, 15, 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Text('有效上门拜访指：', style: titleStyle),
                    SizedBox(height: 5),
                    Text('拜访的是KP：', style: tipStyle),
                    SizedBox(height: 15),
                    Text('有效电话拜访指：', style: titleStyle),
                    SizedBox(height: 5),
                    Text(
                        '1、电话拜访为KP，然后KP电话拜访总数除以5，其中同一个客户一天拜访多次记为一个电话拜访、去除未完善的电话拜访 \n2、如果电话拜访，选择联系人时，选择了无效联系人拜访的选项，直接记为一个无效拜访，不做计算。',
                        style: tipStyle),
                  ],
                ))
          ],
        ),
      );
    }

    showDialog(
        context: context,
        barrierDismissible: true,
        builder: (context) {
          return BasePopMenus(child: _buildMenusView(topP));
        });
  }
}

class BasePopMenus extends Dialog {
  BasePopMenus({
    Key? key,
    this.child,
  }) : super(key: key);

  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Stack(
        fit: StackFit.expand,
        children: <Widget>[
          GestureDetector(onTap: () => Navigator.pop(context)),
          child!
        ],
      ),
    );
  }
}
