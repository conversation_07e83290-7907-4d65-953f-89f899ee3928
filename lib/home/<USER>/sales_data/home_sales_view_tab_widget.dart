import 'dart:collection';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/dialog/widget_loading.dart';
import 'package:XyyBeanSproutsFlutter/common/tabs/custom_tab_controller.dart';
import 'package:XyyBeanSproutsFlutter/common/tabs/custom_tabs.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_org_param_model.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_sales_item_model.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/sales_data/home_sales_day_content.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/sales_data/home_sales_month_content.dart';
import 'package:flutter/material.dart';

class HomeSalesViewTabWidget extends StatefulWidget {
  final HomeORGChooseNotifier orgController;

  final ValueNotifier<int> indexController;

  HomeSalesViewTabWidget(
      {required this.orgController, required this.indexController});

  @override
  State<StatefulWidget> createState() {
    return HomeSalesViewTabWidgetSate();
  }
}

class HomeSalesViewTabWidgetSate extends State<HomeSalesViewTabWidget>
    with SingleTickerProviderStateMixin {
  late CustomTabController _controller;

  List<String> titles = ['自营', 'POP', '控销', '优选', '甄选'];

  /// 当前展示用的数据
  late List<ValueNotifier<HomeSalesItemModel>> sourceData;

  /// 天数据缓存
  List<ValueNotifier<HomeSalesItemModel>> daySourceData = [
    ValueNotifier(HomeSalesItemModel()),
    ValueNotifier(HomeSalesItemModel()),
    ValueNotifier(HomeSalesItemModel()),
    ValueNotifier(HomeSalesItemModel()),
    ValueNotifier(HomeSalesItemModel()),
  ];

  /// 月数据缓存
  List<ValueNotifier<HomeSalesItemModel>> monthSourceData = [
    ValueNotifier(HomeSalesItemModel()),
    ValueNotifier(HomeSalesItemModel()),
    ValueNotifier(HomeSalesItemModel()),
    ValueNotifier(HomeSalesItemModel()),
    ValueNotifier(HomeSalesItemModel()),
  ];

  /// load控制器数组
  List<WidgetLoadingController> loadingSource = [
    WidgetLoadingController(false),
    WidgetLoadingController(false),
    WidgetLoadingController(false),
    WidgetLoadingController(false),
    WidgetLoadingController(false),
  ];

  @override
  void initState() {
    // 初始为天的数据
    this.sourceData = this.daySourceData;

    this._controller = CustomTabController(
      length: this.titles.length,
      initialIndex: 0,
      vsync: this,
      animationDuration: Duration.zero,
    );

    widget.orgController.addListener(() {
      /// 监听到刷新事件 清理本地存储
      this.resetSourceData();

      /// 数据项重新赋值
      this.sourceData = widget.indexController.value == 0
          ? this.daySourceData
          : this.monthSourceData;

      /// 默认请求 自营数据
      this.requestSalesData(_controller.index);
    });

    widget.indexController.addListener(() {
      /// 天/月 切换时会调用此方法刷新页面。
      /// 此处切数据源 并请求当前选中item的数据，
      /// 此处不会重置数据，重置数据仅在刷新时处理
      this.sourceData = widget.indexController.value == 0
          ? this.daySourceData
          : this.monthSourceData;
      // 切天 / 月 展示时处理数据
      this.requestSalesData(this._controller.index);
    });

    super.initState();
  }

  /// 重置缓存的数据
  void resetSourceData() {
    this.daySourceData.forEach((element) {
      element.value = HomeSalesItemModel();
    });
    this.monthSourceData.forEach((element) {
      element.value = HomeSalesItemModel();
    });
  }

  @override
  void dispose() {
    this.loadingSource.forEach((element) {
      element.dispose();
    });
    this._controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          SizedBox(
            height: 38,
            child: Stack(
              children: [
                Container(
                  padding: EdgeInsets.only(left: 5, right: 5, top: 10),
                  child: Row(
                    children: this.backgroudItem(),
                  ),
                ),
                Container(
                  padding: EdgeInsets.only(top: 10),
                  child: CustomTabBar(
                    controller: this._controller,
                    isScrollable: false,
                    indicator: BoxDecoration(
                      color: Color(0xFFFFF0FBF7),
                      borderRadius: BorderRadius.circular(14),
                      border: Border.all(width: 0.5, color: Color(0xFF00B377)),
                    ),
                    indicatorPadding: EdgeInsets.only(left: 5, right: 5),
                    indicatorColor: Color(0xFFFFF0FBF7),
                    indicatorWeight: 0,
                    // 指示器高度不能设置为0
                    unselectedLabelColor: Color(0xFF9494A6),
                    unselectedLabelStyle: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                    labelColor: Color(0xFF00B377),
                    labelStyle: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                    labelPadding: EdgeInsets.only(left: 5, right: 5),
                    padding: EdgeInsets.only(left: 5, right: 5),
                    onTap: (index) {
                      print("guan track ${getActionType(index)}");
                      XYYContainer.bridgeCall('event_track',
                          parameters: HashMap.from(
                              {"action_type": getActionType(index)}));
                      this.requestSalesData(index);
                    },
                    tabs: tabs(),
                  ),
                ),
              ],
            ),
          ),
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Container(
              margin: EdgeInsets.only(top: 10, left: 10, right: 10),
              decoration: BoxDecoration(
                color: Color(0xFFFAFBFC),
                borderRadius: BorderRadius.circular(8),
              ),
              child: SizedBox(
                height: 119,
                child: ValueListenableBuilder(
                  valueListenable: widget.indexController,
                  builder: (BuildContext context, int value, Widget? child) {
                    return CustomTabBarView(
                      physics: new NeverScrollableScrollPhysics(),
                      controller: this._controller,
                      children: this.getTabViews(value),
                    );
                  },
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  List<Widget> tabs() {
    return this.titles.map((e) => Container(
              height: 28,
              child: Tab(
                text: e,
              ),
            )).toList();
  }

  List<Widget> backgroudItem() {
    return this.titles.map((e) => Expanded(
            child: Container(
              margin: EdgeInsets.only(left: 5, right: 5),
              decoration: BoxDecoration(
                color: Color(0xFFFAFBFC),
                borderRadius: BorderRadius.circular(14),
              ),
            ),
          ),
        ).toList();
  }

  List<Widget> getTabViews(int index) {
    List<Widget> list = [];
    this.sourceData.asMap().forEach((itemIndex, notifier) {
      list.add(ValueListenableBuilder(
        valueListenable: notifier,
        builder:
            (BuildContext context, HomeSalesItemModel value, Widget? child) {
          return WidgetLoading(
            loadingController: this.loadingSource[itemIndex],
            child: index == 0
                ? HomeSalesDayContent(value) // 每天
                : HomeSalesMonthContent(value), // 本月
          );
        },
      ));
    });

    return list;
  }

  void requestSalesData(int index) async {
    /// 数据不存在时 再调用接口请求
    if (this.sourceData[index].value.completionValue == null) {
      int period = widget.indexController.value == 0 ? 1 : 3;
      this.loadingSource[index].value = true;
      var result = await HomeSalesRequest.requestSalesData(
          paramModel: widget.orgController.value,
          period: period,
          queryType: index + 2); // 如果调整item 顺序此处需要特殊处理
      this.loadingSource[index].value = false;

      if (result.isSuccess == true) {
        HomeSalesItemModel? data = result.getData();
        if (data != null) {
          /// 判断此处是 天还是月 进行缓存
          if (widget.indexController.value == 0) {
            this.daySourceData[index].value = data;
          } else {
            this.monthSourceData[index].value = data;
          }
          this.sourceData[index].value = data;
        }
      }
    }
  }

  String getActionType(int index) {
    switch (index) {
      case 0:
        return "mc-homepage-performanceSelf";
      case 1:
        return "mc-homepage-performanceThird";
      case 2:
        return "mc-homepage-performanceControl";
      case 3:
        return "mc-homepage-performanceHigh";
      default:
        return "mc-homepage-performanceOther";
    }
  }
}
