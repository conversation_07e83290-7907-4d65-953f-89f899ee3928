import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/dialog/widget_loading.dart';
import 'package:XyyBeanSproutsFlutter/common/tabs/custom_tab_controller.dart';
import 'package:XyyBeanSproutsFlutter/common/tabs/custom_tabs.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_org_param_model.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_sales_item_model.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_user_level_model.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/sales_data/home_sales_day_overview.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/sales_data/home_sales_month_overview.dart';
import 'package:XyyBeanSproutsFlutter/performance/data/team_performance_list_params.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';

class HomeSalesTotalTabWidget extends StatefulWidget {
  final ValueChanged<int> changeIndex;

  final HomeORGChooseNotifier orgController;

  HomeSalesTotalTabWidget(
      {required this.changeIndex, required this.orgController});

  @override
  State<StatefulWidget> createState() {
    return HomeSalesTotalTabWidgetState();
  }
}

class HomeSalesTotalTabWidgetState extends State<HomeSalesTotalTabWidget>
    with SingleTickerProviderStateMixin {
  late CustomTabController _controller;

  HomeSalesItemModel dayModel = HomeSalesItemModel();
  WidgetLoadingController dayLoadingController = WidgetLoadingController(false);

  HomeSalesItemModel monthModel = HomeSalesItemModel();
  WidgetLoadingController monthLoadingController =
      WidgetLoadingController(false);

  List<String> titles = ['今日数据', '本月数据'];

  @override
  void initState() {
    this._controller = CustomTabController(
      length: this.titles.length,
      initialIndex: 0,
      vsync: this,
      animationDuration: Duration.zero,
    );

    /// 监听组织结构选择
    widget.orgController.addListener(() {
      this.requestSalesData();
    });

    super.initState();
  }

  @override
  void dispose() {
    this._controller.dispose();
    this.dayLoadingController.dispose();
    this.monthLoadingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          SizedBox(
            height: 35,
            child: Container(
              padding: EdgeInsets.only(top: 1),
              child: CustomTabBar(
                controller: this._controller,
                isScrollable: false,
                indicatorColor: Colors.transparent,
                indicatorWeight: 0.1,
                // 指示器高度不能设置为0
                indicatorSize: CustomTabBarIndicatorSize.label,
                unselectedLabelColor: Color(0xFF9494A6),
                unselectedLabelStyle: TextStyle(fontSize: 15),
                labelColor: Color(0xFF0D0E10),
                labelStyle:
                    TextStyle(fontSize: 15, fontWeight: FontWeight.w600),
                labelPadding: EdgeInsets.zero,
                tabs: tabs(),
                onTap: widget.changeIndex,
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 9, left: 10, right: 10),
            decoration: BoxDecoration(
                // image: DecorationImage(
                //   image: AssetImage('assets/images/home/<USER>'),
                //   fit: BoxFit.cover,
                // ),
                color: Color(0xFF00AA69),
                borderRadius: BorderRadius.circular(8)),
            child: SizedBox(
              height: 139,
              child: CustomTabBarView(
                physics: new NeverScrollableScrollPhysics(),
                controller: this._controller,
                children: [
                  WidgetLoading(
                    loadingController: this.dayLoadingController,
                    child: HomeSalesDayOverview(this.dayModel),
                  ),
                  WidgetLoading(
                    loadingController: this.monthLoadingController,
                    child: HomeSalesMonthOverview(this.monthModel),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  List<Widget> tabs() {
    return this
        .titles
        .map((e) => Container(
              height: 34,
              child: Tab(
                text: e,
              ),
            ))
        .toList();
  }

  void requestSalesData() async {
    this.requestTodayData();
    this.requestMonthData();
  }

  void requestTodayData() async {

    // 今日
    this.dayLoadingController.value = true;
    var userLevelResult = await NetworkV2<UserLevelModel>(UserLevelModel()).requestDataV2(
        'group/gmv/post/query',
        method: RequestMethod.GET
    );
    var value = await HomeSalesRequest.requestSalesData(
        paramModel: widget.orgController.value, period: 1, queryType: 1);
    this.dayLoadingController.value = false;
    if (value.isSuccess == true) {
      HomeSalesItemModel? data = value.getData();
      if (data != null) {
        this.dayModel = data;
        if (userLevelResult.isSuccess == true) {
          dayModel.isShowTeamPerformanceEntry =
              widget.orgController.value.selectedScopeMatchLoginUser && '${userLevelResult.getData()?.postType}' == USER_TYPE_M;
        }
      }
    }
    setState(() {});
  }

  void requestMonthData() async {

    // 本月
    this.monthLoadingController.value = true;
    var userLevelResult = await NetworkV2<UserLevelModel>(UserLevelModel()).requestDataV2(
        'group/gmv/post/query',
        method: RequestMethod.GET
    );
    var value = await HomeSalesRequest.requestSalesData(
        paramModel: widget.orgController.value, period: 3, queryType: 1);
    this.monthLoadingController.value = false;
    if (value.isSuccess == true) {
      HomeSalesItemModel? data = value.getData();
      if (data != null) {
        this.monthModel = data;
        if (userLevelResult.isSuccess == true) {
          monthModel.isShowTeamPerformanceEntry =
              widget.orgController.value.selectedScopeMatchLoginUser && '${userLevelResult.getData()?.postType}' == USER_TYPE_M;
        }
      }
    }
    setState(() {});
  }
}
