import 'dart:collection';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_org_param_model.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/sales_data/block_tab_base_painter.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/sales_data/home_sales_total_tab_widget.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/sales_data/home_sales_view_tab_widget.dart';
import 'package:XyyBeanSproutsFlutter/utils/permission/permission_data.dart';
import 'package:flutter/material.dart';

class HomeSalesDataWidget extends StatefulWidget {
  final HomeORGChooseNotifier orgController;

  HomeSalesDataWidget({required this.orgController});

  @override
  State<StatefulWidget> createState() {
    return HomeSalesDataWidgetState();
  }
}

class HomeSalesDataWidgetState extends State<HomeSalesDataWidget> {
  ValueNotifier<int> indexController = ValueNotifier(0);

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(Object context) {
    return Container(
      color: Color(0xFFF1F6F9),
      padding: EdgeInsets.fromLTRB(10, 5, 10, 0),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Container(
          decoration: BoxDecoration(
            color: Color(0xFFE9F0F4),
          ),
          child: CustomPaint(
            painter: BlockTabBasePainter(
              factor: this.indexController,
            ),
            child: Column(
              children: [
                Container(
                  child: GestureDetector(
                    onTap: () {
                      bool todayFlag = Permission.isPermission('today');
                      if (!todayFlag) {
                        XYYContainer.toastChannel.toast("无查看权限");
                        return;
                      }
                      XYYContainer.bridgeCall('event_track',
                          parameters: HashMap.from({
                            "action_type": "mc-homepage-performanceDetail"
                          }));
                      // 点击更多数据
                      String period =
                          this.indexController.value == 0 ? "1" : "3";
                      XYYContainer.open("/sales_result_page?period=$period");
                    },
                    behavior: HitTestBehavior.opaque,
                    child: HomeSalesTotalTabWidget(
                      changeIndex: (value) {
                        print(
                            "guan track ${value == 0 ? "mc-homepage-performanceDay" : "mc-homepage-performanceMonth"}");
                        XYYContainer.bridgeCall('event_track',
                            parameters: HashMap.from({
                              "action_type": value == 0
                                  ? "mc-homepage-performanceDay"
                                  : "mc-homepage-performanceMonth"
                            }));
                        this.indexController.value = value;
                      },
                      orgController: widget.orgController,
                    ),
                  ),
                ),
                HomeSalesViewTabWidget(
                  orgController: widget.orgController,
                  indexController: this.indexController,
                ),
                Permission.isPermission('moreQuery') ? GestureDetector(
                  onTap: () {
                    XYYContainer.bridgeCall('event_track',
                        parameters: HashMap.from(
                            {"action_type": "mc-homepage-performanceMore"}));
                    // 点击更多数据
                    String period = this.indexController.value == 0 ? "1" : "3";
                    XYYContainer.open("/sales_result_page?period=$period");
                  },
                  behavior: HitTestBehavior.opaque,
                  child: Container(
                    padding: EdgeInsets.only(top: 15, bottom: 15),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          '查看更多数据',
                          style: TextStyle(
                            color: Color(0xFF00B377),
                            fontSize: 12,
                          ),
                        ),
                        SizedBox(width: 5),
                        Image.asset(
                          'assets/images/home/<USER>',
                          width: 12,
                          height: 12,
                        ),
                      ],
                    ),
                  ),
                ) : SizedBox()
              ],
            ),
          ),
        ),
      ),
    );
  }
}
