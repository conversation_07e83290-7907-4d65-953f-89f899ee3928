import 'package:XyyBeanSproutsFlutter/home/<USER>/home_sales_item_model.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/sales_data/home_sales_progress.dart';
import 'package:XyyBeanSproutsFlutter/utils/permission/permission_data.dart';
import 'package:flutter/material.dart';
import 'dart:math' as math;

class HomeSalesMonthContent extends StatelessWidget {
  final HomeSalesItemModel model;
  bool todayFlag = true;
  HomeSalesMonthContent(this.model);

  @override
  Widget build(BuildContext context) {
    todayFlag = Permission.isPermission('today');
    return Container(
      padding: EdgeInsets.fromLTRB(10, 15, 10, 15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '实付GMV(元）',
                style: TextStyle(
                  color: Color(0xFF0D0E10),
                  fontSize: 12,
                ),
              ),
              SizedBox(width: 10),
              Text(
                '目标(元)：${todayFlag ? (model.targetValue ?? '--') : '--'}',
                style: TextStyle(
                  color: Color(0xFF9494A6),
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              )
            ],
          ),
          SizedBox(height: 5),
          IntrinsicHeight(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${todayFlag ? (model.completionValue ?? "--") : '--'}',
                      style: TextStyle(
                        color: Color(0xFF0D0E10),
                        fontSize: 19,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    // Spacer(),
                    Row(
                      children: [
                        Text(
                          '环比：',
                          style: TextStyle(
                            color: Color(0xFF9494A6),
                            fontSize: 12,
                          ),
                        ),
                        Text(
                          '${todayFlag ? model.getRatio() : '--'}',
                          style: TextStyle(
                            color: model.isRatioUp()
                                ? Color(0xFF00B377)
                                : Color(0xFFFF4741),
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        todayFlag ? Visibility(
                          visible: model.isShowRatioToward(),
                          child: Container(
                            padding: EdgeInsets.only(bottom: 12),
                            child: Transform.rotate(
                              angle: math.pi,
                              child: Image.asset(
                                model.isRatioUp()
                                    ? 'assets/images/home/<USER>'
                                    : 'assets/images/home/<USER>',
                                width: 6,
                                height: 7,
                              ),
                            ),
                          ),
                        ) : SizedBox()
                      ],
                    )
                  ],
                ),
                HomeSalesProgress(
                  progress: model.getFinishedProress(),
                  progressText: '${model.progress ?? '--'}',
                  progressColor: Color(0xFF14B572),
                  progressBackGroundColor: Color(0xFFE5E5E5),
                  percentTextColor: Color(0xFF0D0E10),
                  bottomTextColor: Color(0xFF9494A6),
                  showNum: todayFlag,
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
