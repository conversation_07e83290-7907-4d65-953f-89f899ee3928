// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home_item_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HomeOrderDataModel _$HomeOrderDataModelFromJson(Map<String, dynamic> json) {
  return HomeOrderDataModel()
    ..orderCount = json['orderCount'] == null
        ? null
        : HomeOrderItemModel.fromJson(
            json['orderCount'] as Map<String, dynamic>)
    ..refundOrderCount = json['refundOrderCount'] == null
        ? null
        : HomeOrderItemModel.fromJson(
            json['refundOrderCount'] as Map<String, dynamic>)
    ..notPayOrderCount = json['notPayOrderCount'] == null
        ? null
        : HomeOrderItemModel.fromJson(
            json['notPayOrderCount'] as Map<String, dynamic>)
    ..exceptionOrderCount = json['exceptionOrderCount'] == null
        ? null
        : HomeOrderItemModel.fromJson(
            json['exceptionOrderCount'] as Map<String, dynamic>);
}

Map<String, dynamic> _$HomeOrderDataModelToJson(HomeOrderDataModel instance) =>
    <String, dynamic>{
      'orderCount': instance.orderCount,
      'refundOrderCount': instance.refundOrderCount,
      'notPayOrderCount': instance.notPayOrderCount,
      'exceptionOrderCount': instance.exceptionOrderCount,
    };

HomeOrderItemModel _$HomeOrderItemModelFromJson(Map<String, dynamic> json) {
  return HomeOrderItemModel()
    ..showNum = json['showNum']
    ..statusList = json['statusList']
    ..exceptionTypeList = json['exceptionTypeList']
    ..startCreateTime = json['startCreateTime']
    ..endCreateTime = json['endCreateTime'];
}

Map<String, dynamic> _$HomeOrderItemModelToJson(HomeOrderItemModel instance) =>
    <String, dynamic>{
      'showNum': instance.showNum,
      'statusList': instance.statusList,
      'exceptionTypeList': instance.exceptionTypeList,
      'startCreateTime': instance.startCreateTime,
      'endCreateTime': instance.endCreateTime,
    };

HomeCustomerCardData _$HomeCustomerCardDataFromJson(Map<String, dynamic> json) {
  return HomeCustomerCardData()
    ..moveSaleCustomers = json['moveSaleCustomers']
    ..newCustomers = json['newCustomers']
    ..todayLogins = json['todayLogins']
    ..todayPlaceOrders = json['todayPlaceOrders'];
}

Map<String, dynamic> _$HomeCustomerCardDataToJson(
        HomeCustomerCardData instance) =>
    <String, dynamic>{
      'moveSaleCustomers': instance.moveSaleCustomers,
      'newCustomers': instance.newCustomers,
      'todayLogins': instance.todayLogins,
      'todayPlaceOrders': instance.todayPlaceOrders,
    };

HomeVisitCardDataModel _$HomeVisitCardDataModelFromJson(
    Map<String, dynamic> json) {
  return HomeVisitCardDataModel()
    ..doorVisits = json['doorVisits']
    ..validDoorVisits = json['validDoorVisits']
    ..thisMonthValidVisits = json['thisMonthValidVisits']
    ..thisMonthNonVisits = json['thisMonthNonVisits']
    ..isBd = json['isBd'];
}

Map<String, dynamic> _$HomeVisitCardDataModelToJson(
        HomeVisitCardDataModel instance) =>
    <String, dynamic>{
      'doorVisits': instance.doorVisits,
      'validDoorVisits': instance.validDoorVisits,
      'thisMonthValidVisits': instance.thisMonthValidVisits,
      'thisMonthNonVisits': instance.thisMonthNonVisits,
      'isBd': instance.isBd,
    };

HomeLicenceCardModel _$HomeLicenceCardModelFromJson(Map<String, dynamic> json) {
  return HomeLicenceCardModel()
    ..draft = json['draft']
    ..firstRejection = json['firstRejection']
    ..nearExpiration = json['nearExpiration']
    ..expiration = json['expiration'];
}

Map<String, dynamic> _$HomeLicenceCardModelToJson(
        HomeLicenceCardModel instance) =>
    <String, dynamic>{
      'draft': instance.draft,
      'firstRejection': instance.firstRejection,
      'nearExpiration': instance.nearExpiration,
      'expiration': instance.expiration,
    };

HomeTaskCardItemModel _$HomeTaskCardItemModelFromJson(
    Map<String, dynamic> json) {
  return HomeTaskCardItemModel()
    ..theme = json['theme']
    ..type = json['type']
    ..typeName = json['typeName']
    ..countdown = json['countdown']
    ..endTime = json['endTime']
    ..status = json['status']
    ..id = json['id'];
}

Map<String, dynamic> _$HomeTaskCardItemModelToJson(
        HomeTaskCardItemModel instance) =>
    <String, dynamic>{
      'theme': instance.theme,
      'type': instance.type,
      'typeName': instance.typeName,
      'countdown': instance.countdown,
      'endTime': instance.endTime,
      'status': instance.status,
      'id': instance.id,
    };

HomePriceItemModel _$HomePriceItemModelFromJson(Map<String, dynamic> json) {
  return HomePriceItemModel()
      ..id = json['id'] as int?
      ..productHealth = (json['productHealth'] as num?)?.toDouble()
      ..productHealthWeek = (json['productHealthWeek'] as num?)?.toDouble()
      ..productHealthWeekPercentage = (json['productHealthWeekPercentage'] as num?)?.toDouble()
      ..onSaleProductCount = json['onSaleProductCount'] as int?
      ..onSaleProductCountWeek = json['onSaleProductCountWeek'] as int?
      ..onSaleProductCountWeekPercentage = (json['onSaleProductCountWeekPercentage'] as num?)?.toDouble()
      ..priceAdvantageProductCount = json['priceAdvantageProductCount'] as int?
      ..priceAdvantageProductCountWeek = json['priceAdvantageProductCountWeek'] as int?
      ..priceAdvantageProductCountWeekPercentage = (json['priceAdvantageProductCountWeekPercentage'] as num?)?.toDouble()
      ..priceDisadvantageProductCount = json['priceDisadvantageProductCount'] as int?
      ..priceDisadvantageProductCountWeek = json['priceDisadvantageProductCountWeek'] as int?
      ..priceDisadvantageProductCountWeekPercentage = (json['priceDisadvantageProductCountWeekPercentage'] as num?)?.toDouble()
      ..updateTime = json['updateTime'] as int?;
}

Map<String, dynamic> _$HomePriceItemModelToJson(HomePriceItemModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'productHealth': instance.productHealth,
      'productHealthWeek': instance.productHealthWeek,
      'productHealthWeekPercentage': instance.productHealthWeekPercentage,
      'onSaleProductCount': instance.onSaleProductCount,
      'onSaleProductCountWeek': instance.onSaleProductCountWeek,
      'onSaleProductCountWeekPercentage':
          instance.onSaleProductCountWeekPercentage,
      'priceAdvantageProductCount': instance.priceAdvantageProductCount,
      'priceAdvantageProductCountWeek': instance.priceAdvantageProductCountWeek,
      'priceAdvantageProductCountWeekPercentage':
          instance.priceAdvantageProductCountWeekPercentage,
      'priceDisadvantageProductCount': instance.priceDisadvantageProductCount,
      'priceDisadvantageProductCountWeek':
          instance.priceDisadvantageProductCountWeek,
      'priceDisadvantageProductCountWeekPercentage':
          instance.priceDisadvantageProductCountWeekPercentage,
      'updateTime': instance.updateTime,
    };
