import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';

class HomeOrgChoose extends StatefulWidget {
  final ValueNotifier<String>? controller;

  HomeOrgChoose({this.controller});

  @override
  State<StatefulWidget> createState() {
    return HomeOrgChooseState();
  }
}

class HomeOrgChooseState extends State<HomeOrgChoose> {
  late ValueNotifier<String> controller;

  @override
  void initState() {
    if (widget.controller != null) {
      this.controller = widget.controller!;
    } else {
      this.controller = ValueNotifier("");
    }
    this.setTitle();
    super.initState();
  }

  void setTitle() async {
    var userInfo = await UserInfoUtil.getUserInfo();
    if (userInfo?.realName != null) {
      this.controller.value = userInfo?.realName ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(left: 10, right: 10),
      height: 34,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(17),
        color: Color(0xFFFFFFFF),
      ),
      child: Container(
        padding: EdgeInsets.only(left: 12, right: 10),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            ValueListenableBuilder(
              valueListenable: this.controller,
              builder: (BuildContext context, String value, Widget? child) {
                return Container(
                  constraints: BoxConstraints(maxWidth: 85),
                  child: Text(
                    value,
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF383841),
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                );
              },
            ),
            SizedBox(width: 5),
            Image.asset(
              'assets/images/home/<USER>',
              width: 10,
              height: 10,
            ),
          ],
        ),
      ),
    );
  }
}
