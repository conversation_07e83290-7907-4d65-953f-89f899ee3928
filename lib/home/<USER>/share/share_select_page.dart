import 'dart:io';
import 'package:flutter/material.dart';
import 'package:XYYContainer/XYYContainer.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';

class ShareSelectPage extends StatelessWidget {
  final String path;

  const ShareSelectPage({Key? key, required this.path}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFDCDCDC),
      body: Column(
        children: [
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(
                  top: MediaQuery.of(context).padding.top + 40, bottom: 40),
              child: Image.file(File(path)),
            ),
          ),
          buildButtonsView(),
          buildCancelButton(context),
        ],
      ),
    );
  }

  //微信好友、朋友圈、企业微信好友、保存本地
  Widget buildButtonsView() {
    return Container(
      height: 130.0,
      color: Color(0xFFF4F4F4),
      child: <PERSON>umn(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              buildButtonColumn(
                'assets/images/share/share_WeChat.png',
                '微信好友',
                () {
                  XYYContainer.bridgeCall("open_share", parameters: {
                    "imgPath": path,
                    "sharePlatform": 1,
                    "shareType": 1
                  });
                },
              ),
              buildButtonColumn(
                'assets/images/share/share_circle.png',
                '朋友圈',
                () {
                  XYYContainer.bridgeCall("open_share", parameters: {
                    "imgPath": path,
                    "sharePlatform": 2,
                    "shareType": 1
                  });
                },
              ),
              buildButtonColumn(
                'assets/images/share/share_WWK.png',
                '企业微信好友',
                () {
                  XYYContainer.bridgeCall("open_share", parameters: {
                    "imgPath": path,
                    "sharePlatform": 3,
                    "shareType": 1
                  });
                },
              ),
              buildButtonColumn(
                'assets/images/share/share_saveLocal.png',
                '保存本地',
                () {
                  saveImageToPhotoLibrary();
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  //封装按钮样式
  Widget buildButtonColumn(
      String imagePath, String text, VoidCallback onPressed) {
    return TextButton(
      onPressed: onPressed,
      style: ButtonStyle(
        overlayColor: MaterialStateProperty.all<Color>(Colors.transparent),
      ),
      child: Column(
        children: [
          Image.asset(
            imagePath,
            width: 45,
            height: 45,
          ),
          SizedBox(height: 5.0),
          Text(
            text,
            style: TextStyle(
              color: Color(0xFF666666),
              fontSize: 14.0,
            ),
          ),
        ],
      ),
    );
  }

  // 保存图片到本地相册
  Future<void> saveImageToPhotoLibrary() async {
    try {
      var saveResult = await ImageGallerySaver.saveFile(path);
      if (saveResult != null && saveResult is Map<dynamic, dynamic>) {
        if (saveResult["isSuccess"] == true) {
          XYYContainer.toastChannel.toast("保存成功");
          return;
        }
      }
      XYYContainer.toastChannel.toast("保存失败！");
    } catch (e) {
      XYYContainer.toastChannel.toast("保存失败:${e.runtimeType.toString()}");
    }
  }

  //取消按钮
  Widget buildCancelButton(BuildContext context) {
    return InkWell(
      onTap: () {
        Navigator.pop(context);
      },
      child: Container(
        height: 50.0 + MediaQuery.of(context).padding.bottom,
        width: double.infinity,
        color: Colors.white, // 设置按钮的背景颜色为白色
        child: Center(
          child: Text(
            '取消',
            style: TextStyle(
              color: Color(0xFF333333),
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }
}
