import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'share_data_model.g.dart';

@JsonSerializable()
class UserListModel extends BaseModelV2<UserListModel> {
  dynamic userDisplayName;
  dynamic deptPath;

  UserListModel();

  factory UserListModel.fromJson(Map<String, dynamic> json) {
    return _$UserListModelFromJson(json);
  }

  @override
  UserListModel fromJsonMap(Map<String, dynamic> json) {
    return _$UserListModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$UserListModelToJson(this);
  }
}


@JsonSerializable()
class RankListModel extends BaseModelV2<RankListModel> {
  /// 金额
  dynamic gmvAmount;

  /// 排行类型0-总，1-优选，2-实付
  dynamic rankType;

  /// 是否登顶
  bool? topFlag;

  /// 等级
  dynamic rankLevel;

  /// 等级变动
  dynamic diffLevel;

  /// 超过率
  dynamic overPercent;

  List<UserListModel>? userList;

  RankListModel();

  factory RankListModel.fromJson(Map<String, dynamic> json) {
    return _$RankListModelFromJson(json);
  }

  @override
  RankListModel fromJsonMap(Map<String, dynamic> json) {
    return _$RankListModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$RankListModelToJson(this);
  }
}


@JsonSerializable()
class RankPopupDataModel extends BaseModelV2<RankPopupDataModel> {
  /// 是否弹窗
  bool? showPopup;

  /// 0-M,1-BD,2-KA
  int postType = 0;

  /// 干支日期
  dynamic ganZhiDate;

  /// 日历日期
  dynamic shortDate;

  /// 周几
  dynamic weekDay;

  /// 激励文案
  dynamic encourageText;

  /// 名字：花名（实名）
  dynamic displayName;

  List<RankListModel>? bdRankList;
  List<RankListModel>? kaRankList;
  List<RankListModel>? telemarketingRankList;//电销榜单


  @override
  RankPopupDataModel fromJsonMap(Map<String, dynamic> json) {
    return _$RankPopupDataModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$RankPopupDataModelToJson(this);
  }
}