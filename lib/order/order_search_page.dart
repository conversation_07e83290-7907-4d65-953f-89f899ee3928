import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_appbar_search.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/order_list_item_data.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/order_manage_data.dart';
import 'package:XyyBeanSproutsFlutter/order/order_manage_page.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/order_list_item_widget.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/order_refund_list_item_widget.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';

class OrderSearchPage extends BasePage {
  final String? pageType; // 订单状态
  final Map<String, String?>? filterParams;

  OrderSearchPage(this.pageType, {this.filterParams});

  @override
  BaseState<StatefulWidget> initState() {
    return OrderSearchPageState();
  }
}

class OrderSearchPageState extends BaseState<OrderSearchPage> {
  late OrderListModel _listModel;
  var _refreshController = EasyRefreshController();
  var _scrollController = ScrollController();

  PageStateWidget? pageStateWidget;

  @override
  void onCreate() {
    pageStateWidget = new PageStateWidget();
    _listModel =
        OrderListModel(_scrollController, _refreshController, widget.pageType);
    _listModel.requestSmallImageHost();
    print('333 - ${widget.filterParams}');
    _listModel.filterParams = widget.filterParams;
    super.onCreate();
  }

  @override
  List<SingleChildWidget> getProvider() {
    return [
      ChangeNotifierProvider<OrderListModel>(create: (context) => _listModel)
    ];
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Divider(color: Color(0xFFF6F6F6), height: 1),
          Expanded(
            child: Container(
              color: Color(0xffefeff4),
              child: buildListView(),
            ),
          ),
        ],
      ),
    );
  }

  buildListView() {
    return Consumer<OrderListModel>(builder: (context, model, child) {
      return EasyRefresh.custom(
          controller: _refreshController,
          scrollController: _scrollController,
          enableControlFinishRefresh: true,
          enableControlFinishLoad: true,
          onRefresh: () async {
            _listModel.requestListModel(true);
          },
          onLoad: !_listModel.isLastPage!
              ? () async {
            _listModel.requestListModel(false);
          }
              : null,
          slivers: [
            SliverList(
              delegate:
              SliverChildBuilderDelegate((BuildContext context, int index) {
                //创建列表项
                return buildOrderItem(index);
              }, childCount: _listModel.list?.length ?? 0),
            )
          ],
          emptyWidget: getEmptyWidget());
    });
  }

  @override
  PreferredSizeWidget getTitleBar(BuildContext context) {
    return SAppBarSearch(
      hintText: widget.pageType == ORDER_PAGE_TYPE_REFUND
          ? "请输入退单编号或客户名称"
          : "订单编号/客户名称/商品名称",
      showLeading: false,
      hideCancel: false,
      autoFocus: true,
      onSearch: (value) {
        if (value.isEmpty) {
          showToast("请输入搜索内容");
          return;
        }
        _listModel.keyword = value;
        _listModel.requestListModel(true);
      },
    );
  }

  @override
  String getTitleName() {
    return widget.pageType == ORDER_PAGE_TYPE_REFUND ? "退款单" : "订单";
  }

  Widget buildFilterButton() {
    return GestureDetector(
      onTap: () {
        showToast("filter");
      },
      child: Container(
        height: 44,
        width: 44,
        alignment: Alignment.center,
        child: Image.asset(
          "assets/images/titlebar/icon_filter.png",
          width: 21,
          height: 21,
        ),
      ),
    );
  }

  Widget buildSearchButton() {
    return GestureDetector(
      onTap: () {
        showToast("search");
      },
      child: Container(
        height: 44,
        width: 44,
        alignment: Alignment.center,
        child: Image.asset(
          "assets/images/titlebar/icon_search.png",
          width: 21,
          height: 21,
        ),
      ),
    );
  }

  buildOrderItem(int index) {
    if (_listModel.list == null || index >= (_listModel.list?.length ?? 0)) {
      return Container();
    }
    var itemData = _listModel.list![index];
    return GestureDetector(
        onTap: () {
          if (_listModel.pageType == ORDER_PAGE_TYPE_REFUND) {
            Navigator.of(context).pushNamed("/OrderDetailRefundPage",
                arguments: {
                  "orderId": itemData.id,
                  "merchantId": itemData.merchantId
                });
          } else {
            Navigator.of(context).pushNamed("/order_detail_page", arguments: {
              "orderId": itemData.id,
              "merchantId": itemData.merchantId
            });
          }
        },
        child: widget.pageType == ORDER_PAGE_TYPE_REFUND
            ? OrderRefundListItemWidget(_listModel.smallImageHost, itemData)
            : OrderListItemWidget(_listModel.smallImageHost, itemData));
  }

  Widget? getEmptyWidget() {
    if (_listModel.isSuccess == null) {
      return null;
    }
    if (_listModel.isSuccess == false) {
      return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
        _listModel.requestListModel(true);
      });
    }

    if ((_listModel.list?.length ?? 0) == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }
}

class OrderListModel extends ChangeNotifier {
  var _isDisposed = false;
  bool? isLastPage = false;
  int pageNum = 0;
  bool forceRefresh = false;
  List<OrderListItemData>? list;
  String? smallImageHost;
  bool? isSuccess;
  String? keyword;
  Map<String, String?>? filterParams;

  final String? pageType; // 订单状态

  final EasyRefreshController _refreshController;
  final ScrollController _scrollController;

  OrderListModel(this._scrollController, this._refreshController,
      this.pageType);

  requestSmallImageHost() {
    if (smallImageHost == null) {
      XYYContainer.bridgeCall('app_host').then((value) {
        if (value is Map) {
          smallImageHost = value["image_host"];
          notifyListeners();
        }
      });
    }
  }

  void requestNormalList(Map<String, String?> paramsMap) {
    NetworkV2<OrderManageData>(OrderManageData())
        .requestDataV2("order/selectOrdersForOrderManagementNew",
        method: RequestMethod.GET, parameters: paramsMap)
        .then((value) {
      handleResult(
          value.isSuccess, value.getData(), value
          .getData()
          ?.totalMoney ?? 0.0);
    });
  }

  void requestRefundList(Map<String, String?> paramsMap) {
    NetworkV2<OrderManageData>(OrderManageData())
        .requestDataV2("refund/queryRefundOrderPageNew",
        method: RequestMethod.GET, parameters: paramsMap)
        .then((value) {
      handleResult(
          value.isSuccess, value.getData(), value
          .getData()
          ?.totalMoney ?? 0.0);
    });
  }

  requestListModel(bool isRefresh) async {
    if (isRefresh) {
      pageNum = 0;
      forceRefresh = true;
      list?.clear();
    } else {
      pageNum += 1;
      forceRefresh = false;
    }
    EasyLoading.show(status: "加载中");
    var paramsMap = buildParamsMap();
    if (pageType == ORDER_PAGE_TYPE_REFUND) {
      requestRefundList(paramsMap);
    } else {
      requestNormalList(paramsMap);
    }
  }

  void handleResult(bool? isSuccess, OrderManageData? data, double totalMoney) {
    EasyLoading.dismiss();
    if (!_isDisposed) {
      if (isSuccess == true) {
        this.isSuccess = isSuccess;
        if (data != null) {
          print("guan $pageType lastPage ${data.lastPage} ,${data.isLastPage}");
          var tempList;
          if (pageType == ORDER_PAGE_TYPE_REFUND) {
            isLastPage = data.page?.isLastPage;
            tempList = data.page?.list;
          } else {
            isLastPage = data.page?.isLastPage;
            tempList = data.page?.list;
          }
          if (forceRefresh) {
            this.list = tempList;
            _scrollController.jumpTo(0);
          } else {
            this.list?.addAll(tempList ?? []);
          }
        } else {
          isLastPage = true;
        }
        _refreshController.finishRefresh();
        _refreshController.finishLoad(noMore: isLastPage == true);
        notifyListeners();
      } else {
        _refreshController.finishRefresh();
        _refreshController.finishLoad();
      }
    }
  }

  Map<String, String?> buildParamsMap() {
    var params = Map<String, String?>();
    params.addAll(this.filterParams ?? {});
    //分页参数
    params["offset"] = pageNum.toString();
    params["limit"] = "10";
    params["keyword"] = keyword;
    return params;
  }

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }
}
