import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/base/time_picker/datetime_picker_theme.dart';
import 'package:XyyBeanSproutsFlutter/common/base/time_picker/flutter_datetime_picker.dart';
import 'package:XyyBeanSproutsFlutter/order/order_filter_page.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/order_filter_operation_item.dart';
import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';

typedef OrderFilterTimeValueChange = void Function(Map<String, String>);

// ignore: must_be_immutable
class OrderFilterTimeItem extends StatefulWidget {
  /// 配置参数
  OrderFilterConfigModel? configModel;

  /// 自定义开始时间
  String? startTime;

  /// 自定义结束时间
  String? endTime;

  /// 回调
  final OrderFilterTimeValueChange? callBack;

  OrderFilterTimeItem({
    this.configModel,
    this.startTime,
    this.endTime,
    this.callBack,
  });

  @override
  State<StatefulWidget> createState() {
    return OrderFilterTimeItemState();
  }
}

class OrderFilterTimeItemState extends State<OrderFilterTimeItem> {
  OrderFilterTimeItemState();

  @override
  Widget build(BuildContext context) {
    List<int>? selectIds = widget.configModel!.selectIds;
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          OrderFilterOperationItem(
            title: widget.configModel!.title,
            itemKey: widget.configModel!.itemKey,
            items: widget.configModel!.items,
            selectIds: selectIds != null ? selectIds : [],
            defaultId: widget.configModel!.defaultId,
            allowsMultipleSelection:
                widget.configModel!.allowsMultipleSelection,
            canClean: widget.configModel!.canClean,
            changeValue: (itemkey, selectIds) {
              setState(() {
                widget.startTime = "";
                widget.endTime = "";
                widget.configModel!.selectIds = selectIds;
              });
              this.itemValueChange(
                itemkey,
                selectIds ?? [],
                widget.startTime,
                widget.endTime,
              );
            },
          ),
          OrderFilterTimeOperationItem(
            startTime: widget.startTime,
            endTime: widget.endTime,
            intervalDay: widget.configModel?.intervalDay,
            timeChange: (startTime, endtime) {
              setState(() {
                widget.startTime = startTime;
                widget.endTime = endtime;
                widget.configModel!.selectIds = [];
              });
              this.itemValueChange(
                widget.configModel!.itemKey,
                widget.configModel!.selectIds!,
                widget.startTime,
                widget.endTime,
              );
            },
          ),
        ],
      ),
    );
  }

  /// 时间选项变更回调
  void itemValueChange(
    String? itemKey,
    List<int> selectedIds,
    String? startTime,
    String? endTime,
  ) {
    Map<String, String> params = {};
    if (selectedIds.isNotEmpty) {
      params[itemKey ?? ""] = selectedIds.map((e) => e.toString()).join(',');
      params["startCreateTime"] = "";
      params["endCreateTime"] = "";
    }
    var start = startTime ?? "";
    var end = endTime ?? "";

    if (start.isNotEmpty || end.isNotEmpty) {
      params["startCreateTime"] = start;
      params["endCreateTime"] = end;
      params[itemKey ?? ""] = "5";
    }
    widget.callBack!(params);
  }
}

typedef CustomTimeChange = void Function(String?, String?);

// ignore: must_be_immutable
class OrderFilterTimeOperationItem extends StatefulWidget {
  String? startTime;
  String? endTime;
  final int? intervalDay;
  final CustomTimeChange? timeChange;

  OrderFilterTimeOperationItem({
    this.startTime,
    this.endTime,
    this.intervalDay,
    this.timeChange,
  });

  @override
  State<StatefulWidget> createState() {
    return OrderFilterTimeOperationItemState();
  }
}

class OrderFilterTimeOperationItemState
    extends State<OrderFilterTimeOperationItem> {
  OrderFilterTimeOperationItemState();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 15, right: 15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '具体时间',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.normal,
              color: Color(0xFF333333),
            ),
          ),
          SizedBox(height: 5),
          Row(
            children: [
              GestureDetector(
                onTap: selectStartTime,
                child: OrderFilterTimeInputView(
                  content: widget.startTime,
                  isChoose: widget.startTime?.isNotEmpty ?? false,
                ),
              ),
              Container(
                margin: EdgeInsets.only(left: 5, right: 5),
                decoration: BoxDecoration(color: Color(0xFF333333)),
                height: 1,
                width: 14,
              ),
              GestureDetector(
                onTap: selectEndTime,
                child: OrderFilterTimeInputView(
                  content: widget.endTime,
                  isChoose: widget.endTime?.isNotEmpty ?? false,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void selectStartTime() {
    DatePicker.showDatePicker(
      context,
      title: "选择开始时间",
      theme: datePickerTheme(),
      onConfirm: (value) {
        if (value == null) {
          XYYContainer.toastChannel.toast("时间错误");
          return;
        }
        var time = formatDate(
          value,
          [yyyy, '-', mm, '-', dd, ' ', HH, ':', nn, ":", "00"],
        );
        if (widget.endTime?.isNotEmpty ?? false) {
          int startTimestamp = DateTime.parse(time).millisecondsSinceEpoch;
          int endTimestamp =
              DateTime.parse(widget.endTime!).millisecondsSinceEpoch;
          if (startTimestamp >= endTimestamp) {
            XYYContainer.toastChannel.toast("开始时间不能大于或等于结束时间");
            return;
          }
          int day = widget.intervalDay ?? 365;
          if (endTimestamp - startTimestamp > day * 24 * 60 * 60 * 1000) {
            XYYContainer.toastChannel.toast("时间段不能超过$day天");
            return;
          }
        }
        callChange(time, widget.endTime);
        setState(() {
          widget.startTime = time;
        });
      },
    );
  }

  void selectEndTime() {
    DatePicker.showDatePicker(
      context,
      title: "选择结束时间",
      theme: datePickerTheme(),
      onConfirm: (value) {
        if (value == null) {
          XYYContainer.toastChannel.toast("时间错误");
          return;
        }
        var time = formatDate(
          value,
          [yyyy, '-', mm, '-', dd, ' ', HH, ':', nn, ":", "00"],
        );
        if (widget.startTime?.isNotEmpty ?? false) {
          int startTimestamp =
              DateTime.parse(widget.startTime!).millisecondsSinceEpoch;
          int endTimestamp = DateTime.parse(time).millisecondsSinceEpoch;
          if (startTimestamp >= endTimestamp) {
            XYYContainer.toastChannel.toast("开始时间不能大于或等于结束时间");
            return;
          }
          int day = widget.intervalDay ?? 365;
          if (endTimestamp - startTimestamp > day * 24 * 60 * 60 * 1000) {
            XYYContainer.toastChannel.toast("时间段不能超过$day天");
            return;
          }
        }
        callChange(widget.startTime, time);
        setState(() {
          widget.endTime = time;
        });
      },
    );
  }

  void callChange(String? startTime, String? endTime) {
    if (widget.timeChange != null) {
      widget.timeChange!(startTime, endTime);
    }
  }

  DatePickerTheme datePickerTheme() {
    return DatePickerTheme(
      titleStyle: TextStyle(
        fontSize: 14,
        color: Color(0xFF8E8E93),
        fontWeight: FontWeight.w500,
      ),
      doneStyle: TextStyle(
        fontSize: 15,
        color: Color(0xFF35C561),
        fontWeight: FontWeight.w500,
      ),
      cancelStyle: TextStyle(
        fontSize: 15,
        color: Color(0xFF333333),
        fontWeight: FontWeight.w500,
      ),
    );
  }
}

class OrderFilterTimeInputView extends StatelessWidget {
  final String? content;
  final bool isChoose;

  OrderFilterTimeInputView({this.content, this.isChoose = false});

  @override
  Widget build(BuildContext context) {
    String? time = '年/月/日/时/份';
    if (this.content?.isNotEmpty ?? false) {
      time = this.content;
    }
    return Container(
      alignment: Alignment.center,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(17.5),
          border: Border.all(
            color: this.isChoose ? Color(0xFF35C561) : Color(0xFFB7B7B7),
            width: 1,
          )),
      constraints: BoxConstraints(
        minWidth: 150,
        minHeight: 35,
      ),
      child: Text(
        time ?? "",
        style: TextStyle(
          color: this.isChoose ? Color(0xFF333333) : Color(0xFF999999),
          fontSize: 14,
        ),
      ),
    );
  }
}
