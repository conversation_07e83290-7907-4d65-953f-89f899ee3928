import 'package:flutter/material.dart';

typedef OrderFilterAmountValueChange = void Function(String?, String?);

// ignore: must_be_immutable
class OrderFilterAmount extends StatefulWidget {
  String? startPrice;
  String? endPrice;
  final OrderFilterAmountValueChange? priceChange;

  OrderFilterAmount({
    this.startPrice,
    this.endPrice,
    this.priceChange,
  });

  @override
  State<StatefulWidget> createState() {
    return OrderFilterAmountState();
  }
}

class OrderFilterAmountState extends State<OrderFilterAmount> {
  OrderFilterAmountState();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '订单金额',
            style: TextStyle(
              fontSize: 15,
              color: Color(0xFF333333),
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 10),
          Row(
            children: [
              Expanded(
                child: Container(
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(17.5),
                    border: Border.all(
                      color: Color(0xFFB7B7B7),
                      width: 1,
                    ),
                  ),
                  constraints: BoxConstraints(
                    minWidth: 80,
                    maxHeight: 35,
                  ),
                  child: TextField(
                    keyboardType:
                        TextInputType.numberWithOptions(decimal: true),
                    controller: TextEditingController.fromValue(
                      TextEditingValue(
                        // 设置内容
                        text:
                            widget.startPrice != null ? widget.startPrice! : "",
                        // 保持光标在最后
                        selection: TextSelection.fromPosition(
                          TextPosition(
                            affinity: TextAffinity.downstream,
                            offset: widget.startPrice != null
                                ? widget.startPrice!.length
                                : 0,
                          ),
                        ),
                      ),
                    ),
                    onChanged: (value) {
                      widget.startPrice = value;
                      widget.priceChange!(widget.startPrice, widget.endPrice);
                    },
                    decoration: InputDecoration(
                      contentPadding: EdgeInsets.zero,
                      isDense: true,
                      border: OutlineInputBorder(
                        borderSide: BorderSide.none,
                      ),
                      icon: Padding(
                        padding: EdgeInsets.only(left: 15),
                        child: Text(
                          '¥',
                          style: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF333333),
                          ),
                        ),
                      ),
                    ),
                    style: TextStyle(
                      color: Color(0xFF333333),
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
              Container(
                margin: EdgeInsets.only(left: 5, right: 5),
                decoration: BoxDecoration(color: Color(0xFF333333)),
                height: 1,
                width: 14,
              ),
              Expanded(
                child: Container(
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(17.5),
                      border: Border.all(
                        color: Color(0xFFB7B7B7),
                        width: 1,
                      )),
                  constraints: BoxConstraints(
                    minWidth: 80,
                    minHeight: 35,
                  ),
                  child: TextField(
                    keyboardType:
                        TextInputType.numberWithOptions(decimal: true),
                    controller: TextEditingController.fromValue(
                      TextEditingValue(
                        // 设置内容
                        text: widget.endPrice != null ? widget.endPrice! : "",
                        // 保持光标在最后
                        selection: TextSelection.fromPosition(
                          TextPosition(
                            affinity: TextAffinity.downstream,
                            offset: widget.endPrice != null
                                ? widget.endPrice!.length
                                : 0,
                          ),
                        ),
                      ),
                    ),
                    onChanged: (value) {
                      widget.endPrice = value;
                      widget.priceChange!(widget.startPrice, widget.endPrice);
                    },
                    decoration: InputDecoration(
                      contentPadding: EdgeInsets.zero,
                      isDense: true,
                      border: OutlineInputBorder(
                        borderSide: BorderSide.none,
                      ),
                      icon: Padding(
                        padding: EdgeInsets.only(left: 15),
                        child: Text(
                          '¥',
                          style: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF333333),
                          ),
                        ),
                      ),
                    ),
                    style: TextStyle(
                      color: Color(0xFF333333),
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
