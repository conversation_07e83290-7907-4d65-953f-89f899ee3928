import 'package:flutter/material.dart';

class OrderDetailHeader extends StatelessWidget {
  final String title; // 标题
  final bool? isShouldOpen; // 是否支持展开收起
  final bool? isOpen; // true-展开 false-收起
  final ValueChanged<bool?>? clickCallback;
  final bool? isShouldClick; // 是否支持点击查看详情

  OrderDetailHeader({
    required this.title,
    this.isShouldOpen,
    this.isOpen,
    this.clickCallback,
    this.isShouldClick,
  });

  @override
  Widget build(Object context) {
    return Container(
      margin: EdgeInsets.only(left: 15, top: 10, right: 15),
      padding: EdgeInsets.only(top: 15, left: 10, right: 10, bottom: 10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(2),
          topRight: Radius.circular(2),
        ),
        color: Color(0xFFFFFFFF),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              this.title,
              style: TextStyle(
                fontSize: 17,
                color: Color(0xFF292933),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Visibility(
            visible: (this.isShouldOpen ?? false),
            child: GestureDetector(
              onTap: () {
                if (this.clickCallback != null) {
                  bool? currentOpen;
                  if (this.isOpen != null) {
                    currentOpen = !this.isOpen!;
                  }
                  this.clickCallback!(currentOpen);
                }
              },
              child: Container(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.end,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Text(
                      (this.isOpen ?? true) ? "收起" : "展开",
                      style:
                          TextStyle(color: Color(0xFF9494A6), fontSize: 12.0),
                    ),
                    SizedBox(width: 3),
                    Image.asset(
                      (this.isOpen ?? true)
                          ? 'assets/images/order/order_up_gray_arrow.png'
                          : 'assets/images/order/order_down_gray_arrow.png',
                      width: 10,
                      height: 10,
                    ),
                  ],
                ),
              ),
            ),
          ),
          Visibility(
            visible: (this.isShouldClick ?? false),
            child: GestureDetector(
              onTap: () {
                if (this.clickCallback != null) {
                  this.clickCallback!(null);
                }
              },
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.end,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Text(
                    "查看详情",
                    style: TextStyle(color: Color(0xFF9494A6), fontSize: 12.0),
                  ),
                  SizedBox(width: 3),
                  Image.asset(
                    'assets/images/order/order_detail_arrow.png',
                    width: 12,
                    height: 12,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
