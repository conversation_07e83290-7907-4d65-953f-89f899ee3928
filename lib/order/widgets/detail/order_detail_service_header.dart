import 'package:flutter/material.dart';

class OrderDetailServiceHeader extends StatelessWidget {
  final String title;
  final VoidCallback clickAction;

  OrderDetailServiceHeader({required this.title, required this.clickAction});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: this.clickAction,
      child: Container(
        margin: EdgeInsets.only(left: 15, right: 15, top: 10),
        padding: EdgeInsets.only(top: 15, bottom: 15),
        decoration: BoxDecoration(
          color: Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(2),
          border: Border.all(color: Color(0xFF00B377), width: 1),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              this.title,
              style: TextStyle(
                fontSize: 17,
                color: Color(0xFF00B377),
                fontWeight: FontWeight.w500,
              ),
            ),
            Image.asset(
              "assets/images/order/order_detail_service_icon.png",
              width: 23,
              height: 23,
            )
          ],
        ),
      ),
    );
  }
}
