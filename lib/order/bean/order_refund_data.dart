import 'package:XyyBeanSproutsFlutter/order/bean/order_manage_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'order_refund_data.g.dart';

@JsonSerializable()
class OrderRefundData extends BaseModelV2<OrderRefundData> {
  OrderManageData? page;
  double? totalMoney;

  OrderRefundData();

  @override
  OrderRefundData fromJsonMap(Map<String, dynamic>? json) {
    return OrderRefundData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$OrderRefundDataToJson(this);
  }

  factory OrderRefundData.fromJson(Map<String, dynamic> json) =>
      _$OrderRefundDataFromJson(json);
}
