import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'order_list_data.g.dart';

@JsonSerializable()
class OrderListData extends BaseModel<OrderListData> {
  String? totalAmount;
  dynamic createTime;
  bool? delete;
  String? exceptionReason;
  int? id;
  String? imageUrl;
  String? invoiceTitle;
  int? merchantId;
  String? merchantName;
  double? money;
  String? orderNo;
  int? orderSource;
  String? payChannelName;
  int? paymentTime;
  int? productNum;
  String? refundFee;
  String? refuseReason;
  String? remark;
  int? salesId;
  int? oederStatus;
  String? statusName;
  String? taxNumber;
  bool? useBalance;
  int? varietyNum;

  int? endTime;
  String? expires;
  String? logistics;
  int? logisticsTime;
  String? orderCreator;
  String? orderResourceName;
  int? orderResourceType;
  int? payExpireTime;
  int? payTime;
  int? isFollow;
  int? isRedPoint;

  OrderListData(
      {this.totalAmount,
      this.createTime,
      this.delete,
      this.exceptionReason,
      this.id,
      this.imageUrl,
      this.invoiceTitle,
      this.merchantId,
      this.merchantName,
      this.money,
      this.orderNo,
      this.orderSource,
      this.payChannelName,
      this.paymentTime,
      this.productNum,
      this.refundFee,
      this.refuseReason,
      this.remark,
      this.salesId,
      this.oederStatus,
      this.statusName,
      this.taxNumber,
      this.useBalance,
      this.varietyNum,
      this.endTime,
      this.expires,
      this.logistics,
      this.logisticsTime,
      this.orderCreator,
      this.orderResourceName,
      this.orderResourceType,
      this.payExpireTime,
      this.payTime,
      this.isFollow,
      this.isRedPoint});

  @override
  OrderListData fromJsonMap(Map<String, dynamic>? json) {
    return _$OrderListDataFromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$OrderListDataToJson(this);
  }

  factory OrderListData.fromJson(Map<String, dynamic> json) =>
      _$OrderListDataFromJson(json);
}
