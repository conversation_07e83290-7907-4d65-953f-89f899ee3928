import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'order_detail_get_merchan.g.dart';

@JsonSerializable()
class OrderDetailGetMerchant extends BaseModel<OrderDetailGetMerchant> {
  String? type;
  String? typeMessage;

  OrderDetailGetMerchant();

  factory OrderDetailGetMerchant.fromJson(Map<String, dynamic> json) => _$OrderDetailGetMerchantFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic>? json) {
    return OrderDetailGetMerchant.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$OrderDetailGetMerchantToJson(this);
  }
}
