// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_detail_refund_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderDetailRefundData _$OrderDetailRefundDataFromJson(
    Map<String, dynamic> json) {
  return OrderDetailRefundData()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : OrderDetailRefundData.fromJson(json['data'] as Map<String, dynamic>)
    ..detail = json['detail'] == null
        ? null
        : OrderDetailRefundInfoData.fromJson(
            json['detail'] as Map<String, dynamic>)
    ..skus = (json['skus'] as List<dynamic>?)
        ?.map((e) => OrderDetailListData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$OrderDetailRefundDataToJson(
        OrderDetailRefundData instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'detail': instance.detail,
      'skus': instance.skus,
    };

OrderDetailRefundInfoData _$OrderDetailRefundInfoDataFromJson(
    Map<String, dynamic> json) {
  return OrderDetailRefundInfoData()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : OrderDetailRefundInfoData.fromJson(
            json['data'] as Map<String, dynamic>)
    ..appAuditStatusName = json['appAuditStatusName'] as String?
    ..merchantId = json['merchantId'] as int?
    ..merchantName = json['merchantName'] as String?
    ..refundAuditTime = json['refundAuditTime']
    ..refundChannelName = json['refundChannelName'] as String?
    ..refundCreateTime = json['refundCreateTime']
    ..refundExplain = json['refundExplain'] as String?
    ..refundFee = json['refundFee']
    ..refundOrderNo = json['refundOrderNo'] as String?
    ..refundReason = json['refundReason'] as String?
    ..refundTime = json['refundTime']
    ..refundVarietyNum = json['refundVarietyNum'] as int?
    ..remark3 = json['remark3'] as String?
    ..totalAmount = json['totalAmount']
    ..refundProductAmount = json['refundProductAmount']
    ..saleName = json['saleName'] as String?
    ..branchName = json['branchName'] as String?
    ..additionalFee = json['additionalFee']
    ..refundMode = json['refundMode']
    ..imUrl = json['imUrl']
    ..imButtonDisplay = json['imButtonDisplay']
    ..packageList = (json['packageList'] as List<dynamic>?)
        ?.map((e) => OrderDetailListData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$OrderDetailRefundInfoDataToJson(
        OrderDetailRefundInfoData instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'appAuditStatusName': instance.appAuditStatusName,
      'merchantId': instance.merchantId,
      'merchantName': instance.merchantName,
      'refundAuditTime': instance.refundAuditTime,
      'refundChannelName': instance.refundChannelName,
      'refundCreateTime': instance.refundCreateTime,
      'refundExplain': instance.refundExplain,
      'refundFee': instance.refundFee,
      'refundOrderNo': instance.refundOrderNo,
      'refundReason': instance.refundReason,
      'refundTime': instance.refundTime,
      'refundVarietyNum': instance.refundVarietyNum,
      'remark3': instance.remark3,
      'totalAmount': instance.totalAmount,
      'refundProductAmount': instance.refundProductAmount,
      'saleName': instance.saleName,
      'branchName': instance.branchName,
      'additionalFee': instance.additionalFee,
      'refundMode': instance.refundMode,
      'imUrl': instance.imUrl,
      'imButtonDisplay': instance.imButtonDisplay,
      'packageList': instance.packageList,
    };
