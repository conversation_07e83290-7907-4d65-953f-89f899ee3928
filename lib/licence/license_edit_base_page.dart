import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/license_base_info_data.dart';
import 'package:XyyBeanSproutsFlutter/licence/widget/license_base_info_confirm_widget.dart';
import 'package:XyyBeanSproutsFlutter/licence/widget/license_base_info_widget.dart';
import 'package:XyyBeanSproutsFlutter/licence/widget/license_step_widget.dart';
import 'package:XyyBeanSproutsFlutter/licence/widget/license_update_photo_widget.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';
import 'bean/license_init_data.dart';

typedef DataPostChangeValue = void Function(bool success);

class LicenseEditBasePage extends BasePage {
  final String? mIsAddType; // 资质类型 true 添加首营资质 false 资质变更
  final String? isDraft; // //只有草稿（未提交）态才能保存草稿
  final String? merchantId; //客户id
  final String? from; //如果值为2，代表从发票跳转过来
  final String? ecOrgCode;
  final String? type;
  final String? qualificationNo;
  final String? licenseInitDataJson;

  LicenseEditBasePage(
      {this.qualificationNo,
      this.isDraft,
      this.mIsAddType,
      this.merchantId,
      this.from,
      this.ecOrgCode,
      this.type,
      this.licenseInitDataJson});

  @override
  BaseState<StatefulWidget> initState() {
    return LicenceEditSubmitState();
  }
}

class LicenceEditSubmitState extends BaseState<LicenseEditBasePage> {
  late LicenceEditModel _licenceEditModel;
  bool isError = false;
  bool result = false;

  LicenceEditSubmitState();

  @override
  void onCreate() {
    LicenseInitData? licenseInitData;
    try {
      var decode = json.decode(widget.licenseInitDataJson ?? "");
      if (decode is Map<String, dynamic>) {
        licenseInitData = LicenseInitData.fromJson(decode);
      }
    } catch (e) {
      print(e);
    }
    if (licenseInitData == null) {
      setPageState(PageState.Empty);
      XYYContainer.toastChannel.toast("模板初始化参数错误!");
      isError = true;
    }
    _licenceEditModel = LicenceEditModel(
        widget.qualificationNo, widget.mIsAddType, licenseInitData);

    super.onCreate();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return WillPopScope(
      child: Scaffold(
        body: Container(
            color: Colors.white,
            child: Consumer<LicenceEditModel>(builder: (context, model, child) {
              if (isError) {
                return Container();
              }
              return Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  headTip(),
                  Visibility(
                      visible: widget.mIsAddType != "false",
                      child: LicenseStepWidget(model.currentStep)),
                  Expanded(child: getContentWidget(model.currentStep)),
                ],
              );
            })),
        resizeToAvoidBottomInset: false,
      ),
      onWillPop: back,
    );
  }

  /// 根据step获取对应的子页面
  Widget getContentWidget(EditStep step) {
    return Stack(
      children: [
        buildBaseInfoWidget(),
        Visibility(
          child: buildBaseInfoConfirmWidget(),
          visible: _licenceEditModel.currentStep.index > 0,
        ),
        Visibility(
          child: buildUpdatePhotoWidget(),
          visible: _licenceEditModel.currentStep.index > 1,
        ),
      ],
    );
  }

  @override
  List<SingleChildWidget> getProvider() {
    return [
      ChangeNotifierProvider<LicenceEditModel>(
          create: (context) => _licenceEditModel)
    ];
  }

  ///顶部文案
  Widget headTip() {
    return Visibility(
        visible:
            _licenceEditModel.currentStep == EditStep.UPDATE_LICENSE_PHOTO &&
                _licenceEditModel.showTips,
        child: Container(
          padding: EdgeInsets.all(10),
          color: Color(0xFFFFF7EF),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                  child: Text(
                "若所需资质与实际不对应，请重新选择客户类型",
                style: TextStyle(fontSize: 14, color: Color(0xFF99664D)),
              )),
              GestureDetector(
                child: Container(
                  padding: EdgeInsets.only(top: 5),
                  child: Image.asset(
                    "assets/images/licence/icon_tips_close.png",
                    width: 12,
                    height: 12,
                  ),
                ),
                onTap: () {
                  _licenceEditModel.updateTips(false);
                },
              )
            ],
          ),
        ));
  }

  /// step-0 信息认证
  Widget buildBaseInfoWidget() {
    return LicenseBaseInfoWidget(
      widget.mIsAddType == "true",
      _licenceEditModel.customerTypeCode,
      _licenceEditModel.qualificationNo,
      widget.from,
      widget.merchantId,
      widget.ecOrgCode,
      _licenceEditModel.licenseInitData?.customer?.applicationNumber,
      widget.type,
      (customerTypeCode, baseInfo) {
        setState(() {
          _licenceEditModel.customerTypeCode = customerTypeCode;
          _licenceEditModel.baseInfo = baseInfo;
          _licenceEditModel.currentStep = EditStep.BASE_INFO_CONFIRM;
        });
      },
    );
  }

  /// step-1 信息确认
  Widget buildBaseInfoConfirmWidget() {
    return LicenseBaseInfoConfirmWidget(
        _licenceEditModel.baseInfo,
        widget.merchantId,
        widget.from,
        widget.type,
        widget.ecOrgCode,
        _licenceEditModel.licenseInitData?.customer?.applicationNumber,
        _licenceEditModel.customerTypeCode!, (licenseInitData, provinceCode,
            cityCode, areaCode, streetCode, detailAddress) {
      setState(() {
        _licenceEditModel.mProvinceCode = provinceCode;
        _licenceEditModel.mCityCode = cityCode;
        _licenceEditModel.mAreaCode = areaCode;
        _licenceEditModel.mStreetCode = streetCode;
        _licenceEditModel.detailAddress = detailAddress;
        _licenceEditModel.licenseInitData = licenseInitData;
        _licenceEditModel.currentStep = EditStep.UPDATE_LICENSE_PHOTO;
      });
    });
  }

  /// step-2 更新图片
  Widget buildUpdatePhotoWidget() {
    return LicenseUpdatePhotoWidget(
        widget.isDraft == "true",
        widget.merchantId,
        widget.from,
        _licenceEditModel.licenseInitData?.customer?.applicationNumber,
        _licenceEditModel.customerTypeCode!,
        widget.mIsAddType == "true",
        _licenceEditModel.mProvinceCode,
        _licenceEditModel.mCityCode,
        _licenceEditModel.mAreaCode,
        _licenceEditModel.mStreetCode,
        _licenceEditModel.detailAddress,
        _licenceEditModel.licenseInitData, (isSuccess) {
      if (isSuccess) {
        result = true;
        XYYContainer.close(context, resultData: {"result": true});
      }
    });
  }

  @override
  String getTitleName() {
    return widget.mIsAddType == "true" ? '添加首营资质' : '资质变更';
  }

  @override
  PreferredSizeWidget getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      onLeftPressed: () {
        back().then((value) {
          if (value == true) {
            XYYContainer.close(context, resultData: null);
          }
        });
      },
    );
  }

  Future<bool> back() {
    if (widget.mIsAddType == "false" || result == true) {
      // XYYContainer.close(context, resultData: result ? {"result": true} : null);
      return Future.value(true);
    }
    switch (_licenceEditModel.currentStep) {
      case EditStep.BASE_INFO:
        return Future.value(true);
      case EditStep.BASE_INFO_CONFIRM:
        _licenceEditModel.currentStep = EditStep.BASE_INFO;
        setState(() {});
        break;
      case EditStep.UPDATE_LICENSE_PHOTO:
        _licenceEditModel.currentStep = EditStep.BASE_INFO_CONFIRM;
        setState(() {});
        break;
    }
    return Future.value(false);
  }
}

class LicenceEditModel extends ChangeNotifier {
  bool showTips = true;

  //--------------------------
  EditStep currentStep = EditStep.BASE_INFO;

  String? customerTypeName;
  int? customerTypeCode = -1;
  String? qualificationNo;
  LicenseBaseInfoData? baseInfo;
  LicenseInitData? licenseInitData;

  String? mProvinceCode;
  String? mCityCode;
  String? mAreaCode;
  String? mStreetCode;
  String? detailAddress = "";

  LicenceEditModel(String? qualificationNo, String? mIsAddType,
      LicenseInitData? licenseInitData) {
    this.customerTypeCode = licenseInitData?.customer?.customerType ?? -1;
    this.qualificationNo = qualificationNo;
    this.licenseInitData = licenseInitData;
    // 如果为资质变更，直接跳转到最后一步
    if (mIsAddType == "false") {
      currentStep = EditStep.UPDATE_LICENSE_PHOTO;
      this.mProvinceCode = licenseInitData?.customer?.provinceCode?.toString();
      this.mCityCode = licenseInitData?.customer?.cityCode?.toString();
      this.mAreaCode = licenseInitData?.customer?.areaCode?.toString();
      this.mStreetCode = licenseInitData?.customer?.streetCode?.toString();
      this.detailAddress = licenseInitData?.customer?.address?.toString();
    }
  }

  void updateTips(bool showTips) {
    this.showTips = showTips;
    notifyListeners();
  }
}

enum EditStep { BASE_INFO, BASE_INFO_CONFIRM, UPDATE_LICENSE_PHOTO }
