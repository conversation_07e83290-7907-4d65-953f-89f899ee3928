import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/license_detail_data.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class LicenseDetailHeaderWidget extends StatelessWidget {
  final LicenseAudit? licenseAudit;

  LicenseDetailHeaderWidget(this.licenseAudit);

  @override
  Widget build(BuildContext context) {
    return buildCommonHeaderWidget();
  }

  Widget buildCommonHeaderWidget() {
    return Container(
      margin: EdgeInsets.only(top: 10,left: 10,right: 10),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(8)),
      padding: EdgeInsets.symmetric(vertical: 21.5, horizontal: 12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            onTap: () {
              Clipboard.setData(
                  ClipboardData(text: licenseAudit?.applicationNumber));
              XYYContainer.toastChannel.toast("单据编号复制成功");
            },
            child: buildHeaderRow("单据编号:",
                licenseAudit?.applicationNumber ?? "-", Color(0xff292933)),
          ),
          SizedBox(
            height: 23,
          ),
          buildHeaderRow(
              "审核状态:", licenseAudit?.auditStatusName ?? "-", Color(0xffFE3D3D)),
          SizedBox(
            height: 23,
          ),
          buildHeaderRow(
              "单据类型:", licenseAudit?.typeName ?? "-", Color(0xff292933)),
          SizedBox(
            height: 23,
          ),
          buildHeaderRow(
              "审批机构:", licenseAudit?.oaBranchName ?? "-", Color(0xff292933))
        ],
      ),
    );
  }

  Widget buildHeaderRow(String label, String content, Color contentColor) {
    return Row(
      children: [
        Text(
          label,
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
          style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
              color: Color(0xff676773)),
        ),
        SizedBox(
          width: 12,
        ),
        Expanded(
            child: Text(content,
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
                textAlign: TextAlign.right,
                style: TextStyle(
                    color: contentColor,
                    fontWeight: FontWeight.w500,
                    fontSize: 14)))
      ],
    );
  }
}
