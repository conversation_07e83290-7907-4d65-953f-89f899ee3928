import 'package:flutter/material.dart';

class TriangleUpPainter extends CustomPainter {

  Color? color; //填充颜色
  late Paint _paint; //画笔
  late Path _path; //绘制路径
  double? angle; //角度

  TriangleUpPainter() {
    _paint = Paint()
      ..strokeWidth = 1.0 //线宽
      ..color = Colors.white
      ..isAntiAlias = true;
    _path = Path();
  }

  @override
  void paint(Canvas canvas, Size size) {
    final baseX = size.width;
    final baseY = size.height;
    //起点
    _path.moveTo(baseX*0.5, 0);
    _path.lineTo(baseX, baseY);
    _path.lineTo(0, baseY);
    canvas.drawPath(_path, _paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}

class TriangleUpWidget extends StatefulWidget {
  double height;
  double width;

  TriangleUpWidget({Key? key, this.height = 14, this.width = 16}) : super(key:
  key);

  @override
  CoreTriangleState createState() => CoreTriangleState();
}

class CoreTriangleState extends State<TriangleUpWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
        height: widget.height,
        width: widget.width,
        child: CustomPaint(
          painter: TriangleUpPainter(),
        ));
  }
}