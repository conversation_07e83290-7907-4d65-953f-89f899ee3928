import 'dart:convert';
import 'dart:ui' as ui;
import 'dart:math' as math;
import 'dart:ui';

import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';

class PhotoListViewPage extends BasePage {
  final String? urlPathListJson;
  final String? titleListJson;
  final String? subTitleListJson;
  final String? isWaterMark;
  final String? position;

  PhotoListViewPage(
      {this.urlPathListJson,
      this.titleListJson,
      this.subTitleListJson,
      this.isWaterMark,
      this.position});

  @override
  BaseState<StatefulWidget> initState() {
    return PhotoListViewPageState();
  }
}

class PhotoListViewPageState extends BaseState<PhotoListViewPage> {
  // 水印
  String waterMarkStr = "";

  // 对应当前图片widget
  GlobalKey imageKey = GlobalKey();

  // 已加载的图片Image对象
  ui.Image? loadedImage;

  // 是否添加水印
  late bool isWaterMark;

  // 图片Url列表
  List<dynamic> urlPathList = [];

  // 标题文案列表
  List<dynamic> titleList = [];

  // 子标题文案列表
  List<dynamic> subTitleList = [];

  // 用于更新标题的ValueNotifier
  ValueNotifier<int> positionNotifier = ValueNotifier<int>(0);

  @override
  void onCreate() {
    super.onCreate();
    initWidgetParams();
    if (isWaterMark) {
      UserInfoUtil.getUserInfo().then((value) {
        waterMarkStr = "药帮忙平台专用${value?.sysUserId ?? "--"}";
        setState(() {});
      });
    }
  }

  /// 初始化，将外部传入参数
  void initWidgetParams() {
    print("guan print params----------");
    print("guan ${widget.urlPathListJson}");
    print("guan ${widget.titleListJson}");
    print("guan ${widget.subTitleListJson}");
    // 是否添加水印
    isWaterMark = widget.isWaterMark == "true";
    // 图片Url列表
    urlPathList = jsonConvertToList(widget.urlPathListJson);
    // 标题文案列表
    titleList = jsonConvertToList(widget.titleListJson);
    // 子标题文案列表
    subTitleList = jsonConvertToList(widget.subTitleListJson);

    print("guan print params result ----------");
    // 初始位置
    if (widget.position != null) {
      positionNotifier.value = int.tryParse(widget.position ?? "0") ?? 0;
    }
  }

  List<dynamic> jsonConvertToList(String? jsonStr) {
    List<dynamic>? result;
    if (jsonStr != null) {
      try {
        var jsonDecode = json.decode(jsonStr);
        if (jsonDecode is List) {
          result = jsonDecode;
        }
      } catch (ignore) {}
    }
    return result ?? [];
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: buildImagePageView(),
        ),
        buildSaveImageWidget()
      ],
    );
  }

  // 图片展示组件
  Widget buildImagePageView() {
    print("guan build buildImagePageView ${positionNotifier.value}");
    return CustomPaint(
      foregroundPainter: WaterMarkPainter(paintWaterMarkImage),
      child: ExtendedImageGesturePageView.builder(
        itemBuilder: (context, index) {
          return buildPageItem(index);
        },
        itemCount: urlPathList.length,
        scrollDirection: Axis.horizontal,
        controller: PageController(
            initialPage: positionNotifier.value, keepPage: false),
        onPageChanged: (index) {
          print("guan build onPageChanged ${index}");
          positionNotifier.value = index;
        },
      ),
    );
  }

  Widget buildPageItem(int index) {
    print("guan buildPageItem ${index}");
    var urlPath = urlPathList[index];
    return Container(
      child: urlPath == null || urlPath.isEmpty
          ? Container()
          : urlPath.startsWith("http") || urlPath.startsWith("Http")
              ? ExtendedImage.network(
                  urlPath,
                  mode: ExtendedImageMode.gesture,
                  initGestureConfigHandler: (state) {
                    return GestureConfig(
                        inPageView: true,
                        initialScale: 1.0,
                        maxScale: 5.0,
                        cacheGesture: false,
                        animationMaxScale: 6.0,
                        initialAlignment: InitialAlignment.center);
                  },
                  afterPaintImage: (canvas, rect, image, paint) {
                    loadedImage = image;
                  },
                  fit: BoxFit.contain,
                )
              : ExtendedImage.asset(
                  urlPath,
                  key: imageKey,
                  mode: ExtendedImageMode.gesture,
                  width: MediaQuery.of(context).size.width,
                  fit: BoxFit.contain,
                  afterPaintImage: (canvas, rect, image, paint) {
                    loadedImage = image;
                  },
                  height: MediaQuery.of(context).size.height,
                ),
    );
  }

  // 保存图片按钮
  Widget buildSaveImageWidget() {
    return GestureDetector(
      onTap: saveImageAction,
      behavior: HitTestBehavior.opaque,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.only(top: 15, bottom: 20),
        color: const Color.fromRGBO(0, 0, 0, 0.4),
        alignment: Alignment.center,
        child: Text(
          "保存到相册",
          style: TextStyle(
              fontSize: 14, fontWeight: FontWeight.normal, color: Colors.white),
        ),
      ),
    );
  }

  void saveImageAction() async {
    showLoadingDialog();
    await saveImage();
    dismissLoadingDialog();
  }

  // 添加水印，并保存图片
  Future<void> saveImage() async {
    try {
      if (loadedImage == null) {
        showToast("保存失败，请退出重试");
        return;
      }
      // 创建canvas
      var recorder = PictureRecorder();
      var canvas = Canvas(recorder);
      // 绘制图片
      canvas.drawImage(loadedImage!, Offset(0, 0), Paint());
      // 绘制水印
      paintWaterMarkImage(canvas, loadedImage!.width, loadedImage!.height);
      // 保存图片
      ui.Picture picture = recorder.endRecording();
      final markedImage =
          await picture.toImage(loadedImage!.width, loadedImage!.height);

      final pngBytes =
          await markedImage.toByteData(format: ui.ImageByteFormat.png);

      var saveResult =
          await ImageGallerySaver.saveImage(pngBytes!.buffer.asUint8List());
      if (saveResult != null && saveResult is Map<dynamic, dynamic>) {
        if (saveResult["isSuccess"] == true) {
          showToast("保存成功");
          return;
        }
      }
      showToast("保存失败！");
    } catch (e) {
      showToast("保存失败:${e.runtimeType.toString()}");
    }
  }

  void paintWaterMarkImage(Canvas canvas, int width, int height) {
    if (waterMarkStr.isEmpty) {
      return;
    }
    print("guan w:${width},h:${height}");
    // 计算四边形的对角线长度（宽度）
    double dimensionWidth = math.sqrt(math.pow(width, 2) + math.pow(height, 2));
    var fontSize = 30;
    // 完整覆盖下的矩形高度
    var dimensionHeight = ((width * height) / dimensionWidth) * 2;
    // 完整覆盖下的矩形面积
    var rectSize = dimensionHeight * dimensionWidth;

    var titleTextStyle = TextStyle(
        fontSize: fontSize.toDouble(),
        fontWeight: FontWeight.w900,
        color: Color.fromRGBO(0, 0, 0, .2),
        height: 8);
    var initTotalLength =
        boundingTextSize(waterMarkStr, titleTextStyle).width.toInt()+30;

    // 根据面积与字符大小计算文本重复次数
    int textRepeating =
        (rectSize / (fontSize * initTotalLength*8))
            .round(); // text.length + padding 是因为要添加个空格字符

    print("guan textRepeating:${textRepeating.toString()}");

    math.Point pivotPoint = math.Point(dimensionWidth / 2, dimensionHeight / 2);
    canvas.save();
    canvas.translate(pivotPoint.x.toDouble(), pivotPoint.y.toDouble());
    canvas.rotate(-25 * math.pi / 180);
    canvas.translate(
        -pivotPoint.distanceTo(math.Point(0, height)),
        -pivotPoint.distanceTo(
            math.Point(0, 0))); // 计算文本区域起始坐标分别到图片左侧顶部与底部的距离，作为文本区域移动的距离。

    var titleTextPainter = TextPainter(
      text: TextSpan(
          text:
              getFixedWidthText(initTotalLength, waterMarkStr, titleTextStyle) *
                  textRepeating,
          style: titleTextStyle),
      maxLines: null,
      textDirection: ui.TextDirection.ltr,
      textAlign: TextAlign.center,
    );
    titleTextPainter.layout(maxWidth: dimensionWidth);
    titleTextPainter.paint(canvas, Offset.zero);

    canvas.restore();
  }

  Size boundingTextSize(String? text, TextStyle style) {
    if (text == null || text.isEmpty) {
      return Size.zero;
    }
    final TextPainter textPainter = TextPainter(
        textDirection: TextDirection.ltr,
        text: TextSpan(text: text, style: style),
        maxLines: 1)
      ..layout(maxWidth: double.infinity);
    return textPainter.size;
  }

  String getFixedWidthText(int totalLength, String keyWord, TextStyle style) {
    return keyWord
        .padLeft(totalLength - boundingTextSize(keyWord, style).width.toInt());
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    // return null;
    return CommonTitleBar(
      getTitleName(),
      leftType: LeftButtonType.close,
      titleWidget: ValueListenableBuilder<int>(
        valueListenable: positionNotifier,
        builder: (context, value, child) {
          return Container(
            width: 200,
            alignment: Alignment.center,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Flexible(
                  flex: 1,
                  fit: FlexFit.loose,
                  child: Text(
                    titleList[positionNotifier.value] ?? "--",
                    textAlign: TextAlign.right,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                    style: TextStyle(
                        color: const Color(0xff292933),
                        fontSize: 17,
                        fontWeight: FontWeight.normal),
                  ),
                ),
                Text(
                  subTitleList[positionNotifier.value] ?? "(--)",
                  textAlign: TextAlign.right,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  style: TextStyle(
                      color: const Color(0xff292933),
                      fontSize: 17,
                      fontWeight: FontWeight.normal),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  @override
  String getTitleName() {
    return "";
  }
}

class WaterMarkPainter extends CustomPainter {
  final PaintWaterMarker paintWaterMarker;

  WaterMarkPainter(this.paintWaterMarker);

  @override
  void paint(Canvas canvas, Size size) {
    paintWaterMarker(canvas, size.width.toInt(), size.height.toInt());
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}

typedef PaintWaterMarker = void Function(Canvas canvas, int width, int height);
