import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_appbar_search.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/licence_item_bean.dart';
import 'package:XyyBeanSproutsFlutter/licence/licence_list_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';

class LicenceSearch extends BasePage {
  @override
  BaseState<StatefulWidget> initState() {
    return LicenceSearchState();
  }
}

class LicenceSearchState extends BaseState<LicenceSearch> {
  var _refreshController = EasyRefreshController();
  var _scrollController = ScrollController();
  late LicenceListModel _licenceListModel;

  @override
  void onCreate() {
    _licenceListModel = LicenceListModel(_refreshController, _scrollController);
    super.onCreate();
  }

  @override
  List<SingleChildWidget> getProvider() {
    return [
      ChangeNotifierProvider<LicenceListModel>(
          create: (context) => _licenceListModel)
    ];
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Divider(color: Color(0xFFF6F6F6), height: 1),
          Expanded(
            child: Container(
              color: Color(0xffefeff4),
              child: buildListView(),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildListView() {
    return Consumer<LicenceListModel>(builder: (context, model, child) {
      return EasyRefresh.custom(
          controller: _refreshController,
          scrollController: _scrollController,
          enableControlFinishRefresh: true,
          enableControlFinishLoad: true,
          onRefresh: () async {
            _licenceListModel.requestListData(true);
          },
          onLoad: !_licenceListModel.isLastPage
              ? () async {
                  _licenceListModel.requestListData(false);
                }
              : null,
          slivers: [
            SliverList(
              delegate:
                  SliverChildBuilderDelegate((BuildContext context, int index) {
                //创建列表项
                if (_licenceListModel.list != null &&
                    _licenceListModel.list!.length > index) {
                  LicenceItemBean model = _licenceListModel.list![index];
                  return LicenceListItem(model);
                }
                return Container();
              }, childCount: _licenceListModel.list?.length ?? 0),
            )
          ],
          emptyWidget: getEmptyWidget());
    });
  }

  @override
  PreferredSizeWidget getTitleBar(BuildContext context) {
    return SAppBarSearch(
      hintText: "请输入客户名称",
      showLeading: false,
      hideCancel: false,
      autoFocus: true,
      onSearch: (value) {
        if (value.isEmpty) {
          showToast("请输入搜索内容");
          return;
        }
        _licenceListModel.keyword = value;
        _licenceListModel.requestListData(true);
      },
    );
  }

  Widget? getEmptyWidget() {
    if (_licenceListModel.isSuccess == null) {
      return null;
    }
    if (_licenceListModel.isSuccess == false) {
      return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
        _licenceListModel.requestListData(true);
      });
    }

    if ((_licenceListModel.list?.length ?? 0) == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  @override
  String getTitleName() {
    return "";
  }
}

class LicenceListModel extends ChangeNotifier {
  var _isDisposed = false;
  bool isLastPage = false;
  int pageNum = 0;
  bool forceRefresh = false;
  List<LicenceItemBean>? list;
  bool? isSuccess;
  String? keyword;
  final EasyRefreshController _refreshController;
  final ScrollController _scrollController;

  LicenceListModel(this._refreshController, this._scrollController);

  void requestListData(bool refresh) async {
    if (refresh) {
      pageNum = 0;
      forceRefresh = true;
      list?.clear();
    } else {
      pageNum += 1;
      forceRefresh = false;
    }
    EasyLoading.show(status: "加载中", maskType: EasyLoadingMaskType.clear);
    Network<LicenceItemBean>(LicenceItemBean()).requestListData(
        'licenseOptimize/queryLicenseAuditList',
        method: RequestMethod.POST,
        contentType: RequestContentType.FORM,
        parameters: {
          "offset": pageNum.toString(),
          "limit": "10",
          "keyword": keyword,
        }).then((value) {
      handleResult(value.isSuccess, value.data);
    });
  }

  void handleResult(bool isSuccess, List<LicenceItemBean>? data) {
    if (!_isDisposed && isSuccess) {
      this.isSuccess = isSuccess;
      EasyLoading.dismiss();
      if (data != null) {
        var tempList = data;
        if (forceRefresh) {
          list = tempList;
          _scrollController.jumpTo(0);
        } else {
          list!.addAll(tempList);
        }
        isLastPage = false;
      } else {
        isLastPage = true;
      }
      _refreshController.finishRefresh();
      _refreshController.finishLoad(noMore: isLastPage);
      notifyListeners();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }
}
