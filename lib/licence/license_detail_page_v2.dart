import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/licence_examine_log_bean.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/license_detail_data.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/license_have_type.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/license_init_data.dart';
import 'package:XyyBeanSproutsFlutter/licence/widget/license_detail_examine_log_widget.dart';
import 'package:XyyBeanSproutsFlutter/licence/widget/license_detail_header_widget.dart';
import 'package:XyyBeanSproutsFlutter/licence/widget/license_detail_photo_widget.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

import 'bean/licence_examine_log_list_bean.dart';

/// 资质详情
class LicenseDetailPageV2 extends BasePage {
  final String? licenseAuditId;
  final String? type;

  LicenseDetailPageV2(this.licenseAuditId, this.type);

  @override
  BaseState<StatefulWidget> initState() {
    return LicenseDetailPageState();
  }
}

class LicenseDetailPageState extends BaseState<LicenseDetailPageV2> {
  LicenseDetailData? detailData;
  List<LicenceExamineLogListBean>? examineLogList;
  EasyRefreshController _controller = EasyRefreshController();
  PageState pageState = PageState.Empty;

  @override
  void onCreate() {
    super.onCreate();
    requestAllData();
  }

  @override
  Widget buildWidget(BuildContext context) {
    var isEdit = detailData?.licenseAudit?.isEdit ?? -1;
    List<LicenseDetailItem> licencePhotoList = [];
    licencePhotoList.addAll(detailData?.necessaryLicenceList ?? []);
    licencePhotoList.addAll(detailData?.optionalLicenceList ?? []);
    return Container(
      color: Color(0xFFf1f6f9),
      child: Column(
        children: [
          Expanded(
            child: EasyRefresh(
              onRefresh: () async {
                return await requestAllData();
              },
              onLoad: null,
              emptyWidget: this.getEmptyWidget(),
              child: ListView(
                children: [
                  LicenseDetailHeaderWidget(detailData?.licenseAudit),
                  LicenseDetailPhotoWidget(licencePhotoList),
                  LicenseDetailExamineLogWidget(examineLogList)
                ],
              ),
            ),
          ),
          Visibility(
            visible: isEdit == 0 || isEdit == 2,
            child: GestureDetector(
              onTap: () {
                requestHaveType();
              },
              child: Container(
                decoration: BoxDecoration(
                    color: Color(0xff35C561),
                    borderRadius: BorderRadius.circular(5),
                    border: Border.all(color: Color(0xffd3d3d3), width: 1)),
                margin: EdgeInsets.all(10),
                alignment: Alignment.center,
                height: 45,
                child: Text(
                  "编辑信息",
                  style: TextStyle(
                      color: Colors.white,
                      fontSize: 15,
                      fontWeight: FontWeight.normal),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  /// 空页面
  Widget? getEmptyWidget() {
    switch (pageState) {
      case PageState.Error:
        return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
          requestAllData();
        });
      case PageState.Empty:
        return PageStateWidget.pageEmpty(PageState.Empty);
      default:
        return null;
    }
  }

  @override
  String getTitleName() {
    return "资质详情";
  }

  requestAllData() async {
    EasyLoading.show(status: "加载中...", maskType: EasyLoadingMaskType.clear);
    await Future.wait([requestLicenseDetail(), requestExamineLog()])
        .whenComplete(() {
      EasyLoading.dismiss();
      if (examineLogList == null && detailData == null) {
        pageState = PageState.Error;
      } else {
        pageState = PageState.Normal;
      }
      setState(() {});
    });
  }

  Future<void> requestExamineLog() async {
    var value = await NetworkV2<LicenceExamineLogBean>(LicenceExamineLogBean())
        .requestDataV2("licenseOptimize/queryLicenseAuditLogList",
            contentType: RequestContentType.FORM,
            method: RequestMethod.POST,
            parameters: {
          "licenseAuditId": widget.licenseAuditId,
          "type": widget.type
        });
    if (mounted) {
      if (value.isSuccess == true) {
        examineLogList = value.getData()?.list;
      }
    }
  }

  Future<void> requestLicenseDetail() async {
    var value = await NetworkV2<LicenseDetailData>(LicenseDetailData())
        .requestDataV2("licenseOptimize/queryLicenseAuditDetail",
            method: RequestMethod.GET,
            contentType: RequestContentType.FORM,
            parameters: {
          "licenseAuditId": widget.licenseAuditId,
          "type": widget.type
        });
    if (mounted) {
      if (value.isSuccess == true) {
        detailData = value.getData();
      }
    }
  }

  void requestHaveType() {
    if (detailData?.licenseAudit?.merchantId == null) {
      return;
    }
    EasyLoading.show(status: "加载中...", maskType: EasyLoadingMaskType.clear);
    NetworkV2<LicenseHaveType>(LicenseHaveType()).requestDataV2(
        "invoice/isHaveType",
        method: RequestMethod.GET,
        parameters: {
          "merchantId": detailData?.licenseAudit?.merchantId,
        }).then((value) {
      EasyLoading.dismiss();
      if (mounted) {
        if (value.isSuccess == true) {
          if (value.getData()!.exit == 0) {
            // 提示发票信息
            showReceiptDialog(
                "设置发票类型", "此客户还未设置发票类型，需先设置发票类型再提交资质信息", "是否前往设置？", () {
              // 跳转发票设置
              XYYContainer.open(
                  "xyy://crm-app.ybm100.com/drugstore/detail/item?merchantId=${detailData?.licenseAudit?.merchantId}&position=${4}");
            });
          } else {
            // 请求资质初始化数据
            requestLicenseAuditDetail();
          }
        }
      }
    });
  }

  /// 跳转资质编辑
  void jumpEditPage(LicenseInitData data) {
    // 1是首营，2是变更
    String isAddType = (detailData?.licenseAudit?.type == 1) ? "true" : "false";
    Navigator.of(context).pushNamed('/licence_edit_base_page', arguments: {
      "qualificationNo": data.customer!.qualificationNo,
      "mIsAddType": isAddType,
      "merchantId": detailData?.licenseAudit?.merchantId.toString(),
      "type": detailData?.licenseAudit?.type?.toString(),
      "ecOrgCode": detailData?.licenseAudit?.ecOrgCode,
      "isDraft": "true",
      "from": "",
      "licenseInitDataJson": json.encode(data)
    }).then((value) {
      print("guan :$value");
      if (value != null) {
        requestLicenseDetail();
      }
    });
  }

  void showReceiptDialog(
      String title, String content, String tips, VoidCallback onConfirm) {
    showDialog(
        context: context,
        builder: (context) {
          return Center(
            child: Container(
              margin: EdgeInsets.all(40),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(7), color: Colors.white),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                      margin: EdgeInsets.fromLTRB(25, 25, 25, 0),
                      child: Text(
                        title,
                        style: TextStyle(
                            color: Color(0xff333333),
                            fontSize: 17,
                            fontWeight: FontWeight.normal),
                      )),
                  Container(
                    padding: EdgeInsets.fromLTRB(25, 4, 25, 0),
                    child: Text(
                      content,
                      style: TextStyle(
                          fontSize: 14,
                          color: Color(0xff666666),
                          fontWeight: FontWeight.normal),
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.fromLTRB(0, 10, 0, 18),
                    padding: EdgeInsets.symmetric(horizontal: 25),
                    child: Text(
                      tips,
                      style: TextStyle(
                          fontSize: 14,
                          color: Color(0xff666666),
                          fontWeight: FontWeight.normal),
                    ),
                  ),
                  Divider(
                    color: Color(0xffe1e1e5),
                    height: 0.5,
                  ),
                  Container(
                    height: 50,
                    child: Row(
                      children: [
                        Expanded(
                            child: GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () {
                            Navigator.of(context).pop();
                          },
                          child: Container(
                            height: 50,
                            alignment: Alignment.center,
                            child: Text("取消",
                                style: TextStyle(
                                    fontSize: 14,
                                    color: Color(0xff999999),
                                    fontWeight: FontWeight.normal)),
                          ),
                        )),
                        Container(
                          color: Color(0xffe1e1e5),
                          width: 0.5,
                          child: Container(),
                        ),
                        Expanded(
                            child: GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () {
                            Navigator.of(context).pop();
                            onConfirm();
                          },
                          child: Container(
                            height: 50,
                            alignment: Alignment.center,
                            child: Text("确认",
                                style: TextStyle(
                                    fontSize: 14,
                                    color: Color(0xff35c561),
                                    fontWeight: FontWeight.normal)),
                          ),
                        )),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        });
  }

  void requestLicenseAuditDetail() {
    if (detailData?.licenseAudit?.merchantId == null) {
      return;
    }
    EasyLoading.show(status: "加载中...", maskType: EasyLoadingMaskType.clear);
    NetworkV2<LicenseInitData>(LicenseInitData()).requestDataV2(
        "licenseOptimize/initLicenseAuditDetail",
        method: RequestMethod.POST,
        contentType: RequestContentType.FORM,
        parameters: {
          "merchantId": detailData?.licenseAudit?.merchantId,
          "applicationNumber": detailData?.licenseAudit?.applicationNumber,
          "type": detailData?.licenseAudit?.type,
          "ecOrgCode": detailData?.licenseAudit?.ecOrgCode
        }).then((value) {
      EasyLoading.dismiss();
      if (mounted) {
        if (value.isSuccess == true && value.data != null) {
          jumpEditPage(value.getData()!);
        } else {
          showToast("资质状态获取失败，请先重新获取资质状态");
        }
      }
    });
  }
}
