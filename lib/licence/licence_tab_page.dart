import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/licence_item_bean.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:provider/provider.dart';

import 'licence_list_item.dart';
import 'licence_manager_page.dart';

class LicenceTabPage extends BasePage {
  var _refreshController;
  var _scrollController;
  LicenceListModel? _licenceListModel;

  LicenceTabPage(
      this._refreshController, this._scrollController, this._licenceListModel);

  @override
  BaseState<StatefulWidget> initState() {
    return LicenceTabPageState(
        _refreshController, _scrollController, this._licenceListModel);
  }
}

class LicenceTabPageState extends BaseState {
  var _refreshController;
  var _scrollController;
  LicenceListModel? _licenceListModel;

  LicenceTabPageState(
      this._refreshController, this._scrollController, this._licenceListModel);

  @override
  Widget buildWidget(BuildContext context) {
    return Scaffold(
      body: Container(
        color: Color(0xFFF6F6F6),
        padding: EdgeInsets.only(top: 5, bottom: 5),
        child: buildListView(),
      ),
      resizeToAvoidBottomInset: false,
    );
  }

  @override
  bool isSubPage() {
    return true;
  }

  @override
  String getTitleName() {
    return '';
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return null;
  }

  Widget buildListView() {
    return Consumer<LicenceListModel>(builder: (context, model, child) {
      return EasyRefresh(
          controller: _refreshController,
          scrollController: _scrollController,
          enableControlFinishRefresh: true,
          enableControlFinishLoad: true,
          onRefresh: () async {
            _licenceListModel!.requestListData(true);
          },
          onLoad: !_licenceListModel!.isLastPage
              ? () async {
                  _licenceListModel!.requestListData(false);
                }
              : null,
          child: ListView.builder(
            itemCount: _licenceListModel?.list != null
                ? _licenceListModel?.list?.length
                : 0,
            itemBuilder: (BuildContext context, int index) {
              List<LicenceItemBean>? source = _licenceListModel?.list;
              if (source != null && index < source.length) {
                LicenceItemBean bean = source[index];
                return LicenceListItem(bean);
              }
              return Container();
            },
          ),
          emptyWidget: getEmptyWidget());
    });
  }

  Widget? getEmptyWidget() {
    if (_licenceListModel!.isSuccess == null) {
      return null;
    }
    if (_licenceListModel!.isSuccess == false) {
      return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
        _licenceListModel!.requestListData(true);
      });
    }

    if ((_licenceListModel!.list?.length ?? 0) == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }
}
