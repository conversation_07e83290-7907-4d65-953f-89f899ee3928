// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'licence_filter_bean.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LicenceFilterBean _$LicenceFilterBeanFromJson(Map<String, dynamic> json) {
  return LicenceFilterBean()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : LicenceFilterBean.fromJson(json['data'] as Map<String, dynamic>)
    ..code = json['code'] as int?
    ..name = json['name'] as String?
    ..licenseStatusList = (json['licenseStatusList'] as List<dynamic>?)
        ?.map((e) => LicenceFilterBean.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$LicenceFilterBeanToJson(LicenceFilterBean instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'code': instance.code,
      'name': instance.name,
      'licenseStatusList': instance.licenseStatusList,
    };
