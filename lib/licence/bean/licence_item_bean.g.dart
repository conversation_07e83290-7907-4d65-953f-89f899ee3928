// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'licence_item_bean.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LicenceItemBean _$LicenceItemBeanFromJson(Map<String, dynamic> json) {
  return LicenceItemBean()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : LicenceItemBean.fromJson(json['data'] as Map<String, dynamic>)
    ..branchName = json['branchName'] as String?
    ..code = json['code'] as String?
    ..merchantName = json['merchantName'] as String?
    ..type = json['type'] as int?
    ..createTime = json['createTime'] as int?
    ..auditStatus = json['auditStatus'] as int?
    ..licenseSourceStr = json['licenseSourceStr'] as String?
    ..offset = json['offset'] as int?
    ..sysUserName = json['sysUserName'] as String?
    ..sysUserId = json['sysUserId'] as int?
    ..typeName = json['typeName'] as String?
    ..audit1StatusName = json['audit1StatusName'] as String?
    ..holder = json['holder'] as String?
    ..oaBranchName = json['oaBranchName'] as String?;
}

Map<String, dynamic> _$LicenceItemBeanToJson(LicenceItemBean instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'branchName': instance.branchName,
      'code': instance.code,
      'merchantName': instance.merchantName,
      'type': instance.type,
      'createTime': instance.createTime,
      'auditStatus': instance.auditStatus,
      'licenseSourceStr': instance.licenseSourceStr,
      'offset': instance.offset,
      'sysUserName': instance.sysUserName,
      'sysUserId': instance.sysUserId,
      'typeName': instance.typeName,
      'audit1StatusName': instance.audit1StatusName,
      'holder': instance.holder,
      'oaBranchName': instance.oaBranchName,
    };
