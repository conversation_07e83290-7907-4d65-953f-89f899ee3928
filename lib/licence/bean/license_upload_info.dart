import 'package:XyyBeanSproutsFlutter/licence/bean/license_image_upload_info.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'license_upload_info.g.dart';

@JsonSerializable()
class LicenseUploadInfo extends BaseModelV2<LicenseUploadInfo> {
  List<LicenseImageUpLoadInfo>? credentialList;
  String? customerType;
  String? deliveryAddress;
  String? deliveryCityId;
  String? deliveryDistrictId;
  String? deliveryProvinceId;
  String? deliveryStreetId;
  String? merchantId;
  String? operateType;
  String? applicationNumber;
  String? type;
  String? from;

  LicenseUploadInfo();

  @override
  LicenseUploadInfo fromJsonMap(Map<String, dynamic>? json) {
    return LicenseUploadInfo.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$LicenseUploadInfoToJson(this);
  }

  factory LicenseUploadInfo.fromJson(Map<String, dynamic> json) =>
      _$LicenseUploadInfoFromJson(json);
}
