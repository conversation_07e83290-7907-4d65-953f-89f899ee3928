import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'license_base_info_data.g.dart';

@JsonSerializable()
class LicenseBaseInfoData extends BaseModelV2<LicenseBaseInfoData> {
  String? customerName;
  int? provinceCode;
  String? provinceName;
  int? cityCode;
  String? cityName;
  int? areaCode;
  String? areaName;
  String? address;
  String? street;
  int? streetCode;

  dynamic localCheck;

  LicenseBaseInfoData();

  @override
  fromJsonMap(Map<String, dynamic>? json) {
    return LicenseBaseInfoData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$LicenseBaseInfoDataToJson(this);
  }

  factory LicenseBaseInfoData.fromJson(Map<String, dynamic> json) =>
      _$LicenseBaseInfoDataFromJson(json);
}
