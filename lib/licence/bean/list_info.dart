import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'list_info.g.dart';

@JsonSerializable()
class ListInfo extends BaseModelV2<ListInfo> {
  //"enclosureName":"营业执照.png", //附件名称
  //"url":"http://xx.xx/x.png" //附件url
  String? enclosureName;
  String? url;

  ListInfo();

  @override
  ListInfo fromJsonMap(Map<String, dynamic>? json) {
    return ListInfo.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ListInfoToJson(this);
  }

  factory ListInfo.fromJson(Map<String, dynamic> json) =>
      _$ListInfoFromJson(json);
}
