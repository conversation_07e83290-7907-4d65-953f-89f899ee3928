///@Description(描述)     xxxx
///@author(作者)          zhangyinhong
///@create(时间)          2025-01-25 14:50

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'price_sku_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PriceSkuModel _$PriceSkuModelFromJson(Map<String, dynamic> json) => PriceSkuModel()
  ..total = json['total'] as int?
  ..list = (json['list'] as List<dynamic>?)?.map((e) => PriceSkuItemModel.fromJson(e as Map<String, dynamic>)).toList()
  ..pageNum = json['pageNum'] as int?
  ..pageSize = json['pageSize'] as int?
  ..size = json['size'] as int?
  ..startRow = json['startRow'] as int?
  ..endRow = json['endRow'] as int?
  ..pages = json['pages'] as int?
  ..prePage = json['prePage'] as int?
  ..nextPage = json['nextPage'] as int?
  ..isFirstPage = json['isFirstPage'] as bool?
  ..isLastPage = json['isLastPage'] as bool?
  ..hasPreviousPage = json['hasPreviousPage'] as bool?
  ..hasNextPage = json['hasNextPage'] as bool?
  ..navigatePages = json['navigatePages'] as int?
  ..navigatepageNums = (json['navigatepageNums'] as List<dynamic>?)?.map((e) => e as int).toList()
  ..navigateFirstPage = json['navigateFirstPage'] as int?
  ..navigateLastPage = json['navigateLastPage'] as int?;

Map<String, dynamic> _$PriceSkuModelToJson(PriceSkuModel instance) => <String, dynamic>{
      'total': instance.total,
      'list': instance.list,
      'pageNum': instance.pageNum,
      'pageSize': instance.pageSize,
      'size': instance.size,
      'startRow': instance.startRow,
      'endRow': instance.endRow,
      'pages': instance.pages,
      'prePage': instance.prePage,
      'nextPage': instance.nextPage,
      'isFirstPage': instance.isFirstPage,
      'isLastPage': instance.isLastPage,
      'hasPreviousPage': instance.hasPreviousPage,
      'hasNextPage': instance.hasNextPage,
      'navigatePages': instance.navigatePages,
      'navigatepageNums': instance.navigatepageNums,
      'navigateFirstPage': instance.navigateFirstPage,
      'navigateLastPage': instance.navigateLastPage,
    };

PriceSkuItemModel _$PriceSkuItemModelFromJson(Map<String, dynamic> json) => PriceSkuItemModel()
  ..id = json['id'] as int?
  ..productImage = json['productImage'] as String?
  ..productName = json['productName'] as String?
  ..spec = json['spec'] as String?
  ..manufacturer = json['manufacturer'] as String?
  ..purchaseStoreCount = json['purchaseStoreCount'] as int?
  ..sold = json['sold'] as int?
  ..spuId = json['spuId'] as int?
  ..skuId = json['skuId'] as int?
  ..barcode = json['barcode'] as String?
  ..standardId = json['standardId'] as String?
  ..merchantName = json['merchantName'] as String?
  ..minBasePriceRage = (json['minBasePriceRage'] as num?)?.toDouble()
  ..maxBasePriceRage = (json['maxBasePriceRage'] as num?)?.toDouble()
  ..salePrice = (json['salePrice'] as num?)?.toDouble()
  ..priceLevel = json['priceLevel'] as int?
  ..priceRange = (json['priceRange'] as num?)?.toDouble()
  ..optimizationDetailsDescription = (json['optimizationDetailsDescription'] as List<dynamic>?)?.map((e) => e as String).toList()
  ..productListingInfo = json['productListingInfo'] == null ? null : ProductListingInfo.fromJson(json['productListingInfo'] as Map<String, dynamic>);

Map<String, dynamic> _$PriceSkuItemModelToJson(PriceSkuItemModel instance) => <String, dynamic>{
      'id': instance.id,
      'productImage': instance.productImage,
      'productName': instance.productName,
      'spec': instance.spec,
      'manufacturer': instance.manufacturer,
      'purchaseStoreCount': instance.purchaseStoreCount,
      'sold': instance.sold,
      'spuId': instance.spuId,
      'skuId': instance.skuId,
      'barcode': instance.barcode,
      'standardId': instance.standardId,
      'merchantName': instance.merchantName,
      'minBasePriceRage': instance.minBasePriceRage,
      'maxBasePriceRage': instance.maxBasePriceRage,
      'salePrice': instance.salePrice,
      'priceLevel': instance.priceLevel,
      'priceRange': instance.priceRange,
      'optimizationDetailsDescription': instance.optimizationDetailsDescription,
      'productListingInfo': instance.productListingInfo,
    };

ProductListingInfo _$ProductListingInfoFromJson(Map<String, dynamic> json) => ProductListingInfo()
  ..coveredProvinces = (json['coveredProvinces'] as List<dynamic>?)?.map((e) => e as String).toList()
  ..storeCount = json['storeCount'] as int?
  ..totalInventory = json['totalInventory'] as int?;

Map<String, dynamic> _$ProductListingInfoToJson(ProductListingInfo instance) => <String, dynamic>{
      'coveredProvinces': instance.coveredProvinces,
      'storeCount': instance.storeCount,
      'totalInventory': instance.totalInventory,
    };

PriceTrendModel _$PriceTrendModelFromJson(Map<String, dynamic> json) => PriceTrendModel()
  ..date = (json['date'] as List<dynamic>?)?.map((e) => e as int).toList()
  ..price = (json['price'] as num?)?.toDouble();

Map<String, dynamic> _$PriceTrendModelToJson(PriceTrendModel instance) => <String, dynamic>{
      'date': instance.date,
      'price': instance.price,
    };
