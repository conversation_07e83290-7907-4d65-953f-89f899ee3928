///@Description(描述)     xxxx
///@author(作者)          z<PERSON><PERSON><PERSON>ong
///@create(时间)          2025-01-21 08:13

import 'package:XyyBeanSproutsFlutter/price/data/provice_item_model.dart';
import 'package:flutter/material.dart';

class ProvincePriceRankItem extends StatefulWidget {
  final ProviceListItemModel? provinceItemModel;
  final int? rank;

  ProvincePriceRankItem({required this.provinceItemModel, this.rank = 0});

  @override
  _ProvincePriceRankItemState createState() => _ProvincePriceRankItemState();
}

class _ProvincePriceRankItemState extends State<ProvincePriceRankItem> {
  int selectType = 1; // 1.价格健康度 2.上架情况

  PriceProviceItemModel? get provinceProductStatisticsDTO => widget.provinceItemModel?.provinceProductStatisticsDTO;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(bottom: 10),
      color: Color(0xFFF5F5F5),
      child: Container(
        padding: const EdgeInsets.all(10.0),
        color: Colors.white,
        child: Column(
          // crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildHeaderInfo(),
            Container(
              padding: EdgeInsets.symmetric(vertical: 10, horizontal: 5),
              child: Row(
                // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  buildSelectButton('价格健康度', 1),
                  SizedBox(width: 16),
                  buildSelectButton('上架情况', 2),
                  SizedBox(width: 16),
                  buildSelectButton('动销情况', 3),
                ],
              ),
            ),
            Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Color(0xFFF9F9F9),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Column(
                  children: [
                    Visibility(
                      visible: selectType == 1,
                      child: buildPriceHealth(),
                    ),
                    Visibility(
                      visible: selectType == 2,
                      child: buildOnSales(),
                    ),
                    Visibility(
                      visible: selectType == 3,
                      child: buildDynamicSales(),
                    ),
                  ],
                ))
          ],
        ),
      ),
    );
  }

  Widget buildHeaderInfo() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            buildRankTag(widget.rank),
            SizedBox(width: 8),
            Text(
              '${provinceProductStatisticsDTO?.province ?? '-'}',
              style: TextStyle(fontSize: 16, color: Colors.black, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 5, vertical: 2),
          decoration: BoxDecoration(
            // color: Color(0xFF03C261),
            color: getRemarkColor(provinceProductStatisticsDTO?.remark),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            '${provinceProductStatisticsDTO?.remark ?? '-'}',
            style: TextStyle(color: Colors.white, fontSize: 12),
          ),
        ),
      ],
    );
  }

  Color getRemarkColor(String? remark) {
    switch (remark) {
      case '太棒了':
        return const Color(0xFF03C261); // 绿色
      case '不错哟':
        return const Color(0xFFFF7F33); // 橙色
      case '太差了':
        return const Color(0xFFF23535); // 红色
      default:
        return const Color(0xFF999999); // 默认灰色
    }
  }

  /// 排行标志图片
  Widget buildRankTag(int? rank) {
    if (rank == null) return SizedBox();
    String url = "";
    if (rank <= 3) {
      switch (rank) {
        case 1:
          url = "assets/images/price/provice_rank_1.png";
          break;
        case 2:
          url = "assets/images/price/provice_rank_2.png";
          break;
        case 3:
          url = "assets/images/price/provice_rank_3.png";
          break;
      }
      return Image.asset(url, height: 26, width: 22);
    } else {
      return Stack(
        alignment: Alignment.center,
        children: [
          Image.asset("assets/images/price/provice_rank_other.png", height: 26, width: 22),
          Transform.translate(
            offset: Offset(0, 2),
            child: Text(
              '$rank',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Color(0xFF999999),
              ),
            ),
          ),
        ],
      );
    }
  }

  /// 生成按钮
  Widget buildSelectButton(String title, int type) {
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            selectType = type; // 设置为选中的按钮
          });
        },
        child: Container(
          alignment: Alignment.center,
          padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
          decoration: BoxDecoration(
            color: selectType == type ? Color(0xFFF2FBF8) : Color(0xFFF9F9F9),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            title,
            style: TextStyle(
              color: selectType == type ? Color(0xFF00B377) : Color(0xFF777777),
              fontWeight: selectType == type ? FontWeight.w500 : FontWeight.w400,
              fontSize: 14,
            ),
          ),
        ),
      ),
    );
  }

  // 价格健康度
  Widget buildPriceHealth() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: buildInfoItem('在售商品数：', provinceProductStatisticsDTO?.onSaleProductCount),
            ),
            Expanded(
              child: buildInfoItem('价优品数：', provinceProductStatisticsDTO?.priceAdvantageProductCount),
            ),
          ],
        ),
        SizedBox(height: 8),
        Row(
          children: [
            buildInfoItem('商品价格健康度：', '${provinceProductStatisticsDTO?.priceHealthScore}%'),
            SizedBox(width: 8),
            Text(
              getPercentText(provinceProductStatisticsDTO?.priceHealthScorePercent),
              style: TextStyle(
                fontSize: 12,
                color: getPercentColor(provinceProductStatisticsDTO?.priceHealthScorePercent),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // 上架情况
  Widget buildOnSales() {
    return Column(
      children: [
        Row(
          children: [
            // Expanded(
            //   child: buildInfoItem('在售普通品数：', '${provinceProductStatisticsDTO?.onSaleProductCount}'),
            // ),
            Expanded(
              child: buildInfoItem('在售拼团品数：', provinceProductStatisticsDTO?.onSaleProductCount),
            ),
                Expanded(
                  child: buildInfoItem('在售商家数：', widget.provinceItemModel?.onSaleStoreCount),
                ),
          ],
        ),
        SizedBox(height: 8),
        // Row(
        //   children: [
        //     // Expanded(
        //     //   child: buildInfoItem('在售批购品数：', '10000'),
        //     // ),
        //     // Expanded(
        //     //   child: buildInfoItem('在售商家数：', '10000'),
        //     // ),
        //   ],
        // ),
        // SizedBox(height: 8),
        // Row(
        //   children: [
        //     Expanded(
        //       child: buildInfoItem('总库存数：', '10000'),
        //     ),
        //     Expanded(
        //       child: buildInfoItem('覆盖省份数：', '10000'),
        //     ),
        //   ],
        // ),
      ],
    );
  }

  // 动销情况
  Widget buildDynamicSales() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: buildInfoItem('已售商品数：',  widget.provinceItemModel?.sold),
            ),
            Expanded(
              child: buildInfoItem('采购店数：', widget.provinceItemModel?.purchaseStoreCount),
            ),
          ],
        ),
        // SizedBox(height: 8),
        // Row(
        //   children: [
        //     Expanded(
        //       child: buildInfoItem('动销批购品数：', '123'),
        //     ),
        //     Expanded(
        //       child: buildInfoItem('动销品数：', '123'),
        //     ),
        //   ],
        // ),
      ],
    );
  }


  Color getPercentColor(double? percent) {
    if (percent == null) return Colors.grey;
    return percent >= 0 ? Colors.red : const Color(0xFF03C261);
  }

  String getPercentText(double? percent) {
    if (percent == null) return '同比上周0%';
    final isIncrease = percent >= 0;
    final percentText = '${percent.abs().toStringAsFixed(2)}%';
    final arrow = isIncrease ? '↑' : '↓';
    return '同比上周$percentText $arrow';
  }


  TextStyle itemStyle = TextStyle(color: Color(0xFF777777), fontSize: 14);
  TextStyle valueStyle = TextStyle(color: Color(0xFF222222), fontWeight: FontWeight.w500, fontSize: 14);
  Widget buildInfoItem(String label, dynamic? value) {
    return Row(
      children: [
        Text(label, style: itemStyle),
        Text(value?.toString() ?? '-', style: valueStyle),
      ],
    );
  }
}
