import 'package:json_annotation/json_annotation.dart';

part 'user_info_data.g.dart';

@JsonSerializable()
class UserInfoData {
  String? department;
  String? email;
  String? name;
  String? phone;
  String? realName;
  int? roleType;
  String? sysUserId;
  String? jobNumber;
  String? token;

  UserInfoData();

  factory UserInfoData.fromJson(Map<String, dynamic> json) =>
      _$UserInfoDataFromJson(json);
}
