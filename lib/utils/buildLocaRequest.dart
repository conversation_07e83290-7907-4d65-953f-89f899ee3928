import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/location/location_data.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';

class BuildLocaRequest{
  static String? latitude = '';
  static String? longitude = '';
  static locaRequest() async {
    try {
      LocationData? result = await XYYContainer.locationChannel.locate().timeout(
        Duration(seconds: 3),
        onTimeout: () {
          return LocationData();
        },
      );
      
      latitude = result.latitude ?? '';
      longitude = result.longitude ?? '';
      var body = {
        "latitude":result.latitude,
        "longitude":result.longitude,
      };
      XYYContainer.requestChannel.request('/me/user/post',
              method: RequestMethod.POST,
              contentType: RequestContentType.FORM,
              parameters: body);
    } catch (e) {
    }
  }
}
