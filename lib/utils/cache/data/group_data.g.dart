// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GroupData _$GroupDataFromJson(Map<String, dynamic> json) {
  return GroupData()
    ..id = json['id']
    ..code = json['code']
    ..name = json['name']
    ..parentId = json['parentid']
    ..childGroup = (json['childGroup'] as List<dynamic>?)
        ?.map((e) => GroupData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..sysUserList = (json['sysUserList'] as List<dynamic>?)
        ?.map((e) => GroupData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..realName = json['realName']
    ..groupId = json['groupId']
    ..jobNumber = json['jobNumber'];
}

Map<String, dynamic> _$GroupDataToJson(GroupData instance) => <String, dynamic>{
      'id': instance.id,
      'code': instance.code,
      'name': instance.name,
      'parentid': instance.parentId,
      'childGroup': instance.childGroup,
      'sysUserList': instance.sysUserList,
      'realName': instance.realName,
      'groupId': instance.groupId,
      'jobNumber': instance.jobNumber,
    };
