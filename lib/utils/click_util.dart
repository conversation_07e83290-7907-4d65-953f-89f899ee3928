import 'dart:async';

var _isCanRun = true;

/// 防抖函数
/// [fun]：要执行的方法
/// [delay]：延迟的时长
void debounce(Function? fun,
    [Duration delay = const Duration(milliseconds: 500)]) {
  if (_isCanRun && fun != null) {
    fun.call();
    _isCanRun = false;
    Future.delayed(delay, () {
      _isCanRun = true;
    });
  }
}

void Function() debounceCall(
  Function func, [
  Duration delay = const Duration(milliseconds: 500),
]) {
  Timer? timer;
  void Function() target = () {
    timer?.cancel();
    timer = Timer(delay, () {
      func.call();
    });
  };
  return target;
}
