import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

/// 这个类 没用。。 先放着吧 回头改
class EnsureVisibleWhenFocused extends StatefulWidget {
  const EnsureVisibleWhenFocused({
    Key? key,
    required this.child,
    required this.focusNode,
    this.curve: Curves.ease,
    this.duration: const Duration(milliseconds: 100),
  }) : super(key: key);

  /// 传入focusNode， 用于监听TextField获取焦点事件
  final FocusNode focusNode;

  /// 部件
  final Widget child;

  /// 默认是 Curves.case
  final Curve curve;

  /// 滚动时间
  final Duration duration;

  @override
  State<StatefulWidget> createState() {
    return _EnsureVisibleWhenFocused();
  }
}

class _EnsureVisibleWhenFocused extends State<EnsureVisibleWhenFocused>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    widget.focusNode.addListener(_ensureVisible);
  }

  @override
  void dispose() {
    WidgetsBinding.instance?.removeObserver(this);
    widget.focusNode.removeListener(_ensureVisible);
    // FocusNode 在使用的部件中销毁，此处不做处理，防止多次释放
    // widget.focusNode.dispose();
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    if (widget.focusNode.hasFocus) {
      ///有焦点时，进入滑动显示处理Function
      _ensureVisible();
    }
  }

  /// 等待键盘弹出啊
  Future<Null> _keyboardToggled() async {
    if (mounted) {
      EdgeInsets edgeInsets = MediaQuery.of(context).viewInsets;
      while (mounted && MediaQuery.of(context).viewInsets == edgeInsets) {
        await new Future.delayed(const Duration(milliseconds: 10));
      }
    }
    return;
  }

  Future<Null> _ensureVisible() async {
    // 循环等待 键盘完全弹出
    await Future.any([
      new Future.delayed(const Duration(milliseconds: 300)),
      _keyboardToggled()
    ]);

    // 如果没有获取焦点则不处理
    if (!widget.focusNode.hasFocus) {
      return;
    }

    //找到Current RenderObjectWidget，获得当前获得焦点的widget，这里既TextField
    final RenderObject? object = context.findRenderObject();
    final RenderAbstractViewport? viewport = RenderAbstractViewport.of(object);

    // 如果不是可滚动视窗则不处理
    if (viewport == null) {
      return;
    }

    //获取滑动状态，目的是为了获取滑动的offset
    ScrollableState scrollableState = Scrollable.of(context)!;

    // 获取滚动的offset
    ScrollPosition position = scrollableState.position;
    double alignment;

    ///这里需要解释下
    ///1、position.pixels是指滑动widget，滑动的offset（一般指距离顶部的偏移量(滑出屏幕多少距离)）
    ///2、viewport.getOffsetToReveal(object, 0.0).offset 这个方法，可以看下源码
    ///      他有一个alignment参数，0.0 代表显示在顶部，0.5代表显示在中间，1.0代表显示在底部
    ///       offset是指view显示在三个位置时距离顶部的偏移量
    ///       他们两者相比较就可以知道当前滑动widget是需要向上还是向下滑动，来完全显示TextField

    ///判断TextField处于顶部时是否全部显示，需不需下滑来完整显示
    if (position.pixels > viewport.getOffsetToReveal(object!, 0.0).offset) {
      alignment = 0.0;

      ///判断TextField处于低部时是否全部显示，需不需上滑来完整显示
    } else if (position.pixels <
        viewport.getOffsetToReveal(object, 1.0).offset) {
      alignment = 1.0;
    } else {
      return;
    }

    //这是ScrollPosition的内部方法，将给定的view 滚动到给定的位置，
    //alignment的意义和上面描述的一致， 三种位置顶部，底部，中间
    position.ensureVisible(
      object,
      alignment: alignment,
      duration: widget.duration,
      curve: widget.curve,
    );
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
