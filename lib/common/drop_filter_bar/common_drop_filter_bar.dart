import 'package:XyyBeanSproutsFlutter/common/button/dropdown_controller_button.dart';
import 'package:flutter/material.dart';

class CommonDropConfigModel {
  String defaultTitle;
  String? selectTitle;
  String paramKey;
  DropButtonController? controller;

  CommonDropConfigModel({
    required this.defaultTitle,
    this.selectTitle,
    required this.paramKey,
    this.controller,
  });
}

typedef CommonDropFilterBarAction = void Function(
  GlobalKey,
  CommonDropConfigModel,
  DropButtonController,
);

class CommonDropFilterBar extends StatefulWidget {
  final List<CommonDropConfigModel> configs;
  final CommonDropFilterBarAction action;

  CommonDropFilterBar({required this.configs, required this.action});

  @override
  State<StatefulWidget> createState() {
    return CommonDropFilterBarState();
  }
}

class CommonDropFilterBarState extends State<CommonDropFilterBar> {
  GlobalKey itemKey = GlobalKey();

  DropButtonController? _controller;

  @override
  Widget build(BuildContext context) {
    return Container(
      key: this.itemKey,
      color: Color(0xFFFFFFFF),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        children: this.getDropButtons(),
      ),
    );
  }

  List<Widget> getDropButtons() {
    List<Widget> list = [];
    widget.configs.asMap().forEach((key, value) {
      list.add(SizedBox(
        width: 5,
      ));
      list.add(Expanded(
        child: Container(
          alignment: Alignment.center,
          child: DropControllerButton(
            title: value.defaultTitle,
            selectedText: value.selectTitle,
            controller: value.controller,
            selectedStyle: TextStyle(
                color: Color(0xFF00B377),
                fontSize: 14,
                fontWeight: FontWeight.w600),
            onPressed: (controller) {
              this.changeController(controller);
              widget.action(this.itemKey, value, controller);
            },
          ),
        ),
      ));
    });
    list.add(SizedBox(width: 5));
    return list;
  }

  void changeController(DropButtonController controller) {
    this._controller?.setIsOpen(false);
    this._controller = controller;
  }
}
