import 'package:XyyBeanSproutsFlutter/common/button/dropdown_controller_button.dart';
import 'package:XyyBeanSproutsFlutter/common/button/sort_button_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/button/trigger_text_state_button.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class CommonDropMultiConfigModel {
  String defaultTitle;
  String? selectTitle;
  String paramKey;
  bool isShowImage;
  DropButtonController? controller;

  CommonDropMultiConfigModel({
    required this.defaultTitle,
    this.selectTitle,
    required this.paramKey,
    this.controller,
    this.isShowImage = true,
  });
}

typedef CommonDropFilterBarAction = void Function(
  GlobalKey,
  CommonDropMultiConfigModel,
  DropButtonController,
);

typedef CommonDropMultiFilterBarAction = Function(CommonDropConfigMultiStatusModel);

typedef CommonDropConfigTriggerBarAction = Function(TextStateConfigModel);

class CommonDropConfigMultiStatusModel {
  String paramKey;
  var title = '';
  var isSelected = false;
  SortButtonWidgetType type;
  Color selectedColor;
  Color unSelectedColor;
  Image normalImage;
  Image asceImage;
  Image descImage;

  CommonDropConfigMultiStatusModel({
    required this.paramKey,
    required this.title,
    this.isSelected = false,
    this.type = SortButtonWidgetType.normal,
    this.selectedColor = const Color(0xFF00B377),
    this.unSelectedColor = const Color(0xFF676773),
    this.normalImage = const Image(
      image: AssetImage("assets/images/performance/team_performance_sort_normal.png"),
      width: 9,
      height: 12,
    ),
    this.asceImage = const Image(
      image: AssetImage("assets/images/performance/team_performance_sort_asec.png"),
      width: 9,
      height: 12,
    ),
    this.descImage = const Image(
      image: AssetImage("assets/images/performance/team_performance_sort_dsec.png"),
      width: 9,
      height: 12,
    ),
  });
}

class TextStateConfigModel {
  String title;
  String paramKey;
  ValueNotifier<TriggerTextButtonState>? controller;

  TextStateConfigModel({required this.title, required this.paramKey, this.controller});
}

class CommonDropFilterMultiStatusBar extends StatefulWidget {
  final List<dynamic> configs;
  final CommonDropFilterBarAction action;
  final CommonDropMultiFilterBarAction multiAction;
  final CommonDropConfigTriggerBarAction? triggerAction;

  CommonDropFilterMultiStatusBar({required this.configs, required this.action, required this.multiAction, this.triggerAction});

  @override
  State<StatefulWidget> createState() {
    return CommonDropFilterMultiStatusBarState();
  }
}

class CommonDropFilterMultiStatusBarState extends State<CommonDropFilterMultiStatusBar> {
  GlobalKey itemKey = GlobalKey();
  DropButtonController? _controller;
  final ScrollController _scrollController = ScrollController();
  // 为选中项添加 key
  final GlobalKey _selectedItemKey = GlobalKey();
  @override
  void initState() {
    super.initState();

    this.widget.configs.forEach((element) {
      if (element is TextStateConfigModel) {
        if (element.controller == null) {
          element.controller = ValueNotifier(TriggerTextButtonState.normal);
        }
      }
    });

    // 添加延时，等待布局完成后检查选中项
    // WidgetsBinding.instance!.addPostFrameCallback((_) {
    //   if (_selectedItemKey.currentContext != null) {
    //     _scrollToItem(_selectedItemKey.currentContext!);
    //   }
    // });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      key: this.itemKey,
      color: Color(0xFFFFFFFF),
      // padding: EdgeInsets.only(right: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,  // 添加这个属性
        mainAxisSize: MainAxisSize.max,
        children: this._getDropButtons(),
      ),
    );
  }

  List<Widget> _getDropButtons() {
    List<Widget> list = [];
    this.widget.configs.forEach((element) {
      if (element is CommonDropMultiConfigModel) {
        list.add(_getCommonDropButton(element));
      } else if (element is CommonDropConfigMultiStatusModel) {
        list.add(_getMultiButton(element));
      } else if (element is TextStateConfigModel) {
        list.add(_getTextStateButton(element));
      }
    });
    return list;
  }

  // 在点击事件中添加滚动逻辑
  // void _scrollToItem(BuildContext itemContext) {
  //   final RenderBox renderBox = itemContext.findRenderObject() as RenderBox;
  //   final position = renderBox.localToGlobal(Offset.zero);
  //   final size = renderBox.size;
  //   double scrollWidth = MediaQuery.of(context).size.width;
  //   double itemLeft = position.dx;
  //   double itemMiddle = itemLeft + (size.width / 2);
  //   double currentOffset = _scrollController.offset;
  //   double screenMiddle = scrollWidth / 2;
  //   double targetOffset = currentOffset;
  //   // 如果项目在屏幕左半边
  //   if (itemMiddle < screenMiddle) {
  //     if (currentOffset > 0) {
  //       targetOffset = currentOffset - (screenMiddle - itemMiddle);
  //       targetOffset = targetOffset.clamp(0.0, _scrollController.position.maxScrollExtent);
  //     } else {
  //       targetOffset = 0;
  //     }
  //   } else if (itemMiddle > screenMiddle) {
  //     targetOffset = currentOffset + (itemMiddle - screenMiddle);
  //     targetOffset = targetOffset.clamp(0.0, _scrollController.position.maxScrollExtent);
  //   }
  //   if (targetOffset != currentOffset) {
  //     _scrollController.animateTo(
  //       targetOffset,
  //       duration: Duration(milliseconds: 300),
  //       curve: Curves.easeOutCubic,
  //     );
  //   }
  // }

  //需要滑动效果的
  // Widget _getMultiButton(CommonDropConfigMultiStatusModel element) {
  //   return Container(
  //     padding: EdgeInsets.symmetric(horizontal: 8),
  //     alignment: Alignment.center,
  //     child: Builder(
  //       key: element.isSelected ? _selectedItemKey : null,
  //       // 使用 Builder 获取正确的 context
  //       builder: (context) => SortButtonWidget(
  //         title: element.title,
  //         isSelected: element.isSelected,
  //         type: element.type,
  //         selectedColor: element.selectedColor,
  //         unSelectedColor: element.unSelectedColor,
  //         normalImage: element.normalImage,
  //         asceImage: element.asceImage,
  //         descImage: element.descImage,
  //         onPressed: () {
  //           _scrollToItem(context); // 添加滚动逻辑
  //           switchSortStatus(element, this.widget.configs);
  //           this.widget.multiAction(element);
  //           this.setState(() {});
  //         },
  //       ),
  //     ),
  //   );
  // }

  // 没有滑动效果的
  Widget _getMultiButton(CommonDropConfigMultiStatusModel element) {
    return Container(
      // padding: EdgeInsets.symmetric(horizontal: 8), // 添加间距
      alignment: Alignment.center,
      child: SortButtonWidget(
        title: element.title,
        isSelected: element.isSelected,
        type: element.type,
        selectedColor: element.selectedColor,
        unSelectedColor: element.unSelectedColor,
        normalImage: element.normalImage,
        asceImage: element.asceImage,
        descImage: element.descImage,
        onPressed: () {
          // _scrollToItem(context);  // 添加滚动逻辑
          switchSortStatus(element, this.widget.configs);
          this.widget.multiAction(element);
          this.setState(() {});
        },
      ),
    );
  }

  //今日 今月
  Widget _getCommonDropButton(CommonDropMultiConfigModel element) {
    return Container(
      // padding: EdgeInsets.fromLTRB(20, 0, 15, 0), // 添加间距
      alignment: Alignment.center,
      child: DropControllerButton(
        title: element.defaultTitle,
        selectedText: element.selectTitle,
        controller: element.controller,
        isShowImage: element.isShowImage,
        selectedStyle: TextStyle(color: Color(0xFF00B377), fontSize: 14, fontWeight: FontWeight.w600),
        onPressed: (controller) {
          this.changeController(controller);
          widget.action(this.itemKey, element, controller);
        },
      ),
    );
  }

  // BD或者KA排行榜
  Widget _getTextStateButton(TextStateConfigModel element) {
    return Container(
      // padding: EdgeInsets.symmetric(horizontal: 8), // 添加间距
      alignment: Alignment.center,
      child: TriggerTextStateButton(
        title: element.title,
        controller: element.controller,
        onPressed: (controller) {
          switchTriggerStatus(element, this.widget.configs);
          if (controller.value == TriggerTextButtonState.selected) {
            if (this.widget.triggerAction != null) {
              this.widget.triggerAction!(element);
            }
          } else {
            controller.value = TriggerTextButtonState.selected;
          }
        },
      ),
    );
  }

  switchSortStatus(CommonDropConfigMultiStatusModel model, List<dynamic> configs) {
    if (model.type == SortButtonWidgetType.normal) {
      model.type = SortButtonWidgetType.desc;
      model.isSelected = true;
    } else if (model.type == SortButtonWidgetType.asce) {
      model.type = SortButtonWidgetType.desc;
      model.isSelected = true;
    } else {
      model.type = SortButtonWidgetType.asce;
      model.isSelected = true;
    }
    configs.forEach((element) {
      if (element is CommonDropConfigMultiStatusModel && element != model) {
        element.isSelected = false;
        element.type = SortButtonWidgetType.normal;
      }
    });
  }

  switchTriggerStatus(TextStateConfigModel model, List<dynamic> configs) {
    configs.forEach((element) {
      if (element is TextStateConfigModel && model != element) {
        element.controller?.value = TriggerTextButtonState.normal;
      }
    });
  }

  void changeController(DropButtonController controller) {
    this._controller?.setIsOpen(false);
    this._controller = controller;
  }
}
