import 'package:flutter/material.dart';

///加载弹框
class ProgressDialog {
  static bool _isShowing = false;

  ///展示
  static Future showProgress(BuildContext context, {Widget? child}) async {
    if (!_isShowing) {
      _isShowing = true;
      if (child == null) {
        child = getDefaultWidget();
      }
      return await  Navigator.of(context).push(
          _PopRoute(
            child: _Progress(
              child: child,
            ),
          ));
    }
  }

  static Widget getDefaultWidget() {
    return ClipRRect(
        borderRadius: BorderRadius.circular(5.0),
        child: Container(
          //错误页面中心可以自己调整
          color: Colors.white,
          width: 100,
          height: 100,
          child: Center(
            child:
                // 圆形进度条
                new CircularProgressIndicator(
              strokeWidth: 4.0,
              backgroundColor: Colors.white,
              // value: 0.2,
              valueColor: new AlwaysStoppedAnimation<Color>(
                  Color(0x3072f6)),
            ),
          ),
        ));
  }

  static bool isShowing() {
    return _isShowing;
  }

  ///隐藏
  static void dismiss(BuildContext context) {
    if (_isShowing) {
      Future.sync(() => Navigator.of(context).pop());
      _isShowing = false;
    }
  }
}

///Widget
class _Progress extends StatelessWidget {
  final Widget child;

  _Progress({
    Key? key,
    required this.child,
  })  : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
        color: Colors.transparent,
        child: Center(
          child: child,
        ));
  }
}

///Route
class _PopRoute extends PopupRoute {
  final Duration _duration = Duration(milliseconds: 300);
  Widget child;

  _PopRoute({required this.child});

  @override
  Color? get barrierColor => null;

  @override
  bool get barrierDismissible => true;

  @override
  String? get barrierLabel => null;

  @override
  Widget buildPage(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation) {
    return child;
  }

  @override
  Duration get transitionDuration => _duration;
}
