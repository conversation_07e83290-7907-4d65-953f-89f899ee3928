import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// ignore: must_be_immutable
class CommonToolbar extends StatefulWidget {
  String leftIcon;
  String middleText;
  int middleTextColor;
  double middleTextSize;
  String rightText;
  int rightTextColor;
  double rightTextSize;
  Function? onLeftClicked;
  Function? onRightClicked;

  CommonToolbar(
      {this.leftIcon = "assets/images/titlebar/icon_back.png",
      this.middleText = "",
      this.middleTextColor = 0x292933,
      this.middleTextSize = 16,
      this.rightText = "",
      this.rightTextColor = 0x3072F6,
      this.rightTextSize = 16,
      this.onLeftClicked,
      this.onRightClicked});

  @override
  State<StatefulWidget> createState() {
    return _CommonToolbarState();
  }
}

class _CommonToolbarState extends State<CommonToolbar> {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFFFFF),
      height: 44,
      padding: EdgeInsets.fromLTRB(10, 0, 15, 0),
      child: Row(
        children: [
          GestureDetector(
            child: Image.asset(
              widget.leftIcon,
              width: 22,
            ),
            onTap: () {
              if (widget.onLeftClicked != null) {
                widget.onLeftClicked!();
              } else if (Navigator.canPop(context)) {
                Navigator.maybePop(context);
              } else {
                SystemNavigator.pop(animated: true);
              }
            },
          ),
          Spacer(
            flex: 1,
          ),
          Text(
            widget.middleText,
            style: TextStyle(
                color: Color(widget.middleTextColor),
                fontSize: widget.middleTextSize),
          ),
          Spacer(
            flex: 1,
          ),
          GestureDetector(
            child: Text(
              widget.rightText,
              style: TextStyle(
                  color: Color(widget.rightTextColor),
                  fontSize: widget.rightTextSize),
            ),
            onTap: () {
              if (widget.onRightClicked != null) {
                widget.onRightClicked!();
              }
            },
          ),
        ],
      ),
    );
  }
}
