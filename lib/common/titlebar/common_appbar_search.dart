import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class SAppBarSearch extends StatefulWidget implements PreferredSizeWidget {
  SAppBarSearch({
    Key? key,
    this.autoFocus = false,
    this.focusNode,
    this.controller,
    this.value,
    this.leading,
    this.suffix,
    this.actions = const [],
    this.hintText,
    this.onTap,
    this.onClear,
    this.onCancel,
    this.onChanged,
    this.onSearch,
    this.showLeading,
    this.hideCancel,
    this.height,
    this.decoration = const BoxDecoration(
      color: Color(0xFFF5F5F5),
      borderRadius: BorderRadius.all(Radius.circular(4)),
    ),
  }) : super(key: key);
  final bool autoFocus;
  final FocusNode? focusNode;
  final TextEditingController? controller;

  // 高度
  final double? height;

  // 默认值
  final String? value;

  // 最前面的组件
  final Widget? leading;

  // 搜索框后缀组件
  final Widget? suffix;
  final List<Widget> actions;

  // 提示文字
  final String? hintText;

  // 输入框点击
  final VoidCallback? onTap;

  // 单独清除输入框内容
  final VoidCallback? onClear;

  // 清除输入框内容并取消输入
  final VoidCallback? onCancel;

  // 输入框内容改变
  final ValueChanged? onChanged;

  // 点击键盘搜索
  final ValueChanged? onSearch;

  //是否展示appBar 返回键
  final bool? showLeading;

  //是否隐藏右侧取消按钮
  final bool? hideCancel;

  // 输入框的 样式
  final BoxDecoration decoration;

  @override
  _SAppBarSearchState createState() => _SAppBarSearchState();

  @override
  Size get preferredSize => Size.fromHeight(height ?? kToolbarHeight);
}

class _SAppBarSearchState extends State<SAppBarSearch> {
  TextEditingController? _controller;
  FocusNode? _focusNode;

  @override
  void initState() {
    _controller = widget.controller ?? TextEditingController();
    _focusNode = widget.focusNode ?? FocusNode();
    if (widget.value != null) _controller!.text = widget.value!;
    super.initState();
  }

  // 清除输入框内容
  void _onClearInput() {
    setState(() {
      _controller!.clear();
    });
    if (widget.onClear != null) widget.onClear!();
  }

  // 取消输入框编辑
  void _onCancelInput() {
    setState(() {
      _controller!.clear();
      _focusNode!.unfocus();
    });
    Navigator.of(context).pop();
    if (widget.onCancel != null) widget.onCancel!();
  }

  void _onInputChanged(String value) {
    setState(() {});
    if (widget.onChanged != null) widget.onChanged!(value);
  }

  Widget _suffix() {
    if (_controller!.text.isNotEmpty) {
      return GestureDetector(
        onTap: _onClearInput,
        child: SizedBox(
          width: 40,
          height: 34,
          child: Icon(
            Icons.cancel,
            size: 22,
            color: Color(0xFFcccccc),
          ),
        ),
      );
    }
    return widget.suffix ?? SizedBox();
  }

  List<Widget> _actions() {
    List<Widget> list = [];
    if (!widget.hideCancel!) {
      list.add(GestureDetector(
        onTap: _onCancelInput,
        child: Container(
          width: 62,
          alignment: Alignment.center,
          child: Text(
            '取消',
            style: TextStyle(color: Color(0xFF35C561), fontSize: 16),
          ),
        ),
      ));
    } else if (widget.actions.isNotEmpty) {
      list.addAll(widget.actions);
    }
    return list;
  }

  @override
  Widget build(BuildContext context) {
    final ScaffoldState scaffold = Scaffold.of(context);
    final bool hasDrawer = scaffold.hasDrawer;
    double left = 0;
    double right = 0;
    if (!hasDrawer && (widget.leading == null || !widget.showLeading!))
      left = 15;
    if (widget.hideCancel! && widget.actions.isEmpty) right = 15;
    return AppBar(
      titleSpacing: 0,
      leading: widget.leading,
      automaticallyImplyLeading: widget.showLeading!,
      backgroundColor: Colors.white,
      toolbarHeight: widget.height,
      elevation: 0,
      title: Container(
        margin: EdgeInsets.only(right: right, left: left),
        padding: EdgeInsets.only(left: 10),
        decoration: widget.decoration,
        child: Row(
          children: [
            SizedBox(
              width: 22,
              height: 22,
              child: Image.asset("assets/images/titlebar/icon_search_bar.png"),
            ),
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(left: 3),
                child: TextField(
                  autofocus: widget.autoFocus,
                  focusNode: _focusNode,
                  controller: _controller,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    isDense: true,
                    hintText: widget.hintText ?? '请输入关键字',
                    hintStyle: TextStyle(
                      fontSize: 14,
                      color: Color(0xFFb3b3c2),
                    ),
                  ),
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF292933),
                  ),
                  textInputAction: TextInputAction.search,
                  onTap: widget.onTap,
                  onChanged: _onInputChanged,
                  onSubmitted: widget.onSearch,
                  keyboardAppearance: Brightness.light,
                ),
              ),
            ),
            _suffix(),
          ],
        ),
      ),
      actions: _actions(),
      systemOverlayStyle: SystemUiOverlayStyle.dark,
    );
  }

  @override
  void dispose() {
    super.dispose();
    this._focusNode?.dispose();
  }
}
