import 'package:flutter/material.dart';

class CommonSearchItem extends StatefulWidget {
  CommonSearchItem({
    Key? key,
    this.leading,
    this.suffix,
    this.hintText,
    this.hintStyle = const TextStyle(
      color: Color(0xFFA4A4A4),
      fontSize: 12,
    ),
    this.inputStyle,
    this.decoration = const BoxDecoration(
      color: Color(0xFFF5F5F5),
      borderRadius: BorderRadius.horizontal(
        left: Radius.circular(16),
        right: Radius.circular(16),
      ),
    ),
    this.controller,
    this.focusNode,
    this.backgroundColor = const Color(0xFFFFFFFF),
    this.autoFocus = false,
    this.onTap,
    this.onChange,
    this.onSubmitted,
    this.value,
    this.onClear,
  }) : super(key: key);

  final Widget? leading;
  final Widget? suffix;
  final String? hintText;
  final TextStyle? hintStyle;
  final TextStyle? inputStyle;
  final BoxDecoration? decoration;
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final bool autoFocus;
  final Color? backgroundColor;
  final VoidCallback? onTap;
  final ValueChanged<String>? onChange;
  final ValueChanged<String>? onSubmitted;
  // 单独清除输入框内容
  final VoidCallback? onClear;
  // 默认值
  final String? value;

  @override
  State<StatefulWidget> createState() => _CommonSearchItemState();
}

class _CommonSearchItemState extends State<CommonSearchItem> {
  TextEditingController? _controller;
  FocusNode? _focusNode;

  @override
  void initState() {
    _controller = widget.controller ?? TextEditingController();
    _focusNode = widget.focusNode ?? FocusNode();
    if (widget.value != null) _controller!.text = widget.value!;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: widget.backgroundColor,
      padding: EdgeInsets.fromLTRB(10, 5, 10, 5),
      child: Container(
        decoration: widget.decoration,
        height: 32,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            this.leadingWidget(),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(left: 5),
                child: TextField(
                  autofocus: widget.autoFocus,
                  focusNode: this._focusNode,
                  controller: this._controller,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.zero,
                    isDense: true,
                    hintText: widget.hintText ?? '请输入关键字',
                    hintStyle: widget.hintStyle,
                  ),
                  style: TextStyle(
                    fontSize: 12,
                    color: Color(0xFF292933),
                  ),
                  textInputAction: TextInputAction.search,
                  onTap: this._onInputTap,
                  onChanged: this._onInputChanged,
                  onSubmitted: this._onSubmitted,
                  keyboardAppearance: Brightness.light,
                ),
              ),
            ),
            this._suffix(),
          ],
        ),
      ),
    );
  }

  Widget leadingWidget() {
    if (widget.leading != null) {
      return widget.leading!;
    }
    return Padding(
      padding: const EdgeInsets.only(left: 10),
      child: SizedBox(
        width: 19,
        height: 19,
        child: Image.asset("assets/images/titlebar/icon_search_bar.png"),
      ),
    );
  }

  Widget _suffix() {
    if (_controller?.text.isNotEmpty == true && _focusNode?.hasFocus == true) {
      return GestureDetector(
        onTap: _onClearInput,
        child: SizedBox(
          width: 40,
          height: 34,
          child: Icon(
            Icons.cancel,
            size: 22,
            color: Color(0xFFcccccc),
          ),
        ),
      );
    }
    return SizedBox();
  }

  void _onInputTap() {
    setState(() {});
    if (widget.onTap != null) widget.onTap!();
  }

  void _onInputChanged(String value) {
    setState(() {});
    if (widget.onChange != null) widget.onChange!(value);
  }

  void _onSubmitted(String value) {
    _focusNode?.unfocus();
    setState(() {});
    if (widget.onSubmitted != null) widget.onSubmitted!(value);
  }

  // 清除输入框内容
  void _onClearInput() {
    _focusNode?.unfocus();
    setState(() {
      _controller!.clear();
    });
    if (widget.onClear != null) widget.onClear!();
  }
}
