import 'dart:collection';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/filter_page/commodity_filter_popup_base.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/easy_popup.dart';
import 'package:flutter/material.dart';

class CommodityHighGrossFilterPopup extends StatefulWidget with EasyPopupChild {
  late final VoidCallback _actionForDismiss;
  final double distance;
  final CommodityFilterItemChanged itemChanged;
  final dynamic? selectId;


  CommodityHighGrossFilterPopup({
    this.distance = 0,
    required this.itemChanged,
    this.selectId,
  });

  @override
  State<StatefulWidget> createState() {
    return CommodityHighGrossFilterPopupState();
  }

  @override
  dismiss() {
    this._actionForDismiss();
  }
}

class CommodityHighGrossFilterPopupState
    extends State<CommodityHighGrossFilterPopup>
    with CommodityFilterPopupBase, SingleTickerProviderStateMixin {
  late Animation<Offset> animation;
  late AnimationController controller;

  dynamic selectedId = 0;


  // 优选商品筛选数据
  final Map<int, String> highGrossData = {
    0: "全部商品",
    1: "非优选",
    2: "优选",
  };

  void initalAnimation() {
    this.controller = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 300),
      reverseDuration: Duration(milliseconds: 300),
    );

    this.animation = Tween<Offset>(begin: Offset(0, -1), end: Offset.zero)
        .animate(this.controller);
    this.controller.forward();
  }

  @override
  void initState() {
    this.initalAnimation();
    widget._actionForDismiss = () {
      this.controller.reverse();
    };
    if (widget.selectId != null) {
      this.selectedId = widget.selectId;
    }
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: widget.distance),
      child: ClipRect(
        child: SlideTransition(
          position: this.animation,
          child: Container(
            child: Column(
              children: [
                Divider(
                  color: Color(0xFFF7F7F8),
                  height: 0.5,
                  thickness: 0.5,
                ),
                Container(
                  color: Color(0xFFFFFFFF),
                  padding: EdgeInsets.only(left: 15, top: 10),
                  height: 108,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: generateItem(),
                  ),
                ),
                bottomButton()
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget getCommodityFilterItem(int id) {
    return CommodityFilterItem(
      name: highGrossData[id]??"",
      id: id,
      isSelected: this.selectedId == id,
      onPressed: (value) {
        this.selectedId = value;
        setState(() {});
      },
    );
  }

  List<Widget> generateItem() {
    return [
      getCommodityFilterItem(0),
      SizedBox(width: 10),
      getCommodityFilterItem(1),
      SizedBox(width: 10),
      getCommodityFilterItem(2),
    ];
  }

  @override
  void determineAction() {
    XYYContainer.bridgeCall('event_track',
        parameters: HashMap.from(
            {"action_type": "MC-CommodityRankPage-HighGrossFilterConfirm"}));
    widget.itemChanged(highGrossData[selectedId]??"", {'isHighGross': this.selectedId});
    EasyPopup.pop(context);
  }

  @override
  void resetAction() {
    XYYContainer.bridgeCall('event_track',
        parameters: HashMap.from(
            {"action_type": "MC-CommodityRankPage-HighGrossFilterReset"}));
    this.selectedId = 0;
    setState(() {});
  }
}
