import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'authorize_result_data.g.dart';

@JsonSerializable()
class AuthorizeResultData extends BaseModelV2<AuthorizeResultData> {

  dynamic test;

  AuthorizeResultData();

  factory AuthorizeResultData.fromJson(Map<String, dynamic>? json) =>
      _$AuthorizeResultDataFromJson(json!);

  @override
  AuthorizeResultData fromJsonMap(Map<String, dynamic>? json) {
    return AuthorizeResultData.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$AuthorizeResultDataToJson(this);
  }
}
