// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'goods_management_list_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GoodsManagementList _$GoodsManagementListFromJson(Map<String, dynamic> json) {
  return GoodsManagementList()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : GoodsManagementList.fromJson(json['data'] as Map<String, dynamic>?)
    ..total = json['total']
    ..rows = (json['rows'] as List<dynamic>?)
        ?.map(
            (e) => GoodsManagementListRow.fromJson(e as Map<String, dynamic>?))
        .toList()
    ..other = json['other'] == null
        ? null
        : GoodsManagementListOther.fromJson(
            json['other'] as Map<String, dynamic>?)
    ..lastPage = json['lastPage'] as bool?;
}

Map<String, dynamic> _$GoodsManagementListToJson(
        GoodsManagementList instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'total': instance.total,
      'rows': instance.rows,
      'other': instance.other,
      'lastPage': instance.lastPage,
    };

GoodsManagementListOther _$GoodsManagementListOtherFromJson(
    Map<String, dynamic> json) {
  return GoodsManagementListOther()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : GoodsManagementListOther.fromJson(
            json['data'] as Map<String, dynamic>?)
    ..totalProductNum = json['totalProductNum']
    ..totalTaskNum = json['totalTaskNum'];
}

Map<String, dynamic> _$GoodsManagementListOtherToJson(
        GoodsManagementListOther instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'totalProductNum': instance.totalProductNum,
      'totalTaskNum': instance.totalTaskNum,
    };

GoodsManagementListPromoList _$GoodsManagementListPromoListFromJson(
    Map<String, dynamic> json) {
  return GoodsManagementListPromoList()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : GoodsManagementListPromoList.fromJson(
            json['data'] as Map<String, dynamic>?)
    ..planDescription = json['planDescription']
    ..promoType = json['promoType']
    ..promoTypeStr = json['promoTypeStr'];
}

Map<String, dynamic> _$GoodsManagementListPromoListToJson(
        GoodsManagementListPromoList instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'planDescription': instance.planDescription,
      'promoType': instance.promoType,
      'promoTypeStr': instance.promoTypeStr,
    };

GoodsManagementListRow _$GoodsManagementListRowFromJson(
    Map<String, dynamic> json) {
  return GoodsManagementListRow()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : GoodsManagementListRow.fromJson(json['data'] as Map<String, dynamic>?)
    ..fob = json['fob']
    ..grossMargin = json['grossMargin']
    ..imagesList = json['imagesList']
    ..isTeam = json['isTeam'] as bool?
    ..showName = json['showName']
    ..spec = json['spec']
    ..suggestPrice = json['suggestPrice']
    ..taskFinishNum = json['taskFinishNum']
    ..taskId = json['taskId']
    ..taskName = json['taskName']
    ..taskProductNum = json['taskProductNum']
    ..taskRate = json['taskRate']
    ..taskTargetNum = json['taskTargetNum']
    ..taskTypeStr = json['taskTypeStr']
    ..promoList = (json['promoList'] as List<dynamic>?)
        ?.map((e) =>
            GoodsManagementListPromoList.fromJson(e as Map<String, dynamic>?))
        .toList();
}

Map<String, dynamic> _$GoodsManagementListRowToJson(
        GoodsManagementListRow instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'fob': instance.fob,
      'grossMargin': instance.grossMargin,
      'imagesList': instance.imagesList,
      'isTeam': instance.isTeam,
      'showName': instance.showName,
      'spec': instance.spec,
      'suggestPrice': instance.suggestPrice,
      'taskFinishNum': instance.taskFinishNum,
      'taskId': instance.taskId,
      'taskName': instance.taskName,
      'taskProductNum': instance.taskProductNum,
      'taskRate': instance.taskRate,
      'taskTargetNum': instance.taskTargetNum,
      'taskTypeStr': instance.taskTypeStr,
      'promoList': instance.promoList,
    };

GoodsManagementGroupDetailListRow _$GoodsManagementGroupDetailListRowFromJson(
    Map<String, dynamic> json) {
  return GoodsManagementGroupDetailListRow()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : GoodsManagementGroupDetailListRow.fromJson(
            json['data'] as Map<String, dynamic>?)
    ..id = json['id']
    ..branchCode = json['branchCode']
    ..branchName = json['branchName']
    ..goodsCode = json['goodsCode']
    ..goodsName = json['goodsName']
    ..goodsLabel = json['goodsLabel']
    ..goodsLabelName = json['goodsLabelName']
    ..goodsSpec = json['goodsSpec']
    ..goodsId = json['goodsId']
    ..categoryId = json['categoryId']
    ..goodsManufacturer = json['goodsManufacturer']
    ..goodsShelvesStatus = json['goodsShelvesStatus']
    ..goodsChannel = json['goodsChannel']
    ..goodsStatus = json['goodsStatus']
    ..goodsStatusStr = json['goodsStatusStr']
    ..onTheShelfTime = json['onTheShelfTime']
    ..offTheShelfTime = json['offTheShelfTime']
    ..goodsCycle = json['goodsCycle']
    ..goodsCycleStr = json['goodsCycleStr']
    ..goodsCycleTips = json['goodsCycleTips']
    ..rebateRatio = json['rebateRatio']
    ..rebateRatioStr = json['rebateRatioStr']
    ..rebateRatioTips = json['rebateRatioTips']
    ..getCount = json['getCount']
    ..createTime = json['createTime']
    ..updateTime = json['updateTime']
    ..imageList = json['imageList'] as List<dynamic>?
    ..imageUrl = json['imageUrl']
    ..price = json['price']
    ..priceStr = json['priceStr']
    ..priceDetailStr = json['priceDetailStr']
    ..retailPrice = json['retailPrice']
    ..retailPriceStr = json['retailPriceStr']
    ..grossMargin = json['grossMargin']
    ..stock = json['stock']
    ..stockStr = json['stockStr']
    ..rebateAmount = json['rebateAmount']
    ..rebateAmountStr = json['rebateAmountStr']
    ..validity = json['validity']
    ..mediumPackage = json['mediumPackage']
    ..piecePackage = json['piecePackage']
    ..approvalNumber = json['approvalNumber']
    ..shelfLife = json['shelfLife']
    ..invalidDay = json['invalidDay']
    ..invalidDayStr = json['invalidDayStr']
    ..claimStatus = json['claimStatus']
    ..salesVolume = json['salesVolume']
    ..salesAmount = json['salesAmount']
    ..salesAmountStr = json['salesAmountStr'];
}

Map<String, dynamic> _$GoodsManagementGroupDetailListRowToJson(
        GoodsManagementGroupDetailListRow instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'id': instance.id,
      'branchCode': instance.branchCode,
      'branchName': instance.branchName,
      'goodsCode': instance.goodsCode,
      'goodsName': instance.goodsName,
      'goodsLabel': instance.goodsLabel,
      'goodsLabelName': instance.goodsLabelName,
      'goodsSpec': instance.goodsSpec,
      'goodsId': instance.goodsId,
      'categoryId': instance.categoryId,
      'goodsManufacturer': instance.goodsManufacturer,
      'goodsShelvesStatus': instance.goodsShelvesStatus,
      'goodsChannel': instance.goodsChannel,
      'goodsStatus': instance.goodsStatus,
      'goodsStatusStr': instance.goodsStatusStr,
      'onTheShelfTime': instance.onTheShelfTime,
      'offTheShelfTime': instance.offTheShelfTime,
      'goodsCycle': instance.goodsCycle,
      'goodsCycleStr': instance.goodsCycleStr,
      'goodsCycleTips': instance.goodsCycleTips,
      'rebateRatio': instance.rebateRatio,
      'rebateRatioStr': instance.rebateRatioStr,
      'rebateRatioTips': instance.rebateRatioTips,
      'getCount': instance.getCount,
      'createTime': instance.createTime,
      'updateTime': instance.updateTime,
      'imageList': instance.imageList,
      'imageUrl': instance.imageUrl,
      'price': instance.price,
      'priceStr': instance.priceStr,
      'priceDetailStr': instance.priceDetailStr,
      'retailPrice': instance.retailPrice,
      'retailPriceStr': instance.retailPriceStr,
      'grossMargin': instance.grossMargin,
      'stock': instance.stock,
      'stockStr': instance.stockStr,
      'rebateAmount': instance.rebateAmount,
      'rebateAmountStr': instance.rebateAmountStr,
      'validity': instance.validity,
      'mediumPackage': instance.mediumPackage,
      'piecePackage': instance.piecePackage,
      'approvalNumber': instance.approvalNumber,
      'shelfLife': instance.shelfLife,
      'invalidDay': instance.invalidDay,
      'invalidDayStr': instance.invalidDayStr,
      'claimStatus': instance.claimStatus,
      'salesVolume': instance.salesVolume,
      'salesAmount': instance.salesAmount,
      'salesAmountStr': instance.salesAmountStr,
    };
