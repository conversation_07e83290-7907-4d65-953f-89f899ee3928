// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'authorize_goods_list_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AuthorizeGoodsListData _$AuthorizeGoodsListDataFromJson(
    Map<String, dynamic> json) {
  return AuthorizeGoodsListData()
    ..total = json['total']
    ..lastPage = json['lastPage']
    ..rows = (json['rows'] as List<dynamic>?)
        ?.map(
            (e) => AuthorizeGoodsItemData.fromJson(e as Map<String, dynamic>?))
        .toList();
}

Map<String, dynamic> _$AuthorizeGoodsListDataToJson(
        AuthorizeGoodsListData instance) =>
    <String, dynamic>{
      'total': instance.total,
      'lastPage': instance.lastPage,
      'rows': instance.rows,
    };

AuthorizeGoodsItemData _$AuthorizeGoodsItemDataFromJson(
    Map<String, dynamic> json) {
  return AuthorizeGoodsItemData()
    ..skuId = json['skuId']
    ..skuName = json['skuName']
    ..skuPrice = json['skuPrice']
    ..shopName = json['shopName']
    ..imgUrl = json['imgUrl']
    ..skuStore = json['skuStore']
    ..skuSpec = json['skuSpec']
    ..grantStatus = json['grantStatus']
    ..reason = json['reason']
    ..isSelected = json['isSelected'] as bool?;
}

Map<String, dynamic> _$AuthorizeGoodsItemDataToJson(
        AuthorizeGoodsItemData instance) =>
    <String, dynamic>{
      'skuId': instance.skuId,
      'skuName': instance.skuName,
      'skuPrice': instance.skuPrice,
      'shopName': instance.shopName,
      'imgUrl': instance.imgUrl,
      'skuStore': instance.skuStore,
      'skuSpec': instance.skuSpec,
      'grantStatus': instance.grantStatus,
      'reason': instance.reason,
      'isSelected': instance.isSelected,
    };
