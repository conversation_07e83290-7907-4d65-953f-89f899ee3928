import 'package:XyyBeanSproutsFlutter/goods/bean/authorize_goods_list_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'authorize_goods_list_parent_data.g.dart';

@JsonSerializable()
class AuthorizeGoodsListParentData extends BaseModelV2<AuthorizeGoodsListParentData> {
  dynamic distance;
  AuthorizeGoodsListData? page;

  AuthorizeGoodsListParentData();

  factory AuthorizeGoodsListParentData.fromJson(Map<String, dynamic>? json) =>
      _$AuthorizeGoodsListParentDataFromJson(json!);

  @override
  AuthorizeGoodsListParentData fromJsonMap(Map<String, dynamic>? json) {
    return AuthorizeGoodsListParentData.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$AuthorizeGoodsListParentDataToJson(this);
  }
}
