import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'control_good_list_data.g.dart';

@JsonSerializable()
class ControlGoodListPageData extends BaseModelV2<ControlGoodListPageData> {
  dynamic lastPage;

  // 总数量
  dynamic total;

  List<ControlGoodListData>? rows;

  ControlGoodListPageData();

  @override
  ControlGoodListPageData fromJsonMap(Map<String, dynamic> json) {
    return _$ControlGoodListPageDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ControlGoodListPageDataToJson(this);
  }
}

@JsonSerializable()
class ControlGoodListData extends BaseModelV2<ControlGoodListData> {
  /// 商品id
  dynamic skuId;

  /// 名称
  dynamic skuName;

  /// 规格
  dynamic skuSpec;

  /// 商家
  dynamic shopName;

  /// 单价
  dynamic skuPrice;

  /// 库存
  dynamic skuStore;

  /// 图片
  dynamic imgUrl;

  ControlGoodListData();

  factory ControlGoodListData.fromJson(Map<String, dynamic> json) {
    return _$ControlGoodListDataFromJson(json);
  }

  @override
  ControlGoodListData fromJsonMap(Map<String, dynamic> json) {
    return _$ControlGoodListDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ControlGoodListDataToJson(this);
  }
}
