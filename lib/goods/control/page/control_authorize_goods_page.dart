import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/dialog/common_alert_dialog.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_appbar_search.dart';
import 'package:XyyBeanSproutsFlutter/goods/bean/authorize_goods_list_data.dart';
import 'package:XyyBeanSproutsFlutter/goods/bean/authorize_goods_list_parent_data.dart';
import 'package:XyyBeanSproutsFlutter/goods/bean/authorize_result_data.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/util/authorize_type.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/widget/control_goods_list_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class ControlAuthorizeGoodsPage extends BasePage {
  final AuthorizeType authorizeType;
  final ValueNotifier<bool> batchOperationNotifier;
  final String? skuCollectCode;
  final String? merchantId;
  final dynamic? customerId;

  ControlAuthorizeGoodsPage(
    this.authorizeType,
    this.skuCollectCode,
    this.merchantId,
    this.batchOperationNotifier,
    this.customerId,
  );

  @override
  BaseState<StatefulWidget> initState() {
    return ControlAuthorizeGoodsPageState();
  }
}

class ControlAuthorizeGoodsPageState
    extends BaseState<ControlAuthorizeGoodsPage> {
  EasyRefreshController _easyRefreshController = new EasyRefreshController();

  bool requestSuccess = true;

  List<AuthorizeGoodsItemData> dataSource = [];

  bool isLastPage = true;

  String? keyword = "";

  String? total;

  int offset = 0;

  VoidCallback? batchOperationListener;

  dynamic distance;

  @override
  bool isSubPage() {
    return true;
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return null;
  }

  @override
  String getTitleName() {
    return "";
  }

  @override
  void onCreate() {
    super.onCreate();
    batchOperationListener = () {
      if (!widget.batchOperationNotifier.value) {
        dataSource.forEach((element) {
          element.isSelected = false;
        });
      }
    };
    widget.batchOperationNotifier.addListener(batchOperationListener!);
    showLoadingDialog();
    requestListData(true);
  }

  @override
  void dispose() {
    super.dispose();
    widget.batchOperationNotifier.removeListener(batchOperationListener!);
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      child: Column(
        children: [
          buildSearchWidget(),
          buildGoodsListWidget(),
          buildBatchOperationWidget()
        ],
      ),
    );
  }

  Widget buildBatchOperationWidget() {
    return ValueListenableBuilder<bool>(
      valueListenable: widget.batchOperationNotifier,
      builder: (context, value, child) {
        return Visibility(
          visible: value,
          child: Container(
            height: 62,
            padding: EdgeInsets.symmetric(vertical: 9, horizontal: 10),
            color: Colors.white,
            child: Row(
              children: [
                Expanded(
                  child: GestureDetector(
                      onTap: () {
                        var skuIds = dataSource
                            .where((value) => value.isSelected ?? false)
                            .map((e) => e.skuId?.toString())
                            .join(",");
                        widget.authorizeType == AuthorizeType.Authorized
                            ? requestCancelAuthorize(skuIds, false)
                            : requestAuthorize(skuIds, false);
                      },
                      behavior: HitTestBehavior.opaque,
                      child: Container(
                        height: double.infinity,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(2),
                            color: Color(0xff00b377)),
                        alignment: Alignment.center,
                        child: Text(
                          widget.authorizeType == AuthorizeType.Authorized
                              ? "取消授权"
                              : "授权",
                          style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                              fontSize: 16),
                        ),
                      )),
                ),
                SizedBox(
                  width: 10,
                ),
                Expanded(
                  child: GestureDetector(
                      onTap: () {
                        widget.authorizeType == AuthorizeType.Authorized
                            ? requestCancelAuthorize("", true)
                            : requestAuthorize("", true);
                      },
                      behavior: HitTestBehavior.opaque,
                      child: Container(
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(2),
                            color: Color(0xff00b377)),
                        alignment: Alignment.center,
                        child: Text(
                          widget.authorizeType == AuthorizeType.Authorized
                              ? "取消全部授权"
                              : "全部授权",
                          style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                              fontSize: 16),
                        ),
                      )),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget buildSearchWidget() {
    return Container(
      color: Colors.green,
      child: SAppBarSearch(
        hintText: "请输入商品、厂家名称搜索",
        showLeading: false,
        hideCancel: true,
        autoFocus: false,
        height: 44,
        onClear: () {
          keyword = null;
          showLoadingDialog();
          requestListData(true);
        },
        onCancel: () {
          keyword = null;
          showLoadingDialog();
          requestListData(true);
        },
        onSearch: (value) {
          keyword = value;
          showLoadingDialog();
          requestListData(true);
        },
      ),
    );
  }

  Widget buildGoodsListWidget() {
    print("guan parent build");
    return Expanded(
      child: Container(
        color: Color(0xfff6f6f6),
        child: Column(
          children: [
            buildGoodsCountWidget(),
            Expanded(
              child: EasyRefresh(
                onRefresh: () async {
                  return await requestListData(true);
                },
                onLoad: isLastPage
                    ? null
                    : () async {
                        return await requestListData(false);
                      },
                emptyWidget: buildEmptyWidget(),
                controller: _easyRefreshController,
                child: ListView.builder(
                  itemCount: this.dataSource.length,
                  itemBuilder: (context, index) {
                    print("guan ListView.builder");
                    return ControlGoodsListItemWidget(
                      widget.authorizeType,
                      dataSource[index],
                      widget.batchOperationNotifier,
                      () {
                        jumpGoodsDetail(dataSource[index].skuId);
                      },
                      (operationType) {
                        print("guan operationType:$operationType");
                        switch (operationType) {
                          case OperationType.Authorize:
                            requestAuthorize(
                                dataSource[index].skuId?.toString(), false);

                            break;
                          case OperationType.CancelAuthorize:
                            requestCancelAuthorize(
                                dataSource[index].skuId?.toString(), false);
                            break;
                          case OperationType.Disable:
                            showCommonAlert(
                                context: context,
                                title: "提示",
                                content: dataSource[index].reason ?? "--",
                                actions: [
                                  CommonAlertAction(
                                    title: '我知道了',
                                    style: CommonAlertActionStyle.normal,
                                  )
                                ]);
                            break;
                        }
                      },
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget? buildEmptyWidget() {
    if (this.dataSource.length == 0) {
      if (this.requestSuccess) {
        return PageStateWidget.pageEmpty(PageState.Empty);
      } else {
        return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
          showLoadingDialog();
          this.requestListData(true);
        });
      }
    }

    return null;
  }

  Widget buildGoodsCountWidget() {
    return Visibility(
      visible: buildEmptyWidget() == null,
      child: Container(
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
        child: Text(
          getGoodsCountText(),
          style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
              color: Color(0xff676773)),
        ),
      ),
    );
  }

  String getGoodsCountText() {
    switch (widget.authorizeType) {
      case AuthorizeType.Authorized:
        return "授权商品：${total ?? "--"}个";
      case AuthorizeType.Unauthorized:
        return "未授权商品：${total ?? "--"}个";
    }
  }

  String getRequestListPath() {
    switch (widget.authorizeType) {
      case AuthorizeType.Authorized:
        return "skuCollectMgr/merchant/grantedSkuList";
      case AuthorizeType.Unauthorized:
        return "skuCollectMgr/merchant/nonGrantSkuList";
    }
  }

  Future<void> requestListData(bool isRefresh) async {
    if (isRefresh) {
      offset = 0;
    }
    var params = {
      "merchantId": widget.merchantId ?? "",
      "skuCollectCode": widget.skuCollectCode ?? "",
      "limit": 10,
      "offset": offset + 1 // 分页从1开始
    };
    if (keyword != null && keyword!.isNotEmpty) {
      params["searchKey"] = keyword ?? "";
    }

    var result = await NetworkV2<AuthorizeGoodsListParentData>(
            AuthorizeGoodsListParentData())
        .requestDataV2(getRequestListPath(),
            method: RequestMethod.GET, parameters: params);
    this.dismissLoadingDialog();
    requestSuccess = result.isSuccess ?? false;
    if (result.isSuccess == true && mounted) {
      var data = result.getData()?.page;
      if (offset == 0) {
        _easyRefreshController.finishRefresh(
            success: true, noMore: data?.lastPage == true);
        dataSource = data?.rows ?? [];
        total = data?.total?.toString();
      } else {
        _easyRefreshController.finishLoad(
            success: true, noMore: data?.lastPage == true);
        dataSource.addAll(data?.rows ?? []);
      }
      offset++;

      distance = result.getData()?.distance;
      isLastPage = data?.lastPage ?? false;
      setState(() {});
    }
  }

  void jumpGoodsDetail(dynamic skuId) {
    String router =
        "xyy://crm-app.ybm100.com/good_detail?id=${skuId?.toString()}&isFromCustomer=1&merchantId=${widget.customerId}";
    XYYContainer.open(router);
  }

  void requestCancelAuthorizeReal(String? skuIds, bool isAll) {
    showLoadingDialog();
    NetworkV2<AuthorizeResultData>(AuthorizeResultData()).requestDataV2(
        "skuCollectMgr/merchant/cancelGrant",
        method: RequestMethod.GET,
        parameters: {
          "merchantId": widget.merchantId,
          "skuIds": skuIds,
          "allFlag": isAll ? 1 : 0,
          "skuCollectCode": widget.skuCollectCode
        }).then((value) {
      dismissLoadingDialog();
      if (value.isSuccess == true && mounted) {
        widget.batchOperationNotifier.value = false;
        showLoadingDialog();
        requestListData(true);
      }
    });
  }

  void requestCancelAuthorize(String? skuIds, bool isAll) {
    showCommonAlert(
        context: context,
        title: "提示",
        content: "取消授权后，该客户无法购买该商品，确认是否取消",
        actions: [
          CommonAlertAction(
            title: '取消',
            style: CommonAlertActionStyle.cancle,
          ),
          CommonAlertAction(
            title: '确定',
            style: CommonAlertActionStyle.normal,
            onPressed: () {
              requestCancelAuthorizeReal(skuIds, isAll);
            },
          )
        ]);
  }

  void requestAuthorize(String? skuIds, bool isAll) {
    showCommonAlert(
        context: context,
        title: "提示",
        content: "授权后，距离该客户${distance ?? "--"}米范围内的所有客户不可授权该商品，授权后的商品可在药帮忙进行购买",
        actions: [
          CommonAlertAction(
            title: '取消',
            style: CommonAlertActionStyle.cancle,
          ),
          CommonAlertAction(
            title: '确定',
            style: CommonAlertActionStyle.normal,
            onPressed: () {
              requestAuthorizeReal(skuIds, isAll);
            },
          )
        ]);
  }

  void requestAuthorizeReal(String? skuIds, bool isAll) {
    showLoadingDialog();
    NetworkV2<AuthorizeResultData>(AuthorizeResultData()).requestDataV2(
        "skuCollectMgr/merchant/saveGrant",
        method: RequestMethod.GET,
        parameters: {
          "merchantId": widget.merchantId,
          "allFlag": isAll ? 1 : 0,
          "skuIds": skuIds,
          "skuCollectCode": widget.skuCollectCode
        }).then((value) {
      dismissLoadingDialog();
      if (value.isSuccess == true && mounted) {
        widget.batchOperationNotifier.value = false;
        showLoadingDialog();
        requestListData(true);
      }
    });
  }
}
