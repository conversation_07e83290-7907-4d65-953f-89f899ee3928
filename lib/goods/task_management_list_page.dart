import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/header_footer/header_footer_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/drop_down_menu.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/easy_popup.dart';
import 'package:XyyBeanSproutsFlutter/common/button/dropdown_button_widget.dart';
import 'package:XyyBeanSproutsFlutter/goods/bean/task_list_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'dart:math';

class TaskManagementListPage extends BasePage {
  final int? selectedTab;
  final queryType; //1 我发布的任务，2 我收到的任务。

  TaskManagementListPage({this.selectedTab, this.queryType});

  @override
  BaseState initState() {
    return TaskManagementListPageState(
        selectedTab: selectedTab, queryType: this.queryType);
  }
}

class TaskManagementListPageState extends BaseState<TaskManagementListPage> {
  int? selectedTab;

  final queryType; //区分入口

  TaskManagementListPageState({this.selectedTab, this.queryType});

  var sortSelectType = 0;

  var taskType = 0; //默认全部 0，信息收集 1、系统/软件售卖2、订单卡单3

  var taskStatus = 0; //默认全部0，草搞1、未开始2、进行中3、已结束4。

  var taskTypeList = [
    "全部",
    "订单卡单(资质过期)",
    "信息收集",
    "系统/软件售卖"
  ]; //默认全部 0，信息收集 1、系统/软件售卖2、订单卡单3

  var taskStatusList = [
    "全部",
    "草稿",
    "未开始",
    "进行中",
    "已结束"
  ]; //默认全部0，草搞1、未开始2、进行中3、已结束4

  var offset = 0;

  var refresh = false;

  var _controller = EasyRefreshController();

  var dataList = <TaskManagementListRow>[];

  var taskTypeSelectValue = "全部";

  var taskStatusSelectValue = "全部";

  var taskList = [];

  var selectStr = "";

  String? mImageHost;

  TaskManagementList? detailData;

  PageStateWidget pageStateWidget = PageStateWidget();

  bool isSubPage() {
    return true;
  }

  @override
  bool needKeepAlive() {
    return true;
  }

  @override
  void initState() {
    super.initState();
    this.prepareDtaMethod();
    this.requestData();
  }

  void prepareDtaMethod() {
    if (queryType == 2) {
      //我收到的任务
      taskStatusList = ["全部", "未开始", "进行中", "已结束"];
    } else {
      //我发布的任务
      taskStatusList = ["全部", "草稿", "未开始", "进行中", "已结束"];
    }
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Scaffold(
      // floatingActionButton: FloatingActionButton(
      //   // backgroundColor: Color(0xFFF6F6F6),
      //   backgroundColor: Colors.transparent,
      //   onPressed: () {},
      //   child: Image.asset(
      //     "assets/images/task/task_add_icon.png",
      //     fit: BoxFit.fill,
      //   ),
      // ),
      body: Container(
        color: Color(0xFFF6F6F6),
        child: listView(),
      ),
    );
  }

  void requestData() async {
    EasyLoading.show(status: "加载中");
    NetworkV2<TaskManagementList>(TaskManagementList())
        .requestDataV2("task/taskList",
            parameters: {
              "taskType": taskType,
              "taskStatus": taskStatus,
              "limit": 10,
              "offset": offset,
              "queryType": queryType,
            },
            method: RequestMethod.GET)
        .then((value) {
      if (mounted && value.isSuccess != null && value.isSuccess!) {
        EasyLoading.dismiss();
        refresh = false;
        setState(() {
          var result = value.getData();
          if (result != null && result.rows != null) {
            if (offset == 0) {
              this.detailData = value.getData();
              this.dataList.clear();
              refresh = false;
            }
            this.dataList.addAll(result.rows!);
            if (this.dataList.length == 0) {
              pageStateWidget.setPageState(PageState.Empty);
            } else {
              pageStateWidget.setPageState(PageState.Normal);
            }
          }
        });
      } else {
        // showToast(value.msg);
      }
      EasyLoading.dismiss();
      if (mounted) {
        if (refresh) {
          _controller.resetLoadState();
        }
        _controller.finishRefresh();
        _controller.finishLoad(noMore: value.getData()?.lastPage ?? false);
      }
    });
  }

  void refreshData() {
    offset = 0;
    requestData();
  }

  ///订单列表每条item
  Widget listView() {
    pageStateWidget = new PageStateWidget();
    return EasyRefresh.custom(
      controller: _controller,
      enableControlFinishRefresh: true,
      enableControlFinishLoad: true,
      header: HeaderFooterHelp().getHeader(),
      footer: HeaderFooterHelp().getFooter(),
      onRefresh: () async {
        refresh = true;
        refreshData();
      },
      onLoad: () async {
        refresh = false;
        offset++;
        requestData();
      },
      headerIndex: 1,
      slivers: [
        SliverPersistentHeader(
          pinned: true,
          delegate: _SliverAppBarDelegate(
              minHeight: 50,
              maxHeight: 50,
              child: Column(
                children: [
                  buildHeaderWidget(),
                ],
              )),
        ),
        SliverList(
          delegate:
              SliverChildBuilderDelegate((BuildContext context, int index) {
            return orderItem(this.dataList[index], index);
          }, childCount: this.dataList.length), //thi
        )
      ],
      emptyWidget: getEmptyWidget(),
    );
  }

  Widget? getEmptyWidget() {
    if ((this.dataList.length) == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  Widget buildHeaderWidget() {
    return Container(
      padding: EdgeInsets.only(top: 1),
      color: Color(0xFFFFFFFF),
      height: 50,
      width: double.maxFinite,
      child: Column(
        children: [
          SizedBox(
            height: 14,
          ),
          Container(
            child: Row(
              children: [
                Container(
                  width: getScreenWidth(),
                  child: GestureDetector(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Container(
                          child: DropButtonWidget(
                            title: "任务类型",
                            isSelected: sortSelectType == 1,
                            type: _buttoType(1),
                            onPressed: () {
                              this.updateSort(1);
                            },
                          ),
                        ),
                        Container(
                          child: DropButtonWidget(
                            title: "任务状态",
                            isSelected: sortSelectType == 2,
                            type: _buttoType(2),
                            onPressed: () {
                              this.updateSort(2);
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /*
   * 更新排序状态
   * index: 当前点击按钮位置
   */
  void updateSort(int index) {
    if (index == 1) {
      //任务类型弹窗
      taskList = taskTypeList;
      selectStr = taskTypeSelectValue;
    } else {
      taskList = taskStatusList;
      selectStr = taskStatusSelectValue;
    }
    EasyPopup.show(
      context,
      DropDownMenu(
        values: taskList as List<String>,
        selectValue: selectStr,
        surePressed: (selectIndex, selectValue) {
          if (index == 1) {
            switch (selectValue) {
              case "全部":
                taskType = 0;
                break;
              case "信息收集":
                taskType = 1;
                break;
              case "系统/软件售卖":
                taskType = 2;
                break;
              case "订单卡单(资质过期)":
                taskType = 3;
                break;
              default:
            }
            taskTypeSelectValue = selectValue;
          } else {
            switch (selectValue) {
              case "全部":
                taskStatus = 0;
                break;
              case "草稿":
                taskStatus = 1;
                break;
              case "未开始":
                taskStatus = 2;
                break;
              case "进行中":
                taskStatus = 3;
                break;
              case "已结束":
                taskStatus = 4;
                break;
              default:
            }
            taskStatusSelectValue = selectValue;
          }
          //刷新数据
          refreshData();
        },
        cancelOnPressed: () {
          setState(() {
            if (index == 1) {
              if (sortSelectType == 1) {
                sortSelectType = 0;
              } else {
                sortSelectType = 1;
              }
            } else {
              if (sortSelectType == 2) {
                sortSelectType = 0;
              } else {
                sortSelectType = 2;
              }
            }
          });
        },
      ),
      offsetLT: Offset(0, MediaQuery.of(context).padding.top + 150),
    );

    setState(() {
      if (index == 1) {
        if (sortSelectType == 1) {
          sortSelectType = 0;
        } else {
          sortSelectType = 1;
        }
      } else {
        if (sortSelectType == 2) {
          sortSelectType = 0;
        } else {
          sortSelectType = 2;
        }
      }
    });
  }

  DropButtonWidgetType _buttoType(int index) {
    if (index == 1) {
      if (sortSelectType == 1) {
        return DropButtonWidgetType.desc;
      } else {
        return DropButtonWidgetType.normal;
      }
    } else {
      if (sortSelectType == 2) {
        return DropButtonWidgetType.desc;
      } else {
        return DropButtonWidgetType.normal;
      }
    }
  }

  //订单列表item
  Widget orderItem(TaskManagementListRow list, int index) {
    var textCorlor = Color(0xFFFF7200);
    if (list.taskStatus.toString() == "已结束") {
      textCorlor = Color(0xFF9494A6);
    }
    return GestureDetector(
      child: Container(
        // margin: EdgeInsets.only(top: 41),
        padding: EdgeInsets.all(10),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4), color: Color(0xFFFFFFFF)),
        margin: EdgeInsets.fromLTRB(15, 10, 15, 0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            /// 商品组 content
            Container(
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: EdgeInsets.only(
                        right: 30,
                      ),
                      child: Text(
                        list.theme.toString(),
                        style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF292933)),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(right: 2),
                    child: Text(
                      list.taskStatus.toString(),
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w300,
                        color: textCorlor,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            //任务类型
            Container(
              margin: EdgeInsets.only(top: 10),
              child: RichText(
                /// 元符号
                text: TextSpan(children: [
                  /// 小数点前
                  TextSpan(
                    text: "任务类型     ",
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF9494A6),
                      height: 1,
                    ),
                  ),

                  /// 小数点后
                  TextSpan(
                    text: list.taskType.toString(),
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF676773),
                      height: 1,
                    ),
                  ),
                ]),
              ),
            ),
            //发布时间
            Container(
              margin: EdgeInsets.only(top: 8),
              child: RichText(
                /// 元符号
                text: TextSpan(children: [
                  /// 小数点前
                  TextSpan(
                    text: "发布时间     ",
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF9494A6),
                      height: 1,
                    ),
                  ),

                  /// 小数点后
                  TextSpan(
                    text: list.publishTime.toString(),
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF676773),
                      height: 1,
                    ),
                  ),
                ]),
              ),
            ),

            //任务时间
            Container(
              margin: EdgeInsets.only(top: 8),
              child: RichText(
                /// 元符号
                text: TextSpan(children: [
                  /// 小数点前
                  TextSpan(
                    text: "任务时间     ",
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF9494A6),
                      height: 1,
                    ),
                  ),

                  /// 小数点后
                  TextSpan(
                    text: list.startTime.toString() +
                        " - " +
                        list.endTime.toString(),
                    // text: "${list.startTime.toString() ?? "--"+list.endTime.toString() ?? "--" + "123"}",
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF676773),
                      height: 1,
                    ),
                  ),
                ]),
              ),
            ),
          ],
        ),
      ),
      onTap: () {
        Navigator.of(context).pushNamed('/TaskDetailPage', arguments: {
          "taskId": list.id,
        }).then((value) => {});
      },
    );
  }

  @override
  String getTitleName() {
    return "";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return null;
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  _SliverAppBarDelegate({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });

  final double minHeight;
  final double maxHeight;
  final Widget child;

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => max(maxHeight, minHeight);

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return new SizedBox.expand(child: child);
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return maxHeight != oldDelegate.maxHeight ||
        minHeight != oldDelegate.minHeight ||
        child != oldDelegate.child;
  }
}

class VerticalDottedPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    var paint = Paint() // 画笔
      ..strokeWidth = 1 // 画笔宽度
      ..isAntiAlias = true // 是否开启抗锯齿
      ..color = Color(0xFFFFB1AD); // 画笔颜色

    var dashHeight = 2;
    var dashSpace = 1;
    double startY = 0;
    final space = (dashSpace + dashHeight);

    while (startY < size.height) {
      canvas.drawLine(Offset(0, startY), Offset(0, startY + dashHeight), paint);
      startY += space;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}

/// 数据模型
class _HomeSalesTimeFilter {
  final String title;
  final String filter;
  bool isSelected = false;

  _HomeSalesTimeFilter(this.title, this.filter, {this.isSelected = false});
}
