import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/commodity_rank_page.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/page/control_manager_page.dart';
import 'package:XyyBeanSproutsFlutter/goods/goods_management_list_page.dart';
import 'package:flutter/material.dart';

import 'widgets/tab_custom_indicator.dart';

class GoodsPage extends BasePage {
  @override
  BaseState<StatefulWidget> initState() {
    return GoodsPageState();
  }
}

class GoodsPageState extends BaseState<GoodsPage>
    with SingleTickerProviderStateMixin {
  List _tabTitles = [
    "热销榜单",
    "控销商品",
    "任务商品",
  ];
  TabController? _tabController;

  @override
  void onCreate() {
    super.onCreate();
    _tabController = new TabController(length: _tabTitles.length, vsync: this);
  }

  @override
  bool isSubPage() {
    return true;
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      child: buildTabViewWidget(),
    );
  }

  @override
  String getTitleName() {
    return "";
  }

  buildTabBarWidget() {
    return Container(
      width: double.maxFinite,
      alignment: Alignment.bottomCenter,
      margin: EdgeInsets.only(left: 25),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        labelColor: Color(0xFF292933),
        indicator: TabCustomIndicator(
            wantWidth: 30, insets: EdgeInsets.only(bottom: 6)),
        labelStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        unselectedLabelColor: Color(0xFF676773),
        unselectedLabelStyle:
            TextStyle(fontSize: 14, fontWeight: FontWeight.normal),
        tabs: _tabTitles.map((e) => Tab(text: e)).toList(),
      ),
    );
  }

  @override
  bool needKeepAlive() {
    return true;
  }

  buildTabViewWidget() {
    return TabBarView(
      controller: _tabController,
      children: [
        CommodityRankPage(
          isSubPage: true,
        ),
        ControlManagerPage(isSubPage: true),
        GoodsManagementListPage(
          selectedTab: 0,
        ),
      ],
    );
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      this.getTitleName(),
      backgroundColor: Colors.white,
      leftType: LeftButtonType.none,
      leftButton: null,
      leadingWidth: 0,
      titleWidget: buildTabBarWidget(),
      rightButtons: [
        SizedBox(
          width: 30,
        )
      ],
    );
  }
}
