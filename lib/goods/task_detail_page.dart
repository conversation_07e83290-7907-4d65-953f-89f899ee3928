import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/goods/bean/goods_management_list_data.dart';
import 'package:XyyBeanSproutsFlutter/goods/bean/task_list_detail_data.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';

class TaskDetailPage extends BasePage {
  final taskId;

  TaskDetailPage({this.taskId});

  @override
  BaseState initState() {
    return TaskDetailPageState(taskId: taskId);
  }
}

class TaskDetailPageState extends BaseState {
  final taskId;

  TaskDetailPageState({this.taskId});

  var refresh = false;

  var list = <GoodsManagementListRow>[];

  String? mImageHost;

  TaskListDetailData? detailData = TaskListDetailData();

  PageStateWidget pageStateWidget = PageStateWidget();

  @override
  void initState() {
    super.initState();
    this.requestData();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return SafeArea(
      child: Container(
        color: Color(0xFFFFFFFF),
        child: SingleChildScrollView(
          child: buildHeaderWidget(),
        ),
      ),
    );
  }

  void requestData() async {
    EasyLoading.show(status: "加载中");
    NetworkV2<TaskListDetailData>(TaskListDetailData())
        .requestDataV2("task/taskDetails",
            parameters: {
              "id": taskId,
            },
            method: RequestMethod.GET)
        .then((value) {
      if (mounted) {
        if (value.isSuccess == true) {
          setPageState(PageState.Normal);
          refresh = false;
          setState(() {
            var result = value.getData();
            this.detailData = result;
          });
        } else {
          setPageState(PageState.Error, errorClick: () {
            requestData();
          });
        }
      }
      EasyLoading.dismiss();
    });
  }

  void refreshData() {
    requestData();
  }

  Widget buildHeaderWidget() {
    var textColor = Color(0xFFFF7200);
    if (detailData!.taskStatus.toString() == "已结束") {
      textColor = Color(0xFF9494A6);
    }
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: EdgeInsets.only(top: 10, bottom: 5),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(2),
              color: Colors.white,
              border: Border.all(color: Color(0xFFE7E7E7), width: 0.5),
              boxShadow: [
                BoxShadow(
                  color: Color(0x0D292933),
                  offset: Offset(0, 0.5),
                  blurRadius: 1,
                )
              ]),
          margin: EdgeInsets.fromLTRB(15, 10, 15, 0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10),
                child: Row(
                  children: [
                    Expanded(
                      child: Container(
                        padding: EdgeInsets.only(right: 30),
                        child: Text(
                          formatNullText(detailData?.theme?.toString() ?? ""),
                          maxLines: 2,
                          style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFF292933)),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                    Container(
                      child: Text(
                        detailData!.taskStatus ?? "--",
                        style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w300,
                            color: textColor),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 5,
              ),
              listWidget(detailData!.orderBaseList()),
              SizedBox(
                height: 5,
              ),
              Visibility(
                  visible: detailData?.type != null &&
                      detailData?.type !=
                          TaskListDetailData.TASK_TYPE_BLOCK_BY_LICENSE,
                  child: getChildTaskJumpButton())
            ],
          ),
        ),
        Visibility(
            visible: detailData?.type ==
                TaskListDetailData.TASK_TYPE_BLOCK_BY_LICENSE,
            child: getSingleChildTaskWidget())
      ],
    );
  }

  /// 列表widget
  Widget listWidget(List<OrderEntryData> modelList) {
    List<Widget> listWidget = [];
    modelList.forEach((element) {
      listWidget.add(itemWidget(element));
    });
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10),
      child: Column(
        children: listWidget,
      ),
    );
  }

  /// 列表
  Widget itemWidget(OrderEntryData model) {
    return Container(
      margin: const EdgeInsets.only(top: 5, bottom: 5),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 66,
            child: Text(
              model.title ?? "--",
              style: TextStyle(color: Color(0xFF9494A6), fontSize: 14),
              strutStyle: StrutStyle(leading: 0.2),
            ),
          ),
          SizedBox(width: 20),
          contentWidget(model),
          jumpWidget(model)
        ],
      ),
    );
  }

  // 列表
  Widget contentWidget(OrderEntryData model) {
    return Expanded(
      child: Text(
        model.content ?? "--",
        style: TextStyle(
            color: model.textColor != null
                ? Color(model.textColor!)
                : Color(0xFF676773),
            fontSize: 14,
            fontWeight:
                model.textColor != null ? FontWeight.w500 : FontWeight.normal),
        strutStyle: StrutStyle(leading: 0.2),
        maxLines: 5,
      ),
    );
  }

  String formatNullText(String text) {
    if (text.isEmpty || text == "null") {
      return "--";
    }
    return text;
  }

  @override
  String getTitleName() {
    return "任务详情";
  }

  @override
  PreferredSizeWidget getTitleBar(BuildContext context) {
    return CommonTitleBar(getTitleName());
  }

  Widget getChildTaskJumpButton() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          height: 1,
          color: Color(0xFFF6F6F6),
        ),
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            Navigator.of(context).pushNamed('/task_child_list_page',
                arguments: {"mainTaskId": detailData!.id});
          },
          child: Container(
            height: 44,
            padding: EdgeInsets.symmetric(horizontal: 10),
            child: Row(
              children: [
                Container(
                  margin: EdgeInsets.only(top: 12),
                  child: Text(
                    "子任务列表",
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF676773),
                      height: 1,
                    ),
                  ),
                ),
                Expanded(
                  child: Container(),
                ),
                Container(
                  margin: EdgeInsets.only(left: 10, top: 12),
                  child: Image.asset(
                    "assets/images/task/task_right_row_icon.png",
                    width: 18,
                    height: 18,
                  ),
                ),
              ],
            ),
          ),
        )
      ],
    );
  }

  Widget getSingleChildTaskWidget() {
    if (detailData?.childTask == null) {
      return Container();
    }
    ChildTask childTask = detailData!.childTask!;
    var textColor = Color(0xFFFF7200);
    if (childTask.status == 1) {
      textColor = Color(0xFF9494A6);
    }
    return Container(
      margin: EdgeInsets.fromLTRB(15, 20, 15, 28),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "关联子任务",
            style: TextStyle(
                fontSize: 12,
                color: Color(0xff9494a6),
                fontWeight: FontWeight.normal),
          ),
          SizedBox(
            height: 10,
          ),
          Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2),
                color: Colors.white,
                border: Border.all(color: Color(0xFFE7E7E7), width: 0.5),
                boxShadow: [
                  BoxShadow(
                    color: Color(0x0D292933),
                    offset: Offset(0, 0.5),
                    blurRadius: 1,
                  )
                ]),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  child: Row(
                    children: [
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.only(
                              left: 10, right: 30, top: 10, bottom: 12),
                          child: Text(
                            formatNullText(childTask.customerName ?? "--"),
                            maxLines: 2,
                            style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Color(0xFF292933)),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.only(right: 10),
                        child: Text(
                          childTask.taskStatus ?? "--",
                          style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w300,
                              color: textColor),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  margin: EdgeInsets.symmetric(vertical: 5),
                  height: 1,
                  color: Color(0xfff7f7f8),
                ),
                listWidget(detailData!.getChildTaskList()),
                GestureDetector(
                  onTap: () {
                    if (detailData?.childTask != null) {
                      if (detailData!.childTask!.bindBD == true) {
                        // 跳转资质编辑
                        XYYContainer.open(
                            "xyy://crm-app.ybm100.com/drugstore/detail/item?merchantId=${detailData!.childTask!.customerId}&position=2");
                      } else {
                        // 跳转公海客户详情
                        XYYContainer.open(
                            "/customer_public_detail?openSeaId=${detailData!.childTask!.customerId}");
                      }
                    }
                  },
                  child: Container(
                    height: 32,
                    alignment: Alignment.center,
                    margin: EdgeInsets.fromLTRB(10, 15, 10, 10),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(2),
                        border: Border.all(color: Color(0xff00b377), width: 1)),
                    child: Text(
                      "资质变更",
                      style: TextStyle(
                          color: Color(0xff00b377),
                          fontSize: 14,
                          fontWeight: FontWeight.normal),
                    ),
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  String getChildTaskStatusStr(int? implementStatus) {
    switch (implementStatus) {
      case 0:
        return "未完成";
      case 1:
        return "已完成";
      default:
        return "--";
    }
  }

  Widget jumpWidget(OrderEntryData model) {
    return Visibility(
      visible: model.jumpUrl != null && (model.jumpUrl?.isNotEmpty ?? false),
      child: GestureDetector(
        onTap: () {
          Navigator.of(context).pushNamed(model.jumpUrl!);
        },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              "查看",
              style: TextStyle(
                  fontSize: 12,
                  color: Color(0xff676773),
                  fontWeight: FontWeight.normal),
              strutStyle: StrutStyle(leading: 0.2),
            ),
            Image.asset(
              "assets/images/base/icon_arrow_right.png",
              width: 15,
              height: 15,
            )
          ],
        ),
      ),
    );
  }
}
