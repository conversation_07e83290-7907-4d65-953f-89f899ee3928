import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'goods_already_buy_data.g.dart';

@JsonSerializable()
class GoodsAlreadyBuyListData extends BaseModelV2<GoodsAlreadyBuyListData> {
  // 客户名称
  dynamic customerName;
  // 购买数量
  dynamic buyNum;
  // 购买金额
  dynamic buyGmv;
  // 最近购买时间
  dynamic lastBuyDate;
  // 客户id
  dynamic customerId;
  // 客户编码
  dynamic merchantId;

  GoodsAlreadyBuyListData();

  @override
  GoodsAlreadyBuyListData fromJsonMap(Map<String, dynamic> json) {
    return _$GoodsAlreadyBuyListDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$GoodsAlreadyBuyListDataToJson(this);
  }
}
