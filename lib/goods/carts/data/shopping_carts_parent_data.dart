import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'shopping_carts_parent_data.g.dart';

@JsonSerializable()
class ShoppingCartsParentData extends BaseModelV2<ShoppingCartsParentData> {
  dynamic productTotal;
  dynamic totalPrice;
  dynamic selectedProductNum;
  dynamic selectedTotalPrice;
  ShoppingCartsListData? cartPageVo;

  ShoppingCartsParentData();

  factory ShoppingCartsParentData.fromJson(Map<String, dynamic>? json) =>
      _$ShoppingCartsParentDataFromJson(json!);

  @override
  ShoppingCartsParentData fromJsonMap(Map<String, dynamic>? json) {
    return ShoppingCartsParentData.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ShoppingCartsParentDataToJson(this);
  }
}

@JsonSerializable()
class ShoppingCartsListData extends BaseModelV2<ShoppingCartsListData> {

  dynamic total;
  dynamic isLastPage;
  List<ShoppingCartsItemData>? row;

  ShoppingCartsListData();

  factory ShoppingCartsListData.fromJson(Map<String, dynamic>? json) =>
      _$ShoppingCartsListDataFromJson(json!);

  @override
  ShoppingCartsListData fromJsonMap(Map<String, dynamic>? json) {
    return ShoppingCartsListData.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ShoppingCartsListDataToJson(this);
  }
}

@JsonSerializable()
class ShoppingCartsItemData extends BaseModelV2<ShoppingCartsItemData> {

  dynamic skuId;
  dynamic name;
  dynamic spec;
  dynamic price;
  dynamic amount;
  dynamic selectStatus;
  dynamic skuStatus;
  dynamic imageUrl;
  dynamic subtotal;
  dynamic shopName;
  List<dynamic>? tagList;

  @JsonKey(ignore: true)
  dynamic discountPrice;

  ShoppingCartsItemData();

  factory ShoppingCartsItemData.fromJson(Map<String, dynamic>? json) =>
      _$ShoppingCartsItemDataFromJson(json!);

  @override
  ShoppingCartsItemData fromJsonMap(Map<String, dynamic>? json) {
    return ShoppingCartsItemData.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ShoppingCartsItemDataToJson(this);
  }
}



