// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'personal_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PersonalDataBean _$PersonalDataBeanFromJson(Map<String, dynamic> json) {
  return PersonalDataBean()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : PersonalDataBean.fromJson(json['data'] as Map<String, dynamic>)
    ..realName = json['realName'] as String?
    ..name = json['name'] as String?
    ..department = json['department'] as String?
    ..phone = json['phone'] as String?
    ..email = json['email'] as String?;
}

Map<String, dynamic> _$PersonalDataBeanToJson(PersonalDataBean instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'realName': instance.realName,
      'name': instance.name,
      'department': instance.department,
      'phone': instance.phone,
      'email': instance.email,
    };
