import 'package:flutter/cupertino.dart';

class PersonItem extends StatefulWidget {
  // init方法
  PersonItem({Key? key, required this.title, required this.content})
      : super(key: key);
  // 标题
  final String title;
  // 内容
  final String? content;

  _PersonItemState createState() => _PersonItemState();
}

class _PersonItemState extends State<PersonItem> {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: CupertinoColors.white,
      margin: EdgeInsets.only(top: 5, bottom: 5),
      child: Row(
          mainAxisSize: MainAxisSize.max, // 主轴
          crossAxisAlignment: CrossAxisAlignment.baseline, // 交叉轴
          textBaseline: TextBaseline.alphabetic,
          children: <Widget>[
            Container(
              padding: EdgeInsets.fromLTRB(15, 3, 0, 3),
              width: 100,
              child: Text(
                widget.title,
                style: TextStyle(fontSize: 15, color: Color(0xFF666666)),
              ),
            ),
            Expanded(
              child: Container(
                child: Text(
                  widget.content!,
                  style: TextStyle(fontSize: 15, color: Color(0xFF333333)),
                ),
              ),
            ),
          ]),
    );
  }
}
