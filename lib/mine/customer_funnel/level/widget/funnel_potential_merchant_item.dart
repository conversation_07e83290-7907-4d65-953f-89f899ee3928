import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_content_span.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_merchant_base_item.dart';
import 'package:flutter/material.dart';

class FunnelPotentialMerchantItem extends FunnelMerchantBaseItem {
  FunnelPotentialMerchantItem({
    required String title,
    required String totalOrderAmount,
    required String potentialInfo,
    bool isTop = false,
    bool isBottom = false,
    required VoidCallback clickAction,
  }) : super(
          title: title,
          content: Container(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                FunnelContentSpan(
                  title: '订单金额：',
                  content: totalOrderAmount + "元",
                ),
                SizedBox(height: 10),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('潜力分析：',
                        style: TextStyle(
                          color: Color(0xFF666666),
                          fontSize: 12,
                          fontWeight: FontWeight.normal,
                        )),
                    Text(
                      potentialInfo,
                      style: TextStyle(
                        color: Color(0xFF292933),
                        fontSize: 12,
                        fontWeight: FontWeight.normal,
                      ),
                    )
                  ],
                ),
              ],
            ),
          ),
          isFirst: isTop,
          isBottom: isBottom,
          clickAction: clickAction,
        );
}
