import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/level/data/funnel_level_model.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/level/widget/funnel_potential_sort_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class FunnelPotentialPage extends BasePage {
  final dynamic oaId;
  final dynamic deptCode;

  FunnelPotentialPage({this.oaId, this.deptCode});

  @override
  BaseState<StatefulWidget> initState() {
    return FunnelPotentialPageState();
  }
}

class FunnelPotentialPageState extends BaseState<FunnelPotentialPage> {
  EasyRefreshController _controller = EasyRefreshController();

  List<FunnelLevelDeptItemData> dataSource = [];

  @override
  void dispose() {
    this._controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    this.refreshData();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF1F1F1),
      padding: EdgeInsets.only(top: 10, bottom: 10),
      child: EasyRefresh(
        controller: _controller,
        onRefresh: () async {
          this.refreshData();
        },
        child: ListView.builder(
          itemCount: this.dataSource.length,
          itemBuilder: (context, index) {
            FunnelLevelDeptItemData model = this.dataSource[index];
            return FunnelPotentialSortItem(
              title: '${model.deptAndUserName ?? "--"}',
              index: index,
              isBottom: index == this.dataSource.length - 1,
              potentialNumber: "${model.potentialCustomers}",
              contentHidden: model.potentialCustomers == null,
              isBd: model.isBd,
              clickAction: () {
                bool haveChild = "${model.haveChild}" == "1";
                dynamic oaId = model.oaId;
                dynamic isBd = model.isBd;
                dynamic deptCode = model.deptCode;
                dynamic potentialCustomers = model.potentialCustomers ?? 0;
                if (haveChild) {
                  XYYContainer.open(
                      '/funnel_potential_page?oaId=$oaId&deptCode=$deptCode');
                } else {
                  XYYContainer.open(
                      '/funnel_potential_merchant_page?oaId=$oaId&isBd=$isBd&potentialNumber=$potentialCustomers');
                }
              },
            );
          },
        ),
        emptyWidget: this.getEmptyWidget(),
      ),
    );
  }

  Widget? getEmptyWidget() {
    if (this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  void refreshData() async {
    this.requestListData();
  }

  void requestListData() async {
    Map<String, dynamic> params = {"movesaleType": "0"};
    if (widget.oaId != null && widget.oaId != "null") {
      params["oaId"] = widget.oaId;
    }
    if (widget.deptCode != null && widget.deptCode != "null") {
      params["deptCode"] = widget.deptCode;
    }
    if (this.dataSource.length == 0) {
      this.showLoadingDialog();
    }
    var result = await NetworkV2<FunnelLevelDeptData>(FunnelLevelDeptData())
        .requestDataV2(
      'funnerV2/potentialStatistic',
      parameters: params,
      contentType: RequestContentType.FORM,
      method: RequestMethod.GET,
    );
    this.dismissLoadingDialog();
    this._controller.finishRefresh();
    if (result.isSuccess == true) {
      if (mounted) {
        FunnelLevelDeptData? data = result.getData();
        if (data != null) {
          this.dataSource = data.crmUserList ?? [];
          setState(() {});
        }
      }
    }
  }

  @override
  String getTitleName() {
    return "S级潜力客户";
  }
}
