import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'funnel_level_model.g.dart';

@JsonSerializable()
class FunnelLevelDeptData extends BaseModelV2<FunnelLevelDeptData> {
  List<FunnelLevelDeptItemData>? crmUserList;

  dynamic thisMonthSCustomers; // 本月S级客户数
  dynamic potentialCustomers; // 潜力客户数

  FunnelLevelDeptData();

  @override
  FunnelLevelDeptData fromJsonMap(Map<String, dynamic> json) {
    return _$FunnelLevelDeptDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$FunnelLevelDeptDataToJson(this);
  }
}

@JsonSerializable()
class FunnelLevelDeptItemData extends BaseModelV2<FunnelLevelDeptItemData> {
  dynamic oaId; // 员工（部门负责人）编码
  dynamic crmUserName; // 员工展示名称
  dynamic deptCode; // 部门编码
  dynamic crmUserDepartment; // 员工所属部门名称
  dynamic haveChild; // 是否有子节点 ： 1有 0无
  dynamic isBd; // 1=BD，2管理层 3 未关联BD
  dynamic postOaIds; // 岗位下所有oaId
  dynamic deptAndUserName; // 部门加员工
  dynamic isPostDept; // 是否岗位
  dynamic thisMonthSCustomers; // 本月S级客户数
  dynamic lastMonthSCustomers; // 上月S级客户数
  dynamic potentialCustomers; // 潜力客户数

  FunnelLevelDeptItemData();

  factory FunnelLevelDeptItemData.fromJson(Map<String, dynamic> json) {
    return _$FunnelLevelDeptItemDataFromJson(json);
  }

  @override
  FunnelLevelDeptItemData fromJsonMap(Map<String, dynamic> json) {
    return _$FunnelLevelDeptItemDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$FunnelLevelDeptItemDataToJson(this);
  }
}

@JsonSerializable()
class FunnelLevelMerchantData extends BaseModelV2<FunnelLevelMerchantData> {
  /// S级客户数
  dynamic sLevelCustomers;

  /// 最后一页
  dynamic isLastPage;

  /// 客户列表
  List<FunnelLevelMerchantItemData>? merchantDtoList;

  FunnelLevelMerchantData();

  @override
  FunnelLevelMerchantData fromJsonMap(Map<String, dynamic> json) {
    return _$FunnelLevelMerchantDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$FunnelLevelMerchantDataToJson(this);
  }
}

@JsonSerializable()
class FunnelLevelMerchantItemData
    extends BaseModelV2<FunnelLevelMerchantItemData> {
  /// 药店编码
  dynamic merchantId;

  /// 药店名称
  dynamic merchantName;

  /// 订单金额
  dynamic totalOrderAmount;

  /// 订单数量
  dynamic orderNum;

  /// 最近下单时间
  dynamic lastOrderTime;

  /// 等级
  dynamic levelDesc;

  FunnelLevelMerchantItemData();

  @override
  FunnelLevelMerchantItemData fromJsonMap(Map<String, dynamic> json) {
    return _$FunnelLevelMerchantItemDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$FunnelLevelMerchantItemDataToJson(this);
  }

  factory FunnelLevelMerchantItemData.fromJson(Map<String, dynamic> json) {
    return _$FunnelLevelMerchantItemDataFromJson(json);
  }
}

@JsonSerializable()
class FunnelPotentialMerchantData
    extends BaseModelV2<FunnelPotentialMerchantData> {
  dynamic total;
  dynamic isLastPage;

  List<FunnelPotentialMerchantItemData>? potentialDtos;

  FunnelPotentialMerchantData();

  @override
  FunnelPotentialMerchantData fromJsonMap(Map<String, dynamic> json) {
    return _$FunnelPotentialMerchantDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$FunnelPotentialMerchantDataToJson(this);
  }
}

@JsonSerializable()
class FunnelPotentialMerchantItemData
    extends BaseModelV2<FunnelPotentialMerchantItemData> {
  dynamic merchantId;
  dynamic merchantName;
  dynamic totalOrderAmount;
  dynamic potentialInfo;

  FunnelPotentialMerchantItemData();

  factory FunnelPotentialMerchantItemData.fromJson(Map<String, dynamic> json) {
    return _$FunnelPotentialMerchantItemDataFromJson(json);
  }

  @override
  FunnelPotentialMerchantItemData fromJsonMap(Map<String, dynamic> json) {
    return _$FunnelPotentialMerchantItemDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$FunnelPotentialMerchantItemDataToJson(this);
  }
}
