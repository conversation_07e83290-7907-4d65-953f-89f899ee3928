import 'package:flutter/material.dart';

class FunnelContentSpan extends StatelessWidget {
  final String title;
  final String content;

  FunnelContentSpan({required this.title, required this.content});

  @override
  Widget build(BuildContext context) {
    return Container(
      child: RichText(
        text: TextSpan(
          text: this.title,
          style: TextStyle(
            color: Color(0xFF666666),
            fontSize: 12,
            fontWeight: FontWeight.normal,
          ),
          children: [
            TextSpan(
              text: this.content,
              style: TextStyle(
                color: Color(0xFF292933),
                fontSize: 12,
                fontWeight: FontWeight.normal,
              ),
            )
          ],
        ),
      ),
    );
  }
}
