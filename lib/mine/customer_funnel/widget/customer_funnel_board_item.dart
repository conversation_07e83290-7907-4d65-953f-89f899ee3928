import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/customer_funnel_mark_base.dart';
import 'package:flutter/material.dart';

import '../../../common/event_bus/event_bus.dart';

class CustomerFunnelBoardItem extends StatelessWidget {
  final String? newPois; // 新开门店数
  final String? registeredNoBDNum; // 	已注册无人认领
  CustomerFunnelBoardItem({
    Key? key,
    this.newPois,
    this.registeredNoBDNum,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: CustomerFunnelMarkBase(
        content: Container(
          padding: EdgeInsets.fromLTRB(15, 5, 13, 15),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "客户大盘",
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF292933),
                    ),
                  ),
                  Image.asset(
                    'assets/images/funnel/funnel_arrow_right.png',
                    width: 15,
                    height: 15,
                  )
                ],
              ),
              <PERSON><PERSON><PERSON><PERSON>(height: 10),
              Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      child: _CustomerFunnelBoardBlock(
                        title: "新开业门店数:",
                        content: "${this.newPois ?? "0"}",
                        iconPath: "assets/images/funnel/funnel_add_shop_icon.png",
                      ),
                      onTap: (){
                        Navigator.pop(context);
                        // XYYContainer.bridgeCall('public_list_jump');
                        EventBus().sendMessage(MainTabBarEventBusName.CHANGE_FILTER_POI_REGISTER, arg: 2);
                        Future.delayed(Duration(milliseconds: 100), () {
                          EventBus().sendMessage(
                              MainTabBarEventBusName.CHANGE_TAB_INDEX,
                              arg: 1);
                          Future.delayed(Duration(milliseconds: 100), () {
                            EventBus().sendMessage(
                                CustomerEventBusName.YBM_CUSTOMER_TAB_CHANGE,
                                arg: 1);
                          });
                        });
                      },
                    ),
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: GestureDetector(
                      child: _CustomerFunnelBoardBlock(
                        title: "已注册无人认领:",
                        content: "${this.registeredNoBDNum ?? "0"}",
                        iconPath: "assets/images/funnel/funnel_noboday_icon.png",
                      ),
                      onTap: (){
                        Navigator.pop(context);
                        // XYYContainer.bridgeCall('public_list_jump');
                        EventBus().sendMessage(MainTabBarEventBusName.CHANGE_FILTER_POI_REGISTER, arg: 1);
                        Future.delayed(Duration(milliseconds: 100), () {
                          EventBus().sendMessage(
                              MainTabBarEventBusName.CHANGE_TAB_INDEX,
                              arg: 1);
                          Future.delayed(Duration(milliseconds: 100), () {
                            EventBus().sendMessage(
                                CustomerEventBusName.YBM_CUSTOMER_TAB_CHANGE,
                                arg: 1);
                          });
                        });
                      },
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}

class _CustomerFunnelBoardBlock extends StatelessWidget {
  final String title;
  final String iconPath;
  final String content;

  _CustomerFunnelBoardBlock({
    required this.title,
    required this.content,
    required this.iconPath,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(10),
      decoration: BoxDecoration(
        border: Border.all(
          color: Color(0xFFCCCCCC),
          width: 0.5,
        ),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(iconPath, width: 30, height: 30),
          SizedBox(width: 5),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  style: TextStyle(
                    color: Color(0xFF676773),
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                RichText(
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  text: TextSpan(
                    text: content,
                    style: TextStyle(
                      color: Color(0xFF292933),
                      fontSize: 20,
                      fontWeight: FontWeight.w500,
                    ),
                    children: [
                      TextSpan(
                        text: " 家",
                        style: TextStyle(
                          color: Color(0xFF9494A6),
                          fontSize: 11,
                          fontWeight: FontWeight.w400,
                        ),
                      )
                    ],
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
