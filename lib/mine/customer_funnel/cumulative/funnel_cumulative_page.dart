import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/cumulative/data/funnel_cumulative_model.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_column_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/cumulative/widget/funnel_cumulative_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class FunnelCumulativePage extends BasePage {
  final dynamic totalNum;
  final dynamic oaId;
  final dynamic deptCode;

  FunnelCumulativePage({this.totalNum, this.oaId, this.deptCode});

  @override
  BaseState<StatefulWidget> initState() {
    return FunnelCumulativePageState();
  }
}

class FunnelCumulativePageState extends BaseState<FunnelCumulativePage> {
  EasyRefreshController _controller = EasyRefreshController();

  /// 本月新增
  dynamic currMonthsAddCustNum;

  List<FunnelCumulativeSalesItemModel> dataSource = [];

  @override
  void dispose() {
    this._controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    this.refreshData();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF1F1F1),
      child: Column(
        children: [
          this.getTopWidget(),
          Expanded(
            child: EasyRefresh(
              controller: this._controller,
              onRefresh: () async {
                this.refreshData();
              },
              child: ListView.builder(
                itemCount: this.dataSource.length,
                itemBuilder: (context, index) {
                  FunnelCumulativeSalesItemModel model = this.dataSource[index];
                  return FunnelCumulativeItem(
                    index: index,
                    title: "${model.deptAndUserName ?? "--"}",
                    isBottom: index == this.dataSource.length - 1,
                    contentHidden: "${model.isPostDept}" == "1",
                    cooperativeNum: '${model.totalCustNum ?? 0}',
                    newNum: '${model.addCustNum ?? 0}',
                    clickAction: () {
                      dynamic isBd = model.isBd;
                      dynamic oaId = model.oaId;
                      dynamic totalNum = model.totalCustNum ?? 0;
                      if ("${model.haveChild ?? 0}" == "0") {
                        XYYContainer.open(
                            '/funnel_cumulative_merchant_page?oaId=$oaId&isBd=$isBd&totalNum=$totalNum');
                      } else {
                        dynamic deptCode = model.deptCode;
                        XYYContainer.open(
                            '/funnel_cumulative_page?oaId=$oaId&totalNum=$totalNum&deptCode=$deptCode');
                      }
                    },
                  );
                },
              ),
              emptyWidget: this.getEmptyWidget(),
            ),
          )
        ],
      ),
    );
  }

  Widget? getEmptyWidget() {
    if (this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  Widget getTopWidget() {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(10),
          child: Row(
            children: [
              Expanded(
                flex: 1,
                child: FunnelColumnItem(
                  title: "累计客户数:",
                  content: "${widget.totalNum ?? 0}",
                  unit: "家",
                ),
              ),
              SizedBox(width: 10),
              Expanded(
                flex: 1,
                child: FunnelColumnItem(
                  title: "本月新增:",
                  content: "${this.currMonthsAddCustNum ?? 0}",
                  unit: "家",
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void refreshData() {
    this.requestListData();
  }

  void requestListData() async {
    Map<String, dynamic> params = {};
    if (widget.oaId != null && widget.oaId != "null") {
      params["oaId"] = widget.oaId;
    }
    if (widget.deptCode != null && widget.deptCode != "null") {
      params["deptCode"] = widget.deptCode;
    }
    if (this.dataSource.length == 0) {
      this.showLoadingDialog();
    }
    var result =
        await NetworkV2<FunnelCumulativeSalesData>(FunnelCumulativeSalesData())
            .requestDataV2(
      'customerFunnel/totalCustomer',
      parameters: params,
      contentType: RequestContentType.JSON,
      method: RequestMethod.POST,
    );
    this.dismissLoadingDialog();
    this._controller.finishRefresh();
    if (result.isSuccess == true) {
      if (mounted) {
        FunnelCumulativeSalesData? data = result.getData();
        if (data != null) {
          this.currMonthsAddCustNum = data.currMonthsAddCustNum;
          this.dataSource = data.crmUserList ?? [];
          setState(() {});
        }
      }
    }
  }

  @override
  String getTitleName() {
    return "累计客户";
  }
}
