import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/event_bus/event_bus.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/data/customer_funnel_list_data.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/customer_funnel_board_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/customer_funnel_cumulative_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/customer_funnel_level_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/customer_funnel_pin_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/customer_funnel_total_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';

import 'package:flutter_easyrefresh/easy_refresh.dart';

import '../../common/event_bus/event_bus.dart';

class CustomerFunnelPage extends BasePage {
  /// 当前登陆人id
  final String? oaId;

  CustomerFunnelPage({this.oaId});

  @override
  BaseState<StatefulWidget> initState() {
    return CustomerFunnelPageState();
  }
}

class CustomerFunnelPageState extends BaseState<CustomerFunnelPage> {
  EasyRefreshController _controller = EasyRefreshController();
  ScrollController _scrollController = ScrollController();
  List<GlobalKey> _itemKeys = [
    GlobalKey(),
    GlobalKey(),
    GlobalKey(),
    GlobalKey()
  ];

  /// 顶部大盘数据
  CustomerFunnelData? indexData;

  /// 累计客户数据;
  CustomerFunnelIndexCumulativeData? cumulativeData;

  /// 本月动销客户数据
  CustomerFunnelIndexPurchaseData? purchaseData;

  /// 本月s级客户数据
  CustomerSLevelModelData? sLevelData;

  bool requestSuccess = true;

  bool haveChild = false;

  @override
  void dispose() {
    _controller.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    this.refreshFunnelData();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      child: Column(
        children: [
          Container(height: 0.5, color: Color(0xFFE1E1E5)),
          Expanded(
            child: Container(
              color: Color(0xFFF1F1F1),
              child: EasyRefresh(
                onRefresh: () async {
                  this.refreshFunnelData();
                },
                controller: _controller,
                child: ListView.builder(
                  controller: _scrollController,
                  itemCount: 5,
                  cacheExtent: 2000,
                  itemBuilder: (context, index) {
                    if (index == 0) {
                      return CustomerFunnelTotalItem(
                        tapItem: scrollAction,
                        indexData: this.indexData,
                      );
                    }
                    if (index == 1) {
                      return GestureDetector(
                        onTap: () {
                          // 跳公海海列表
                          Navigator.pop(context);
                          // XYYContainer.bridgeCall('public_list_jump');
                          Future.delayed(Duration(milliseconds: 100), () {
                            EventBus().sendMessage(
                                MainTabBarEventBusName.CHANGE_TAB_INDEX,
                                arg: 1);
                            Future.delayed(Duration(milliseconds: 100), () {
                              EventBus().sendMessage(
                                  CustomerEventBusName.YBM_CUSTOMER_TAB_CHANGE,
                                  arg: 1);
                            });
                          });
                        },
                        behavior: HitTestBehavior.opaque,
                        child: CustomerFunnelBoardItem(
                          key: _itemKeys[0],
                          newPois: "${this.indexData?.newPois ?? 0}",
                          registeredNoBDNum:
                              "${this.indexData?.registeredNoBDNum ?? 0}",
                        ),
                      );
                    }
                    if (index == 2) {
                      return CustomerFunnelCumulativeItem(
                        key: _itemKeys[1],
                        data: this.cumulativeData,
                        actionCallback: cumulativePageJump,
                      );
                    }
                    if (index == 3) {
                      return CustomerFunnelPinItem(
                        key: _itemKeys[2],
                        pinItemCallback: pinPageJump,
                        data: this.purchaseData,
                      );
                    }
                    if (index == 4) {
                      return CustomerFunnelLevelItem(
                        key: _itemKeys[3],
                        itemClickCallback: levelPageJump,
                        data: sLevelData,
                      );
                    }
                    return Container();
                  },
                ),
                emptyWidget: this.getEmptyWidget(),
              ),
            ),
          )
        ],
      ),
    );
  }

  /// 滚动到指定item
  void scrollAction(int index) {
    GlobalKey _anchorKey = this._itemKeys[index];
    dynamic renderBox = _anchorKey.currentContext?.findRenderObject()!;
    Offset offset = renderBox.localToGlobal(Offset(0, 0));

    double statusHeight = MediaQuery.of(context).viewPadding.top;
    double navigationBarHeight = 44;

    double offsetY = _scrollController.offset +
        offset.dy -
        statusHeight -
        navigationBarHeight;
    if (offsetY > _scrollController.position.maxScrollExtent) {
      offsetY = _scrollController.position.maxScrollExtent;
    }
    _scrollController.animateTo(offsetY,
        duration: Duration(milliseconds: 300), curve: Curves.easeInOut);
  }

  Widget? getEmptyWidget() {
    if (!this.requestSuccess && this.indexData == null) {
      return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
        this.refreshFunnelData();
      });
    }
    if (this.indexData == null) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  @override
  String getTitleName() {
    return "客户漏斗";
  }

  /// 累计客户跳转
  void cumulativePageJump() {
    CustomerFunnelData showModel = this.indexData ?? CustomerFunnelData();
    dynamic totalNum = showModel.accumulateCustomers ?? 0;
    if (this.haveChild) {
      XYYContainer.open(
          '/funnel_cumulative_page?oaId=${widget.oaId}&totalNum=$totalNum');
    } else {
      XYYContainer.open(
          '/funnel_cumulative_merchant_page?oaId=${widget.oaId}&totalNum=$totalNum&isBd=1');
    }
  }

  /// 动销客户跳转
  void pinPageJump(bool isPin) {
    CustomerFunnelIndexPurchaseData showModel =
        this.purchaseData ?? CustomerFunnelIndexPurchaseData();
    if (isPin) {
      dynamic totalNum = showModel.moveSale?.purchaseCustomers ?? 0;
      if (this.haveChild) {
        XYYContainer.open(
            '/funnel_pin_page?oaId=${widget.oaId}&moveSaleTotalCustNum=$totalNum');
      } else {
        XYYContainer.open(
            '/funnel_pin_merchant_page?oaId=${widget.oaId}&totalCustNum=$totalNum&isBd=1');
      }
    } else {
      dynamic totalNum = showModel.nonMoveSale?.purchaseCustomers ?? 0;
      if (this.haveChild) {
        XYYContainer.open(
            '/funnel_no_pin_page?oaId=${widget.oaId}&noMoveSaleTotalCustNum=$totalNum');
      } else {
        XYYContainer.open(
            '/funnel_no_pin_merchant_page?oaId=${widget.oaId}&nonMoveSaleTotalCustNum=$totalNum&isBd=1');
      }
    }
  }

  /// 跳转S级客户页面
  void levelPageJump(bool isPotential) {
    if (!isPotential) {
      CustomerSLevelModelData showModel =
          sLevelData ?? CustomerSLevelModelData();
      dynamic totalNum = showModel.sLevelCustomers ?? 0;
      if (this.haveChild) {
        XYYContainer.open(
            '/funnel_level_page?oaId=${widget.oaId}&sLevelNumber=$totalNum');
      } else {
        XYYContainer.open(
            '/funnel_level_merchant_page?oaId=${widget.oaId}&sLevelNumber=$totalNum&isBd=1');
      }
    } else {
      CustomerSLevelModelData showModel =
          sLevelData ?? CustomerSLevelModelData();
      dynamic totalNum = showModel.potentialCustomers ?? 0;
      if (this.haveChild) {
        XYYContainer.open(
            '/funnel_potential_page?oaId=${widget.oaId}&potentialNumber=$totalNum');
      } else {
        XYYContainer.open(
            '/funnel_potential_merchant_page?oaId=${widget.oaId}&potentialNumber=$totalNum&isBd=1');
      }
    }
  }

  void refreshFunnelData() {
    this.requestFunnelIndexData();
    this.requestFunnelCumulativeData();
    this.requestCustomerData();
    this.requestSLevelData();
  }

  /// 客户大盘及顶部数据请求
  void requestFunnelIndexData() async {
    if (this.indexData == null) {
      showLoadingDialog();
    }
    var result =
        await NetworkV2<CustomerFunnelData>(CustomerFunnelData()).requestDataV2(
      'funnerV2/index',
      contentType: RequestContentType.JSON,
      method: RequestMethod.GET,
      parameters: {'oaId': widget.oaId ?? ""},
    );
    dismissLoadingDialog();
    this.requestSuccess = (result.isSuccess == true);
    if (result.isSuccess == true) {
      if (mounted) {
        CustomerFunnelData? source = result.getData();

        this.haveChild = source?.haveChild == 1;

        if (source != null) {
          this.indexData = source;
          setState(() {});
        }
      }
    }
  }

  /// 累计客户模块数据请求
  void requestFunnelCumulativeData() async {
    var result = await NetworkV2<CustomerFunnelIndexCumulativeData>(
            CustomerFunnelIndexCumulativeData())
        .requestDataV2(
      'funnerV2/indexAccumulateModular',
      contentType: RequestContentType.JSON,
      method: RequestMethod.GET,
      parameters: {'oaId': widget.oaId ?? ""},
    );
    this.requestSuccess = (result.isSuccess == true);
    if (result.isSuccess == true) {
      if (mounted) {
        CustomerFunnelIndexCumulativeData? source = result.getData();
        if (source != null) {
          this.cumulativeData = source;
          setState(() {});
        }
      }
    }
  }

  /// 动销客户模块数据请求
  void requestCustomerData() async {
    var result = await NetworkV2<CustomerFunnelIndexPurchaseData>(
            CustomerFunnelIndexPurchaseData())
        .requestDataV2(
      'funnerV2/indexPurchaseModular',
      contentType: RequestContentType.JSON,
      method: RequestMethod.GET,
      parameters: {'oaId': widget.oaId ?? ""},
    );
    this.requestSuccess = (result.isSuccess == true);
    if (result.isSuccess == true) {
      if (mounted) {
        CustomerFunnelIndexPurchaseData? source = result.getData();
        if (source != null) {
          this.purchaseData = source;
          setState(() {});
        }
      }
    }
  }

  /// S级潜力客户模块数据请求
  void requestSLevelData() async {
    var result =
        await NetworkV2<CustomerSLevelModelData>(CustomerSLevelModelData())
            .requestDataV2(
      'funnerV2/indexSLevelModular',
      contentType: RequestContentType.JSON,
      method: RequestMethod.GET,
      parameters: {'oaId': widget.oaId ?? ""},
    );
    this.requestSuccess = (result.isSuccess == true);
    if (result.isSuccess == true) {
      if (mounted) {
        CustomerSLevelModelData? source = result.getData();
        if (source != null) {
          this.sLevelData = source;
          setState(() {});
        }
      }
    }
  }
}
