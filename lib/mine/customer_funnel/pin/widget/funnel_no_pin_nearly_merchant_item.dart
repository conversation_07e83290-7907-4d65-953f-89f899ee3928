import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_content_span.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_merchant_base_item.dart';
import 'package:flutter/material.dart';

class FunnelNoPinNearlyMerchantItem extends FunnelMerchantBaseItem {
  FunnelNoPinNearlyMerchantItem({
    required String title,
    required String orderAmount,
    required String orderTime,
    bool isTop = false,
    bool isBottom = false,
    required VoidCallback clickAction,
  }) : super(
          title: title,
          content: Container(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                FunnelContentSpan(
                  title: '下单金额（最近一单）：',
                  content: orderAmount + "元",
                ),
                Si<PERSON><PERSON><PERSON>(height: 10),
                FunnelContentSpan(
                  title: '下单时间（最近）：',
                  content: orderTime,
                ),
              ],
            ),
          ),
          isFirst: isTop,
          isBottom: isBottom,
          clickAction: clickAction,
        );
}
