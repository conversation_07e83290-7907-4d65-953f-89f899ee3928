import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_content_span.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_merchant_base_item.dart';
import 'package:flutter/material.dart';

class FunnelNoPinMerchantItem extends FunnelMerchantBaseItem {
  FunnelNoPinMerchantItem({
    required String title,
    required String orderAmount,
    required String orderNum,
    required String orderTime,
    required String level,
    bool isTop = false,
    bool isBottom = false,
    required VoidCallback clickAction,
  }) : super(
          title: title,
          content: Container(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 20,
                  width: 63,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: const Color(0xffF2FFFA),
                      border:
                      Border.all(color: const Color(0xff00b377), width: 0.5)),
                  child: Text(
                    level,
                    style: TextStyle(
                        color: const Color(0xff00b377),
                        fontSize: 12,
                        fontWeight: FontWeight.normal),
                  ),
                ),
                SizedBox(
                  height: 10,
                ),
                FunnelContentSpan(
                  title: '下单金额（最近3月内）：',
                  content: orderAmount + "元",
                ),
                SizedBox(height: 10),
                FunnelContentSpan(
                  title: '下单数量（最近3月内）：',
                  content: orderNum + "单",
                ),
                SizedBox(height: 10),
                FunnelContentSpan(
                  title: '下单时间（最近）：',
                  content: orderTime,
                ),
              ],
            ),
          ),
          isFirst: isTop,
          isBottom: isBottom,
          clickAction: clickAction,
        );
}
