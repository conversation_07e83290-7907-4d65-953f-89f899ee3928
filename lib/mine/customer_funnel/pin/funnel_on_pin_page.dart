import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/pin/data/funnel_pin_model.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/pin/widget/funnel_no_pin_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_row_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class FunnelNoPinPage extends BasePage {
  final dynamic oaId;
  final dynamic deptCode;
  final dynamic noMoveSaleTotalCustNum;

  FunnelNoPinPage({
    this.oaId,
    this.deptCode,
    this.noMoveSaleTotalCustNum,
  });

  @override
  BaseState<StatefulWidget> initState() {
    return FunnelNoPinPageState();
  }
}

class FunnelNoPinPageState extends BaseState<FunnelNoPinPage> {
  EasyRefreshController _controller = EasyRefreshController();

  List<FunnelPinSalesItemModel> dataSource = [];

  @override
  void dispose() {
    this._controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    this.refreshData();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF1F1F1),
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.all(10),
            child: FunnelRowItem(
              title: "未动销客户数：",
              content: "${widget.noMoveSaleTotalCustNum ?? 0}",
              unit: "家",
            ),
          ),
          Expanded(
            child: EasyRefresh(
              controller: _controller,
              onRefresh: () async {
                this.refreshData();
              },
              child: ListView.builder(
                itemCount: this.dataSource.length,
                itemBuilder: (context, index) {
                  FunnelPinSalesItemModel model = this.dataSource[index];
                  return FunnelNoPinItem(
                    title: '${model.deptAndUserName ?? "--"}',
                    noPinNum: '${model.totalCustNum ?? 0}',
                    noPinRate: '${model.movesaleRatio ?? 0}',
                    isTop: index == 0,
                    isBottom: index == this.dataSource.length - 1,
                    contentHidden: "${model.isPostDept}" == "1",
                    clickAction: () {
                      bool haveChild = "${model.haveChild}" == "1";
                      dynamic oaId = model.oaId;
                      dynamic isBd = model.isBd;
                      dynamic deptCode = model.deptCode;
                      dynamic totalCustNum = model.totalCustNum ?? 0;
                      if (haveChild) {
                        XYYContainer.open(
                            '/funnel_no_pin_page?oaId=$oaId&deptCode=$deptCode&noMoveSaleTotalCustNum=$totalCustNum');
                      } else {
                        XYYContainer.open(
                            '/funnel_no_pin_merchant_page?oaId=$oaId&isBd=$isBd&nonMoveSaleTotalCustNum=$totalCustNum');
                      }
                    },
                  );
                },
              ),
              emptyWidget: this.getEmptyWidget(),
            ),
          )
        ],
      ),
    );
  }

  Widget? getEmptyWidget() {
    if (this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  void refreshData() async {
    this.requestListData();
  }

  void requestListData() async {
    Map<String, dynamic> params = {"movesaleType": "0"};
    if (widget.oaId != null && widget.oaId != "null") {
      params["oaId"] = widget.oaId;
    }
    if (widget.deptCode != null && widget.deptCode != "null") {
      params["deptCode"] = widget.deptCode;
    }
    if (this.dataSource.length == 0) {
      this.showLoadingDialog();
    }
    var result =
        await NetworkV2<FunnelPinSalesData>(FunnelPinSalesData()).requestDataV2(
      'customerFunnel/movesaleCustomerCurrmonth',
      parameters: params,
      contentType: RequestContentType.JSON,
      method: RequestMethod.POST,
    );
    this.dismissLoadingDialog();
    this._controller.finishRefresh();
    if (result.isSuccess == true) {
      if (mounted) {
        FunnelPinSalesData? data = result.getData();
        if (data != null) {
          this.dataSource = data.crmUserList ?? [];
          setState(() {});
        }
      }
    }
  }

  @override
  String getTitleName() {
    return "本月未动销客户";
  }
}
