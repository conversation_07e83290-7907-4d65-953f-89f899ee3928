import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/pin/data/funnel_pin_model.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/pin/widget/funnel_pin_sort_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/utils/funnel_util.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_column_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class FunnelPinPage extends BasePage {
  final dynamic oaId;

  final dynamic deptCode;

  final dynamic moveSaleTotalCustNum;

  FunnelPinPage({
    this.oaId,
    this.deptCode,
    this.moveSaleTotalCustNum,
  });

  @override
  BaseState<StatefulWidget> initState() {
    return FunnelPinPageState();
  }
}

class FunnelPinPageState extends BaseState<FunnelPinPage> {
  EasyRefreshController _controller = EasyRefreshController();

  List<FunnelPinSalesItemModel> dataSource = [];

  dynamic movesaleRatio;
  dynamic nonMoveSaleTotalCustNum;

  @override
  void dispose() {
    this._controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    this.refreshData();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF1F1F1),
      child: Column(
        children: [
          this.getTopWidget(),
          Expanded(
            child: EasyRefresh(
              controller: _controller,
              onRefresh: () async {
                this.refreshData();
              },
              child: ListView.builder(
                itemCount: this.dataSource.length,
                itemBuilder: (context, index) {
                  FunnelPinSalesItemModel model = this.dataSource[index];
                  return Container(
                    child: FunnelPinSortItem(
                      title: '${model.deptAndUserName ?? "--"}',
                      index: index,
                      isBottom: index == this.dataSource.length - 1,
                      contentHidden: "${model.isPostDept}" == "1",
                      pinNum: "${model.totalCustNum ?? 0}",
                      pinRate: "${model.movesaleRatio ?? 0}",
                      orderNum: "${model.orderNum ?? 0}",
                      orderAmount:
                          FunnelUtil.amountFlex(model.totalOrderAmount),
                      clickAction: () {
                        bool haveChild = "${model.haveChild}" == "1";
                        dynamic oaId = model.oaId;
                        dynamic totalNum = model.totalCustNum ?? 0;
                        dynamic deptCode = model.deptCode;
                        if (haveChild) {
                          XYYContainer.open(
                              '/funnel_pin_page?oaId=$oaId&moveSaleTotalCustNum=$totalNum&deptCode=$deptCode');
                        } else {
                          dynamic isBd = model.isBd;
                          XYYContainer.open(
                              '/funnel_pin_merchant_page?oaId=$oaId&totalCustNum=$totalNum&isBd=$isBd');
                        }
                      },
                    ),
                  );
                },
              ),
              emptyWidget: this.getEmptyWidget(),
            ),
          ),
        ],
      ),
    );
  }

  Widget? getEmptyWidget() {
    if (this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  Widget getTopWidget() {
    double ratio = double.parse("${this.movesaleRatio ?? 0}");
    return Container(
      padding: EdgeInsets.all(10),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                flex: 1,
                child: FunnelColumnItem(
                  title: "动销客户数：",
                  content: "${widget.moveSaleTotalCustNum ?? 0}",
                  unit: "家",
                ),
              ),
              SizedBox(width: 10),
              Expanded(
                flex: 1,
                child: FunnelColumnItem(
                  title: "动销率:",
                  content: ratio.toStringAsFixed(2),
                  unit: "%",
                ),
              ),
            ],
          ),
          SizedBox(height: 10),
        ],
      ),
    );
  }

  void refreshData() async {
    this.requestListData();
  }

  void requestListData() async {
    Map<String, dynamic> params = {"movesaleType": "1"};
    if (widget.oaId != null && widget.oaId != "null") {
      params["oaId"] = widget.oaId;
    }
    if (widget.deptCode != null && widget.deptCode != "null") {
      params["deptCode"] = widget.deptCode;
    }
    if (this.dataSource.length == 0) {
      this.showLoadingDialog();
    }
    var result =
        await NetworkV2<FunnelPinSalesData>(FunnelPinSalesData()).requestDataV2(
      'customerFunnel/movesaleCustomerCurrmonth',
      parameters: params,
      contentType: RequestContentType.JSON,
      method: RequestMethod.POST,
    );
    this.dismissLoadingDialog();
    if (result.isSuccess == true) {
      if (mounted) {
        FunnelPinSalesData? data = result.getData();
        if (data != null) {
          this.nonMoveSaleTotalCustNum = data.nonMoveSaleTotalCustNum;
          this.movesaleRatio = data.movesaleRatio;
          this.dataSource = data.crmUserList ?? [];
          setState(() {});
        }
      }
    }
  }

  @override
  String getTitleName() {
    return "本月动销客户";
  }
}
