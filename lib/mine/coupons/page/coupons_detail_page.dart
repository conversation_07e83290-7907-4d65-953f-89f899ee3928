import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/coupons/data/coupons_detail_data.dart';
import 'package:XyyBeanSproutsFlutter/mine/coupons/data/coupons_list_data.dart';
import 'package:XyyBeanSproutsFlutter/mine/coupons/widget/coupons_detail_header.dart';
import 'package:XyyBeanSproutsFlutter/mine/coupons/widget/coupons_detail_merchant_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/coupons/widget/coupons_list_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/jump_page_utils.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
// ignore: implementation_imports
import 'package:flutter_easyrefresh/src/widget/empty_widget.dart';

class CouponsDetailPage extends BasePage {
  final dynamic couponId;
  final dynamic searchUserId;

  CouponsDetailPage({
    this.couponId,
    this.searchUserId,
  });

  @override
  BaseState<StatefulWidget> initState() {
    return CouponsDetailState();
  }
}

class CouponsDetailState extends BaseState<CouponsDetailPage> {
  CouponsDetailHeaderModel? headerModel;

  int page = 1;
  dynamic type = 0;

  bool isLastPage = true;

  List<CouponsDetailMerchantData> dataSource = [];

  EasyRefreshController _controller = EasyRefreshController();

  @override
  void initState() {
    this.refreshList();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF7F7F8),
      child: EasyRefresh(
        onRefresh: refreshList,
        onLoad: this.isLastPage ? null : loadMoreList,
        controller: _controller,
        child: CustomScrollView(
          slivers: [
            this.getCouponsItem(),
            this.getHeaderWidget(),
            this.getListWidget(),
          ],
        ),
      ),
    );
  }

  Widget getCouponsItem() {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          return Container(
            color: Color(0xFFF7F7F8),
            padding: EdgeInsets.only(bottom: 10),
            child: CouponsListItem.detail(
              model: this.headerModel?.couponVo ?? CouponsListDataModel(),
            ),
          );
        },
        childCount: 1,
      ),
    );
  }

  Widget getHeaderWidget() {
    return CouponsDetailHeader(
      titles: [
        "未领取" + "(${this.headerModel?.unclaimedCount ?? '-'})",
        "未使用" + "(${this.headerModel?.unusedCount ?? '-'})",
        "已使用" + "(${this.headerModel?.usedCount ?? '-'})",
      ],
      typeChanged: (type) async {
        this.type = type;
        switch (type) {
          case 0:
            track('mc-mine-couponUnclaimed');
            break;
          case 1:
            track("mc-mine-couponUnused");
            break;
          case 2:
            track("mc-mine-couponUsed");
            break;
          default:
        }
        this.page = 1;
        showLoadingDialog();
        await requestList();
        dismissLoadingDialog();
      },
    );
  }

  Widget getListWidget() {
    if (this.dataSource.length == 0) {
      return EmptyWidget(child: PageStateWidget(state: PageState.Empty));
    }
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          var model = this.dataSource[index];
          return GestureDetector(
            onTap: () {
              jumpCustomerPageByCustomerId(
                model.customerId,
                canJumpPublic: false,
              );
            },
            behavior: HitTestBehavior.translucent,
            child: CouponsDetailMerchantItem(
              model: model,
            ),
          );
        },
        childCount: this.dataSource.length,
      ),
    );
  }

  Future<void> refreshList() async {
    this.page = 1;
    showLoadingDialog();
    await Future.wait([requestList(), requestHeaderData()]);
    dismissLoadingDialog();
    return;
  }

  Future<void> loadMoreList() async {
    await requestList();
    return;
  }

  Future<void> requestList() async {
    Map<String, dynamic> params = {
      'offset': this.page,
      'type': this.type,
      'limit': 10,
      'couponId': widget.couponId,
    };
    if (widget.searchUserId != null) {
      params['searchUserId'] = widget.searchUserId;
    }
    var result =
        await NetworkV2<CouponsDetailPageModel>(CouponsDetailPageModel())
            .requestDataV2(
      'coupon/merchantList',
      parameters: params,
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    if (mounted) {
      if (result.isSuccess == true) {
        var data = result.getData();
        if (data != null) {
          if (this.page == 1) {
            this.dataSource = data.list ?? [];
          } else {
            this.dataSource.addAll(data.list ?? []);
          }
          this.page += 1;
          this.isLastPage = data.isLastPage == true;
          setState(() {});
        }
      }
    }
    return;
  }

  Future<void> requestHeaderData() async {
    print("guan requestHeaderData");
    Map<String, dynamic> params = {
      'couponId': widget.couponId,
    };
    if (widget.searchUserId != null) {
      params['searchUserId'] = widget.searchUserId;
    }
    var result =
        await NetworkV2<CouponsDetailHeaderModel>(CouponsDetailHeaderModel())
            .requestDataV2(
      'coupon/detail',
      parameters: params,
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );

    if (mounted) {
      if (result.isSuccess == true) {
        this.headerModel = result.getData();
        setState(() {});
      }
    }
    return;
  }

  @override
  String getTitleName() {
    return "优惠券详情";
  }
}
