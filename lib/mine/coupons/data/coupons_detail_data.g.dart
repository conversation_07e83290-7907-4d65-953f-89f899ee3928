// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'coupons_detail_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CouponsDetailHeaderModel _$CouponsDetailHeaderModelFromJson(
    Map<String, dynamic> json) {
  return CouponsDetailHeaderModel()
    ..unclaimedCount = json['unclaimedCount']
    ..unusedCount = json['unusedCount']
    ..usedCount = json['usedCount']
    ..couponVo = json['couponVo'] == null
        ? null
        : CouponsListDataModel.fromJson(
            json['couponVo'] as Map<String, dynamic>);
}

Map<String, dynamic> _$CouponsDetailHeaderModelToJson(
        CouponsDetailHeaderModel instance) =>
    <String, dynamic>{
      'unclaimedCount': instance.unclaimedCount,
      'unusedCount': instance.unusedCount,
      'usedCount': instance.usedCount,
      'couponVo': instance.couponVo,
    };

CouponsDetailPageModel _$CouponsDetailPageModelFromJson(
    Map<String, dynamic> json) {
  return CouponsDetailPageModel()
    ..isLastPage = json['isLastPage']
    ..list = (json['list'] as List<dynamic>?)
        ?.map((e) =>
            CouponsDetailMerchantData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$CouponsDetailPageModelToJson(
        CouponsDetailPageModel instance) =>
    <String, dynamic>{
      'isLastPage': instance.isLastPage,
      'list': instance.list,
    };

CouponsDetailMerchantData _$CouponsDetailMerchantDataFromJson(
    Map<String, dynamic> json) {
  return CouponsDetailMerchantData()
    ..merchantId = json['merchantId']
    ..customerId = json['customerId']
    ..merchantName = json['merchantName']
    ..merchantAddress = json['merchantAddress'];
}

Map<String, dynamic> _$CouponsDetailMerchantDataToJson(
        CouponsDetailMerchantData instance) =>
    <String, dynamic>{
      'merchantId': instance.merchantId,
      'customerId': instance.customerId,
      'merchantName': instance.merchantName,
      'merchantAddress': instance.merchantAddress,
    };
