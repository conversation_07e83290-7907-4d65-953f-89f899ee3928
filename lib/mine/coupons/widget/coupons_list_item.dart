import 'package:XyyBeanSproutsFlutter/mine/coupons/data/coupons_list_data.dart';
import 'package:flutter/material.dart';

class CouponsListItem extends StatelessWidget {
  final bool _isDetail;

  final Color _backgroundColor;

  final CouponsListDataModel model;

  final VoidCallback? clickAction;

  final bool _isDisable;

  CouponsListItem({required this.model, this.clickAction})
      : _isDetail = false,
        _backgroundColor = Color(0xFFFAE4E4),
        _isDisable = false;

  CouponsListItem.detail({required this.model})
      : _isDetail = true,
        clickAction = null,
        _backgroundColor = Color(0xFFFAE4E4),
        _isDisable = false;

  CouponsListItem.gary({required this.model})
      : _isDetail = true,
        clickAction = null,
        _backgroundColor = Color(0xFFE5E5E5),
        _isDisable = true;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
      color: Color(0xFFF7F7F8),
      child: Container(
        child: IntrinsicHeight(
          child: Row(
            children: [
              this.leftWidget(),
              this.rightWidget(),
            ],
          ),
        ),
      ),
    );
  }

  Widget leftWidget() {
    return CustomPaint(
      painter: _CouponsListBackgroundPainter(
          isLeft: true, fillColor: _backgroundColor),
      child: Container(
        padding: EdgeInsets.only(left: 10, right: 10),
        constraints: BoxConstraints(
          minHeight: 110,
          minWidth: 110,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.max,
          children: [
            this.getDiscountWidget(),
            Visibility(
              visible: this.model.couponCondition != null,
              child: Text(
                '${this.model.couponCondition ?? ''}',
                style: TextStyle(
                  color: this._getPriceColor(),
                  fontSize: 12,
                ),
              ),
            ),
            Visibility(
              visible: this.model.maxSubValue != null,
              child: Text(
                '${this.model.maxSubValue ?? ''}',
                style: TextStyle(
                  color: Color(0xFF676773),
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget getDiscountWidget() {
    return RichText(
      text: TextSpan(
        text: '',
        children: [
          WidgetSpan(
            child: Visibility(
              visible: this.model.isPriceCoupons,
              child: Text(
                "¥",
                style: TextStyle(
                  color: this._getPriceColor(),
                  fontSize: 15,
                ),
              ),
            ),
          ),
          TextSpan(
            text: this.model.faceValue,
            style: TextStyle(
              color: this._getPriceColor(),
              fontSize: 32,
              fontWeight: FontWeight.w500,
            ),
          ),
          WidgetSpan(
            child: Visibility(
              visible: !this.model.isPriceCoupons,
              child: Text(
                "折",
                style: TextStyle(
                  color: this._getPriceColor(),
                  fontSize: 15,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getPriceColor() {
    if (this._isDisable) {
      return Color(0xFF9793A5);
    }
    return Color(0xFFFF2121);
  }

  Widget rightWidget() {
    return Expanded(
      child: CustomPaint(
        painter: _CouponsListBackgroundPainter(
            isLeft: false, fillColor: Color(0xFFFFFFFF)),
        child: Container(
          padding: EdgeInsets.all(10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              this.getTitleWidget(),
              SizedBox(height: 5),
              this.getContentDesc(),
              SizedBox(height: 8),
              Divider(height: 1, thickness: 0.5, color: Color(0xFFEEEEEE)),
              this.getDateWidget(),
            ],
          ),
        ),
      ),
    );
  }

  Widget getTitleWidget() {
    return RichText(
      text: TextSpan(
        children: [
          WidgetSpan(
            child: _CouponsIconWidget(
              couponTypeId: this.model.couponTypeId,
              couponTypeName: "${this.model.couponTypeDesc}",
              isDisable: this._isDisable,
            ),
          ),
          WidgetSpan(child: SizedBox(width: 5)),
          TextSpan(
            text: "${this.model.couponName ?? '--'}",
            style: TextStyle(
              color: this._isDisable ? Color(0xFFA09EA9) : Color(0xFF292933),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          )
        ],
      ),
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
    );
  }

  Widget getContentDesc() {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${this.model.couponDocument ?? ''}',
                style: TextStyle(color: Color(0xFF292933), fontSize: 14),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
              SizedBox(height: 5),
              Text(
                '${this.model.couponRange ?? ''}',
                style: TextStyle(color: Color(0xFF676773), fontSize: 12),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ],
          ),
        ),
        Visibility(visible: !_isDetail, child: SizedBox(width: 15)),
        Visibility(
          visible: !_isDetail,
          child: TextButton(
            onPressed: this.clickAction,
            child: Container(
              width: 74,
              height: 28,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: [Color(0xFFFF6025), Color(0xFFFF4244)],
                ),
                borderRadius: BorderRadius.circular(28),
              ),
              child: Text(
                '查看',
                style: TextStyle(color: Color(0xFFFFFFFF), fontSize: 12),
              ),
            ),
            style: ButtonStyle(
              overlayColor:
                  MaterialStateProperty.all<Color>(Colors.transparent),
              padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.zero),
              minimumSize: MaterialStateProperty.all<Size>(Size.zero),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ),
        ),
      ],
    );
  }

  Widget getDateWidget() {
    return Container(
      padding: EdgeInsets.only(top: 10),
      alignment: Alignment.bottomLeft,
      child: Text(
        '${this.model.couponDate ?? '--'}',
        style: TextStyle(color: Color(0xFF676773), fontSize: 12),
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
    );
  }
}

class _CouponsIconWidget extends StatelessWidget {
  _CouponsIconWidget({
    this.couponTypeId,
    this.couponTypeName,
    this.isDisable = false,
  });

  final dynamic couponTypeId;
  final dynamic couponTypeName;

  final bool isDisable;

  Color getBackgroundColor() {
    if (this.isDisable) {
      return Color(0xFFE6E5E5);
    }
    switch (this.couponTypeId) {
      case 1:
      case 6:
        return Color(0xFFFFF1E7);
      case 2:
        return Color(0xFFF0F4FF);
      case 3:
        return Color(0xFFFFF1E7);
      case 4:
        return Color(0xFFFFEEF2);
      case 5:
      case 8:
        return Color(0xFFFFE7E7);
      case 7:
        return Color(0xFFF0FAF6);
      default:
        return Color(0xFFFFE7E7);
    }
  }

  Color getBorderColor() {
    if (this.isDisable) {
      return Color(0xFFE6E5E5);
    }
    switch (this.couponTypeId) {
      case 1:
      case 6:
        return Color(0xFFFFCEAB);
      case 2:
        return Color(0xFFD0DDFF);
      case 3:
        return Color(0xFFFFCEAB);
      case 4:
        return Color(0xFFFFBCCA);
      case 5:
      case 8:
        return Color(0xFFFFABAB);
      case 7:
        return Color(0xFFA6DFC2);
      default:
        return Color(0xFFFFE7E7);
    }
  }

  Color getTextColor() {
    if (this.isDisable) {
      return Color(0xFFFFFFFF);
    }
    switch (this.couponTypeId) {
      case 1:
      case 6:
        return Color(0xFFFE6A04);
      case 2:
        return Color(0xFF5276FF);
      case 3:
        return Color(0xFFFE6A04);
      case 4:
        return Color(0xFFFF527A);
      case 5:
      case 8:
        return Color(0xFFFE0707);
      case 7:
        return Color(0xFF00BB5A);
      default:
        return Color(0xFFFFE7E7);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(5, 1, 5, 1),
      decoration: BoxDecoration(
        color: this.getBackgroundColor(),
        border: Border.all(color: this.getBorderColor(), width: 0.5),
        borderRadius: BorderRadius.circular(18),
      ),
      child: Text(
        "${this.couponTypeName ?? '-'}",
        style: TextStyle(
          color: this.getTextColor(),
          fontSize: 11,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}

class _CouponsListBackgroundPainter extends CustomPainter {
  _CouponsListBackgroundPainter({
    this.isLeft = true,
    this.fillColor = const Color(0xFFFAE4E4),
    this.raduis = 5.0,
  });

  final bool isLeft;
  final Color fillColor;
  final double raduis;

  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint();
    paint.color = this.fillColor;
    paint.strokeWidth = 0;
    paint.style = PaintingStyle.fill;

    // 路径
    var path = Path();
    path.moveTo(0, raduis);
    // 左上角
    if (this.isLeft) {
      path.quadraticBezierTo(0, 0, raduis, 0);
    } else {
      path.quadraticBezierTo(raduis, raduis, raduis, 0);
    }

    path.lineTo(size.width - raduis, 0);
    // 右上角
    if (this.isLeft) {
      path.quadraticBezierTo(size.width - raduis, raduis, size.width, raduis);
    } else {
      path.quadraticBezierTo(size.width, 0, size.width, raduis);
    }

    path.lineTo(size.width, size.height - raduis);
    // 右下角
    if (this.isLeft) {
      path.quadraticBezierTo(size.width - raduis, size.height - raduis,
          size.width - raduis, size.height);
    } else {
      path.quadraticBezierTo(
          size.width, size.height, size.width - raduis, size.height);
    }

    path.lineTo(raduis, size.height);
    // 左下角
    if (this.isLeft) {
      path.quadraticBezierTo(0, size.height, 0, size.height - raduis);
    } else {
      path.quadraticBezierTo(
          raduis, size.height - raduis, 0, size.height - raduis);
    }

    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
