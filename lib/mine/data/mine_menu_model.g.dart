// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mine_menu_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MineMenuModel _$MineMenuModelFromJson(Map<String, dynamic> json) {
  return MineMenuModel()
    ..name = json['name']
    ..icon = json['icon']
    ..actionType = json['actionType']
    ..jumpUrl = json['jumpUrl']
    ..platform = json['platform']
    ..sectionId = json['sectionId']
    ..typeID = json['typeID']
    ..image = json['image'];
}

Map<String, dynamic> _$MineMenuModelToJson(MineMenuModel instance) =>
    <String, dynamic>{
      'name': instance.name,
      'icon': instance.icon,
      'actionType': instance.actionType,
      'jumpUrl': instance.jumpUrl,
      'platform': instance.platform,
      'sectionId': instance.sectionId,
      'typeID': instance.typeID,
      'image': instance.image,
    };

MineMenuParentModel _$MineMenuParentModelFromJson(Map<String, dynamic> json) {
  return MineMenuParentModel()
    ..rows = (json['rows'] as List<dynamic>?)
        ?.map((e) => MineMenuModel.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$MineMenuParentModelToJson(
        MineMenuParentModel instance) =>
    <String, dynamic>{
      'rows': instance.rows,
    };
