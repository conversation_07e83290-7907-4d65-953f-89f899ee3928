import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/button/background_state_button.dart';
import 'package:XyyBeanSproutsFlutter/mine/pop_statistics/data/pop_statistics_data.dart';
import 'package:XyyBeanSproutsFlutter/mine/pop_statistics/widget/pop_data_statistics_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class PopMerchantStatistics extends BasePage {
  final String merchantName;
  final dynamic merchantId;

  PopMerchantStatistics({
    required this.merchantName,
    required this.merchantId,
  });

  @override
  BaseState<StatefulWidget> initState() {
    return PopMerchantStatisticsState();
  }
}

class PopMerchantStatisticsState extends BaseState<PopMerchantStatistics> {
  List<String> filterTitles = ['昨天', '本月', '上月'];

  ValueNotifier<BackgroundButtonState>? controller =
      ValueNotifier(BackgroundButtonState.selected);

  PopStatisticsIndexModel? sourceData;

  bool requestSuccess = true;

  dynamic period = "6";

  EasyRefreshController _refreshController = EasyRefreshController();

  @override
  void initState() {
    this.refreshData();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    int itemCount = (this.sourceData?.dataStatisticsResult?.length ?? 0) + 1;
    return Container(
      color: Color(0xFFFFFFFF),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.only(top: 10, bottom: 10, left: 10),
            child: Wrap(
              runSpacing: 10,
              spacing: 10,
              runAlignment: WrapAlignment.start,
              alignment: WrapAlignment.start,
              children: getFilterItem(),
            ),
          ),
          Expanded(
            child: Container(
              color: Color(0xFFF7F7F8),
              child: EasyRefresh(
                onRefresh: () async {
                  await this.refreshData();
                },
                controller: _refreshController,
                child: ListView.builder(
                  itemCount: itemCount,
                  itemBuilder: (context, index) {
                    // if (index == 2) {
                    //   return PopMerchantStatisticsTimeOutItem();
                    // }
                    // if (index == 3) {
                    //   return PopMerchantStatisticsCustomerNumberItem();
                    // }
                    if (index == itemCount - 1) {
                      return Container(
                        margin: EdgeInsets.fromLTRB(15, 10, 15, 15),
                        child: PopDataStatisticsRowItem(
                          title: "店铺SKU数：",
                          content: "${this.sourceData?.skuNum ?? 0}",
                        ),
                      );
                    }
                    return PopDataStatisticsItem(
                      data: this.sourceData?.dataStatisticsResult?[index] ?? [],
                    );
                  },
                ),
                emptyWidget: getEmptyWidget(),
              ),
            ),
          )
        ],
      ),
    );
  }

  List<Widget> getFilterItem() {
    int index = 0;
    return this.filterTitles.map((e) {
      dynamic option = 1;
      switch (index) {
        case 0:
          option = 6;
          break;
        case 1:
          option = 3;
          break;
        case 2:
          option = 8;
          break;
        default:
      }
      Widget button = Container(
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(2)),
        clipBehavior: Clip.hardEdge,
        child: BackgroundStateButton(
          title: e,
          controller: index == 0 ? this.controller : null,
          panding: EdgeInsets.fromLTRB(17, 6, 17, 6),
          option: option,
          onPressed: (controller, option) {
            if (this.controller != controller) {
              this.controller?.value = BackgroundButtonState.normal;
              this.controller = controller;
              this.period = option;
              this._refreshController.callRefresh();
            }
          },
        ),
      );
      index += 1;
      return button;
    }).toList();
  }

  Widget? getEmptyWidget() {
    if (!this.requestSuccess && this.sourceData == null) {
      return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
        this.refreshData();
      });
    }
    if (this.sourceData == null) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  @override
  String getTitleName() {
    return widget.merchantName;
  }

  /// Request
  Future<void> refreshData() async {
    if (this.sourceData == null) {
      showLoadingDialog();
    }
    var result =
        await NetworkV2<PopStatisticsIndexModel>(PopStatisticsIndexModel())
            .requestDataV2(
      'pop/shopDetail',
      parameters: {'period': this.period, 'shopCode': widget.merchantId},
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    dismissLoadingDialog();
    if (mounted) {
      _refreshController.finishRefresh();
      this.requestSuccess = result.isSuccess == true;
      if (result.isSuccess == true) {
        PopStatisticsIndexModel? source = result.getData();
        if (source != null) {
          this.sourceData = source;
        }
      }
      setState(() {});
    }
  }
}
