import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_appbar_search.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/mine/pop_statistics/widget/pop_data_merchant_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/pop_statistics/data/pop_statistics_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class PopDataMerchantList extends BasePage {
  final String? isSearch;

  final dynamic period;

  final dynamic groupId;

  final dynamic searchUserId;

  PopDataMerchantList(
      {this.isSearch = "0", this.period, this.groupId, this.searchUserId});

  @override
  BaseState<StatefulWidget> initState() {
    return PopDataMerchantListState();
  }
}

class PopDataMerchantListState extends BaseState<PopDataMerchantList> {
  EasyRefreshController _easyRefreshController = EasyRefreshController();

  String? keyWord;

  int page = 1;

  bool requestSuccess = true;

  List<PopDataMerchantListItemData> dataSource = [];

  bool isLastPage = true;

  @override
  void dispose() {
    _easyRefreshController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    this.refreshListData();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (!FocusScope.of(context).hasPrimaryFocus &&
            FocusScope.of(context).focusedChild != null) {
          FocusManager.instance.primaryFocus?.unfocus();
        }
      },
      onPanDown: (_) {
        if (!FocusScope.of(context).hasPrimaryFocus &&
            FocusScope.of(context).focusedChild != null) {
          FocusManager.instance.primaryFocus?.unfocus();
        }
      },
      behavior: HitTestBehavior.translucent,
      child: Container(
        color: Color(0xFFF7F7F8),
        child: EasyRefresh(
          onRefresh: () async {
            await this.refreshListData();
          },
          onLoad: this.isLastPage ? null : loadMoreListData,
          child: ListView.builder(
            itemCount: this.dataSource.length,
            itemBuilder: (context, index) {
              return GestureDetector(
                onTap: () {
                  PopDataMerchantListItemData model = this.dataSource[index];
                  var merchantName = Uri.encodeFull("${model.shopName}");
                  var merchantId = model.shopCode;
                  XYYContainer.open(
                      '/pop_merchant_statistics?merchantName=$merchantName&merchantId=$merchantId');
                },
                behavior: HitTestBehavior.opaque,
                child: PopDataMerchantItem(
                  data: this.dataSource[index],
                ),
              );
            },
          ),
          emptyWidget: getEmptyWidget(),
        ),
      ),
    );
  }

  Widget? getEmptyWidget() {
    if (!this.requestSuccess && this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
        this.refreshListData();
      });
    }
    if (this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  Future<void> refreshListData() async {
    this.page = 1;
    await this.requestMerchantListData();
  }

  Future<void> loadMoreListData() async {
    await this.requestMerchantListData();
  }

  Future<void> requestMerchantListData() async {
    if (this.dataSource.length == 0) {
      showLoadingDialog();
    }

    var params= {
      'period': widget.period,
      'pageNum': this.page,
      'pageSize': 10
    };

    if (widget.groupId != null) {
      // 组织结构
      params['groupId'] = widget.groupId;
    } else if (widget.searchUserId != null) {
      // 个人
      params['searchUserId'] = widget.searchUserId;
    }

    var result =
        await NetworkV2<PopDataMerchantListData>(PopDataMerchantListData())
            .requestDataV2(
      'pop/shopList',
      parameters: params,
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    dismissLoadingDialog();
    if (mounted) {
      this._easyRefreshController.finishRefresh();
      this._easyRefreshController.finishLoad();
      this.requestSuccess = result.isSuccess == true;
      if (result.isSuccess == true) {
        PopDataMerchantListData? source = result.getData();
        if (source != null) {
          if (this.page == 1) {
            this.dataSource = source.list ?? [];
          } else {
            this.dataSource.addAll(source.list ?? []);
          }
          this.page += 1;
          this.isLastPage = source.isLastPage == true;
        }
      }
      setState(() {});
    }
  }

  @override
  String getTitleName() {
    return "店铺列表";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    if (widget.isSearch == "1") {
      return SAppBarSearch(
        hintText: "请输入客户名称",
        showLeading: false,
        hideCancel: false,
        autoFocus: true,
        onSearch: (value) {
          if (value.isEmpty) {
            showToast("请输入搜索内容");
            return;
          }
          if (value is String) {
            this.keyWord = value;
            this._easyRefreshController.callRefresh();
          }
        },
      );
    }
    return CommonTitleBar(
      getTitleName(),
      // rightButtons: [
      //   TextButton(
      //     onPressed: () {
      //       XYYContainer.open(
      //           '/pop_data_merchant_list?isSearch=1&period=${widget.period}');
      //     },
      //     child: Container(
      //       // margin: EdgeInsets.only(right: 15),
      //       child: Image.asset(
      //         'assets/images/pop_data/pop_data_search.png',
      //         width: 21,
      //         height: 21,
      //       ),
      //     ),
      //     style: ButtonStyle(
      //       overlayColor: MaterialStateProperty.all<Color>(Colors.transparent),
      //       padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.all(10)),
      //       tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      //       minimumSize: MaterialStateProperty.all<Size>(Size.zero),
      //     ),
      //   )
      // ],
    );
  }
}
