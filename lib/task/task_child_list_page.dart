import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/task/data/task_child_list_data.dart';
import 'package:XyyBeanSproutsFlutter/task/widget/tab_custom_indicator.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/time_util.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';

class TaskChildListPage extends BasePage {
  final String mainTaskId;

  TaskChildListPage(this.mainTaskId);

  @override
  BaseState<StatefulWidget> initState() {
    return TaskChildListPageState();
  }
}

const CHILD_TASK_STATUS = <ChildTaskStatus>[
  ChildTaskStatus("全部", -1),
  ChildTaskStatus("已执行", 1),
  ChildTaskStatus("未执行", 0),
];

class ChildTaskStatus {
  final String text;
  final int id;

  const ChildTaskStatus(this.text, this.id);
}

class TaskChildListPageState extends BaseState<TaskChildListPage>
    with SingleTickerProviderStateMixin {
  List<EasyRefreshController> _refreshControllerList =
      <EasyRefreshController>[];
  List<ScrollController> _scrollControllerList = <ScrollController>[];
  List<dynamic> _childTaskModelList = <dynamic>[];

  TitleBarModel _titleBarModel = TitleBarModel();

  TabController? _tabController;

  var providerList = <ChangeNotifierProvider<dynamic>>[];

  bool hasInit = false;

  @override
  void onCreate() {
    _titleBarModel.checkUserLevel(this);
    _tabController =
        TabController(length: CHILD_TASK_STATUS.length, vsync: this);
    _tabController!.addListener(() {
      print(
          "guan:index:${_tabController!.index.toDouble()},animIndex：${_tabController!.animation!.value}");
      if (_tabController!.index.toDouble() ==
          _tabController!.animation!.value) {
        // tab更新
        print("guan: request item ${_tabController!.index.toDouble()}");
        _childTaskModelList[_tabController!.index]?.requestTaskList(
            true,
            _titleBarModel.isManagerLevel,
            _titleBarModel.id,
            _titleBarModel.isGroup);
      }
    });
    providerList.add(ChangeNotifierProvider<TitleBarModel>(
        create: (context) => _titleBarModel));

    for (int i = 0; i < CHILD_TASK_STATUS.length; i++) {
      var easyRefreshController = EasyRefreshController();
      var scrollController = ScrollController();
      _refreshControllerList.add(easyRefreshController);
      _scrollControllerList.add(scrollController);
      switch (i) {
        case 0:
          var childTaskListModel1 = ChildTaskListModel1(
              widget.mainTaskId,
              CHILD_TASK_STATUS[i],
              easyRefreshController,
              scrollController,
              this);
          _childTaskModelList.add(childTaskListModel1);
          providerList.add(ChangeNotifierProvider<ChildTaskListModel1>(
              create: (context) => childTaskListModel1));
          break;
        case 1:
          var childTaskListModel2 = ChildTaskListModel2(
              widget.mainTaskId,
              CHILD_TASK_STATUS[i],
              easyRefreshController,
              scrollController,
              this);
          _childTaskModelList.add(childTaskListModel2);
          providerList.add(ChangeNotifierProvider<ChildTaskListModel2>(
              create: (context) => childTaskListModel2));
          break;
        case 2:
          var childTaskListModel3 = ChildTaskListModel3(
              widget.mainTaskId,
              CHILD_TASK_STATUS[i],
              easyRefreshController,
              scrollController,
              this);
          _childTaskModelList.add(childTaskListModel3);
          providerList.add(ChangeNotifierProvider<ChildTaskListModel3>(
              create: (context) => childTaskListModel3));
          break;
      }
    }

    super.onCreate();
  }

  @override
  List<SingleChildWidget> getProvider() {
    return providerList;
  }

  @override
  Widget buildWidget(BuildContext context) {
    if (_titleBarModel.isManagerLevel == null) {
      return Container();
    }
    if (!hasInit) {
      (_childTaskModelList[0] as ChildTaskListModel).requestTaskList(
          true,
          _titleBarModel.isManagerLevel,
          _titleBarModel.id,
          _titleBarModel.isGroup);
      hasInit = true;
    }
    return Container(
      color: const Color(0xFFF7F7F8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Divider(
            height: 1,
            color: const Color(0xFFf0f0f2),
          ),
          buildFilterBarWidget(),
          buildListWidget(),
        ],
      ),
    );
  }

  @override
  PreferredSizeWidget getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      rightButtons: _titleBarModel.isBDShow() ? [buildSearchButton()] : [],
    );
  }

  @override
  String getTitleName() {
    return "子任务列表";
  }

  Widget buildSearchButton() {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pushNamed("/task_child_search_page",
            arguments: {"mainTaskId": widget.mainTaskId});
      },
      child: Container(
        width: 52,
        height: 44,
        alignment: Alignment.center,
        child: Image.asset(
          "assets/images/task/task_child_title_search.png",
          width: 22,
          height: 22,
        ),
      ),
    );
  }

  Widget buildFilterBarWidget() {
    return Consumer<TitleBarModel>(builder: (context, model, child) {
      var childrenWidgets = <Widget>[];
      print("guan:${_titleBarModel.isBDMShow()}");
      if (_titleBarModel.isBDMShow()) {
        childrenWidgets.add(buildTabBarWidget());
        childrenWidgets.add(Expanded(child: Container()));
        childrenWidgets.add(buildScopeWidget());
      } else {
        childrenWidgets.add(Expanded(child: buildTabBarWidget()));
      }
      return Container(
        height: 44,
        color: Colors.white,
        child: Row(
          children: childrenWidgets,
        ),
      );
    });
  }

  Widget buildScopeWidget() {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        XYYContainer.open(
            "xyy://crm-app.ybm100.com/executor?needSetResult=true",
            callback: (resultData) {
          if (resultData != null &&
              resultData is Map &&
              resultData.containsKey("id")) {
            setState(() {
              _titleBarModel.areaName = resultData["name"]?.toString() ?? "执行人";
              _titleBarModel.id = resultData["id"]?.toString() ?? "";
              if (resultData["isgroup"]?.toString() == "true") {
                _titleBarModel.isGroup = true;
              } else {
                _titleBarModel.isGroup = false;
              }
              (_childTaskModelList[_tabController!.index] as ChildTaskListModel)
                  .requestTaskList(true, _titleBarModel.isManagerLevel,
                      _titleBarModel.id, _titleBarModel.isGroup);
            });
          }
        });
      },
      child: Container(
        padding: EdgeInsets.only(right: 15),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              _titleBarModel.areaName,
              style: TextStyle(
                  color: const Color(0xFF676773),
                  fontSize: 14,
                  fontWeight: FontWeight.normal),
            ),
            SizedBox(
              width: 1,
            ),
            Image.asset(
              'assets/images/task/task_child_filter.png',
              width: 15,
              height: 15,
            )
          ],
        ),
      ),
    );
  }

  Widget buildListWidget() {
    return Expanded(
      child: Container(
        child: TabBarView(
          controller: _tabController,
          children: List.generate(CHILD_TASK_STATUS.length,
              (index) => buildTabViewItemWidget(index)),
        ),
      ),
    );
  }

  Widget buildTabItemWidget(ChildTaskStatus status) {
    return Container(
      height: 44,
      alignment: Alignment.center,
      padding: EdgeInsets.symmetric(horizontal: 15),
      child: Tab(
        text: status.text,
      ),
    );
  }

  TextStyle getStatusTextStyle(bool isSelected) {
    if (isSelected) {
      // 选中
      return TextStyle(
          color: Color(0xFF292933), fontSize: 14, fontWeight: FontWeight.w500);
    } else {
      // 未选中
      return TextStyle(
          color: Color(0xFF676773),
          fontSize: 14,
          fontWeight: FontWeight.normal);
    }
  }

  Widget buildTabBarWidget() {
    return TabBar(
        controller: _tabController,
        labelColor: Color(0xFF292933),
        labelStyle: getStatusTextStyle(true),
        unselectedLabelStyle: getStatusTextStyle(false),
        unselectedLabelColor: Color(0xFF676773),
        labelPadding: _titleBarModel.isBDMShow()
            ? EdgeInsets.only(right: 2.5, left: 2.5)
            : null,
        isScrollable: _titleBarModel.isBDMShow(),
        // indicatorSize: TabBarIndicatorSize.tab,
        indicator: TabCustomIndicator(
            wantWidth: 20,
            borderSide: const BorderSide(width: 3.0, color: Color(0xFF00B377))),
        tabs: List.generate(CHILD_TASK_STATUS.length,
            (index) => buildTabItemWidget(CHILD_TASK_STATUS[index])));
  }

  Widget buildTabViewItemWidget(int index) {
    var listModel = _childTaskModelList[index];
    switch (index) {
      case 0:
        return Consumer<ChildTaskListModel1>(builder: (context, model, child) {
          print("guan: build item1 $index");
          return buildListItemWidget(index, listModel);
        });
      case 1:
        return Consumer<ChildTaskListModel2>(builder: (context, model, child) {
          print("guan: build item2 $index");
          return buildListItemWidget(index, listModel);
        });
      case 2:
        return Consumer<ChildTaskListModel3>(builder: (context, model, child) {
          print("guan: build item3 $index");
          return buildListItemWidget(index, listModel);
        });
    }
    return Consumer<ChildTaskListModel>(builder: (context, model, child) {
      print("guan: build item $index");
      return buildListItemWidget(index, listModel);
    });
  }

  EasyRefresh buildListItemWidget(int index, ChildTaskListModel listModel) {
    return EasyRefresh.custom(
        controller: _refreshControllerList[index],
        scrollController: _scrollControllerList[index],
        enableControlFinishRefresh: true,
        enableControlFinishLoad: true,
        onRefresh: () async {
          listModel.requestTaskList(true, _titleBarModel.isManagerLevel,
              _titleBarModel.id, _titleBarModel.isGroup);
        },
        onLoad: !listModel.isLastPage!
            ? () async {
                listModel.requestTaskList(false, _titleBarModel.isManagerLevel,
                    _titleBarModel.id, _titleBarModel.isGroup);
              }
            : null,
        slivers: [
          SliverPadding(padding: EdgeInsets.only(top: 10)),
          SliverList(
            delegate:
                SliverChildBuilderDelegate((BuildContext context, int index) {
              //创建列表项
              return buildTaskItem(listModel.list![index], listModel);
            }, childCount: listModel.list?.length ?? 0),
          )
        ],
        emptyWidget: getEmptyWidget(listModel));
  }

  Widget buildTaskItem(
      TaskChildItemData itemData, ChildTaskListModel listModel) {
    if (itemData == null) {
      return Container();
    } else {
      var hasExecute = itemData.status == 1;
      return Container(
        margin: EdgeInsets.fromLTRB(15, 0, 15, 10),
        padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(2)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                    child: Text(
                  itemData.customerName ?? "",
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  style: TextStyle(
                      color: const Color(0xFF292933),
                      fontWeight: FontWeight.w500,
                      fontSize: 16),
                )),
                SizedBox(
                  width: 37,
                ),
                Text(
                  itemData.taskStatus!,
                  style: TextStyle(
                      color: hasExecute ? Color(0xFF9494A6) : Color(0xFFFF7200),
                      fontWeight: FontWeight.normal,
                      fontSize: 14),
                )
              ],
            ),
            buildInfoRowWidget("执行人", itemData.executor ?? "", null),
            Visibility(
              visible: hasExecute && itemData.scheduleId != null,
              child: Divider(
                height: 1,
                color: Color(0xFFF0F0F2),
              ),
            ),
            Visibility(
                visible: hasExecute && itemData.scheduleId != null,
                child: buildInfoRowWidget(
                    itemData.visitorType ?? "", itemData.visitorTime ?? "", () {
                  XYYContainer.open(
                      "/visit_detail_page?scheduleId=${itemData.scheduleId}");
                })),
            Visibility(
              visible: hasExecute,
              child: Divider(
                height: 1,
                color: Color(0xFFF0F0F2),
              ),
            ),
            Visibility(
              visible: hasExecute,
              child: buildInfoRowWidget("执行结果", "", () {
                Navigator.of(context).pushNamed("/task_child_result_page",
                    arguments: {"subTaskId": itemData.taskId?.toString()});
              }),
            ),
            Visibility(
                visible: itemData.doTask == 1,
                child: GestureDetector(
                  onTap: () {
                    Navigator.of(context).pushNamed("/task_dotask",
                        arguments: {"taskId": itemData.taskId}).then((value) {
                      if (value is Map) {
                        listModel.requestTaskList(
                            true,
                            _titleBarModel.isManagerLevel,
                            _titleBarModel.id,
                            _titleBarModel.isGroup);
                      }
                    });
                  },
                  child: Container(
                    width: double.infinity,
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(bottom: 10),
                    height: 34,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(2),
                        border:
                            Border.all(color: Color(0xFF00B377), width: 0.5)),
                    child: Text(
                      "做任务",
                      style: TextStyle(
                          color: Color(0xFF00B377),
                          fontSize: 14,
                          fontWeight: FontWeight.normal),
                    ),
                  ),
                ))
          ],
        ),
      );
    }
  }

  Widget? getEmptyWidget(ChildTaskListModel listModel) {
    if (listModel.isSuccess == null) {
      return null;
    }
    if (listModel.isSuccess == false || _titleBarModel.isManagerLevel == null) {
      return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
        listModel.requestTaskList(true, _titleBarModel.isManagerLevel,
            _titleBarModel.id, _titleBarModel.isGroup);
      });
    }

    if ((listModel.list?.length ?? 0) == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  buildInfoRowWidget(String label, String content, VoidCallback? onPress) {
    return GestureDetector(
      onTap: () {
        if (onPress != null) {
          onPress();
        }
      },
      child: Container(
          height: 44,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 66,
                alignment: Alignment.centerLeft,
                child: Text(
                  label,
                  style: TextStyle(
                      color: Color(0xFF9494A6),
                      fontSize: 14,
                      fontWeight: FontWeight.normal),
                  strutStyle: StrutStyle(leading: 0.2),
                ),
              ),
              SizedBox(
                width: 5,
              ),
              Expanded(
                  child: Text(
                content,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                    color: Color(0xFF676773),
                    fontSize: 14,
                    fontWeight: FontWeight.normal),
                strutStyle: StrutStyle(leading: 0.2),
              )),
              Visibility(
                  visible: onPress != null,
                  child: Image.asset(
                    "assets/images/task/task_child_item_arrow.png",
                    width: 15,
                    height: 15,
                  ))
            ],
          )),
    );
  }

  String formatTime(int visitorTime) {
    try {
      var dateTime = DateTime.fromMillisecondsSinceEpoch(visitorTime);
      return TimeUtil.formatTime2(dateTime);
    } catch (e) {
      return "";
    }
  }
}

class TitleBarModel extends ChangeNotifier {
  bool? isManagerLevel;
  String? id;
  bool isGroup = false;
  String areaName = "执行人";

  void checkUserLevel(BaseState state) {
    UserInfoUtil.isBDMOrGJRBDM().then((value) {
      isManagerLevel = value;
      state.setState(() {});
    });
  }

  bool isBDMShow() {
    return isManagerLevel == true;
  }

  bool isBDShow() {
    return isManagerLevel == false;
  }

  @override
  void dispose() {
    print("guan:TitleBarModel dispose");
    super.dispose();
  }
}

class ChildTaskListModel1 extends ChildTaskListModel {
  ChildTaskListModel1(
      String taskId,
      ChildTaskStatus status,
      EasyRefreshController easyRefreshController,
      ScrollController scrollController,
      BaseState state)
      : super(taskId, status, easyRefreshController, scrollController, state);
}

class ChildTaskListModel2 extends ChildTaskListModel {
  ChildTaskListModel2(
      String taskId,
      ChildTaskStatus status,
      EasyRefreshController easyRefreshController,
      ScrollController scrollController,
      BaseState state)
      : super(taskId, status, easyRefreshController, scrollController, state);
}

class ChildTaskListModel3 extends ChildTaskListModel {
  ChildTaskListModel3(
      String taskId,
      ChildTaskStatus status,
      EasyRefreshController easyRefreshController,
      ScrollController scrollController,
      BaseState state)
      : super(taskId, status, easyRefreshController, scrollController, state);
}

class ChildTaskListModel extends ChangeNotifier {
  List<TaskChildItemData>? list;
  ChildTaskStatus status;
  String taskId;
  bool? isLastPage = true;
  bool? isSuccess;
  bool isDisposed = false;
  int? offset = 0;
  String? customerName;
  EasyRefreshController easyRefreshController;
  ScrollController scrollController;
  BaseState state;

  ChildTaskListModel(this.taskId, this.status, this.easyRefreshController,
      this.scrollController, this.state);

  void requestTaskList(
      bool isRefresh, bool? isManager, String? id, bool isGroup) {
    var params = Map<String, dynamic>();
    if (isRefresh) {
      params["offset"] = 0;
    } else {
      params["offset"] = offset! + 1;
    }
    params["limit"] = 10;
    if (status.id != -1) {
      params["taskStatus"] = status.id;
    }
    params["id"] = taskId;
    if (isManager == true) {
      requestBDMTaskList(isRefresh, params, id, isGroup);
    } else if (isManager == false) {
      requestBDTaskList(isRefresh, params);
    }
  }

  void requestBDMTaskList(
      bool isRefresh, Map<String, dynamic> params, String? id, bool isGroup) {
    if (isRefresh) {
      EasyLoading.show(status: "加载中...", maskType: EasyLoadingMaskType.clear);
    }
    if (id != null && id.isNotEmpty) {
      if (isGroup) {
        params["groupId"] = id;
      } else {
        params["searchUserId"] = id;
      }
    }
    NetworkV2<TaskChildListData>(TaskChildListData())
        .requestDataV2("task/m/childTask",
            method: RequestMethod.GET, parameters: params)
        .then((value) {
      handleResult(value.isSuccess, value.getData(), params, isRefresh);
    });
  }

  void handleResult(bool? isSuccess, TaskChildListData? value,
      Map<String, dynamic> params, bool isRefresh) {
    if (!isDisposed) {
      EasyLoading.dismiss();
      this.isSuccess = isSuccess;
      easyRefreshController.finishLoad();
      easyRefreshController.finishRefresh();
      if (isSuccess==true) {
        offset = params["offset"] as int?;
        if (isRefresh) {
          scrollController.jumpTo(0.0);
          list = value!.rows;
        } else {
          list!.addAll(value!.rows!);
        }
        isLastPage = value.lastPage;
      } else if (isRefresh) {
        offset = 0;
        list = null;
        isLastPage = true;
      }
      state.setState(() {});
    }
  }

  void requestBDTaskList(bool isRefresh, Map<String, dynamic> params) {
    if (isRefresh) {
      EasyLoading.show(status: "加载中...", maskType: EasyLoadingMaskType.clear);
    }
    if (customerName != null && customerName!.isNotEmpty) {
      params["customerName"] = customerName;
    }
    NetworkV2<TaskChildListData>(TaskChildListData())
        .requestDataV2("task/bd/childTask",
            method: RequestMethod.GET, parameters: params)
        .then((value) {
      handleResult(value.isSuccess, value.getData(), params, isRefresh);
    });
  }

  @override
  void dispose() {
    isDisposed = true;
    print("guan:ChildTaskListModel dispose $status");
    super.dispose();
  }
}
