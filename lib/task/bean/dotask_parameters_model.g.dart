// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dotask_parameters_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DoTaskParametersModel _$DoTaskParametersModelFromJson(
    Map<String, dynamic> json) {
  return DoTaskParametersModel()
    ..id = json['id'] as String?
    ..remark = json['remark'] as String?
    ..imageUrl = json['imageUrl'] as String?
    ..result = (json['result'] as Map<String, dynamic>?)?.map(
      (k, e) => MapEntry(k, e as String),
    )
    ..scheduleId = json['scheduleId'] as int?
    ..taskType = json['taskType'] as int?
    ..longitude = (json['longitude'] as num?)?.toDouble()
    ..latitude = (json['latitude'] as num?)?.toDouble()
    ..collectAddress = json['collectAddress'] as String?;
}

Map<String, dynamic> _$DoTaskParametersModelToJson(
        DoTaskParametersModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'remark': instance.remark,
      'imageUrl': instance.imageUrl,
      'result': instance.result,
      'scheduleId': instance.scheduleId,
      'taskType': instance.taskType,
      'longitude': instance.longitude,
      'latitude': instance.latitude,
      'collectAddress': instance.collectAddress,
    };
