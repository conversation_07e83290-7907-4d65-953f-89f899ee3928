import 'package:flutter/material.dart';

typedef DoTaskSelectValueChange = void Function(
  String? itemKey,
  ValueNotifier<String?>? controller,
);

// ignore: must_be_immutable
class TaskDoTaskSelectItem extends StatefulWidget {
  final String? itemTitle;
  final String? itemKey;
  final String? placehold;
  String? content;

  final DoTaskSelectValueChange? selectChange;

  TaskDoTaskSelectItem({
    this.itemTitle,
    this.itemKey,
    this.placehold,
    this.content,
    this.selectChange,
  });

  @override
  State<StatefulWidget> createState() {
    return TaskDoTaskSelectItemState();
  }
}

class TaskDoTaskSelectItemState extends State<TaskDoTaskSelectItem> {
  ValueNotifier<String?>? contentNotifier;

  @override
  void initState() {
    super.initState();
    this.contentNotifier = ValueNotifier(widget.content);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(15, 15, 15, 0),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: 90,
                child: Text(
                  widget.itemTitle ?? "",
                  style: TextStyle(
                    fontSize: 15,
                    color: Color(0xFF292933),
                  ),
                  maxLines: 2,
                ),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    widget.selectChange!(widget.itemKey, this.contentNotifier);
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Container(
                          margin: EdgeInsets.only(left: 15, right: 10),
                          child: ValueListenableBuilder(
                            valueListenable: contentNotifier!,
                            builder: (BuildContext context, String? value,
                                Widget? child) {
                              return Text(
                                isEmptyContent(value)
                                    ? (widget.placehold ?? "")
                                    : value!,
                                style: TextStyle(
                                  fontSize: 15,
                                  color: isEmptyContent(value)
                                      ? Color(0xFF9494A6)
                                      : Color(0xFF292933),
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              );
                            },
                          ),
                        ),
                      ),
                      Image.asset(
                        'assets/images/task/dotask_select_arrow.png',
                        width: 12,
                        height: 12,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          SizedBox(
            height: 15,
          ),
          Container(
            color: Color(0xFFF6F6F6),
            height: 1,
          ),
        ],
      ),
    );
  }

  bool isEmptyContent(String? content) {
    return content?.isEmpty ?? true;
  }
}
