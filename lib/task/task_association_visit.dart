import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/task/bean/task_association_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:date_format/date_format.dart';

class TaskAssociationVisitPage extends BasePage {
  final String? taskId;

  TaskAssociationVisitPage({this.taskId});

  @override
  BaseState<StatefulWidget> initState() {
    return TaskAssociationVisitState();
  }
}

class TaskAssociationVisitState extends BaseState<TaskAssociationVisitPage> {
  List<TaskAssociationListModel>? dataSource;

  String? keyWord;

  int page = 0;

  bool requestSuccess = true;

  EasyRefreshController _refreshController = EasyRefreshController();

  @override
  void initState() {
    super.initState();
    this.refreshData();
  }

  @override
  Widget buildWidget(Object context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context as BuildContext).requestFocus(FocusNode());
      },
      child: Container(
        child: Column(
          children: [
            searchInput(context as BuildContext),
            Expanded(
              child: EasyRefresh(
                controller: _refreshController,
                onRefresh: () async {
                  this.refreshData();
                },
                onLoad: () async {
                  this.loadMoreData();
                },
                child: ListView.builder(
                  itemCount: this.dataSource?.length ?? 0,
                  itemBuilder: getItemForIndex,
                ),
                emptyWidget: getEmptyWidget(),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget? getEmptyWidget() {
    if (requestSuccess == false &&
        (dataSource == null || dataSource!.length == 0)) {
      return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
        this.refreshData();
      });
    }

    if ((dataSource?.length ?? 0) == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  Widget searchInput(BuildContext context) {
    return Container(
      margin: EdgeInsets.fromLTRB(15, 10, 15, 5),
      alignment: Alignment.center,
      height: 34,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(2),
        color: Color(0xFFF5F5F5),
      ),
      child: TextField(
        onChanged: (text) {},
        onSubmitted: onChangeKeyWord,
        style: TextStyle(
          fontSize: 13,
          color: Color(0xFF292933),
        ),
        controller: TextEditingController.fromValue(
          TextEditingValue(
            text: keyWord ?? "",
            selection: TextSelection.fromPosition(
              ///用来设置文本的位置
              TextPosition(
                affinity: TextAffinity.downstream,
                // 光标向后移动的长度
                offset: keyWord?.length ?? 0,
              ),
            ),
          ),
        ),
        scrollPadding: EdgeInsets.zero,
        textInputAction: TextInputAction.search,
        decoration: InputDecoration(
          counterText: "",
          hintText: "搜索拜访",
          hintStyle: TextStyle(
            fontSize: 13,
            color: Color(0xFF676773),
          ),
          hintMaxLines: 1,
          border: OutlineInputBorder(
            borderSide: BorderSide.none,
          ),
          contentPadding: EdgeInsets.zero,
          prefixIcon: Container(
            margin: EdgeInsets.only(left: 3, right: 1.5),
            child: Image.asset(
              'assets/images/task/task_association_search.png',
            ),
          ),
          prefixIconConstraints: BoxConstraints(
            maxHeight: 22,
            maxWidth: 26.5,
          ),
          isDense: true,
        ),
        // 最大长度
        maxLines: 1,
      ),
    );
  }

  Widget getItemForIndex(BuildContext context, int index) {
    var model = this.dataSource![index];
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        onTapItemForIndex(index);
      },
      child: Container(
        padding: EdgeInsets.fromLTRB(15, 10, 15, 5),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              model.scheduleTheme ?? "-",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Color(0xFF292933),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: 4),
            Row(
              children: [
                Container(
                  width: 70,
                  child: Text(
                    model.userName ?? "-",
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF9494A6),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(left: 12),
                  child: Text(
                    model.type == 1 ? '电话拜访' : '上门拜访',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF9494A6),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Spacer(),
                Container(
                  child: Text(
                    formatDate(
                      DateTime.fromMillisecondsSinceEpoch(model.createTime!),
                      [yyyy, '.', mm, '.', 'dd', ' ', HH, ':', nn, ':', 'ss'],
                    ),
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF9494A6),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            SizedBox(height: 15),
            Container(
              color: Color(0xFFF7F7F8),
              height: 1,
            ),
          ],
        ),
      ),
    );
  }

  /// 关键词变更
  void onChangeKeyWord(String keyword) {
    this.keyWord = keyword;
    refreshData();
  }

  /// 点击item方法
  void onTapItemForIndex(int index) {
    FocusScope.of(context).requestFocus(FocusNode());

    var model = this.dataSource![index];

    Navigator.of(context).pop({
      'visitId': model.id,
      'visitTheme': model.scheduleTheme,
    });
  }

  void refreshData() {
    showLoadingDialog();
    this.page = 0;
    this.requestListData();
  }

  void loadMoreData() {
    this.requestListData();
  }

  Future requestListData() async {
    var result =
        await Network<TaskAssociationModel>(TaskAssociationModel()).requestData(
      'schedule/linkableScheduleList',
      parameters: {
        "taskId": widget.taskId,
        "offset": this.page,
        "limit": 10,
        "keyword": this.keyWord ?? "",
      },
    );
    dismissLoadingDialog();
    if (mounted) {
      _refreshController.finishRefresh();
      _refreshController.finishLoad(noMore: result.rows!.length < 10);
      if (result.isSuccess==true) {
        setState(() {
          if (page == 0) {
            this.dataSource = result.rows;
          } else {
            this.dataSource!.addAll(result.rows!);
          }
        });
      } else {
        showToast(result.errorMsg!);
      }
      if (!result.lastPage!) {
        this.page += 1;
      }
    }
  }

  @override
  String getTitleName() {
    return "关联拜访";
  }
}
