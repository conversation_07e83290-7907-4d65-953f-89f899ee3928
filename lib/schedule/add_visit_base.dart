import 'dart:convert';
import 'dart:io';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/base/picker/string_value_picker.dart';
import 'package:XyyBeanSproutsFlutter/common/section_group_handler/section_group_handler.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_config_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_contactor_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_external_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_post_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_show_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/sources/add_config_source.dart';
import 'package:XyyBeanSproutsFlutter/schedule/widget/schedule_image_item.dart';
import 'package:XyyBeanSproutsFlutter/schedule/widget/schedule_input_item.dart';
import 'package:XyyBeanSproutsFlutter/schedule/widget/schedule_location_item.dart';
import 'package:XyyBeanSproutsFlutter/schedule/widget/schedule_select_item.dart';
import 'package:XyyBeanSproutsFlutter/schedule/widget/schedule_switch_item.dart';
import 'package:XyyBeanSproutsFlutter/schedule/widget/schedule_text_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'data/schedule_post_data_params.dart';

class AddVisitBasePage extends BasePage {
  /// 1:新建拜访 2:完善拜访 3:新建陪访
  final int pageType;

  late final String? externalJson;

  /// 权限JSON
  final String? rolesJSON;

  final dynamic isHeyeVisit;

  AddVisitBasePage({
    required this.pageType,
    this.externalJson,
    this.rolesJSON,
    this.isHeyeVisit,
  });

  @override
  BaseState<StatefulWidget> initState() {
    return AddVisitBasePageState();
  }
}

class AddVisitBasePageState extends BaseState<AddVisitBasePage> {
  List<ScheduleConfigSectionData> configList = [];

  /// 外部传入的初始化参数
  ScheduleExternalModel? externalModel;

  /// 上传接口使用的模型
  SchedulePostDataModel postModel = SchedulePostDataModel();

  /// 页面展示数据
  ScheduleShowData showModel = ScheduleShowData();

  late SectionGroupHandler _groupHandler;

  double bottomInset = 0;

  @override
  void initState() {
    this.loadConfigData();

    /// 存在外部传入的数据
    if (widget.externalJson != null && widget.externalJson!.length > 0) {
      var externalJsonCopy = widget.externalJson!.replaceAll("pppoooppp", "#");
      Map<String, dynamic> externalMap = json.decode(externalJsonCopy);
      this.externalModel = ScheduleExternalModel.fromJson(externalMap);
      if (widget.isHeyeVisit.toString() == "1") {
        this.showModel.poiId = this.externalModel?.customer?.poiId ?? null;
        this.showModel.isHeyeVisit = true;
      }
      this.configDefaultModel();
    }
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return super.build(context);
  }

  @override
  Widget buildWidget(BuildContext context) {
    this.generateGroupHandler();

    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Container(
        color: Color(0xFFEFEFF4),
        width: MediaQuery.of(context).size.width,
        padding: EdgeInsets.only(bottom: bottomInset),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            Container(
              color: Color(0xFFEFEFF4),
              height: 0.5,
            ),
            Expanded(
              child: ListView.builder(
                itemCount: _groupHandler.allItemCount,
                itemBuilder: (context, index) {
                  return _groupHandler.cellAtIndex(index);
                },
                cacheExtent: 9999,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      leftType: LeftButtonType.custom,
      leftButton: TextButton(
        child: Text(
          "取消",
          style: TextStyle(
            color: Color(0xFF35C561),
            fontSize: 15,
            fontWeight: FontWeight.normal,
          ),
        ),
        onPressed: () {
          if (Navigator.canPop(context)) {
            Navigator.maybePop(context);
          } else {
            XYYContainer.close(context);
          }
        },
        style: ButtonStyle(
          overlayColor: MaterialStateProperty.all<Color>(Colors.transparent),
          padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.zero),
          minimumSize: MaterialStateProperty.all<Size>(Size.zero),
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
      ),
      rightButtons: [
        TextButton(
          child: Text(
            '确定',
            style: TextStyle(
              color: Color(0xFF35C561),
              fontSize: 15,
              fontWeight: FontWeight.normal,
            ),
          ),
          onPressed: () {
            FocusScope.of(context).requestFocus(FocusNode());
            this.requestAddVisitAction();
          },
          style: ButtonStyle(
            overlayColor: MaterialStateProperty.all<Color>(Colors.transparent),
            padding: MaterialStateProperty.all<EdgeInsets>(
                EdgeInsets.only(right: 10)),
            minimumSize: MaterialStateProperty.all<Size>(Size.zero),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ),
      ],
    );
  }

  @override
  String getTitleName() {
    switch (widget.pageType) {
      case 1:
        return '添加拜访';
      case 2:
        return '完善拜访';
      case 3:
        return '添加陪访';
      default:
        return '添加拜访';
    }
  }

  /// Data
  // 读取文件
  void loadConfigData() async {
    List<dynamic> jsonList = AddConfigSource.addConfig();
    switch (widget.pageType) {
      case 1:
        jsonList = AddConfigSource.addConfig();
        break;
      case 2:
        jsonList = AddConfigSource.perfectConfig();
        break;
      case 3:
        jsonList = AddConfigSource.accompanyConfig();
        break;
      default:
        jsonList = AddConfigSource.addConfig();
    }
    // 转模型list
    this.configList = jsonList
        .map((e) =>
            ScheduleConfigSectionData.fromJson(e as Map<String, dynamic>))
        .toList();
    this.reloadPage();
  }

  // 生成分组控制器
  void generateGroupHandler() {
    _groupHandler = SectionGroupHandler(
      numberOfSections: this.configList.length,
      numberOfRowsInSection: (section) {
        return this.configList[section].items.length;
      },
      cellForRowAtIndexPath: (indexPath) {
        ScheduleConfigItemData itemModel =
            this.configList[indexPath.section].items[indexPath.row];
        switch (itemModel.itemType) {
          case 1: // 选择
            return ScheduleSelectItem(
              title: itemModel.itemTitle,
              placeHolder: itemModel.itemPlaceHolder,
              placeHolderColor: itemModel.itemPlaceHolderColor,
              itemKey: itemModel.itemKey,
              content: this.showText(itemModel.itemKey),
              selectAction: (itemKey, controller) {
                this.selectAction(itemModel, controller);
              },
            );
          case 2: // 单行文本输入
            return ScheduleInputItem(
              title: itemModel.itemTitle,
              placeHolder: itemModel.itemPlaceHolder,
              itemKey: itemModel.itemKey,
              isCanEdit: itemModel.isCanEdit ?? true,
              content: this.showText(itemModel.itemKey),
              valueChange: (itemKey, content) {
                if (itemKey != null) {
                  this.inputText(content, itemKey);
                }
              },
            );
          case 3: // 多行文本输入
            return ScheduleTextItem(
              title: itemModel.itemTitle,
              placehold: itemModel.itemPlaceHolder,
              itemKey: itemModel.itemKey,
              content: this.showText(itemModel.itemKey),
              maxLenght: itemModel.textCount,
              valueChange: (itemKey, text) {
                if (itemKey != null) {
                  this.inputText(text, itemKey);
                }
              },
              keyboardShow: (isShow, isCustomKeyboard) {
                this.bottomInset = isShow ? (isCustomKeyboard ? 280 : 40) : 0;
                setState(() {});
              },
            );
          case 4: // switch
            return ScheduleSwitchItem(
              title: itemModel.itemTitle,
              itemKey: itemModel.itemKey,
              isOn: itemModel.isOn ?? false,
              valueChanged: (itemKey, isOn) {
                this.configSwith(isOn, itemKey);
              },
            );
          case 5:
            return ScheduleImageItem(
              isPerfectVisit: false,
              hasTalkTime: false,
              address: this.postModel.merchantVisit,
              limitCount: 10,
              itemKey: itemModel.itemKey,
              imageCahnge: (imageList, itemKey) {
                this.postModel.merchantVisit.image = imageList ?? [];
              },
            );
          default:
            return Container();
        }
      },
      headerForSection: (section) {
        ScheduleConfigSectionData sectionModel = this.configList[section];
        if (sectionModel.sectionKey == "location") {
          return ScheduleLocationItem(
            locationChange: (value) {
              if (value.isSuccess == true) {
                this.postModel.merchantVisit.lng = value.longitude;
                this.postModel.merchantVisit.lat = value.latitude;
                this.postModel.merchantVisit.address = value.address;
                this.reloadPage();
              }
            },
          );
        }
        return Container();
      },
      footerForSection: (section) {
        ScheduleConfigSectionData sectionModel = this.configList[section];
        if (sectionModel.sectionKey == "info" ||
            sectionModel.sectionKey == "object") {
          return Container();
        }
        return Container(
          color: Color(0xFFEFEFF4),
          height: 10,
        );
      },
    );
  }

  /// 配置初始化参数
  void configDefaultModel() {
    if (this.externalModel != null) {
      /// 外部传入客户信息则设置对象不可选择
      if (this.externalModel?.customer?.id != null) {
        ScheduleConfigSectionData sectionModel = this.configList.firstWhere(
            (element) => element.sectionKey == "info",
            orElse: () => ScheduleConfigSectionData());
        if (sectionModel.sectionKey == "info") {
          ScheduleConfigItemData itemModel = sectionModel.items.firstWhere(
              (element) => element.itemKey == "object",
              orElse: () => ScheduleConfigItemData());
          if (itemModel.itemKey == "object") {
            itemModel.isCanEdit = false;
            itemModel.itemType = 2;
          }
        }

        /// 如果存在客户 则请求客户的任务状态
        // this.requestCustomHaveTask(this.externalModel!.customer!.id!);
      }

      /// 配置荷叶拜访的对象经营状况
      this.configHeyeObjects();

      /// 配置拜访信息
      this.configMerchant(this.externalModel!);
    }
  }

  /// 配置荷叶拜访的对象经营状况
  void configHeyeObjects() {
    if (this.showModel.isHeyeVisit == true) {
      /// 隐藏对象经营状况
      ScheduleConfigSectionData sectionModel = this.configList.firstWhere(
          (element) => element.sectionKey == "object",
          orElse: () => ScheduleConfigSectionData());
      if (sectionModel.sectionKey == "object") {
        ScheduleConfigItemData item = sectionModel.items.firstWhere(
            (element) => element.itemKey == "status",
            orElse: () => ScheduleConfigItemData());
        if (item.itemKey == "status") {
          sectionModel.items
              .removeWhere((element) => element.itemKey == "status");
          this.reloadPage();
        }
      }
    } else {
      /// 显示对象经营状况
      ScheduleConfigSectionData sectionModel = this.configList.firstWhere(
          (element) => element.sectionKey == "object",
          orElse: () => ScheduleConfigSectionData());
      if (sectionModel.sectionKey == "object") {
        ScheduleConfigItemData item = sectionModel.items.firstWhere(
            (element) => element.itemKey == "status",
            orElse: () => ScheduleConfigItemData());
        if (item.itemKey != "status") {
          ScheduleConfigItemData itemData = ScheduleConfigItemData.fromJson({
            "selectType": 6,
            "itemType": 1,
            "itemPlaceHolder": "(必填)",
            "itemKey": "status",
            "itemTitle": "对象经营状况"
          });
          sectionModel.items.insert(0, itemData);
          this.reloadPage();
        }
      }
    }
  }

  /// 设置对象经营状况显示 必填项
  void configMerchantPlaceHolder(bool need) {
    ScheduleConfigSectionData sectionModel = this.configList.firstWhere(
        (element) => element.sectionKey == "object",
        orElse: () => ScheduleConfigSectionData());
    if (sectionModel.sectionKey == "object") {
      ScheduleConfigItemData item = sectionModel.items.firstWhere(
          (element) => element.itemKey == "status",
          orElse: () => ScheduleConfigItemData());
      if (item.itemKey == "status") {
        item.itemPlaceHolder = need ? "(必填)" : "";
      }
    }
  }

  /// 刷新页面
  void reloadPage() {
    if (mounted) {
      setState(() {});
    }
  }

  /// 选择调用
  void selectAction(
      ScheduleConfigItemData itemModel, ValueNotifier<String?> controller) {
    /// 调用选择方法前 收起键盘
    FocusScope.of(context).requestFocus(FocusNode());

    switch (itemModel.selectType) {
      case 1: // 关联任务
        this.selectTaskAction(controller);
        break;
      case 2: // 拜访方式
        this.selectTypeAction(controller);
        break;
      case 3: // 拜访对象
        this.selectObjectAction(controller);
        break;
      case 4: // 联系人
        this.selectContactsAction(controller);
        break;
      case 5: // 拜访目的
        this.selectPurposeAction(controller);
        break;
      case 6: // 对象经营状况
        this.selectStatusAction();
        break;
      case 7: // 陪访人
        this.selectAccompanyAction(controller);
        break;
      case 8: // 通话时长
        this.selectDurationAction(controller);
        break;
      case 9: // 岗位
        this.selectJobAction(controller);
        break;
      default:
    }
  }

  /// 选择陪访人员 + 交给子类重写
  void selectAccompanyAction(ValueNotifier<String?> controller) {}

  /// 选择通话时长 + 交给子类重写
  void selectDurationAction(ValueNotifier<String?> controller) {}

  void showSuccessToast() {
    showToast('新建拜访成功');
  }

  /// 检查必填项
  bool checkNeedData() {
    // / 上门拜访经纬度必传
    if (this.postModel.merchantVisit.visitType == "1") {
      if (this.postModel.merchantVisit.lat == null ||
          this.postModel.merchantVisit.lng == null ||
          this.postModel.merchantVisit.address == null) {
        showToast("请定位成功后，再提交拜访");
        return false;
      }
    }

    if (this.postModel.personalSchedule.type == null) {
      showToast("请选择拜访方式");
      return false;
    }

    if (this.postModel.merchantVisit.merchantName == null) {
      showToast("请选择拜访对象");
      return false;
    }

    /// 如何选择的对象是注册对象
    if (this.showModel.isRegister == true) {
      if (this.postModel.merchantVisit.contactor == null) {
        showToast("请添加联系人");
        return false;
      }
    }

    if (this.postModel.merchantVisit.visitReason == null) {
      showToast("请选择拜访目的");
      return false;
    }

    if (this.postModel.personalSchedule.remark == null ||
        this.postModel.personalSchedule.remark?.length == 0) {
      showToast("请输入拜访总结");
      return false;
    }

    if (this.postModel.merchantContactNew != null) {
      if (this.postModel.merchantContactNew!.contactJob == null ||
          this.postModel.merchantContactNew!.contactJob?.length == 0) {
        showToast("请选择联系人岗位");
        return false;
      }

      if (this.postModel.merchantContactNew!.contactName == null ||
          this.postModel.merchantContactNew!.contactName?.length == 0) {
        showToast("请填写联系人姓名");
        return false;
      }
    }

    if (this.postModel.merchantVisit.visitType == "1" &&
        this.postModel.merchantVisit.image.length == 0) {
      showToast("请拍照上传");
      return false;
    }

    if (this.postModel.merchantVisit.visitType == "1" &&
        this.postModel.crmMerchantBasicInfo.checkNeedData() == false &&
        this.showModel.isHeyeVisit == false) {
      showToast("请完善对象经营状况");
      return false;
    }

    return true;
  }

  /// 输入事件处理
  void inputText(String? text, String itemKey) {
    switch (itemKey) {
      case "contacts":
        this.postModel.merchantVisit.contactor = text;
        this.showModel.contactor = text;
        break;
      case "mobile":
        this.postModel.merchantVisit.mobile = text;
        break;
      case "summary":
        this.postModel.personalSchedule.remark = text;
        break;
      case "name":
        if (this.postModel.merchantContactNew != null) {
          this.postModel.merchantContactNew?.contactName = text;
          this.postModel.merchantVisit.contactor = text;
        } else {
          this.postModel.merchantContactNew = CRMMerchantContactNewModel();
          this.postModel.merchantContactNew?.contactName = text;
          this.postModel.merchantVisit.contactor = text;
        }
        break;
      default:
    }
  }

  /// 展示信息处理
  String? showText(String itemKey) {
    switch (itemKey) {
      case "task":
        return this.showModel.scheduleTheme;
      case "visitType":
        return this.showModel.visitTypeName;
      case "object":
        return this.postModel.merchantVisit.merchantName;
      case "contacts":

        /// 选择了客户
        if (this.postModel.merchantVisit.merchantId != null) {
          /// 已注册客户
          if (this.showModel.isRegister) {
            /// 存在手机号的情况下 展示注册电话
            String? mobile = this.postModel.merchantVisit.mobile;
            if (mobile != null && mobile.length > 0) {
              /// 客户联系人名称为空，且已注册
              String? contactor = this.showModel.contactor;
              if (contactor != null && contactor.length > 0) {
                return contactor;
              }
              return "注册电话";
            }
          }
        }
        return this.showModel.contactor;
      case "mobile":
        return this.postModel.merchantVisit.mobile;
      case "purpose":
        return this.showModel.purposeName;
      case "kp":
        return this.showModel.isKPName;
      case "summary":
        return this.postModel.personalSchedule.remark;
      case "job":
        return this.showModel.jobName;
      case "name":
        return this.postModel.merchantContactNew?.contactName;
      default:
        return null;
    }
  }

  /// 经营状况是否必填
  bool merchantBasicInfoIsNeed() {
    return this.postModel.merchantVisit.visitType == "1";
  }
}

/// 选择事件处理
extension SelectAction on AddVisitBasePageState {
  /// 选择任务
  void selectTaskAction(ValueNotifier<String?> controller) async {
    if (this.postModel.merchantVisit.merchantName == null) {
      showToast('请先选择拜访对象');
      return;
    }
    String router =
        "/schedule_relevance_task_page?customerId=${this.postModel.merchantVisit.merchantId ?? ""}";
    dynamic result = await Navigator.of(context).pushNamed(router);
    if (result != null) {
      Map<String, dynamic> resultMap = result;
      if (resultMap.containsKey('selectedList')) {
        List<dynamic> resultList = resultMap["selectedList"];
        List<String> themeList = resultList.map((e) {
          Map<String, dynamic> taskItem = e;
          String themeTitle = taskItem['themeTitle'];
          return themeTitle;
        }).toList();
        String themeTitle = themeList.join(',');
        this.showModel.scheduleTheme = themeTitle;
        this.postModel.taskMamagemDetailList = resultList;
        controller.value = themeTitle;
      }
    }
  }

  /// 选择拜访方式
  void selectTypeAction(ValueNotifier<String?> controller) {
    List<Map<String, String>> typeValues = [
      {"电话拜访": "2"},
      {"上门拜访": "1"}
    ];
    Map<String, String> visitTypeValue = {"电话拜访": "1", "上门拜访": "2"};
    showStringValuePickerView<Map<String, String>>(
      context: context,
      source: typeValues,
      defaultValue: this.showModel.visitTypeName,
      middleTitle: "选择拜访方式",
      sourceBuilder: (value) {
        return value.keys.first;
      },
      selectedAction: (value) {
        this.postModel.merchantVisit.visitType = value.values.first;
        this.postModel.personalSchedule.type = visitTypeValue[value.keys.first];
        this.showModel.visitTypeName = value.keys.first;
        controller.value = value.keys.first;

        /// 设置经营状况展示
        this.configMerchantPlaceHolder((value.keys.first == "上门拜访"));
        this.reloadPage();
      },
    );
  }

  /// 选择对象
  void selectObjectAction(ValueNotifier<String?> controller) {
    var router = '/schedule_select_object?rolesJSON=${widget.rolesJSON ?? ""}';
    router = Uri.encodeFull(router);
    Navigator.of(context).pushNamed(router).then((value) {
      if (value is Map<String, dynamic>?) {
        Map<String, dynamic>? result = value;
        if (result != null) {
          if (result.containsKey("isHeyeVisit")) {
            this.showModel.isHeyeVisit = result["isHeyeVisit"];
          } else {
            this.showModel.isHeyeVisit = false;
          }
          this.configHeyeObjects();
          ScheduleExternalModel allinfoModel =
              ScheduleExternalModel.fromJson(result);
          controller.value = allinfoModel.customer?.name ?? "";
          this.showModel.poiId = allinfoModel.customer?.poiId;
          // this.requestCustomHaveTask(allinfoModel.customer?.id ?? "");
          this.configMerchant(allinfoModel);
        }
      }
    });
  }

  /// 选择联系人
  void selectContactsAction(ValueNotifier<String?> controller) {
    if (this.postModel.merchantVisit.merchantId == null) {
      showToast('请选择对象');
      return;
    }
    if (this.postModel.merchantVisit.merchantName == null) {
      showToast('对象信息异常');
      return;
    }

    /// 如何是荷叶拜访则需要传递poiId
    if (this.showModel.isHeyeVisit == true) {
      String routerPath =
          "xyy://crm-app.ybm100.com/schedule/hy_select_contacts?customerId=${this.postModel.merchantVisit.merchantId}&merchantName=${this.postModel.merchantVisit.merchantName}&poiId=${this.showModel.poiId}";
      if (this.showModel.selectContactorId != null) {
        routerPath =
            routerPath + "&selectId=${this.showModel.selectContactorId}";
      }
      routerPath = Uri.encodeFull(routerPath);
      XYYContainer.open(routerPath, callback: (result) {
        if (result != null) {
          ScheduleContactorData contactorModel =
              ScheduleContactorData.fromJson(result);
          this.configContractor(contactorModel);
          controller.value = this.showModel.contactor;
        }
      });
    } else {
      var customerId = this.externalModel?.customer?.id == null
          ? this.postModel.merchantVisit.customerId
          : this.externalModel?.customer?.id;
      String routerPath =
          '/visit_select_contact?customerId=$customerId&customerName=${this.postModel.merchantVisit.merchantName}';
      if (this.showModel.selectContactorId != null) {
        routerPath =
            routerPath + "&selectId=${this.showModel.selectContactorId}";
      }
      routerPath = Uri.encodeFull(routerPath);
      Navigator.of(context).pushNamed(routerPath).then((value) {
        if (value is Map<String, dynamic>?) {
          Map<String, dynamic>? result = value;
          if (result != null) {
            ScheduleContactorData contactorModel =
                ScheduleContactorData.fromJson(result);
            this.configContractor(contactorModel);
            controller.value = this.showModel.contactor;
          }
        }
      });
    }
  }

  /// 选择拜访目的
  void selectPurposeAction(ValueNotifier<String?> controller) {
    List<Map<String, String>> purposeValues = [
      {"开发新客户": "1"},
      {"客情维护": "2"},
      {"新品推介": "3"},
      {"活动促销": "4"},
      {"商品维价": "5"},
      {"资质回收": "9"},
      {"老客唤醒": "10"},
      {"售后处理": "11"},
    ];
    showStringValuePickerView<Map<String, String>>(
      context: context,
      source: purposeValues,
      defaultValue: this.showModel.purposeName,
      middleTitle: "请选择拜访目的",
      sourceBuilder: (value) {
        return value.keys.first;
      },
      selectedAction: (value) {
        this.postModel.merchantVisit.visitReason = value.values.first;
        this.showModel.purposeName = value.keys.first;
        controller.value = value.keys.first;
      },
    );
  }

  /// 对象经营状况
  void selectStatusAction() {
    Map<String, dynamic> basicInfo =
        this.postModel.crmMerchantBasicInfo.toJson();
    String jsonStr = json.encode(basicInfo);
    String isNeedEdit = this.merchantBasicInfoIsNeed() ? "true" : "false";
    String routerPath =
        "xyy://crm-app.ybm100.com/schedule/object_status?crmMerchantBasicInfo=$jsonStr&isNeedEdit=$isNeedEdit";
    routerPath = Uri.encodeFull(routerPath);
    XYYContainer.open(routerPath, callback: (result) {
      if (result != null) {
        if (Platform.isAndroid) {
          var resultJson = result["result"];
          if (resultJson != null) {
            ScheduleMerchantBasicInfoModel basicInfoModel =
                ScheduleMerchantBasicInfoModel.fromJson(
                    json.decode(resultJson));
            this.postModel.crmMerchantBasicInfo = basicInfoModel;
          }
        } else {
          ScheduleMerchantBasicInfoModel basicInfoModel =
              ScheduleMerchantBasicInfoModel.fromJson(result);
          this.postModel.crmMerchantBasicInfo = basicInfoModel;
        }
      }
    });
  }

  /// 选择岗位
  void selectJobAction(ValueNotifier<String?> controller) async {
    // 读取文件内容
    String jsonString =
        await rootBundle.loadString("assets/sources/schedule/CRMJobMap.json");
    // 转List
    List<dynamic> jsonList = json.decode(jsonString);
    List<Map<String, dynamic>> resultMap =
        jsonList.map((e) => e as Map<String, dynamic>).toList();
    showStringValuePickerView<Map<String, dynamic>>(
      context: context,
      source: resultMap,
      defaultValue: this.showModel.jobName,
      middleTitle: "请选择岗位",
      sourceBuilder: (value) {
        return value['contactJobName']!;
      },
      selectedAction: (value) {
        this.showModel.jobName = value['contactJobName'];
        if (this.postModel.merchantContactNew != null) {
          this.postModel.merchantContactNew?.contactJob = value['contactJob'];
        } else {
          this.postModel.merchantContactNew = CRMMerchantContactNewModel();
          this.postModel.merchantContactNew?.contactJob = value['contactJob'];
        }
        this.postModel.merchantVisit.isEffective = value['isEffective']!;
        this.showModel.isKPName = value['isEffective'] == "1" ? "是" : "否";
        controller.value = value['contactJobName'];
        this.reloadPage();
      },
    );
  }
}

/// 变更信息处理
extension ChangeAction on AddVisitBasePageState {
  /// 配置选择的客户
  void configMerchant(ScheduleExternalModel allInfoModel) {
    /// 重置拜访信息
    this.resetMerchantInfo();

    /// 关联任务重置
    this.showModel.scheduleTheme = null;
    this.postModel.taskMamagemDetailList = [];

    /// 对象经营信息 重置
    if (allInfoModel.merchantBasicInfo != null) {
      this.postModel.crmMerchantBasicInfo =
          ScheduleMerchantBasicInfoModel.fromJson(
              allInfoModel.merchantBasicInfo!.toJson());
    } else {
      this.postModel.crmMerchantBasicInfo = ScheduleMerchantBasicInfoModel();
    }

    /// 设置经营状况展示
    this.configMerchantPlaceHolder(
        this.postModel.merchantVisit.visitType == "1");

    /// 选择的对象
    this.postModel.merchantVisit.merchantId =
        allInfoModel.merchantMap!.merchantId.toString();
    this.postModel.merchantVisit.customerId =
        allInfoModel.merchantMap!.id.toString();
    this.postModel.merchantVisit.merchantName = allInfoModel.customer?.name;

    /// 是否是注册用户
    this.configObjectInfo(allInfoModel.registerFlag.toString() == "1");

    /// 任务关联的客户，如果是未注册，则保持与外部传入的客户信息处理方式一致 不回显联系人信息
    if (allInfoModel.contactList != null &&
        allInfoModel.contactList!.length > 0 &&
        (allInfoModel.registerFlag.toString() == "1" ||
            this.showModel.isHeyeVisit == true)) {
      ScheduleExternalContactModel firstContactItem =
          allInfoModel.contactList!.first;

      /// 已注册 存在联系人信息
      this.postModel.merchantVisit.contactor = firstContactItem.contactName;
      this.showModel.contactor = firstContactItem.contactName;
      this.postModel.merchantVisit.mobile = firstContactItem.contactMobile;
      this.postModel.merchantVisit.isEffective =
          firstContactItem.isEffective == 1 ? "1" : "2";
      this.showModel.isKPName = firstContactItem.isEffective == 1 ? "是" : "否";
      this.showModel.isExistContactor = true;
      this.showModel.selectContactorId = firstContactItem.id.toString();

      /// 联系人配置
      ScheduleContactorData model =
          ScheduleContactorData.fromJson(firstContactItem.toJson());
      this.configContractor(model);
    } else {
      /// 未注册 或不存在联系人信息
      this.postModel.merchantVisit.contactor = null;
      this.showModel.contactor = null;
      this.postModel.merchantVisit.mobile = null;
      this.postModel.merchantVisit.isEffective = "1";
      this.showModel.isKPName = "是";
      this.showModel.isExistContactor = false;
      this.showModel.selectContactorId = null;
    }

    this.reloadPage();
  }

  /// 重置拜访信息
  void resetMerchantInfo() {
    this.postModel.merchantVisit.contactor = null;
    this.showModel.contactor = null;
    this.postModel.merchantVisit.isEffective = "1";
    this.postModel.merchantVisit.mobile = null;
    this.postModel.merchantVisit.merchantId = null;
    this.postModel.merchantVisit.customerId = null;
    this.postModel.merchantVisit.merchantName = null;

    this.postModel.merchantContactNew = null;
    this.showModel.jobName = null;
  }

  /// 配置联系人选项
  void configObjectInfo(bool isRegister) {
    this.showModel.isRegister = isRegister;

    ScheduleConfigSectionData sectionModel = this.configList.firstWhere(
        (element) => element.sectionKey == "info",
        orElse: () => ScheduleConfigSectionData());
    if (sectionModel.sectionKey == "info") {
      sectionModel.items.removeWhere((element) => element.itemKey == "job");
      sectionModel.items.removeWhere((element) => element.itemKey == "name");
      sectionModel.items.removeWhere(
          (element) => element.itemKey == "mobile" && element.itemType == 2);
      ScheduleConfigItemData itemModel = sectionModel.items.firstWhere(
          (element) => element.itemKey == "contacts",
          orElse: () => ScheduleConfigItemData());
      if (itemModel.itemKey == "contacts") {
        itemModel.itemType = 1;
        itemModel.isCanEdit = true;
        itemModel.itemPlaceHolder = "请添加联系人(必填)";
        ScheduleConfigItemData kpItem = sectionModel.items.firstWhere(
            (element) => element.itemKey == "kp",
            orElse: () => ScheduleConfigItemData());
        if (kpItem.itemKey == "kp") {
          kpItem.itemType = 2;
          kpItem.isCanEdit = false;
          kpItem.defaultText =
              this.postModel.merchantVisit.isEffective == "1" ? "是" : "否";
        }
      }
    }
  }

  /// 联系人数据配置
  void configContractor(ScheduleContactorData model) {
    this.showModel.isKPName = model.isEffective == 1 ? "是" : "否";
    this.postModel.merchantVisit.isEffective =
        model.isEffective == 1 ? "1" : "2";
    this.postModel.merchantVisit.contactor = model.contactName;
    this.showModel.contactor = model.contactName;
    this.postModel.merchantVisit.mobile = model.contactMobile;

    /// 选择联系人后，重置完善联系人信息的数据
    this.postModel.merchantContactNew = null;

    /// 设置选择的
    this.showModel.selectContactorId = "${model.id}";
    this.showModel.isExistContactor = true;
    this.showModel.jobName = null;

    // /// 如果是未完善的联系人, 则赋值处理
    // bool isPerfect = this.checkContactForPerfect(model);
    // if (!isPerfect) {
    //   this.postModel.merchantContactNew = CRMMerchantContactNewModel();
    //   this.postModel.merchantContactNew?.contactMobile = model.contactMobile;
    // }
    //
    // /// 配置联系人展示数据
    // this.configRegisterNotPerfect(model);

    this.reloadPage();
  }

  /// 已注册且联系人信息未完善的 处理
  void configRegisterNotPerfect(ScheduleContactorData model) {
    /// 已注册客户         未注册走填写逻辑此处不处理判断信息
    if (this.showModel.isRegister == true) {
      ScheduleConfigSectionData sectionModel = this.configList.firstWhere(
          (element) => element.sectionKey == "info",
          orElse: () => ScheduleConfigSectionData());
      if (sectionModel.sectionKey == "info") {
        /// id为-1的是无效联系人，当成已完善的联系人处理
        bool isPerfect = this.checkContactForPerfect(model);
        if (isPerfect) {
          /// 如果是完善的联系人信息 走之前的默认逻辑
          sectionModel.items.removeWhere((element) => element.itemKey == "job");
          this.configObjectInfo(this.showModel.isRegister);
        } else {
          /// 如果是未完善的联系人信息，则添加岗位与联系方式处理
          int itemIndex = sectionModel.items
              .indexWhere((element) => element.itemKey == "contacts");
          if (itemIndex != -1) {
            ScheduleConfigItemData itemModel = sectionModel.items[itemIndex];
            itemModel.itemType = 1;
            itemModel.isCanEdit = true;
            itemModel.itemPlaceHolder = "请添加联系人(必填)";
            ScheduleConfigItemData mobileItemModel = sectionModel.items
                .firstWhere((element) => element.itemKey == "mobile",
                    orElse: () => ScheduleConfigItemData());
            if (mobileItemModel.itemKey == "mobile") {
              mobileItemModel.itemType = 2;
              mobileItemModel.itemKey = "mobile";
              mobileItemModel.itemTitle = "联系方式";
              mobileItemModel.isCanEdit = false;
              mobileItemModel.itemPlaceHolder = "请添加联系人电话(必填)";
            } else {
              ScheduleConfigItemData mobileItemModel = ScheduleConfigItemData();
              mobileItemModel.itemType = 2;
              mobileItemModel.itemKey = "mobile";
              mobileItemModel.itemTitle = "联系方式";
              mobileItemModel.isCanEdit = false;
              mobileItemModel.itemPlaceHolder = "请添加联系人电话(必填)";
              sectionModel.items.insert(itemIndex + 1, mobileItemModel);
              itemIndex += 1;
            }
            ScheduleConfigItemData nameItemModel = sectionModel.items
                .firstWhere((element) => element.itemKey == "name",
                    orElse: () => ScheduleConfigItemData());
            if (nameItemModel.itemKey == "name") {
              nameItemModel.itemType = 2;
              nameItemModel.itemKey = "name";
              nameItemModel.itemTitle = "姓名";
              nameItemModel.isCanEdit = true;
              nameItemModel.itemPlaceHolder = "请填写联系人姓名(必填)";
            } else {
              ScheduleConfigItemData nameItemModel = ScheduleConfigItemData();
              nameItemModel.itemType = 2;
              nameItemModel.itemKey = "name";
              nameItemModel.itemTitle = "姓名";
              nameItemModel.isCanEdit = true;
              nameItemModel.itemPlaceHolder = "请填写联系人姓名(必填)";
              sectionModel.items.insert(itemIndex + 1, nameItemModel);
              itemIndex += 1;
            }
            ScheduleConfigItemData jobItemModel = sectionModel.items.firstWhere(
                (element) => element.itemKey == "job",
                orElse: () => ScheduleConfigItemData());
            if (jobItemModel.itemKey == "name") {
              jobItemModel.itemTitle = "岗位";
              jobItemModel.itemKey = "job";
              jobItemModel.itemType = 1;
              jobItemModel.selectType = 9;
              jobItemModel.isCanEdit = true;
              jobItemModel.itemPlaceHolder = "请选择(必填)";
            } else {
              ScheduleConfigItemData jobItemModel = ScheduleConfigItemData();
              jobItemModel.itemTitle = "岗位";
              jobItemModel.itemKey = "job";
              jobItemModel.itemType = 1;
              jobItemModel.selectType = 9;
              jobItemModel.isCanEdit = true;
              jobItemModel.itemPlaceHolder = "请选择(必填)";
              sectionModel.items.insert(itemIndex + 1, jobItemModel);
            }
          }
        }
      }
    }
  }

  /// 判断当前选择的联系人是否已完善
  bool checkContactForPerfect(ScheduleContactorData model) {
    String contactName = model.contactName ?? "";
    String contactJob = model.contactJob ?? "";
    String contactId = "${model.id}";
    return (contactName.length > 0 && contactJob.length > 0) ||
        (contactId == "-1");
  }
}

extension Switch on AddVisitBasePageState {
  void configSwith(bool isOn, String itemKey) {
    switch (itemKey) {
      case "share":
        this.postModel.shareStatus = isOn ? "1" : "0";
        ScheduleConfigSectionData sectionModel = this.configList.firstWhere(
            (element) => element.sectionKey == "location",
            orElse: () => ScheduleConfigSectionData());
        if (sectionModel.sectionKey == "location") {
          ScheduleConfigItemData kpItem = sectionModel.items.firstWhere(
              (element) => element.itemKey == "share",
              orElse: () => ScheduleConfigItemData());
          if (kpItem.itemKey == "share") {
            kpItem.isOn = isOn;
          }
        }
        this.reloadPage();
        break;
      case "kp":
        this.postModel.merchantVisit.isEffective = isOn ? "1" : "2";
        ScheduleConfigSectionData sectionModel = this.configList.firstWhere(
            (element) => element.sectionKey == "info",
            orElse: () => ScheduleConfigSectionData());
        if (sectionModel.sectionKey == "info") {
          ScheduleConfigItemData kpItem = sectionModel.items.firstWhere(
              (element) => element.itemKey == "kp",
              orElse: () => ScheduleConfigItemData());
          if (kpItem.itemKey == "kp") {
            kpItem.isOn = isOn;
          }
        }
        this.reloadPage();
        break;
      default:
        break;
    }
  }
}

/// 请求
extension RequestAction on AddVisitBasePageState {
  /// 请求选择客户是否存在待完成的任务
  void requestCustomHaveTask(String customerId) async {
    var result =
        await Network<NetworkBaseModel>(NetworkBaseModel()).requestData(
      '/task/v2/isHaveTask',
      method: RequestMethod.GET,
      parameters: {"customerId": customerId},
    );
    if (result.isSuccess != null && result.isSuccess == true) {
      ScheduleConfigSectionData sectionModel = this.configList.firstWhere(
          (element) => element.sectionKey == "related",
          orElse: () => ScheduleConfigSectionData());
      if (sectionModel.sectionKey == "related") {
        ScheduleConfigItemData taskItemModel = sectionModel.items.firstWhere(
            (element) => element.itemKey == "task",
            orElse: () => ScheduleConfigItemData());
        if (taskItemModel.itemKey == "task") {
          taskItemModel.itemPlaceHolder =
              ("${result.data}" == "1") ? "此客户有待完成的任务" : "请选择关联任务";
          this.reloadPage();
        }
      }
    }
  }

  void requestAddVisitAction() {
    if (this.checkNeedData()) {
      this.postVisitData();
    }
  }

  void postVisitData() async {
    showLoadingDialog();
    var lat = this.postModel.merchantVisit.lat;
    var lng = this.postModel.merchantVisit.lng;
    var userInfo = await UserInfoUtil.getUserInfo();
    var paramsModel = SchedulePostDataParamsModel();
    paramsModel.crmMerchantBasicInfoVo = this.postModel.crmMerchantBasicInfo;
    paramsModel.merchantVisitParam =
        ScheduleMerchantVisitParamsModel.fromScheduleMerchantVisitModel(
            this.postModel.merchantVisit,
            this.postModel.merchantVisit.customerId,
            userInfo?.sysUserId,
            this.postModel.personalSchedule.remark,
            this.postModel.personalSchedule.id);
    String jsonStr = json.encode(paramsModel);
    var result =
        await Network<NetworkBaseModel>(NetworkBaseModel()).requestData(
      '/visit/doSaveVisit',
      method: RequestMethod.POST,
      contentType: RequestContentType.FORM,
      parameters: {"visitParam": jsonStr},
    );
    dismissLoadingDialog();
    if (mounted && result.isSuccess == true) {
      this.showSuccessToast();

      // Future.delayed(Duration(seconds: 2), () {
      if ((widget.pageType == 2 || widget.pageType == 1) &&
          this.postModel.merchantVisit.visitType == "2") {
      } else {
        XYYContainer.open('/add_visit_recommendation_page?lat=$lat&lng=$lng');
      }
      XYYContainer.close(context, resultData: {"success": "true"});
      // });
    }
  }
}
