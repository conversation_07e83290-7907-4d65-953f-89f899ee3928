import 'package:XyyBeanSproutsFlutter/customer/data/customer_sku_collect_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/ybm_customer_contact_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'ybm_customer_private_model.g.dart';

@JsonSerializable()
class YBMCusomerPrivateModel extends BaseModelV2<YBMCusomerPrivateModel> {
  @JsonKey()
  YBMCustomerPrivatePageModel? pageData;

  @override
  YBMCusomerPrivateModel fromJsonMap(Map<String, dynamic> json) {
    return _$YBMCusomerPrivateModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$YBMCusomerPrivateModelToJson(this);
  }
}

@JsonSerializable()
class YBMCustomerPrivatePageModel
    extends BaseModelV2<YBMCustomerPrivatePageModel> {
  @JsonKey(defaultValue: [])
  List<YBMCustomerPrivateItemModel>? rows;

  dynamic lastPage;

  YBMCustomerPrivatePageModel();

  @override
  YBMCustomerPrivatePageModel fromJsonMap(Map<String, dynamic> json) {
    return _$YBMCustomerPrivatePageModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$YBMCustomerPrivatePageModelToJson(this);
  }

  factory YBMCustomerPrivatePageModel.fromJson(Map<String, dynamic> json) {
    return _$YBMCustomerPrivatePageModelFromJson(json);
  }
}

///资质状态-正常
const LICENSE_VALIDATE_STATUS_NORMAL = -1;
///资质状态-临期
const LICENSE_VALIDATE_STATUS_ADVENT = 1;
///资质状态-过期
const LICENSE_VALIDATE_STATUS_EXPIRE = 2;

///组合资质状态-正常
const LICENSE_VALIDATE_STATUS_COMPOSE_NORMAL = 0;
///组合资质状态-必填临期
const LICENSE_VALIDATE_STATUS_COMPOSE_REQUIRED_ADVENT = 1;
///组合资质状态-非必临期
const LICENSE_VALIDATE_STATUS_COMPOSE_NO_REQUIRED_ADVENT = 2;
///组合资质状态-必填过期
const LICENSE_VALIDATE_STATUS_COMPOSE_REQUIRED_EXPIRE = 3;
///组合资质状态-非必过期
const LICENSE_VALIDATE_STATUS_COMPOSE_NO_REQUIRED_EXPIRE = 4;

@JsonSerializable()
class YBMCustomerPrivateItemModel
    extends BaseModelV2<YBMCustomerPrivateItemModel> {

  // 详细地址-- POI地址
  dynamic address;

  /// 客户级别名称
  dynamic biLevelName;

  /// 购物车数量
  @JsonKey(defaultValue: 0)
  dynamic cartNum;

  /// 客户名称-- POI客户名称
  dynamic customerName;

  /// 客户类型名称
  dynamic customerTypeName;

  /// bi生命周期
  dynamic biLifecycle;

  /// bi生命周期对应的名称
  dynamic biLifecycleName;

  /// 距当前位置的距离
  dynamic distance;

  /// 是否可分配 1是0否
  dynamic distributable;

  /// 主键
  dynamic id;

  /// poiid
  dynamic poiId;

  /// 荷叶门店id
  dynamic hyId;

  /// 资质需回收标识：1是 0 否
  dynamic licenseRecoveryFlag;

  /// 资质临期过期标识  1临期2过期
  dynamic licenseValidateFlag;

  /// 资质状态名称
  dynamic licenseStatusName;

  /// 客户ID（已注册有）
  dynamic merchantId;

  /// 手机号
  dynamic mobile;

  /// 认领人级别
  dynamic oaUserLevel;

  /// 距离上次拜访的天数
  @JsonKey(defaultValue: '-')
  dynamic overLastVisitDays;

  /// POI纬度
  dynamic poiLatitude;

  /// POI经度
  dynamic poiLongitude;

  /// 是否注册标识 1是，2否 默认注册
  dynamic registerFlag;

  /// 本月下单金额
  @JsonKey(defaultValue: '')
  dynamic thisMonthOrderAmt;

  /// 本月订单数
  @JsonKey(defaultValue: '')
  dynamic thisMonthOrderNum;

  /// 本月下单频次
  @JsonKey(defaultValue: '')
  dynamic thisMonthOrderCount;

  /// 本月购买的SKU数
  @JsonKey(defaultValue: '')
  dynamic thisMonthOrderSkuCount;

  /// 上月下单SKU数
  @JsonKey(defaultValue: '')
  dynamic lastMonthOrderSkuCount;

  /// 上月订单数
  @JsonKey(defaultValue: '')
  dynamic lastMonthOrderNum;

  /// 上月下单金额
  @JsonKey(defaultValue: '')
  dynamic lastMonthOrderAmt;

  /// 药帮忙注册时的地址
  dynamic ecAddress;

  /// 药帮忙注册时的门店名称
  @JsonKey(defaultValue: '')
  dynamic ecCustomerName;

  /// 客户状态
  @JsonKey(defaultValue: '')
  dynamic merchantStatusName;

  /// 1:审核中  2:已认证   3:已驳回
  dynamic status;

  /// 客户状态
  dynamic statusName;

  /// 距离释放多少天
  dynamic relaseDays;

  /// 荷叶合作状态
  dynamic customerStatusName;

  /// 距离掉公海时长
  dynamic bindCountdown;

  /// 是否新开业门店 1:新开业
  dynamic poiRegisterFlag;

  /// 联系人
  List<YBMCustomerContactModel>? contactList;

  /// 合作内容
  List<YBMCustomerServiceLinesModel>? serviceLines;

  /// 商品集
  List<CustomerSkuCollectData>? bindSkuCollect;

  ///必填资质；-1 正常；1-临期；2-过期
  dynamic licenseValidateMust;

  ///非必填资质 -1 正常；1-临期；2-过期
  dynamic licenseValidateIssue;

  ///是否已经加入拜访计划
  @JsonKey(defaultValue: false)
  dynamic inPlanFlag;

  YBMCustomerPrivateItemModel();

  String getAddress() {
    if ("$registerFlag" == "1") {
      if ("$ecAddress".length != 0) {
        return "$ecAddress";
      } else {
        return "-";
      }
    } else {
      if ("$address".length != 0) {
        return "$address";
      } else {
        return "-";
      }
    }
  }

  String getName() {
    if ("$registerFlag" == "1") {
      if ("$ecCustomerName".length != 0) {
        return "$ecCustomerName";
      } else if ("$customerName".length != 0) {
        return "$customerName";
      } else {
        return "-";
      }
    } else {
      if ("$customerName".length != 0) {
        return "$customerName";
      } else {
        return "-";
      }
    }
  }

  /// 获取是否已认领商品集  地图列表使用
  @JsonKey(ignore: true)
  bool get isRevice {
    bool isRevice = false;
    this.bindSkuCollect?.forEach((element) {
      if ("${element.receiveType}" == "1" &&
          element.permissionClaimedFlag == true) {
        isRevice = true;
      }
    });
    return isRevice;
  }

  ///获取当前资质状态
  int getLicenseValidateStatus(String licenseCode) {
    if(licenseCode != ""){
      return this.hasLicenseCodeVadateStatus(licenseCode);
    }
    if (licenseValidateMust == LICENSE_VALIDATE_STATUS_NORMAL && licenseValidateIssue == LICENSE_VALIDATE_STATUS_NORMAL) {
      return LICENSE_VALIDATE_STATUS_COMPOSE_NORMAL;
    } else if (licenseValidateMust == LICENSE_VALIDATE_STATUS_NORMAL && licenseValidateIssue == LICENSE_VALIDATE_STATUS_ADVENT) {
      return LICENSE_VALIDATE_STATUS_COMPOSE_NO_REQUIRED_ADVENT;
    } else if (licenseValidateMust == LICENSE_VALIDATE_STATUS_NORMAL && licenseValidateIssue == LICENSE_VALIDATE_STATUS_EXPIRE) {
      return LICENSE_VALIDATE_STATUS_COMPOSE_NO_REQUIRED_EXPIRE;
    } else if (licenseValidateMust == LICENSE_VALIDATE_STATUS_ADVENT && licenseValidateIssue == LICENSE_VALIDATE_STATUS_NORMAL) {
      return LICENSE_VALIDATE_STATUS_COMPOSE_REQUIRED_ADVENT;
    } else if (licenseValidateMust == LICENSE_VALIDATE_STATUS_EXPIRE && licenseValidateIssue == LICENSE_VALIDATE_STATUS_NORMAL) {
      return LICENSE_VALIDATE_STATUS_COMPOSE_REQUIRED_EXPIRE;
    } else if (licenseValidateMust == LICENSE_VALIDATE_STATUS_EXPIRE) {
      return LICENSE_VALIDATE_STATUS_COMPOSE_REQUIRED_EXPIRE;
    } else if (licenseValidateMust == LICENSE_VALIDATE_STATUS_ADVENT) {
      return LICENSE_VALIDATE_STATUS_COMPOSE_REQUIRED_ADVENT;
    } else if (licenseValidateIssue == LICENSE_VALIDATE_STATUS_ADVENT) {
      return LICENSE_VALIDATE_STATUS_COMPOSE_NO_REQUIRED_ADVENT;
    }
    return LICENSE_VALIDATE_STATUS_COMPOSE_NORMAL;
  }

  //如果有筛选项的资质，那么优先显示筛选项的
  int hasLicenseCodeVadateStatus(String licenseCode) {
    if(licenseValidateMust == LICENSE_VALIDATE_STATUS_EXPIRE && licenseCode.contains("12")){
      return LICENSE_VALIDATE_STATUS_COMPOSE_REQUIRED_EXPIRE;
    } else if (licenseValidateIssue == LICENSE_VALIDATE_STATUS_EXPIRE && licenseCode.contains("22")) {
      return LICENSE_VALIDATE_STATUS_COMPOSE_NO_REQUIRED_EXPIRE;
    } else if (licenseValidateMust == LICENSE_VALIDATE_STATUS_ADVENT && licenseCode.contains("11")) {
      return LICENSE_VALIDATE_STATUS_COMPOSE_REQUIRED_ADVENT;
    } else if (licenseValidateIssue == LICENSE_VALIDATE_STATUS_ADVENT && licenseCode.contains("21")) {
      return LICENSE_VALIDATE_STATUS_COMPOSE_NO_REQUIRED_ADVENT;
    }
    return LICENSE_VALIDATE_STATUS_COMPOSE_NORMAL;
  }

  @override
  YBMCustomerPrivateItemModel fromJsonMap(Map<String, dynamic> json) {
    return _$YBMCustomerPrivateItemModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$YBMCustomerPrivateItemModelToJson(this);
  }

  factory YBMCustomerPrivateItemModel.fromJson(Map<String, dynamic> json) {
    return _$YBMCustomerPrivateItemModelFromJson(json);
  }
}

/// 合作服务model
@JsonSerializable()
class YBMCustomerServiceLinesModel
    extends BaseModelV2<YBMCustomerServiceLinesModel> {
  dynamic code;
  dynamic name;
  dynamic lighten;
  dynamic iOSIcon;

  YBMCustomerServiceLinesModel();

  @override
  YBMCustomerServiceLinesModel fromJsonMap(Map<String, dynamic> json) {
    return _$YBMCustomerServiceLinesModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$YBMCustomerServiceLinesModelToJson(this);
  }

  factory YBMCustomerServiceLinesModel.fromJson(Map<String, dynamic> json) {
    return _$YBMCustomerServiceLinesModelFromJson(json);
  }
}
