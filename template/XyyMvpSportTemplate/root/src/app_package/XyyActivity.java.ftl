package ${ativityPackageName};

import android.os.Bundle;
import android.support.annotation.NonNull;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.activity.BaseMVPCompatActivity;
import ${contractPackageName}.${pageName}Contract;
import ${presenterPackageName}.${pageName}Presenter;
import ${packageName}.R;
<#import "root://activities/XyyMvpSportTemplate/globals.xml.ftl" as gb>

<@gb.fileHeader />
public class ${pageName}Activity extends BaseMVPCompatActivity<${pageName}Presenter> implements ${pageName}Contract.I${pageName}View {

    @NonNull
    @Override
    public BasePresenter initPresenter() {
        return ${pageName}Presenter.newInstance();
    }

     @Override
    protected int getLayoutId() {
        return R.layout.${activityLayoutName};
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
      
    }

    @Override
    public void showNetError() {

    }

}
